"""
ComputationFrame图变化演示
专门展示能够产生明显变化的CF图概念案例

重点展示：
1. 扩展操作如何改变图结构
2. 不同起始点如何影响图的构建
3. 图的动态演化过程

通过对比展示图概念的变化原因
"""

import os
import sys
import tempfile

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


class CFGraphChangesDemo:
    """ComputationFrame图变化演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        
    def print_cf_summary(self, cf: ComputationFrame, title: str):
        """打印CF的简要信息"""
        nodes = len(cf.nodes)
        edges = len(list(cf.edges()))
        sources = len(cf.sources)
        sinks = len(cf.sinks)
        density = edges / (nodes * (nodes - 1)) if nodes > 1 else 0

        print(f"\n📊 {title}:")
        print(f"  🔢 节点: {nodes} | 🔗 边: {edges} | 🌱 源: {sources} | 🎯 汇: {sinks} | 📈 密度: {density:.3f}")

        # 显示节点详情
        if cf.vnames:
            print(f"  📦 变量节点: {list(cf.vnames)}")
        if cf.fnames:
            print(f"  ⚙️  函数节点: {list(cf.fnames)}")
        if cf.sources:
            print(f"  🌱 源节点详情: {list(cf.sources)}")
        if cf.sinks:
            print(f"  🎯 汇节点详情: {list(cf.sinks)}")

    def print_cf_detailed_analysis(self, cf: ComputationFrame, title: str):
        """打印CF的详细内部结构分析"""
        print(f"\n🔍 {title} - 内部结构详细分析:")

        # 1. nodes的组成分析
        print(f"\n  📋 nodes属性详解:")
        print(f"    💡 cf.nodes = cf.vnames ∪ cf.fnames (变量节点 ∪ 函数节点)")
        print(f"    🔢 cf.nodes: {cf.nodes}")
        print(f"    📦 cf.vnames: {cf.vnames} (变量节点名称集合)")
        print(f"    ⚙️  cf.fnames: {cf.fnames} (函数节点名称集合)")
        print(f"    ✅ 验证: len(nodes) = len(vnames) + len(fnames) = {len(cf.vnames)} + {len(cf.fnames)} = {len(cf.nodes)}")

        # 2. vs和fs的详细分析
        print(f"\n  🗂️  内部存储结构分析:")
        print(f"    💡 cf.vs: 变量名 → 历史ID集合的映射")
        print(f"    💡 cf.fs: 函数名 → 历史ID集合的映射")

        if cf.vs:
            print(f"    📦 cf.vs详情:")
            for vname, hids in cf.vs.items():
                print(f"      • {vname}: {len(hids)}个历史ID - {list(hids)[:3]}{'...' if len(hids) > 3 else ''}")

        if cf.fs:
            print(f"    ⚙️  cf.fs详情:")
            for fname, hids in cf.fs.items():
                print(f"      • {fname}: {len(hids)}个历史ID - {list(hids)[:3]}{'...' if len(hids) > 3 else ''}")

        # 3. 变量名的含义分析
        print(f"\n  🏷️  变量名含义分析:")
        for vname in cf.vnames:
            if vname.startswith('var_'):
                print(f"    • {vname}: 自动生成的变量名，通常是函数的输出结果")
            elif vname == 'v':
                print(f"    • {vname}: 通用变量名，通常是用户指定的起始节点")
            elif vname in ['data', 'raw_data', 'source']:
                print(f"    • {vname}: 描述性变量名，反映数据的性质或来源")
            else:
                print(f"    • {vname}: 用户定义的变量名")

        # 4. 源汇节点的计算逻辑
        print(f"\n  🔄 源汇节点计算逻辑:")
        print(f"    🌱 源节点计算: sources = {{node for node in cf.vs.keys() if len(cf.inp[node]) == 0}}")
        print(f"      💡 含义: 没有输入边的变量节点")
        print(f"    🎯 汇节点计算: sinks = {{node for node in cf.vs.keys() if len(cf.out[node]) == 0}}")
        print(f"      💡 含义: 没有输出边的变量节点")
        print(f"    ⚠️  注意: 源汇节点只考虑变量节点，不包括函数节点")

        # 5. 边的计算和含义
        edges = list(cf.edges())
        print(f"\n  🔗 边的详细分析:")
        print(f"    🔢 边总数: {len(edges)}")
        if edges:
            print(f"    📋 边的类型分析:")
            func_to_var = 0
            var_to_func = 0
            for src, dst, label in edges[:5]:  # 只显示前5条边
                if src in cf.fnames and dst in cf.vnames:
                    func_to_var += 1
                    print(f"      • {src}({label}) → {dst}: 函数输出到变量")
                elif src in cf.vnames and dst in cf.fnames:
                    var_to_func += 1
                    print(f"      • {src}({label}) → {dst}: 变量输入到函数")

            if len(edges) > 5:
                print(f"      ... 还有{len(edges)-5}条边")

            # 统计边类型
            total_func_to_var = sum(1 for src, dst, _ in edges if src in cf.fnames and dst in cf.vnames)
            total_var_to_func = sum(1 for src, dst, _ in edges if src in cf.vnames and dst in cf.fnames)
            print(f"    📊 边类型统计:")
            print(f"      • 函数→变量: {total_func_to_var}条 (数据生产)")
            print(f"      • 变量→函数: {total_var_to_func}条 (数据消费)")

        # 6. 图密度的含义
        print(f"\n  📈 图密度分析:")
        max_edges = len(cf.nodes) * (len(cf.nodes) - 1) if len(cf.nodes) > 1 else 0
        density = len(edges) / max_edges if max_edges > 0 else 0
        print(f"    💡 密度公式: 实际边数 / 最大可能边数 = {len(edges)} / {max_edges} = {density:.3f}")
        if density < 0.1:
            print(f"    🔍 稀疏图: 节点间连接较少，计算相对独立")
        elif density > 0.5:
            print(f"    🔍 密集图: 节点间连接较多，计算高度耦合")
        else:
            print(f"    🔍 中等密度: 适中的计算复杂度")

    def print_cf_member_variables_explanation(self):
        """打印ComputationFrame所有成员变量的详细说明"""
        print(f"\n📚 ComputationFrame成员变量完整说明:")
        print(f"=" * 80)

        print(f"\n🏗️  图结构定义 (Graph Schema):")
        print(f"  📥 inp: Dict[str, Dict[str, Set[str]]]")
        print(f"    💡 含义: 节点名 → 输入名 → {{连接的节点名集合}}")
        print(f"    🎯 作用: 定义每个节点的输入连接关系")
        print(f"    📝 示例: {{'func1': {{'input1': {{'var1', 'var2'}}}}")

        print(f"  📤 out: Dict[str, Dict[str, Set[str]]]")
        print(f"    💡 含义: 节点名 → 输出名 → {{连接的节点名集合}}")
        print(f"    🎯 作用: 定义每个节点的输出连接关系")
        print(f"    📝 示例: {{'func1': {{'output1': {{'var3', 'var4'}}}}")

        print(f"\n🗃️  图实例数据 (Graph Instance Data):")
        print(f"  📦 vs: Dict[str, Set[str]]")
        print(f"    💡 含义: 变量名 → {{历史ID集合}}")
        print(f"    🎯 作用: 存储每个变量节点包含的所有Ref对象的历史ID")
        print(f"    📝 示例: {{'data': {{'hid1', 'hid2'}}, 'result': {{'hid3'}}}}")
        print(f"    🔍 重要性: cf.vnames = set(cf.vs.keys())")

        print(f"  ⚙️  fs: Dict[str, Set[str]]")
        print(f"    💡 含义: 函数名 → {{历史ID集合}}")
        print(f"    🎯 作用: 存储每个函数节点包含的所有Call对象的历史ID")
        print(f"    📝 示例: {{'process_data': {{'call_hid1', 'call_hid2'}}}}")
        print(f"    🔍 重要性: cf.fnames = set(cf.fs.keys())")

        print(f"  🔄 refinv: Dict[str, Set[str]]")
        print(f"    💡 含义: Ref历史ID → {{包含该Ref的变量名集合}}")
        print(f"    🎯 作用: 反向索引，快速查找Ref属于哪些变量节点")
        print(f"    📝 示例: {{'ref_hid1': {{'var1', 'var2'}}}}")

        print(f"  🔄 callinv: Dict[str, Set[str]]")
        print(f"    💡 含义: Call历史ID → {{包含该Call的函数名集合}}")
        print(f"    🎯 作用: 反向索引，快速查找Call属于哪些函数节点")
        print(f"    📝 示例: {{'call_hid1': {{'func1', 'func2'}}}}")

        print(f"  🏭 creator: Dict[str, str]")
        print(f"    💡 含义: Ref历史ID → 创建该Ref的Call历史ID")
        print(f"    🎯 作用: 追踪每个数据对象的创建来源")
        print(f"    📝 示例: {{'ref_hid1': 'call_hid1'}}")
        print(f"    🔍 重要性: 支持数据血缘追踪")

        print(f"  🍽️  consumers: Dict[str, Set[str]]")
        print(f"    💡 含义: Ref历史ID → {{消费该Ref的Call历史ID集合}}")
        print(f"    🎯 作用: 追踪每个数据对象的使用情况")
        print(f"    📝 示例: {{'ref_hid1': {{'call_hid2', 'call_hid3'}}}}")
        print(f"    🔍 重要性: 支持影响分析")

        print(f"\n📦 对象存储 (Object Storage):")
        print(f"  🔗 refs: Dict[str, Union[Ref, Any]]")
        print(f"    💡 含义: 历史ID → Ref对象")
        print(f"    🎯 作用: 存储所有的数据对象引用")
        print(f"    📝 示例: {{'hid1': AtomRef(42), 'hid2': ListRef([1,2,3])}}")

        print(f"  📞 calls: Dict[str, Call]")
        print(f"    💡 含义: 历史ID → Call对象")
        print(f"    🎯 作用: 存储所有的函数调用记录")
        print(f"    📝 示例: {{'call_hid1': Call(op=my_func, inputs=..., outputs=...)}}")

        print(f"\n🔍 关键属性计算:")
        print(f"  📊 nodes = vnames ∪ fnames")
        print(f"    💡 含义: 所有节点 = 变量节点 ∪ 函数节点")
        print(f"    🎯 作用: 获取图中的所有节点")

        print(f"  🌱 sources = {{node for node in vs.keys() if len(inp[node]) == 0}}")
        print(f"    💡 含义: 没有输入边的变量节点")
        print(f"    🎯 作用: 识别计算的起始点")

        print(f"  🎯 sinks = {{node for node in vs.keys() if len(out[node]) == 0}}")
        print(f"    💡 含义: 没有输出边的变量节点")
        print(f"    🎯 作用: 识别计算的终点")

        print(f"\n💡 设计理念:")
        print(f"  🎯 分离关注点: 图结构(inp/out) vs 图数据(vs/fs)")
        print(f"  🔄 双向索引: 正向(vs/fs) + 反向(refinv/callinv)")
        print(f"  📈 血缘追踪: creator(数据来源) + consumers(数据使用)")
        print(f"  ⚡ 性能优化: 预计算的索引结构，快速查询")
        print(f"=" * 80)
    
    def print_change_analysis(self, cf_before: ComputationFrame, cf_after: ComputationFrame, operation: str):
        """分析并打印图变化"""
        nodes_before, nodes_after = len(cf_before.nodes), len(cf_after.nodes)
        edges_before, edges_after = len(list(cf_before.edges())), len(list(cf_after.edges()))
        sources_before, sources_after = len(cf_before.sources), len(cf_after.sources)
        sinks_before, sinks_after = len(cf_before.sinks), len(cf_after.sinks)
        
        print(f"\n🔄 {operation} - 变化分析:")
        print(f"  📊 节点: {nodes_before} → {nodes_after} ({nodes_after - nodes_before:+d})")
        print(f"  🔗 边: {edges_before} → {edges_after} ({edges_after - edges_before:+d})")
        print(f"  🌱 源: {sources_before} → {sources_after} ({sources_after - sources_before:+d})")
        print(f"  🎯 汇: {sinks_before} → {sinks_after} ({sinks_after - sinks_before:+d})")
        
        # 分析变化原因
        if nodes_after > nodes_before:
            print(f"  ✅ 发现了 {nodes_after - nodes_before} 个新节点")
        if edges_after > edges_before:
            print(f"  ✅ 发现了 {edges_after - edges_before} 条新的依赖关系")
        if sources_after > sources_before:
            print(f"  ✅ 发现了 {sources_after - sources_before} 个新的数据源")
        if sinks_after > sinks_before:
            print(f"  ✅ 发现了 {sinks_after - sinks_before} 个新的数据汇")
    
    def demonstrate_expansion_changes(self):
        """演示扩展操作的图变化"""
        print("🚀 ComputationFrame扩展操作图变化演示")
        print("=" * 80)

        # 首先说明ComputationFrame的内部结构
        self.print_cf_member_variables_explanation()
        
        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "expansion_changes.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            # 定义完整的计算流水线
            @op
            def load_data(source: str = "database"):
                """加载数据"""
                return f"data_from_{source}"

            @op
            def clean_data(raw_data: str):
                """清洗数据"""
                return f"cleaned_{raw_data}"

            @op
            def extract_features(data: str):
                """提取特征"""
                return f"features_{data}"

            @op
            def train_model(features: str):
                """训练模型"""
                return f"model_{features}"

            @op
            def evaluate_model(model: str):
                """评估模型"""
                return f"evaluation_{model}"

            @op
            def generate_report(evaluation: str):
                """生成报告"""
                return f"report_{evaluation}"

            # 执行完整流水线
            print("\n🔧 执行完整的机器学习流水线...")
            raw_data = load_data("database")
            cleaned = clean_data(raw_data)
            features = extract_features(cleaned)
            model = train_model(features)
            evaluation = evaluate_model(model)
            report = generate_report(evaluation)

            print("\n📍 第一步：从中间节点创建最小CF")
            print("💡 只包含指定的节点，不包含计算历史")
            
            # 从中间节点创建最小CF
            cf_minimal = self.storage.cf(features)
            self.print_cf_summary(cf_minimal, "最小CF（从features创建）")
            self.print_cf_detailed_analysis(cf_minimal, "最小CF")

        # 在context外部进行扩展操作
        print("\n📍 第二步：向后扩展 - 追溯数据来源")
        print("💡 发现生成features的完整计算历史")
        
        cf_back = cf_minimal.expand_back(recursive=True)
        self.print_cf_summary(cf_back, "向后扩展后的CF")
        self.print_change_analysis(cf_minimal, cf_back, "expand_back")
        self.print_cf_detailed_analysis(cf_back, "向后扩展后的CF")

        print("\n📍 第三步：向前扩展 - 追踪数据使用")
        print("💡 发现使用features的所有下游计算")
        
        cf_forward = cf_minimal.expand_forward(recursive=True)
        self.print_cf_summary(cf_forward, "向前扩展后的CF")
        self.print_change_analysis(cf_minimal, cf_forward, "expand_forward")

        print("\n📍 第四步：全方向扩展 - 完整上下文")
        print("💡 获取features相关的完整计算图")
        
        cf_all = cf_minimal.expand_all()
        self.print_cf_summary(cf_all, "全方向扩展后的CF")
        self.print_change_analysis(cf_forward, cf_all, "expand_all vs expand_forward")

        print("\n📊 扩展效果对比总结:")
        print(f"  📦 最小CF:     {len(cf_minimal.nodes):2d}节点, {len(list(cf_minimal.edges())):2d}边, {len(cf_minimal.sources)}源, {len(cf_minimal.sinks)}汇")
        print(f"  📈 向后扩展:   {len(cf_back.nodes):2d}节点, {len(list(cf_back.edges())):2d}边, {len(cf_back.sources)}源, {len(cf_back.sinks)}汇")
        print(f"  📉 向前扩展:   {len(cf_forward.nodes):2d}节点, {len(list(cf_forward.edges())):2d}边, {len(cf_forward.sources)}源, {len(cf_forward.sinks)}汇")
        print(f"  🔄 全方向扩展: {len(cf_all.nodes):2d}节点, {len(list(cf_all.edges())):2d}边, {len(cf_all.sources)}源, {len(cf_all.sinks)}汇")

        print("\n💡 关键洞察:")
        print("  • 最小CF只包含指定节点，信息量最少")
        print("  • 向后扩展发现数据来源，增加源节点和上游计算")
        print("  • 向前扩展发现数据使用，增加汇节点和下游计算")
        print("  • 全方向扩展结合两者，提供完整的计算上下文")
        print("  • 扩展操作是理解计算图结构的关键工具")

    def demonstrate_different_starting_points(self):
        """演示不同起始点对图构建的影响"""
        print("\n🚀 不同起始点的图构建差异演示")
        print("=" * 80)
        
        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "starting_points.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            # 定义计算流水线
            @op
            def input_data(value: int = 42):
                """输入数据"""
                return value

            @op
            def process_step1(data: int):
                """处理步骤1"""
                return data * 2

            @op
            def process_step2(data: int):
                """处理步骤2"""
                return data + 10

            @op
            def combine_results(data1: int, data2: int):
                """合并结果"""
                return data1 + data2

            @op
            def final_output(data: int):
                """最终输出"""
                return f"final_{data}"

            # 执行计算
            input_val = input_data(42)
            step1_result = process_step1(input_val)
            step2_result = process_step2(input_val)
            combined = combine_results(step1_result, step2_result)
            final = final_output(combined)

        print("\n📍 从不同节点创建CF并扩展")
        
        # 从源节点开始
        cf_from_source = self.storage.cf(input_val).expand_all()
        self.print_cf_summary(cf_from_source, "从源节点(input_val)扩展")

        # 从中间节点开始
        cf_from_middle = self.storage.cf(combined).expand_all()
        self.print_cf_summary(cf_from_middle, "从中间节点(combined)扩展")

        # 从汇节点开始
        cf_from_sink = self.storage.cf(final).expand_all()
        self.print_cf_summary(cf_from_sink, "从汇节点(final)扩展")

        print("\n🔄 起始点影响分析:")
        print(f"  🌱 从源节点: {len(cf_from_source.nodes)}节点 - 主要向前扩展，发现下游使用")
        print(f"  🔄 从中间节点: {len(cf_from_middle.nodes)}节点 - 双向扩展，平衡的上下游")
        print(f"  🎯 从汇节点: {len(cf_from_sink.nodes)}节点 - 主要向后扩展，发现上游来源")

        print("\n💡 起始点选择的指导原则:")
        print("  • 调试问题：从出错的节点开始，向后追溯")
        print("  • 影响分析：从要修改的节点开始，向前分析")
        print("  • 完整理解：从关键节点开始，全方向扩展")

    def run_demo(self):
        """运行演示"""
        try:
            # 1. 扩展操作变化演示
            self.demonstrate_expansion_changes()
            
            # 2. 不同起始点演示
            self.demonstrate_different_starting_points()
            
            print("\n🎉 演示完成!")
            print("=" * 80)
            print("通过以上演示，我们看到了ComputationFrame图概念的动态变化：")
            print("• 扩展操作能够显著改变图的结构和规模")
            print("• 不同的起始点会影响扩展的方向和结果")
            print("• 图的节点数、边数、源汇数量都会发生变化")
            print("• 这些变化反映了计算图的真实结构和依赖关系")
            print("=" * 80)
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理临时文件
            if self.temp_dir and os.path.exists(self.temp_dir):
                import shutil
                try:
                    shutil.rmtree(self.temp_dir)
                    print(f"🧹 已清理临时目录: {self.temp_dir}")
                except Exception as e:
                    print(f"⚠️  清理临时目录失败: {e}")


def main():
    """主函数"""
    demo = CFGraphChangesDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
