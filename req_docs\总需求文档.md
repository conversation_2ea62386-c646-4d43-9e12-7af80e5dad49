# 栈重放系统需求文档

## 1. 系统概述
栈重放系统是一个高级调试工具，能够在程序运行后捕获完整的函数执行信息，并提供可视化界面让用户能够回放任意函数调用（使用原始或修改后的参数），同时支持在调用栈中动态插入或删除函数节点。系统主要用于复杂程序的调试、性能分析和代码理解。

## 2. 核心功能

### 2.1 运行信息捕获
- **捕获范围**：
  - 支持全量函数捕获（捕获程序中所有函数调用）
  - 支持模块级捕获（仅捕获指定模块的函数调用）
- **捕获内容**：
  - 函数名称、所属模块
  - 位置参数和关键字参数
  - 函数返回值或异常信息
  - 函数执行前的完整上下文环境
  - 函数调用关系（父子节点关系）
- **捕获方式**：
  - 使用装饰器对目标函数进行标注
  - 支持猴子补丁方式无侵入式注入捕获逻辑
  - 运行时自动构建调用树结构

### 2.2 栈回放功能
- **节点回放**：
  - 使用原始参数和上下文重放选定函数
  - 支持修改参数后重放函数
  - 保留原始上下文环境不变
- **子树回放**：
  - 重放选定节点及其所有子孙节点
  - 按原始调用顺序依次执行
  - 自动更新后续节点的上下文环境
- **回放控制**：
  - 支持单步重放
  - 支持断点设置
  - 执行结果实时反馈

### 2.3 调用图操作
- **节点添加**：
  - 在现有节点之间插入新函数节点
  - 自动继承父节点的上下文环境
  - 新节点执行结果传递给后续节点
- **节点删除**：
  - 移除选定节点（根节点除外）
  - 自动连接父节点和子节点
  - 使用父节点的上下文重新执行子节点
- **动态更新**：
  - 操作后自动更新调用图结构
  - 受影响的节点自动重执行
  - 保持调用关系的一致性

### 2.4 可视化界面
- **图形展示**：
  - 树形结构可视化函数调用关系
  - 节点显示函数名称和关键信息
  - 支持节点拖拽重新布局
- **节点交互**：
  - 右键菜单提供完整操作功能
  - 支持节点展开/折叠
  - 选中节点高亮显示
- **信息展示**：
  - 实时显示运行日志
  - 参数和返回值查看器
  - 上下文环境查看器

## 3. 系统架构

### 3.1 整体架构
```
+----------------+       +----------------+       +----------------+
|  数据捕获层     |       |  图管理层       |       |  可视化层       |
| (Data Capture) | ----> | (Graph Manager)| ----> | (Visualization)|
+----------------+       +----------------+       +----------------+
```

### 3.2 数据捕获层
- **FrameCapture**：
  - 管理捕获范围和方式
  - 创建并组织ComputationFrame对象
- **ComputationFrame**：
  - 表示单个函数调用实例
  - 包含函数信息、参数、结果和上下文
  - 维护父子节点关系
- **ContextManager**：
  - 基于contentvars实现
  - 捕获和恢复函数执行上下文
  - 管理闭包和全局变量环境

### 3.3 图管理层
- **GraphManager**：
  - 基于NetworkX实现
  - 构建和操作调用图结构
  - 提供节点操作API
- **节点操作**：
  - 重放节点（带参数修改）
  - 重放子树
  - 添加/删除节点
  - 更新节点关系
- **依赖管理**：
  - 维护节点间依赖关系
  - 确保操作后的图一致性
  - 处理节点状态传播

### 3.4 可视化层
- **GraphVisualizer**：
  - 基于PyQtGraph实现
  - 主界面管理和布局
- **NodeWidget**：
  - 表示单个图节点
  - 支持拖拽和右键菜单
- **ContextEditor**：
  - 参数修改对话框
  - 支持复杂数据结构编辑
- **运行信息面板**：
  - 实时日志显示
  - 错误信息高亮

## 4. 操作流程

### 4.1 初始捕获流程
1. 用户配置捕获范围（全量或模块级）
2. 运行目标程序
3. 系统自动捕获函数调用信息
4. 构建完整的调用树结构
5. 在可视化界面展示调用图

### 4.2 节点回放流程
1. 用户在图中选择目标节点
2. 右键选择"重放节点"或"修改参数后重放"
3. 如选择修改参数，打开参数编辑器
4. 系统恢复原始上下文环境
5. 使用指定参数执行函数
6. 更新节点结果和后续上下文
7. 在界面中反馈执行结果

### 4.3 子树回放流程
1. 用户选择目标节点
2. 右键选择"重放子树"
3. 系统按深度优先顺序恢复节点上下文
4. 依次执行节点及其所有子孙节点
5. 更新整个子树的结果
6. 可视化界面刷新子树状态

### 4.4 添加节点流程
1. 用户选择父节点A
2. 右键选择"添加节点"
3. 在对话框中指定新函数和参数
4. 系统在A和其子节点之间插入新节点B
5. 使用A的上下文执行B
6. 使用B的结果更新后续节点上下文
7. 重新执行受影响的所有子节点
8. 更新调用图结构

### 4.5 删除节点流程
1. 用户选择目标节点B（非根节点）
2. 右键选择"删除节点"
3. 系统连接B的父节点A和子节点C
4. 使用A的上下文重新执行C及其子树
5. 更新调用图结构
6. 刷新可视化界面

## 5. 非功能需求

### 5.1 性能要求
- 支持包含数千节点的调用图
- 节点操作响应时间<500ms
- 内存高效管理节点数据

### 5.2 可靠性
- 异常处理机制保证系统稳定
- 回放失败时恢复原始状态
- 参数编辑安全处理机制

### 5.3 用户体验
- 直观的可视化界面
- 流畅的交互体验
- 清晰的错误提示
- 上下文信息可读性强

### 5.4 扩展性
- 插件式架构支持新功能扩展
- 支持自定义捕获策略
- API接口支持外部工具集成

## 6. 用户界面设计

### 6.1 主界面布局
```
+-------------------------------------------+
| [总控面板]                                |
|   ▶ 执行程序                              |
|   🔁 重放选中节点                         |
|   🔄 重放选中的子节点                     |
|   📸 捕获调用栈                          |
|   💾 导出数据                            |
+-----------------+-------------------------+
| [节点树位置]    |                         |
| 折叠所有        | [函数调用关系树]        |
| 折叠选中        |   A                     |
| 展开所有        |   ├─ B1                 |
|                 |   │  ├─ c1             |
|                 |   │  ├─ c2             |
|                 |   │  └─ c3             |
|                 |   └─ B2                |
|                 |      └─ c4             |
+-----------------+-------------------------+
| [运行信息]                                |
| 09:45:23 程序开始执行                    |
| 09:45:24 加载函数调用栈信息...           |
| ...                                      |
+-------------------------------------------+
```

### 6.2 节点右键菜单
```
+----------------------+
|   重放节点           |
|   重放子树           |
|   修改参数后重放     |
|   ------------------ |
|   添加节点           |
|   删除节点           |
+----------------------+
```

### 6.3 参数编辑器
```
+------------------------------+
| [编辑参数]                   |
|                              |
| 参数1: [__________]         |
| 参数2: [__________]         |
|                              |
| keyword1: [__________]      |
| keyword2: [__________]      |
|                              |
|       [确定]   [取消]       |
+------------------------------+
```

## 7. 使用场景

### 7.1 调试复杂逻辑
- 重现特定条件下的函数行为
- 修改参数测试边界条件
- 观察上下文变化对后续调用的影响

### 7.2 性能优化
- 定位性能瓶颈函数
- 测试不同参数下的执行效率
- 比较不同实现的性能差异

### 7.3 代码理解
- 可视化复杂调用关系
- 动态观察数据流变化
- 理解第三方库的内部逻辑

### 7.4 教学演示
- 展示算法执行过程
- 可视化数据流变化
- 演示不同参数下的行为差异

## 8. 限制与注意事项

1. **递归处理**：
   - 系统需正确处理递归调用
   - 为每次递归创建独立节点
   - 限制最大递归深度避免资源耗尽

2. **副作用函数**：
   - 回放具有副作用的函数可能产生意外结果
   - 提供明确警告提示
   - 建议在隔离环境中使用

3. **外部依赖**：
   - 外部服务状态变化可能影响回放结果
   - 建议使用模拟环境进行回放

4. **并发支持**：
   - 初始版本不直接支持并发调用
   - 多线程调用按顺序记录
   - 后续版本增加并发可视化支持

本需求文档描述了栈重放系统的核心功能和操作流程，为后续设计实现提供明确指导。系统专注于提供强大的调试能力，同时保持用户界面的直观性和操作的便捷性。