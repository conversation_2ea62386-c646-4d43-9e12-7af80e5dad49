{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# `Storage` & the `@op` Decorator\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/topics/01_storage_and_ops.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "A `Storage` object holds all data (saved calls, code and dependencies) for a\n", "collection of memoized functions. In a given project, you should have just one\n", "`Storage` and many `@op`s connected to it. This way, the calls to memoized\n", "functions create a queriable web of interlinked objects.  "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:35.163619Z", "iopub.status.busy": "2024-07-11T14:31:35.162794Z", "iopub.status.idle": "2024-07-11T14:31:35.173665Z", "shell.execute_reply": "2024-07-11T14:31:35.172980Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a `Storage`\n", "\n", "When creating a storage, you must decide if it will be in-memory or persisted on\n", "disk, and whether the storage will automatically version the `@op`s used with\n", "it:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:35.176299Z", "iopub.status.busy": "2024-07-11T14:31:35.176085Z", "iopub.status.idle": "2024-07-11T14:31:37.876054Z", "shell.execute_reply": "2024-07-11T14:31:37.875495Z"}}, "outputs": [], "source": ["from mandala.imports import Storage\n", "import os\n", "\n", "DB_PATH = 'my_persistent_storage.db'\n", "if os.path.exists(DB_PATH):\n", "    os.remove(DB_PATH)\n", "\n", "storage = Storage(\n", "    # omit for an in-memory storage\n", "    db_path=DB_PATH,\n", "    # omit to disable automatic dependency tracking & versioning\n", "    # use \"__main__\" to only track functions defined in the current session\n", "    deps_path='__main__', \n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating `@op`s and saving calls to them\n", "**Any Python function can be decorated with `@op`**:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:37.880287Z", "iopub.status.busy": "2024-07-11T14:31:37.879681Z", "iopub.status.idle": "2024-07-11T14:31:37.914152Z", "shell.execute_reply": "2024-07-11T14:31:37.913319Z"}}, "outputs": [], "source": ["from mandala.imports import op\n", "\n", "@op \n", "def sum_args(a, *args, b=1, **kwargs):\n", "    return a + sum(args) + b + sum(kwargs.values())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In general, calling `sum_args` will behave as if the `@op` decorator is not\n", "there. `@op`-decorated functions will interact with a `Storage` instance **only\n", "when** called inside a `with storage:` block:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:37.918828Z", "iopub.status.busy": "2024-07-11T14:31:37.918430Z", "iopub.status.idle": "2024-07-11T14:31:38.022162Z", "shell.execute_reply": "2024-07-11T14:31:38.021195Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AtomRef(42, hid=168...)\n"]}], "source": ["with storage: # all `@op` calls inside this block use `storage`\n", "    s = sum_args(6, 7, 8, 9, c=11,)\n", "    print(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This code runs the call to `sum_args`, and saves the inputs and outputs in the\n", "`storage` object, so that doing the same call later will directly load the saved\n", "outputs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### When should something be an `@op`?\n", "As a general guide, you should make something an `@op` if you want to save its\n", "outputs, e.g. if they take a long time to compute but you need them for later\n", "analysis. Since `@op` [encourages\n", "composition](https://amakelov.github.io/mandala/02_retracing/#how-op-encourages-composition),\n", "you should aim to have `@op`s work on the outputs of other `@op`s, or on the\n", "[collections and/or items](https://amakelov.github.io/mandala/05_collections/)\n", "of outputs of other `@op`s."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with `@op` outputs (`Ref`s)\n", "The objects (e.g. `s`) returned by `@op`s are always instances of a subclass of\n", "`Ref` (e.g., `AtomRef`), i.e.  **references to objects in the storage**. Every\n", "`Ref` contains two metadata fields:\n", "\n", "- `cid`: a hash of the **content** of the object\n", "- `hid`: a hash of the **computational history** of the object, which is the precise\n", "composition of `@op`s that created this ref.  \n", "\n", "Two `Ref`s with the same `cid` may have different `hid`s, and `hid` is the\n", "unique identifier of `Ref`s in the storage. However, only 1 copy per unique\n", "`cid` is stored to avoid duplication in the storage.\n", "\n", "### `Ref`s can be in memory or not\n", "Additionally, `Ref`s have the `in_memory` property, which indicates if the\n", "underlying object is present in the `Ref` or if this is a \"lazy\" `Ref` which\n", "only contains metadata. **`Ref`s are only loaded in memory when needed for a new\n", "call to an `@op`**. For example, re-running the last code block:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:38.032711Z", "iopub.status.busy": "2024-07-11T14:31:38.032185Z", "iopub.status.idle": "2024-07-11T14:31:38.077234Z", "shell.execute_reply": "2024-07-11T14:31:38.076082Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AtomRef(hid=168..., in_memory=False)\n"]}], "source": ["with storage: \n", "    s = sum_args(6, 7, 8, 9, c=11,)\n", "    print(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To get the object wrapped by a `Ref`, call `storage.unwrap`:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:38.081995Z", "iopub.status.busy": "2024-07-11T14:31:38.081258Z", "iopub.status.idle": "2024-07-11T14:31:38.115821Z", "shell.execute_reply": "2024-07-11T14:31:38.114805Z"}}, "outputs": [{"data": {"text/plain": ["42"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["storage.unwrap(s) # loads from storage only if necessary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Other useful `Storage` methods\n", "\n", "- `Storage.attach(inplace: bool)`: like `unwrap`, but puts the objects in the\n", "`Ref`s if they are not in-memory.\n", "- `Storage.load_ref(hid: str, in_memory: bool)`: load a `Ref` by its history ID,\n", "optionally also loading the underlying object."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:38.120583Z", "iopub.status.busy": "2024-07-11T14:31:38.119825Z", "iopub.status.idle": "2024-07-11T14:31:38.150984Z", "shell.execute_reply": "2024-07-11T14:31:38.150207Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AtomRef(42, hid=168...)\n", "AtomRef(42, hid=168...)\n"]}], "source": ["print(storage.attach(obj=s, inplace=False))\n", "print(storage.load_ref(s.hid))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with `Call` objects\n", "Besides `Ref`s, the other kind of object in the storage is the `Call`, which\n", "stores references to the inputs and outputs of a call to an `@op`, together with\n", "metadata that mirrors the `Ref` metadata:\n", "\n", "- `Call.cid`: a content ID for the call, based on the `@op`'s identity, its\n", "version at the time of the call, and the `cid`s of the inputs\n", "- `Call.hid`: a history ID for the call, the same as `Call.cid`, but using the \n", "`hid`s of the inputs.\n", "\n", "**For every `Ref` history ID, there's at most one `Call` that has an output with\n", "this history ID**, and if it exists, this call can be found by calling\n", "`storage.get_ref_creator()`: "]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:38.154659Z", "iopub.status.busy": "2024-07-11T14:31:38.154326Z", "iopub.status.idle": "2024-07-11T14:31:38.205594Z", "shell.execute_reply": "2024-07-11T14:31:38.203966Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Call(sum_args, hid=f99...)\n"]}, {"data": {"text/plain": ["{'a': AtomRef(hid=c6a..., in_memory=False),\n", " 'args_0': AtomRef(hid=e0f..., in_memory=False),\n", " 'args_1': AtomRef(hid=479..., in_memory=False),\n", " 'args_2': AtomRef(hid=c37..., in_memory=False),\n", " 'b': AtomRef(hid=610..., in_memory=False),\n", " 'c': AtomRef(hid=a33..., in_memory=False)}"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'output_0': AtomRef(hid=168..., in_memory=False)}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["call = storage.get_ref_creator(ref=s)\n", "print(call)\n", "display(call.inputs)\n", "display(call.outputs)"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}