"""
验证节点操作修复的脚本
"""

def verify_node_operations_fix():
    """验证节点操作修复是否正确"""
    print("🔍 验证节点操作修复")
    print("=" * 50)
    
    # 读取修复后的代码
    try:
        with open('cf_deep_understanding.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 无法读取文件: {e}")
        return False
    
    # 检查关键修复点
    checks = []
    
    # 1. 检查增加节点操作的修复
    if 'cf_with_new_nodes = cf | new_computation_cf' in content:
        checks.append("✅ 增加节点操作使用正确的并集合并")
    else:
        checks.append("❌ 增加节点操作未使用正确的并集合并")
    
    # 2. 检查新增节点统计
    if 'new_nodes = set(cf_with_new_nodes.nodes) - original_nodes' in content:
        checks.append("✅ 正确计算新增节点")
    else:
        checks.append("❌ 未正确计算新增节点")
    
    # 3. 检查修改节点操作的修复
    if 'cf_modified = cf_without_target | new_computation_cf' in content:
        checks.append("✅ 修改节点操作使用正确的合并策略")
    else:
        checks.append("❌ 修改节点操作未使用正确的合并策略")
    
    # 4. 检查节点保留验证
    if 'preserved_nodes = original_nodes_except_target & modified_nodes' in content:
        checks.append("✅ 包含节点保留验证")
    else:
        checks.append("❌ 缺少节点保留验证")
    
    # 5. 检查详细统计输出
    if '实际新增节点数' in content and '新增的节点:' in content:
        checks.append("✅ 包含详细的节点统计输出")
    else:
        checks.append("❌ 缺少详细的节点统计输出")
    
    # 6. 检查错误的代码是否已移除
    if 'cf_with_new_nodes = self.storage.cf(derived_result).expand_back(recursive=True)' not in content:
        checks.append("✅ 已移除错误的CF创建代码")
    else:
        checks.append("❌ 仍包含错误的CF创建代码")
    
    # 输出检查结果
    print("\n📋 修复验证结果:")
    for check in checks:
        print(f"  {check}")
    
    # 统计结果
    passed = sum(1 for check in checks if check.startswith("✅"))
    total = len(checks)
    
    print(f"\n📊 验证统计:")
    print(f"  通过: {passed}/{total}")
    print(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有修复验证通过！")
        return True
    else:
        print(f"\n⚠️  还有 {total-passed} 个问题需要修复")
        return False


def analyze_expected_behavior():
    """分析修复后的预期行为"""
    print("\n🎯 修复后的预期行为分析")
    print("=" * 50)
    
    print("1️⃣ 增加节点操作:")
    print("  📊 原始CF节点数: 6")
    print("  📊 新计算CF节点数: 4-6 (取决于依赖)")
    print("  📊 合并后CF节点数: 8-10 (应该 >= 原始节点数)")
    print("  ➕ 实际新增节点数: 2-4 (应该 > 0)")
    print("  🆕 新增节点应包含: ['new_computation', 'derived_analysis', ...]")
    
    print("\n2️⃣ 修改节点操作:")
    print("  🎯 删除目标节点: 1个")
    print("  🗑️  删除后节点数: 5 (原始-1)")
    print("  ➕ 合并后节点数: 7-9 (删除后+新增)")
    print("  ✅ 保留原节点数: 4-5 (原始-删除的)")
    print("  🆕 新增节点数: 2-4 (来自新计算)")
    
    print("\n3️⃣ 关键改进:")
    print("  ✅ 使用并集合并 (cf | new_cf) 而不是替换")
    print("  ✅ 详细的节点统计和验证")
    print("  ✅ 清晰的新增节点展示")
    print("  ✅ 正确的节点保留逻辑")


def create_test_expectations():
    """创建测试期望"""
    print("\n📝 创建测试期望")
    print("=" * 50)
    
    expectations = {
        "增加节点操作": {
            "节点数变化": "应该增加或保持不变",
            "新增节点数": "应该 > 0",
            "原始节点保留": "应该 100% 保留",
            "新增节点类型": "应该包含新计算函数"
        },
        "修改节点操作": {
            "删除节点": "应该正确删除目标节点",
            "保留节点": "应该保留除目标外的所有原始节点",
            "新增节点": "应该添加新计算的节点",
            "总节点数": "应该 = 保留节点数 + 新增节点数"
        }
    }
    
    for operation, criteria in expectations.items():
        print(f"\n{operation}:")
        for criterion, expectation in criteria.items():
            print(f"  📋 {criterion}: {expectation}")
    
    return expectations


if __name__ == "__main__":
    print("🧪 节点操作修复验证工具")
    print("=" * 80)
    
    # 验证修复
    fix_verified = verify_node_operations_fix()
    
    # 分析预期行为
    analyze_expected_behavior()
    
    # 创建测试期望
    expectations = create_test_expectations()
    
    print("\n" + "=" * 80)
    if fix_verified:
        print("🎉 修复验证完成！代码已正确修复。")
        print("💡 建议运行 cf_deep_understanding.py 验证实际效果")
    else:
        print("⚠️  修复验证未完全通过，请检查代码")
    
    print("📚 详细分析请参考: NODE_OPERATIONS_FIX_REPORT.md")
