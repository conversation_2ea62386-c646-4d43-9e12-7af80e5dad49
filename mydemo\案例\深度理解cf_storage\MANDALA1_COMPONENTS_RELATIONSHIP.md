# mandala1框架核心组件关系详解

## 概述

mandala1框架由四个核心组件构成：`@op装饰器`、`Ref引用`、`ComputationFrame计算图`、`Storage存储`。这些组件协同工作，实现了计算的记忆化、版本管理、依赖追踪和图分析功能。

## 核心组件关系图

```mermaid
graph TD
    A[@op装饰器] --> B[Op对象]
    B --> C[函数调用]
    C --> D[Storage.call()]
    D --> E[Call对象]
    D --> F[Ref对象]
    E --> G[Storage数据库]
    F --> G
    G --> H[ComputationFrame]
    H --> I[图分析]
    
    J[Context上下文] --> D
    K[版本管理] --> D
    L[记忆化缓存] --> D
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#e8f5e8
    style G fill:#fff3e0
```

## 1. @op装饰器 (Op)

### 核心作用
- **函数转换器**：将普通Python函数转换为可记忆化的操作
- **元数据管理**：为函数添加版本、输出配置等元数据
- **调用拦截**：拦截函数调用，转发给Storage处理

### 工作机制
```python
@op(output_names=['result'], ignore_args=['debug'])
def compute(data, multiplier, debug=False):
    return data * multiplier

# 等价于：
compute = Op(
    name='compute',
    f=原始函数,
    output_names=['result'],
    ignore_args=['debug']
)
```

### 关键属性
- `name`: 函数名称
- `version`: 函数版本（基于代码内容）
- `f`: 原始函数对象
- `output_names`: 输出参数名称
- `ignore_args`: 忽略的参数（不影响缓存）
- `nout`: 输出数量
- `__structural__`: 是否为结构化操作
- `__allow_side_effects__`: 是否允许副作用

## 2. Ref引用系统

### 核心作用
- **数据包装器**：包装所有计算结果和中间数据
- **唯一标识**：为每个数据对象提供内容ID和历史ID
- **内存管理**：支持数据在内存和磁盘间的转换
- **类型系统**：支持不同数据类型的专门处理

### Ref类型层次
```
Ref (抽象基类)
├── AtomRef (原子类型：int, str, float, bool)
├── ListRef (列表类型)
├── DictRef (字典类型)
├── SetRef (集合类型)
└── TupleRef (元组类型)
```

### 关键属性
- `cid`: 内容ID（基于对象内容的哈希）
- `hid`: 历史ID（基于计算历史的哈希）
- `in_memory`: 是否在内存中
- `obj`: 实际的数据对象（如果在内存中）

### 双ID系统的意义
```python
# 相同内容，不同来源
@op
def method1(): return 42

@op  
def method2(): return 42

result1 = method1()  # cid相同，hid不同
result2 = method2()  # cid相同，hid不同

# cid用于：存储去重、内容比较
# hid用于：计算溯源、依赖追踪
```

## 3. Storage存储系统

### 核心作用
- **计算管理器**：管理所有计算的执行和存储
- **记忆化引擎**：实现函数调用的智能缓存
- **版本控制器**：跟踪函数和依赖的版本变化
- **数据持久化**：将计算历史保存到数据库

### 关键组件
```python
Storage
├── 数据库层 (SQLite)
│   ├── calls表 (函数调用记录)
│   ├── atoms表 (原子数据)
│   └── refs表 (引用信息)
├── 缓存层
│   ├── call_cache (调用缓存)
│   ├── atoms_cache (原子缓存)
│   └── ops_cache (操作缓存)
├── 版本管理
│   ├── versioner (版本跟踪器)
│   └── deps_tracker (依赖跟踪)
└── 上下文管理
    └── Context (执行上下文)
```

### 记忆化流程
```python
# 1. 函数调用
result = compute(data, 2.0)

# 2. Storage.call()处理
# 3. 计算缓存键：hash(函数名 + 参数 + 版本)
# 4. 检查缓存
if 缓存命中:
    return 缓存结果
else:
    执行函数
    保存结果到缓存
    return 新结果
```

## 4. ComputationFrame计算图

### 核心作用
- **图构建器**：将计算历史组织为有向图
- **依赖分析器**：分析数据和函数的依赖关系
- **图操作器**：提供丰富的图查询和操作功能
- **可视化工具**：支持计算图的可视化展示

### 图结构
```
ComputationFrame图结构：
节点类型：
├── 变量节点 (vnames) - 表示数据
└── 函数节点 (fnames) - 表示计算

边类型：
├── 函数→变量 (数据生产)
└── 变量→函数 (数据消费)

图属性：
├── 有向无环图 (DAG)
├── 支持部分执行
└── 支持条件分支
```

### 关键数据结构
```python
ComputationFrame
├── inp: Dict[节点, Dict[输入名, Set[源节点]]]  # 输入邻接表
├── out: Dict[节点, Dict[输出名, Set[目标节点]]]  # 输出邻接表
├── vs: Dict[变量名, Set[引用ID]]  # 变量到引用的映射
├── fs: Dict[函数名, Set[调用ID]]  # 函数到调用的映射
├── refs: Dict[引用ID, Ref对象]  # 引用对象存储
└── calls: Dict[调用ID, Call对象]  # 调用对象存储
```

## 组件间的协作流程

### 完整执行流程
```python
# 1. 定义阶段
@op
def process_data(raw_data, config):
    return clean_data(raw_data, config)

# 2. 执行阶段
with Storage() as storage:  # Context管理
    result = process_data(data, config)  # Op拦截调用
    # Storage.call() 处理记忆化
    # 创建Call和Ref对象
    # 保存到数据库

# 3. 分析阶段
cf = storage.cf(result)  # 创建ComputationFrame
cf_expanded = cf.expand_back(recursive=True)  # 扩展图
topo_order = cf_expanded.topsort_modulo_sccs()  # 拓扑分析
```

### 数据流转图
```
用户代码
    ↓ 调用@op函数
Op.__call__()
    ↓ 检查Context
Context.current_context
    ↓ 获取Storage
Storage.call()
    ↓ 记忆化处理
Call对象 + Ref对象
    ↓ 保存到数据库
Storage数据库
    ↓ 构建图
ComputationFrame
    ↓ 分析和可视化
图操作和分析结果
```

## 关键设计模式

### 1. 装饰器模式
```python
# @op装饰器增强函数功能
原始函数 → @op装饰 → Op对象 → 记忆化函数
```

### 2. 代理模式
```python
# Ref作为实际对象的代理
实际对象 → Ref包装 → 延迟加载 → 内存管理
```

### 3. 观察者模式
```python
# Storage观察函数调用
函数调用 → Storage拦截 → 记录历史 → 构建图
```

### 4. 上下文管理器模式
```python
# Context管理执行环境
with Storage():  # 设置上下文
    # 函数调用自动记忆化
    pass  # 清理上下文
```

## 核心概念对比

| 概念 | 作用域 | 生命周期 | 主要职责 |
|------|--------|----------|----------|
| @op | 函数级 | 程序运行期 | 函数增强和调用拦截 |
| Ref | 对象级 | 数据生命周期 | 数据包装和标识 |
| Storage | 会话级 | 上下文期间 | 计算管理和持久化 |
| ComputationFrame | 分析级 | 按需创建 | 图构建和分析 |

## 性能和优化考虑

### 1. 记忆化优化
- **缓存策略**：LRU缓存 + 数据库持久化
- **版本管理**：智能版本检测，避免无效缓存
- **参数忽略**：ignore_args减少缓存键冲突

### 2. 内存管理
- **延迟加载**：Ref支持detached状态
- **溢出处理**：大对象自动存储到磁盘
- **垃圾回收**：自动清理无用的缓存项

### 3. 图操作优化
- **按需构建**：ComputationFrame默认最小化
- **增量扩展**：支持渐进式图扩展
- **并行分析**：拓扑排序支持并行执行规划

这种设计使得mandala1能够在保持简单易用的同时，提供强大的计算管理、版本控制和分析功能。

## 详细概念对比分析

### 1. 函数调用的三种状态对比

#### 普通Python函数
```python
def compute(x, y):
    return x + y

result = compute(1, 2)  # 直接执行，无记录
# 特点：
# - 立即执行，无缓存
# - 无版本管理
# - 无依赖追踪
# - 结果类型：原始Python对象
```

#### 无Context的@op函数
```python
@op
def compute(x, y):
    return x + y

result = compute(1, 2)  # 表现如普通函数
# 特点：
# - 立即执行，无缓存
# - 有版本信息但不使用
# - 无依赖追踪
# - 结果类型：原始Python对象
```

#### 有Context的@op函数
```python
@op
def compute(x, y):
    return x + y

with Storage():
    result = compute(1, 2)  # 记忆化执行
# 特点：
# - 智能缓存，重复调用从缓存加载
# - 版本管理，代码变更自动检测
# - 完整依赖追踪
# - 结果类型：Ref对象
```

### 2. 数据对象的演化过程

#### 原始数据 → Ref包装 → 图节点
```python
# 阶段1：原始Python对象
raw_data = [1, 2, 3, 4, 5]
# 特点：普通Python对象，无特殊功能

# 阶段2：Ref包装
@op
def create_data():
    return [1, 2, 3, 4, 5]

with Storage():
    data_ref = create_data()
# 特点：
# - 有唯一标识（cid, hid）
# - 支持延迟加载
# - 可追踪来源

# 阶段3：图节点
cf = storage.cf(data_ref)
# 特点：
# - 成为计算图的节点
# - 可分析依赖关系
# - 支持图操作
```

### 3. 计算历史的三个层次

#### 层次1：Call对象（单次调用）
```python
call = storage.get_ref_creator(result)
# 包含：
# - 函数信息（op）
# - 输入参数（inputs）
# - 输出结果（outputs）
# - 版本信息（semantic_version, content_version）
```

#### 层次2：Storage数据库（所有调用）
```python
# 数据库表结构：
# calls表：所有函数调用记录
# atoms表：所有原子数据
# refs表：所有引用信息
# 支持：
# - 持久化存储
# - 跨会话复用
# - 版本管理
```

#### 层次3：ComputationFrame（图视图）
```python
cf = storage.cf(result).expand_back(recursive=True)
# 提供：
# - 图结构视图
# - 依赖关系分析
# - 拓扑排序
# - 可视化支持
```

### 4. 版本管理的多个维度

#### 函数版本（Op.version）
```python
@op
def compute(x):
    return x * 2  # 版本1

# 修改函数
@op
def compute(x):
    return x * 3  # 版本2，自动检测变更
```

#### 语义版本（Call.semantic_version）
```python
# 基于函数签名和依赖的版本
# 用于确定是否可以复用缓存
```

#### 内容版本（Call.content_version）
```python
# 基于实际执行内容的版本
# 用于精确的版本控制
```

### 5. 图扩展策略的应用场景对比

#### expand_back：问题诊断
```python
# 场景：某个结果不正确，需要追踪原因
error_result = problematic_function(data)
debug_cf = storage.cf(error_result).expand_back(recursive=True)

# 分析：
# 1. 查看完整的数据流：input → process1 → process2 → error
# 2. 检查每一步的输入输出
# 3. 定位问题出现的环节
```

#### expand_forward：影响评估
```python
# 场景：要修改某个函数，评估影响范围
critical_data = important_computation(input)
impact_cf = storage.cf(critical_data).expand_forward(recursive=True)

# 分析：
# 1. 查看所有使用这个数据的计算
# 2. 评估修改的影响范围
# 3. 制定测试策略
```

#### expand_all：系统理解
```python
# 场景：理解整个计算系统的结构
any_node = some_computation(data)
full_cf = storage.cf(any_node).expand_all()

# 分析：
# 1. 查看完整的计算生态系统
# 2. 理解数据流和依赖关系
# 3. 识别优化机会
```

### 6. 内存管理策略对比

#### 立即加载策略
```python
# 所有数据都在内存中
ref.in_memory == True
# 优点：访问速度快
# 缺点：内存占用大
```

#### 延迟加载策略
```python
# 数据按需加载
detached_ref = ref.detached()
detached_ref.in_memory == False
# 优点：内存占用小
# 缺点：访问时需要加载
```

#### 混合策略
```python
# 小对象在内存，大对象延迟加载
if object_size > threshold:
    use_detached_ref()
else:
    keep_in_memory()
```

### 7. 性能优化的多个层面

#### Op层面优化
```python
@op(ignore_args=['debug', 'verbose'])  # 减少缓存键冲突
@op(__structural__=True)  # 标记结构化操作
def optimized_function(data, debug=False):
    pass
```

#### Storage层面优化
```python
storage = Storage(
    overflow_threshold_MB=100,  # 控制内存使用
    deps_path='__main__'  # 限制版本跟踪范围
)
```

#### ComputationFrame层面优化
```python
# 按需扩展，避免构建过大的图
cf = storage.cf(result)  # 最小图
cf = cf.expand_back()    # 只扩展需要的部分
```

## 实际应用模式

### 1. 数据科学流水线
```python
@op
def load_data(path): ...

@op
def clean_data(raw_data): ...

@op
def train_model(clean_data): ...

@op
def evaluate_model(model, test_data): ...

# 完整流水线
with Storage():
    raw = load_data("data.csv")
    clean = clean_data(raw)
    model = train_model(clean)
    results = evaluate_model(model, clean)

    # 分析流水线
    cf = storage.cf(results).expand_back(recursive=True)
```

### 2. 实验管理
```python
# 不同实验使用不同Storage
with Storage("experiment_1.db"):
    result1 = run_experiment(config1)

with Storage("experiment_2.db"):
    result2 = run_experiment(config2)

# 比较实验结果
cf1 = storage1.cf(result1).expand_all()
cf2 = storage2.cf(result2).expand_all()
```

### 3. 调试和优化
```python
# 性能分析
with Storage() as storage:
    slow_result = slow_computation(data)

    # 分析计算图找到瓶颈
    cf = storage.cf(slow_result).expand_back(recursive=True)
    topo_order = cf.topsort_modulo_sccs()

    # 识别并行机会
    parallel_groups = identify_parallel_groups(topo_order)
```

这种多层次、多维度的设计使得mandala1能够适应各种复杂的计算场景，同时保持良好的性能和可维护性。
