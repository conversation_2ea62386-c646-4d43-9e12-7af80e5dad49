{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from mandala.imports import *\n", "from sklearn.datasets import make_moons\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from typing import Any\n", "import numpy as np\n", "np.random.seed(42)\n", "\n", "storage = Storage(deps_path='__main__')\n", "\n", "def exit_hook(storage: Storage):\n", "    ops = storage.ops.cache\n", "    if \"scale_data\" not in ops:\n", "        cf = storage.cf(ops['get_data'])\n", "    elif \"get_train_test_split\" not in ops:\n", "        cf = storage.cf(ops['scale_data']) | storage.cf(ops['get_data'])\n", "    elif \"train_svc\" not in ops:\n", "        cf = storage.cf(ops['get_train_test_split'])\n", "    elif \"eval_model\" not in ops:\n", "        cf = storage.cf(ops['train_svc']) | storage.cf(ops['train_random_forest'])\n", "    elif \"eval_ensemble\" not in ops:\n", "        cf = storage.cf(ops['eval_model'])\n", "    else:\n", "        cf = storage.cf(ops['eval_ensemble']) | storage.cf(ops['eval_model'])\n", "    cf = cf.expand_all()\n", "    cf.draw(path='demo.svg', verbose=True, show_how=\"none\")\n", "    df = cf.df(values='objs', include_calls=False)\n", "    bad_cols = ['X', 'y', 'X_train', 'X_test', 'y_train', 'y_test', 'X_scaled']\n", "    for col in bad_cols:\n", "        if col in df:\n", "            # replace the values with \"...\"\n", "            df[col] = df[col].apply(lambda x: \"...\")\n", "    display(df)\n", "storage._exit_hooks.append(exit_hook)\n", "\n", "@op(output_names=[\"X\", \"y\"])\n", "def get_data():\n", "    return make_moons(n_samples=1000, noise=0.3, random_state=42)\n", "\n", "@op(output_names=[\"X_train\", \"X_test\", \"y_train\", \"y_test\"])\n", "def get_train_test_split(X, y):\n", "    return tuple(train_test_split(X, y, test_size=0.2, random_state=42))\n", "\n", "@op(output_names=[\"X_scaled\"])\n", "def scale_data(X):\n", "    scaler = StandardScaler()\n", "    X = scaler.fit_transform(X)\n", "    return X\n", "\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score\n", "\n", "@op(output_names=[\"svc_model\"])\n", "def train_svc(X_train, y_train, C: float = 1.0, kernel: str = \"linear\"):\n", "    model = SVC(C=C, kernel=kernel)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "@op(output_names=[\"rf_model\"])\n", "def train_random_forest(X_train, y_train, n_estimators: int = 5, max_depth: int = 5):\n", "    model = RandomForestClassifier(n_estimators=n_estimators, max_depth=max_depth)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "@op(output_names=[\"accuracy\",])\n", "def eval_model(model, X_test, y_test):\n", "    y_pred = model.predict(X_test)\n", "    acc = accuracy_score(y_test, y_pred)\n", "    return acc\n", "\n", "@op(output_names=[\"accuracy\"])\n", "def eval_ensemble(models: MList[Any], X_test, y_test):\n", "    y_preds = [model.predict(X_test) for model in models]\n", "    y_pred = np.mean(y_preds, axis=0) > 0.5\n", "    acc = accuracy_score(y_test, y_pred)\n", "    return acc"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n_estimators</th>\n", "      <th>max_depth</th>\n", "      <th>kernel</th>\n", "      <th>y</th>\n", "      <th>X</th>\n", "      <th>X_train</th>\n", "      <th>y_test</th>\n", "      <th>y_train</th>\n", "      <th>X_test</th>\n", "      <th>C</th>\n", "      <th>model</th>\n", "      <th>models</th>\n", "      <th>accuracy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>poly</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>SVC(kernel='poly')</td>\n", "      <td>None</td>\n", "      <td>0.820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>rbf</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>SVC()</td>\n", "      <td>None</td>\n", "      <td>0.915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>5.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>(DecisionTreeClassifier(max_depth=5, max_featu...</td>\n", "      <td>None</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>poly</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>SVC(kernel='poly')</td>\n", "      <td>None</td>\n", "      <td>0.835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>5.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>(DecisionTreeClassifier(max_depth=5, max_featu...</td>\n", "      <td>None</td>\n", "      <td>0.880</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20</td>\n", "      <td>5.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>(DecisionTreeClassifier(max_depth=5, max_featu...</td>\n", "      <td>None</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>linear</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>SVC(kernel='linear')</td>\n", "      <td>None</td>\n", "      <td>0.820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ValueCollection([20, 10, 5])</td>\n", "      <td>5.0</td>\n", "      <td>ValueCollection(['linear', 'rbf', 'poly'])</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>ValueCollection([SVC(), RandomForestClassifier...</td>\n", "      <td>[SVC(kernel='linear'), SVC(), SVC(kernel='poly...</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>ValueCollection([20, 10, 5])</td>\n", "      <td>5.0</td>\n", "      <td>ValueCollection(['linear', 'rbf', 'poly'])</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>ValueCollection([RandomForestClassifier(max_de...</td>\n", "      <td>[SVC(kernel='linear'), SVC(), SVC(kernel='poly...</td>\n", "      <td>0.890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>20</td>\n", "      <td>5.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>(DecisionTreeClassifier(max_depth=5, max_featu...</td>\n", "      <td>None</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>linear</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>SVC(kernel='linear')</td>\n", "      <td>None</td>\n", "      <td>0.820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>rbf</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>SVC()</td>\n", "      <td>None</td>\n", "      <td>0.910</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>10</td>\n", "      <td>5.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>(DecisionTreeClassifier(max_depth=5, max_featu...</td>\n", "      <td>None</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>10</td>\n", "      <td>5.0</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>(DecisionTreeClassifier(max_depth=5, max_featu...</td>\n", "      <td>None</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    n_estimators  max_depth  \\\n", "0                           None        NaN   \n", "1                           None        NaN   \n", "2                              5        5.0   \n", "3                           None        NaN   \n", "4                              5        5.0   \n", "5                             20        5.0   \n", "6                           None        NaN   \n", "7   ValueCollection([20, 10, 5])        5.0   \n", "8   ValueCollection([20, 10, 5])        5.0   \n", "9                             20        5.0   \n", "10                          None        NaN   \n", "11                          None        NaN   \n", "12                            10        5.0   \n", "13                            10        5.0   \n", "\n", "                                        kernel    y    X X_train y_test  \\\n", "0                                         poly  ...  ...     ...    ...   \n", "1                                          rbf  ...  ...     ...    ...   \n", "2                                         None  ...  ...     ...    ...   \n", "3                                         poly  ...  ...     ...    ...   \n", "4                                         None  ...  ...     ...    ...   \n", "5                                         None  ...  ...     ...    ...   \n", "6                                       linear  ...  ...     ...    ...   \n", "7   ValueCollection(['linear', 'rbf', 'poly'])  ...  ...     ...    ...   \n", "8   ValueCollection(['linear', 'rbf', 'poly'])  ...  ...     ...    ...   \n", "9                                         None  ...  ...     ...    ...   \n", "10                                      linear  ...  ...     ...    ...   \n", "11                                         rbf  ...  ...     ...    ...   \n", "12                                        None  ...  ...     ...    ...   \n", "13                                        None  ...  ...     ...    ...   \n", "\n", "   y_train X_test    C                                              model  \\\n", "0      ...    ...  1.0                                 SVC(kernel='poly')   \n", "1      ...    ...  1.0                                              SVC()   \n", "2      ...    ...  NaN  (DecisionTreeClassifier(max_depth=5, max_featu...   \n", "3      ...    ...  1.0                                 SVC(kernel='poly')   \n", "4      ...    ...  NaN  (DecisionTreeClassifier(max_depth=5, max_featu...   \n", "5      ...    ...  NaN  (DecisionTreeClassifier(max_depth=5, max_featu...   \n", "6      ...    ...  1.0                               SVC(kernel='linear')   \n", "7      ...    ...  1.0  ValueCollection([SVC(), RandomForestClassifier...   \n", "8      ...    ...  1.0  ValueCollection([RandomForestClassifier(max_de...   \n", "9      ...    ...  NaN  (DecisionTreeClassifier(max_depth=5, max_featu...   \n", "10     ...    ...  1.0                               SVC(kernel='linear')   \n", "11     ...    ...  1.0                                              SVC()   \n", "12     ...    ...  NaN  (DecisionTreeClassifier(max_depth=5, max_featu...   \n", "13     ...    ...  NaN  (DecisionTreeClassifier(max_depth=5, max_featu...   \n", "\n", "                                               models  accuracy  \n", "0                                                None     0.820  \n", "1                                                None     0.915  \n", "2                                                None     0.900  \n", "3                                                None     0.835  \n", "4                                                None     0.880  \n", "5                                                None     0.900  \n", "6                                                None     0.820  \n", "7   [SVC(kernel='linear'), SVC(), SVC(kernel='poly...     0.900  \n", "8   [SVC(kernel='linear'), SVC(), SVC(kernel='poly...     0.890  \n", "9                                                None     0.900  \n", "10                                               None     0.820  \n", "11                                               None     0.910  \n", "12                                               None     0.900  \n", "13                                               None     0.900  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["with storage:\n", "    for scale in (True, False):\n", "        X, y = get_data()\n", "        if scale:\n", "            X = scale_data(X=X)\n", "        X_train, X_test, y_train, y_test = get_train_test_split(X=X, y=y)\n", "\n", "        svc_models = []\n", "        for kernel in ('linear', 'rbf', 'poly'):\n", "            svc_model = train_svc(X_train=X_train, y_train=y_train, kernel=kernel)\n", "            svc_acc = eval_model(model=svc_model, X_test=X_test, y_test=y_test)\n", "            svc_models.append(svc_model)\n", "        \n", "        rf_models = []\n", "        for n_estimators in (5, 10, 20):\n", "            rf_model = train_random_forest(X_train=X_train, y_train=y_train, n_estimators=n_estimators)\n", "            rf_acc = eval_model(model=rf_model, X_test=X_test, y_test=y_test)\n", "            rf_models.append(rf_model)\n", "        \n", "        ensemble_acc = eval_ensemble(models=svc_models + rf_models, X_test=X_test, y_test=y_test)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}