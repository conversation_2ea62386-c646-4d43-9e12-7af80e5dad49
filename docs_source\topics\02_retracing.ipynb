{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Patterns for Incremental Computation & Development\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/topics/02_retracing.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "**`@op`-decorated functions are designed to be composed** with one another. This\n", "enables the same piece of imperative code to adapt to multiple goals depending\n", "on the situation: \n", "\n", "- saving new `@op` calls and/or loading previous ones;\n", "- cheaply resuming an `@op` program after a failure;\n", "- incrementally adding more logic and computations to the same code without\n", "re-doing work.\n", "\n", "**This section of the documentation does not introduce new methods or classes**.\n", "Instead, it demonstrates the programming patterns needed to make effective use\n", "of `mandala`'s memoization capabilities."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How `@op` encourages composition\n", "There are several ways in which the `@op` decorator encourages (and even\n", "enforces) composition of `@op`s:\n", "\n", "- **`@op`s return special objects**, `Ref`s, which prevents accidentally calling \n", "a non-`@op` on the output of an `@op`\n", "- If the inputs to an `@op` call are already `Ref`s, this **speeds up the cache\n", "lookups**.\n", "- If the call can be reused, the **input `Ref`s don't even need to be in memory**\n", "(because the lookup is based only on `Ref` metadata).\n", "- When `@op`s are composed, **computational history propagates** through this\n", "composition. This is automatically leveraged by `ComputationFrame`s when\n", "querying the storage.\n", "- Though not documented here, **`@op`s can natively handle Python\n", "collections** like lists and dicts. This \n", "\n", "When `@op`s are composed in this way, the entire computation becomes \"end-to-end\n", "[memoized](https://en.wikipedia.org/wiki/Memoization)\". "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Toy ML pipeline example\n", "Here's a small example of a machine learning pipeline:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:08.077550Z", "iopub.status.busy": "2024-07-11T14:32:08.077015Z", "iopub.status.idle": "2024-07-11T14:32:08.089967Z", "shell.execute_reply": "2024-07-11T14:32:08.089215Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:08.093185Z", "iopub.status.busy": "2024-07-11T14:32:08.092928Z", "iopub.status.idle": "2024-07-11T14:32:10.789087Z", "shell.execute_reply": "2024-07-11T14:32:10.788126Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data\n", "Training model\n", "Getting accuracy\n", "AtomRef(1.0, hid=d16...)\n"]}], "source": ["from mandala.imports import *\n", "from sklearn.datasets import load_digits\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score\n", "\n", "@op\n", "def load_data(n_class=2):\n", "    print(\"Loading data\")\n", "    return load_digits(n_class=n_class, return_X_y=True)\n", "\n", "@op\n", "def train_model(X, y, n_estimators=5):\n", "    print(\"Training model\")\n", "    return RandomForestClassifier(n_estimators=n_estimators,\n", "                                  max_depth=2).fit(X, y)\n", "\n", "@op\n", "def get_acc(model, X, y):\n", "    print(\"Getting accuracy\")\n", "    return round(accuracy_score(y_pred=model.predict(X), y_true=y), 2)\n", "\n", "storage = Storage()\n", "\n", "with storage:\n", "    X, y = load_data() \n", "    model = train_model(X, y)\n", "    acc = get_acc(model, X, y)\n", "    print(acc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Retracing your steps with memoization\n", "Running the computation again will not execute any calls, because it will\n", "exactly **retrace** calls that happened in the past. Moreover, the retracing is\n", "**lazy**: none of the values along the way are actually loaded from storage:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:10.805816Z", "iopub.status.busy": "2024-07-11T14:32:10.804535Z", "iopub.status.idle": "2024-07-11T14:32:10.855279Z", "shell.execute_reply": "2024-07-11T14:32:10.854352Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AtomRef(hid=d0f..., in_memory=False) AtomRef(hid=f1a..., in_memory=False)\n", "AtomRef(hid=caf..., in_memory=False)\n", "AtomRef(hid=d16..., in_memory=False)\n"]}], "source": ["with storage:\n", "    X, y = load_data() \n", "    print(X, y)\n", "    model = train_model(X, y)\n", "    print(model)\n", "    acc = get_acc(model, X, y)\n", "    print(acc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This puts all the `Ref`s along the way in your local variables (as if you've\n", "just ran the computation), which lets you easily inspect any intermediate\n", "variables in this `@op` composition:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:10.858135Z", "iopub.status.busy": "2024-07-11T14:32:10.857846Z", "iopub.status.idle": "2024-07-11T14:32:10.899864Z", "shell.execute_reply": "2024-07-11T14:32:10.899155Z"}}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["storage.unwrap(acc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding new calls \"in-place\" in `@op`-based programs\n", "With `mandala`, you don't need to think about what's already been computed and\n", "split up code based on that. All past results are automatically reused, so you can\n", "directly build upon the existing composition of `@op`s when you want to add new\n", "functions and/or run old ones with different parameters:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:10.902501Z", "iopub.status.busy": "2024-07-11T14:32:10.902236Z", "iopub.status.idle": "2024-07-11T14:32:11.097760Z", "shell.execute_reply": "2024-07-11T14:32:11.097105Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AtomRef(hid=d16..., in_memory=False)\n", "Training model\n", "Getting accuracy\n", "AtomRef(1.0, hid=6fd...)\n", "Loading data\n", "Training model\n", "Getting accuracy\n", "AtomRef(0.88, hid=158...)\n", "Training model\n", "Getting accuracy\n", "AtomRef(0.88, hid=214...)\n"]}], "source": ["# reuse the previous code to loop over more values of n_class and n_estimators \n", "with storage:\n", "    for n_class in (2, 5,):\n", "        X, y = load_data(n_class) \n", "        for n_estimators in (5, 10):\n", "            model = train_model(X, y, n_estimators=n_estimators)\n", "            acc = get_acc(model, X, y)\n", "            print(acc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the first value of `acc` from the nested loop is with\n", "`in_memory=False`, because it was reused from the call we did before; the other\n", "values are in memory, as they were freshly computed. \n", "\n", "This pattern lets you incrementally build towards the final computations you\n", "want without worrying about how results will be reused."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using control flow efficiently with `@op`s\n", "Because the unit of storage is the function call (as opposed to an entire script\n", "or notebook), you can transparently use Pythonic control flow. If the control\n", "flow depends on a `Ref`, you can explicitly load just this `Ref` in memory\n", "using `storage.unwrap`:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:11.101382Z", "iopub.status.busy": "2024-07-11T14:32:11.100655Z", "iopub.status.idle": "2024-07-11T14:32:11.176600Z", "shell.execute_reply": "2024-07-11T14:32:11.175196Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2 5 1.0\n", "2 10 1.0\n"]}], "source": ["with storage:\n", "    for n_class in (2, 5,):\n", "        X, y = load_data(n_class) \n", "        for n_estimators in (5, 10):\n", "            model = train_model(X, y, n_estimators=n_estimators)\n", "            acc = get_acc(model, X, y)\n", "            if storage.unwrap(acc) > 0.9: # load only the `Ref`s needed for control flow\n", "                print(n_class, n_estimators, storage.unwrap(acc))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Memoized code as storage interface\n", "An end-to-end memoized composition of `@op`s is like an \"imperative\" storage\n", "interface. You can modify the code to only focus on particular results of\n", "interest:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:11.181423Z", "iopub.status.busy": "2024-07-11T14:32:11.180801Z", "iopub.status.idle": "2024-07-11T14:32:11.221119Z", "shell.execute_reply": "2024-07-11T14:32:11.220414Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.88 RandomForestClassifier(max_depth=2, n_estimators=5)\n"]}], "source": ["with storage:\n", "    for n_class in (5,):\n", "        X, y = load_data(n_class) \n", "        for n_estimators in (5,):\n", "            model = train_model(X, y, n_estimators=n_estimators)\n", "            acc = get_acc(model, X, y)\n", "            print(storage.unwrap(acc), storage.unwrap(model))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}