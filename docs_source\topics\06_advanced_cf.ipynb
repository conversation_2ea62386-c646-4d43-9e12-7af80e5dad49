{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Advanced `ComputationFrame` tools\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/topics/06_advanced_cf.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "This section of the documentation contains some more advanced `ComputationFrame`\n", "topics."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set-like and graph-like operations on `ComputationFrame`s\n", "A CF is like a \"graph of sets\", where the elements of each set are either `Ref`s\n", "(for variables) or `Call`s (for functions). As such, it supports both natural\n", "set-like operations applied node/edge-wise, and natural operations using the\n", "graph's connectivity:\n", "\n", "- **union**: given by `cf_1 | cf_2`, this takes the union of the two computation\n", "graphs (merging nodes/edges with the same names), and in case of a merge, the\n", "resulting set at the node is the union of the two sets of `Ref`s or `Call`s.\n", "- **intersection**: given by `cf_1 & cf_2`, this takes the intersection of the\n", "two computation graphs (leaving only nodes/edges with the same name in both), \n", "and the set at each node is the intersection of the two corresponding sets. \n", "- **`.downstream(varnames)`**: restrict the CF to computations that are\n", "downstream of the `Ref`s in chosen variables\n", "- **`.upstream(varnames)`**: dual to `downstream`\n", "\n", "Consider the following example:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:58.600151Z", "iopub.status.busy": "2024-07-11T14:31:58.599573Z", "iopub.status.idle": "2024-07-11T14:31:58.611209Z", "shell.execute_reply": "2024-07-11T14:31:58.610360Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:58.615177Z", "iopub.status.busy": "2024-07-11T14:31:58.614504Z", "iopub.status.idle": "2024-07-11T14:32:01.254704Z", "shell.execute_reply": "2024-07-11T14:32:01.253903Z"}}, "outputs": [], "source": ["from mandala.imports import *\n", "storage = Storage()\n", "\n", "@op\n", "def inc(x): return x + 1\n", "\n", "@op\n", "def add(y, z): return y + z\n", "\n", "@op\n", "def square(w): return w ** 2\n", "\n", "@op\n", "def divmod_(u, v): return divmod(u, v)\n", "\n", "with storage:\n", "    xs = [inc(i) for i in range(5)]\n", "    ys = [add(x, z=42) for x in xs] + [square(x) for x in range(5, 10)]\n", "    zs = [divmod_(x, y) for x, y in zip(xs, ys[3:8])]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We have a \"middle layer\" in the computation that uses both `add` and `square`.\n", "We can get a shared view of the entire computation by taking the union of the\n", "expanded CFs for these two ops:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:01.259087Z", "iopub.status.busy": "2024-07-11T14:32:01.258387Z", "iopub.status.idle": "2024-07-11T14:32:01.924176Z", "shell.execute_reply": "2024-07-11T14:32:01.922637Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"372pt\" height=\"620pt\"\n", " viewBox=\"0.00 0.00 371.50 620.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 616)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-616 367.5,-616 367.5,4 -4,4\"/>\n", "<!-- z -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>z</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M216,-420C216,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 216,-384 216,-384 222,-384 228,-390 228,-396 228,-396 228,-408 228,-408 228,-414 222,-420 216,-420\"/>\n", "<text text-anchor=\"start\" x=\"172\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">z</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- add -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>add</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M214.5,-326C214.5,-326 182.5,-326 182.5,-326 176.5,-326 170.5,-320 170.5,-314 170.5,-314 170.5,-298 170.5,-298 170.5,-292 176.5,-286 182.5,-286 182.5,-286 214.5,-286 214.5,-286 220.5,-286 226.5,-292 226.5,-298 226.5,-298 226.5,-314 226.5,-314 226.5,-320 220.5,-326 214.5,-326\"/>\n", "<text text-anchor=\"start\" x=\"187.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">add</text>\n", "<text text-anchor=\"start\" x=\"178.5\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:add</text>\n", "<text text-anchor=\"start\" x=\"184\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- z&#45;&gt;add -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>z&#45;&gt;add</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M173.53,-383.7C172.77,-372.23 172.82,-356.95 176.5,-344 177.35,-341.01 178.51,-338.01 179.85,-335.08\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"183.02,-336.58 184.61,-326.1 176.83,-333.3 183.02,-336.58\"/>\n", "<text text-anchor=\"middle\" x=\"198\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">z</text>\n", "<text text-anchor=\"middle\" x=\"198\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- var_2 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>var_2</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M239.5,-36C239.5,-36 169.5,-36 169.5,-36 163.5,-36 157.5,-30 157.5,-24 157.5,-24 157.5,-12 157.5,-12 157.5,-6 163.5,0 169.5,0 169.5,0 239.5,0 239.5,0 245.5,0 251.5,-6 251.5,-12 251.5,-12 251.5,-24 251.5,-24 251.5,-30 245.5,-36 239.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"188.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_2</text>\n", "<text text-anchor=\"start\" x=\"165.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sinks)</text>\n", "</g>\n", "<!-- x -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>x</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M327,-612C327,-612 246,-612 246,-612 240,-612 234,-606 234,-600 234,-600 234,-588 234,-588 234,-582 240,-576 246,-576 246,-576 327,-576 327,-576 333,-576 339,-582 339,-588 339,-588 339,-600 339,-600 339,-606 333,-612 327,-612\"/>\n", "<text text-anchor=\"start\" x=\"283\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"start\" x=\"242\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sources)</text>\n", "</g>\n", "<!-- inc -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>inc</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M301.5,-518C301.5,-518 271.5,-518 271.5,-518 265.5,-518 259.5,-512 259.5,-506 259.5,-506 259.5,-490 259.5,-490 259.5,-484 265.5,-478 271.5,-478 271.5,-478 301.5,-478 301.5,-478 307.5,-478 313.5,-484 313.5,-490 313.5,-490 313.5,-506 313.5,-506 313.5,-512 307.5,-518 301.5,-518\"/>\n", "<text text-anchor=\"start\" x=\"277.5\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">inc</text>\n", "<text text-anchor=\"start\" x=\"268\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:inc</text>\n", "<text text-anchor=\"start\" x=\"272\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- x&#45;&gt;inc -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>x&#45;&gt;inc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M286.5,-575.76C286.5,-562.5 286.5,-543.86 286.5,-528.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"290,-528.07 286.5,-518.07 283,-528.07 290,-528.07\"/>\n", "<text text-anchor=\"middle\" x=\"308\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"middle\" x=\"308\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- var_1 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>var_1</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M351.5,-36C351.5,-36 281.5,-36 281.5,-36 275.5,-36 269.5,-30 269.5,-24 269.5,-24 269.5,-12 269.5,-12 269.5,-6 275.5,0 281.5,0 281.5,0 351.5,0 351.5,0 357.5,0 363.5,-6 363.5,-12 363.5,-12 363.5,-24 363.5,-24 363.5,-30 357.5,-36 351.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"300.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_1</text>\n", "<text text-anchor=\"start\" x=\"277.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sinks)</text>\n", "</g>\n", "<!-- var_0 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M236,-228C236,-228 161,-228 161,-228 155,-228 149,-222 149,-216 149,-216 149,-204 149,-204 149,-198 155,-192 161,-192 161,-192 236,-192 236,-192 242,-192 248,-198 248,-204 248,-204 248,-216 248,-216 248,-222 242,-228 236,-228\"/>\n", "<text text-anchor=\"start\" x=\"182.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"157\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">10 values (5 sinks)</text>\n", "</g>\n", "<!-- divmod_ -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>divmod_</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M287,-134C287,-134 234,-134 234,-134 228,-134 222,-128 222,-122 222,-122 222,-106 222,-106 222,-100 228,-94 234,-94 234,-94 287,-94 287,-94 293,-94 299,-100 299,-106 299,-106 299,-122 299,-122 299,-128 293,-134 287,-134\"/>\n", "<text text-anchor=\"start\" x=\"235.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">divmod_</text>\n", "<text text-anchor=\"start\" x=\"230\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:divmod_</text>\n", "<text text-anchor=\"start\" x=\"246\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- var_0&#45;&gt;divmod_ -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>var_0&#45;&gt;divmod_</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M209.87,-191.76C218.96,-177.99 231.86,-158.42 242.37,-142.49\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"245.34,-144.35 247.92,-134.07 239.49,-140.49 245.34,-144.35\"/>\n", "<text text-anchor=\"middle\" x=\"257\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"middle\" x=\"257\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- y -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M301.5,-420C301.5,-420 271.5,-420 271.5,-420 265.5,-420 259.5,-414 259.5,-408 259.5,-408 259.5,-396 259.5,-396 259.5,-390 265.5,-384 271.5,-384 271.5,-384 301.5,-384 301.5,-384 307.5,-384 313.5,-390 313.5,-396 313.5,-396 313.5,-408 313.5,-408 313.5,-414 307.5,-420 301.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"283\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"start\" x=\"268\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values</text>\n", "</g>\n", "<!-- y&#45;&gt;divmod_ -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>y&#45;&gt;divmod_</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M289.18,-383.96C294.98,-343.01 306.38,-236.23 282.5,-152 281.65,-149 280.5,-146 279.16,-143.08\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"282.17,-141.3 274.4,-134.1 275.99,-144.57 282.17,-141.3\"/>\n", "<text text-anchor=\"middle\" x=\"318\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">u</text>\n", "<text text-anchor=\"middle\" x=\"318\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- y&#45;&gt;add -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y&#45;&gt;add</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M262.7,-383.89C255.87,-378.51 248.61,-372.31 242.5,-366 233.19,-356.39 224.14,-344.78 216.69,-334.4\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"219.44,-332.23 210.83,-326.05 213.71,-336.25 219.44,-332.23\"/>\n", "<text text-anchor=\"middle\" x=\"264\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"264\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- w -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>w</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420\"/>\n", "<text text-anchor=\"start\" x=\"47.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">w</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sources)</text>\n", "</g>\n", "<!-- square -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>square</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M108.5,-326C108.5,-326 62.5,-326 62.5,-326 56.5,-326 50.5,-320 50.5,-314 50.5,-314 50.5,-298 50.5,-298 50.5,-292 56.5,-286 62.5,-286 62.5,-286 108.5,-286 108.5,-286 114.5,-286 120.5,-292 120.5,-298 120.5,-298 120.5,-314 120.5,-314 120.5,-320 114.5,-326 108.5,-326\"/>\n", "<text text-anchor=\"start\" x=\"65.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">square</text>\n", "<text text-anchor=\"start\" x=\"58.5\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:square</text>\n", "<text text-anchor=\"start\" x=\"71\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- w&#45;&gt;square -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>w&#45;&gt;square</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M58.55,-383.76C63.25,-370.37 69.88,-351.5 75.38,-335.82\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"78.79,-336.67 78.8,-326.07 72.19,-334.35 78.79,-336.67\"/>\n", "<text text-anchor=\"middle\" x=\"94\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">w</text>\n", "<text text-anchor=\"middle\" x=\"94\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- inc&#45;&gt;y -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>inc&#45;&gt;y</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M286.5,-477.98C286.5,-464.34 286.5,-445.75 286.5,-430.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"290,-430.1 286.5,-420.1 283,-430.1 290,-430.1\"/>\n", "<text text-anchor=\"middle\" x=\"308\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"308\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- square&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>square&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.89,-285.91C118.73,-274.12 135.79,-258.83 151.5,-246 156.25,-242.13 161.36,-238.14 166.4,-234.3\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"168.85,-236.84 174.76,-228.04 164.65,-231.24 168.85,-236.84\"/>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- divmod_&#45;&gt;var_2 -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>divmod_&#45;&gt;var_2</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M246.91,-93.64C243.1,-88.02 239.04,-81.83 235.5,-76 229.47,-66.08 223.27,-54.91 218.02,-45.1\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"221.08,-43.4 213.3,-36.2 214.89,-46.68 221.08,-43.4\"/>\n", "<text text-anchor=\"middle\" x=\"257\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"257\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- divmod_&#45;&gt;var_1 -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>divmod_&#45;&gt;var_1</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M271.83,-93.98C280.27,-79.81 291.9,-60.3 301.17,-44.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"304.2,-46.48 306.31,-36.1 298.19,-42.9 304.2,-46.48\"/>\n", "<text text-anchor=\"middle\" x=\"316\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"316\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- add&#45;&gt;var_0 -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>add&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M198.5,-285.98C198.5,-272.34 198.5,-253.75 198.5,-238.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"202,-238.1 198.5,-228.1 195,-238.1 202,-238.1\"/>\n", "<text text-anchor=\"middle\" x=\"220\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"220\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x747cb47f91b0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = (storage.cf(add) | storage.cf(square)).expand_all()\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Selection\n", "TODO"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}