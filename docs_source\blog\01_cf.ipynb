{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tidy Computations\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/blog/01_cf.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "In data-driven fields, such as machine learning, a lot of effort is spent\n", "organizing *computational data* &mdash; results of running programs &mdash; so\n", "that it can be analyzed and manipulated. This blog post introduces the\n", "`ComputationFrame` (CF) data structure &mdash; a synthesis of computation graphs\n", "and relational databases &mdash; which provides a natural and simple grammar of\n", "operations to eliminate this effort.\n", "\n", "The main benefit of CFs is that they give a single view of heterogeneous\n", "computations that go beyond a fixed schema. They automatically represent in a\n", "familiar and intuitive way constructs like conditional execution, feedback\n", "loops, branching/merging pipelines, and aggregation/indexing using collections.\n", "This view can be declaratively queried for relationships between any variables\n", "in (literally) a single line of code, without leaving Python or writing in a\n", "domain-specific language like SQL.\n", "\n", "`ComputationFrame` is implemented as [part\n", "of](https://amakelov.github.io/mandala/topics/03_cf/)\n", "[mandala](https://github.com/amakelov/mandala), a Python library for experiment\n", "tracking and incremental computation. All examples in this post are ran using\n", "the library.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## So what's a `ComputationFrame`?\n", "A `ComputationFrame` is a \"generalized dataframe\", where the set of columns is\n", "replaced by a computation graph of variables and operations, and rows are\n", "(possibly partial) executions of the graph.\n", "\n", "### Minimal interesting example\n", "Below is a simple example of defining two Python functions  (decorated via `@op`\n", "from `mandala`), running some computations using them, and creating a CF from\n", "the results."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:06.283838Z", "iopub.status.busy": "2024-07-11T14:33:06.283612Z", "iopub.status.idle": "2024-07-11T14:33:08.772825Z", "shell.execute_reply": "2024-07-11T14:33:08.772044Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"756pt\" height=\"94pt\"\n", " viewBox=\"0.00 0.00 756.00 94.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 90)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-90 752,-90 752,4 -4,4\"/>\n", "<!-- x -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>x</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-62C93,-62 12,-62 12,-62 6,-62 0,-56 0,-50 0,-50 0,-38 0,-38 0,-32 6,-26 12,-26 12,-26 93,-26 93,-26 99,-26 105,-32 105,-38 105,-38 105,-50 105,-50 105,-56 99,-62 93,-62\"/>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-46.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-36\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sources)</text>\n", "</g>\n", "<!-- add -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>add</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M563,-65C563,-65 531,-65 531,-65 525,-65 519,-59 519,-53 519,-53 519,-37 519,-37 519,-31 525,-25 531,-25 531,-25 563,-25 563,-25 569,-25 575,-31 575,-37 575,-37 575,-53 575,-53 575,-59 569,-65 563,-65\"/>\n", "<text text-anchor=\"start\" x=\"536\" y=\"-52.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">add</text>\n", "<text text-anchor=\"start\" x=\"527\" y=\"-42\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:add</text>\n", "<text text-anchor=\"start\" x=\"532.5\" y=\"-32\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">3 calls</text>\n", "</g>\n", "<!-- x&#45;&gt;add -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>x&#45;&gt;add</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.03,-52.25C111.07,-52.97 117.16,-53.58 123,-54 290.57,-65.99 333.82,-70.55 501,-54 503.5,-53.75 506.06,-53.43 508.64,-53.05\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"509.24,-56.5 518.52,-51.39 508.08,-49.6 509.24,-56.5\"/>\n", "<text text-anchor=\"middle\" x=\"306.5\" y=\"-78\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">z</text>\n", "<text text-anchor=\"middle\" x=\"306.5\" y=\"-67\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- increment -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>increment</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M255,-40C255,-40 196,-40 196,-40 190,-40 184,-34 184,-28 184,-28 184,-12 184,-12 184,-6 190,0 196,0 196,0 255,0 255,0 261,0 267,-6 267,-12 267,-12 267,-28 267,-28 267,-34 261,-40 255,-40\"/>\n", "<text text-anchor=\"start\" x=\"196.5\" y=\"-27.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">increment</text>\n", "<text text-anchor=\"start\" x=\"192\" y=\"-17\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:increment</text>\n", "<text text-anchor=\"start\" x=\"211\" y=\"-7\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- x&#45;&gt;increment -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>x&#45;&gt;increment</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.07,-34.78C111.11,-33.8 117.18,-32.85 123,-32 139.42,-29.61 157.39,-27.37 173.58,-25.48\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"174.4,-28.91 183.93,-24.29 173.6,-21.96 174.4,-28.91\"/>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-46\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-35\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- w -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>w</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M736,-63C736,-63 666,-63 666,-63 660,-63 654,-57 654,-51 654,-51 654,-39 654,-39 654,-33 660,-27 666,-27 666,-27 736,-27 736,-27 742,-27 748,-33 748,-39 748,-39 748,-51 748,-51 748,-57 742,-63 736,-63\"/>\n", "<text text-anchor=\"start\" x=\"696\" y=\"-47.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">w</text>\n", "<text text-anchor=\"start\" x=\"662\" y=\"-37\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- y -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>y</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M428,-40C428,-40 358,-40 358,-40 352,-40 346,-34 346,-28 346,-28 346,-16 346,-16 346,-10 352,-4 358,-4 358,-4 428,-4 428,-4 434,-4 440,-10 440,-16 440,-16 440,-28 440,-28 440,-34 434,-40 428,-40\"/>\n", "<text text-anchor=\"start\" x=\"389.5\" y=\"-24.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"start\" x=\"354\" y=\"-14\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (2 sinks)</text>\n", "</g>\n", "<!-- y&#45;&gt;add -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y&#45;&gt;add</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M440.09,-24.4C459.07,-25.88 481.2,-28.25 501,-32 503.52,-32.48 506.1,-33.03 508.68,-33.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"508.03,-37.09 518.59,-36.2 509.78,-30.31 508.03,-37.09\"/>\n", "<text text-anchor=\"middle\" x=\"479.5\" y=\"-46\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"479.5\" y=\"-35\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- add&#45;&gt;w -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>add&#45;&gt;w</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M575.17,-45C594.22,-45 620.35,-45 643.79,-45\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"643.98,-48.5 653.98,-45 643.98,-41.5 643.98,-48.5\"/>\n", "<text text-anchor=\"middle\" x=\"614.5\" y=\"-59\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">w</text>\n", "<text text-anchor=\"middle\" x=\"614.5\" y=\"-48\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- increment&#45;&gt;y -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>increment&#45;&gt;y</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M267.41,-20.49C288,-20.74 313.27,-21.05 335.66,-21.32\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"335.68,-24.82 345.72,-21.44 335.76,-17.82 335.68,-24.82\"/>\n", "<text text-anchor=\"middle\" x=\"306.5\" y=\"-35\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"306.5\" y=\"-24\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7908db1026b0>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["|    |   x | increment                   |   y | add                   |   w |\n", "|---:|----:|:----------------------------|----:|:----------------------|----:|\n", "|  0 |   2 | Call(increment, hid=5dd...) |   3 | Call(add, hid=626...) |   5 |\n", "|  1 |   4 | Call(increment, hid=adf...) |   5 | Call(add, hid=deb...) |   9 |\n", "|  2 |   3 | Call(increment, hid=df2...) |   4 |                       | nan |\n", "|  3 |   0 | Call(increment, hid=230...) |   1 | Call(add, hid=247...) |   1 |\n", "|  4 |   1 | Call(increment, hid=6e2...) |   2 |                       | nan |\n"]}], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass\n", "\n", "from mandala.imports import *\n", "\n", "@op(output_names=['y'])\n", "def increment(x): return x + 1\n", "\n", "@op(output_names=['w'])\n", "def add(y, z): return y + z\n", "\n", "# compose the operations\n", "with Storage() as storage: # the `storage` automatically stores calls to `@op`s\n", "    for x in range(5):\n", "        y = increment(x)\n", "        if x % 2 == 0:\n", "            w = add(y=y, z=x)\n", "\n", "# get a CF for just the `increment` operation \n", "cf = storage.cf(increment)\n", "# expand the CF to include the `add` operation\n", "cf = cf.expand_forward()\n", "# draw the CF\n", "cf.draw(verbose=True, orientation='LR')\n", "# convert the CF to a dataframe\n", "print(cf.df().to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This small example illustrates the main components of the CF workflow:\n", "\n", "- **run some computations**: we ran a computation by composing calls to\n", "`increment` and `add` (which are automatically saved by `mandala` in a way that \n", "keeps track of how the calls compose).\n", "- **create a CF and add desired context to it**: we explored the computation by\n", "starting from a CF containing all calls to `increment` and *expanding it*\n", "forward to add the downstream calls to `add`.\n", "- **convert to a dataframe for downstream analysis**: we turned the expanded CF\n", "into a dataframe, where the columns are the variables and operations in the\n", "graph, and the rows are the executions of the graph &mdash; recorded as values\n", "of variables and `Call` objects for operations. \n", "\n", "When a given computation is partial, missing values and calls are represented by\n", "nulls in the dataframe, which allows for easy filtering and analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Precise definition\n", "Concretely, a `ComputationFrame` consists of:\n", "\n", "- a directed computation graph of operations and variables, where each operation\n", "has multiple named inputs and outputs. The names of these inputs and outputs are\n", "the edge labels in the graph;\n", "- a set of calls for each operation node, and a set of values for each variable\n", "node. Details about how calls/values are distinguished are beyond the scope of\n", "this post, but the key idea is that each call/value has a unique ID based on its\n", "full computational history in terms of composition of `@op` calls; see\n", "[here](https://amakelov.github.io/mandala/topics/01_storage_and_ops/#working-with-op-outputs-refs)\n", "for more.\n", "\n", "The only &mdash; but key &mdash; property this data must satisfy is that,\n", "whenever input/output variables are connected to an operation node `f`, the\n", "corresponding input/output values of all calls in `f` are found in these\n", "variables."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Case studies\n", "To illustrate the versatility of `ComputationFrame`s and the grammar of\n", "operations they support, let's now consider a fairly standard machine learning\n", "pipeline as a running example. \n", "\n", "The goal will be to train several kinds of machine learning models on the [moons\n", "dataset](https://scikit-learn.org/0.16/modules/generated/sklearn.datasets.make_moons.html)\n", "from [scikit-learn](https://scikit-learn.org/stable/), and iterate on the\n", "pipeline by adding new preprocessing steps, models, hyperparameters and\n", "ensembling across model types. Here's some code to get us started:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:08.778745Z", "iopub.status.busy": "2024-07-11T14:33:08.778298Z", "iopub.status.idle": "2024-07-11T14:33:09.270527Z", "shell.execute_reply": "2024-07-11T14:33:09.269891Z"}}, "outputs": [], "source": ["from sklearn.datasets import make_moons\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "import numpy as np\n", "np.random.seed(42)\n", "\n", "@op(output_names=[\"X\", \"y\"])\n", "def get_data():\n", "    return make_moons(n_samples=1000, noise=0.3, random_state=42)\n", "\n", "@op(output_names=[\"X_train\", \"X_test\", \"y_train\", \"y_test\"])\n", "def get_train_test_split(X, y):\n", "    return tuple(train_test_split(X, y, test_size=0.2, random_state=42))\n", "\n", "@op(output_names=[\"X_scaled\"])\n", "def scale_data(X):\n", "    scaler = StandardScaler()\n", "    X = scaler.fit_transform(X)\n", "    return X"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We begin by loading the dataset, optionally scaling it, and splitting it into\n", "training and test sets:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:09.273708Z", "iopub.status.busy": "2024-07-11T14:33:09.273494Z", "iopub.status.idle": "2024-07-11T14:33:09.383231Z", "shell.execute_reply": "2024-07-11T14:33:09.382436Z"}}, "outputs": [], "source": ["with Storage() as storage:\n", "    for scale in (True, False):\n", "        X, y = get_data()\n", "        if scale:\n", "            X = scale_data(X=X)\n", "        X_train, X_test, y_train, y_test = get_train_test_split(X=X, y=y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### In-place updates as cycles in the computation graph\n", "When a variable in the program is updated in-place (like how `X` is\n", "scaled in the above code) or feeds back on itself in a more complex way, it's\n", "natural to represent this as a cycle in the high-level computation graph. \n", "\n", "The CF expansion algorithm notices these cases by default. When we expand a CF\n", "back starting from `get_train_test_split`, we see two values for `X` used as\n", "inputs. When we keep expanding, we see that one of these values was actually\n", "obtained from the other by applying the `scale_data` operation, so we add a \n", "cycle to the graph:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:09.386684Z", "iopub.status.busy": "2024-07-11T14:33:09.386380Z", "iopub.status.idle": "2024-07-11T14:33:09.597934Z", "shell.execute_reply": "2024-07-11T14:33:09.597235Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"438pt\" height=\"334pt\"\n", " viewBox=\"0.00 0.00 438.00 334.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 330)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-330 434,-330 434,4 -4,4\"/>\n", "<!-- y_test -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M82,-36C82,-36 12,-36 12,-36 6,-36 0,-30 0,-24 0,-24 0,-12 0,-12 0,-6 6,0 12,0 12,0 82,0 82,0 88,0 94,-6 94,-12 94,-12 94,-24 94,-24 94,-30 88,-36 82,-36\"/>\n", "<text text-anchor=\"start\" x=\"29.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M194,-36C194,-36 124,-36 124,-36 118,-36 112,-30 112,-24 112,-24 112,-12 112,-12 112,-6 118,0 124,0 124,0 194,0 194,0 200,0 206,-6 206,-12 206,-12 206,-24 206,-24 206,-30 200,-36 194,-36\"/>\n", "<text text-anchor=\"start\" x=\"139\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"120\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M306,-36C306,-36 236,-36 236,-36 230,-36 224,-30 224,-24 224,-24 224,-12 224,-12 224,-6 230,0 236,0 236,0 306,0 306,0 312,0 318,-6 318,-12 318,-12 318,-24 318,-24 318,-30 312,-36 306,-36\"/>\n", "<text text-anchor=\"start\" x=\"252.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"232\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- y -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>y</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M225,-228C225,-228 195,-228 195,-228 189,-228 183,-222 183,-216 183,-216 183,-204 183,-204 183,-198 189,-192 195,-192 195,-192 225,-192 225,-192 231,-192 237,-198 237,-204 237,-204 237,-216 237,-216 237,-222 231,-228 225,-228\"/>\n", "<text text-anchor=\"start\" x=\"206.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"start\" x=\"191.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- get_train_test_split -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M265.5,-134C265.5,-134 164.5,-134 164.5,-134 158.5,-134 152.5,-128 152.5,-122 152.5,-122 152.5,-106 152.5,-106 152.5,-100 158.5,-94 164.5,-94 164.5,-94 265.5,-94 265.5,-94 271.5,-94 277.5,-100 277.5,-106 277.5,-106 277.5,-122 277.5,-122 277.5,-128 271.5,-134 265.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"160.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">get_train_test_split</text>\n", "<text text-anchor=\"start\" x=\"161.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:get_train_test_split</text>\n", "<text text-anchor=\"start\" x=\"200.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- y&#45;&gt;get_train_test_split -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>y&#45;&gt;get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M191.66,-191.64C187.33,-186.43 183.3,-180.38 181,-174 177.68,-164.8 177.45,-161.11 181,-152 182.35,-148.55 184.18,-145.22 186.31,-142.07\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"189.07,-144.23 192.48,-134.2 183.56,-139.91 189.07,-144.23\"/>\n", "<text text-anchor=\"middle\" x=\"202.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"202.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>X</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M312,-228C312,-228 282,-228 282,-228 276,-228 270,-222 270,-216 270,-216 270,-204 270,-204 270,-198 276,-192 282,-192 282,-192 312,-192 312,-192 318,-192 324,-198 324,-204 324,-204 324,-216 324,-216 324,-222 318,-228 312,-228\"/>\n", "<text text-anchor=\"start\" x=\"292.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"start\" x=\"278.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- X&#45;&gt;get_train_test_split -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>X&#45;&gt;get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M276.4,-191.96C270.22,-186.48 263.61,-180.21 258,-174 249.17,-164.23 240.43,-152.67 233.15,-142.38\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"235.99,-140.33 227.42,-134.11 230.24,-144.32 235.99,-140.33\"/>\n", "<text text-anchor=\"middle\" x=\"279.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"279.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- scale_data -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>scale_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M375,-134C375,-134 311,-134 311,-134 305,-134 299,-128 299,-122 299,-122 299,-106 299,-106 299,-100 305,-94 311,-94 311,-94 375,-94 375,-94 381,-94 387,-100 387,-106 387,-106 387,-122 387,-122 387,-128 381,-134 375,-134\"/>\n", "<text text-anchor=\"start\" x=\"312\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">scale_data</text>\n", "<text text-anchor=\"start\" x=\"307\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:scale_data</text>\n", "<text text-anchor=\"start\" x=\"328.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- X&#45;&gt;scale_data -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>X&#45;&gt;scale_data</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M299.91,-191.86C302.34,-180.18 306.48,-164.6 313,-152 314.7,-148.72 316.71,-145.46 318.89,-142.32\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"321.69,-144.41 324.89,-134.31 316.09,-140.21 321.69,-144.41\"/>\n", "<text text-anchor=\"middle\" x=\"334.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"334.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M418,-36C418,-36 348,-36 348,-36 342,-36 336,-30 336,-24 336,-24 336,-12 336,-12 336,-6 342,0 348,0 348,0 418,0 418,0 424,0 430,-6 430,-12 430,-12 430,-24 430,-24 430,-30 424,-36 418,-36\"/>\n", "<text text-anchor=\"start\" x=\"362.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"344\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;y_test -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;y_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M168.22,-93.83C156.21,-88.45 143.46,-82.34 132,-76 113.63,-65.85 94.12,-52.93 78.42,-41.96\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"80.32,-39.01 70.13,-36.09 76.28,-44.72 80.32,-39.01\"/>\n", "<text text-anchor=\"middle\" x=\"153.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"153.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;y_train -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M202.31,-93.94C198.59,-88.22 194.56,-81.9 191,-76 184.95,-65.99 178.59,-54.8 173.14,-45\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"176.14,-43.19 168.24,-36.12 170.01,-46.57 176.14,-43.19\"/>\n", "<text text-anchor=\"middle\" x=\"212.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"212.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;X_test -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M226.33,-93.98C234.77,-79.81 246.4,-60.3 255.67,-44.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"258.7,-46.48 260.81,-36.1 252.69,-42.9 258.7,-46.48\"/>\n", "<text text-anchor=\"middle\" x=\"271.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"271.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;X_train -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M260.76,-93.93C272.74,-88.51 285.52,-82.34 297,-76 315.52,-65.77 335.26,-52.85 351.15,-41.89\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"353.35,-44.63 359.55,-36.04 349.35,-38.88 353.35,-44.63\"/>\n", "<text text-anchor=\"middle\" x=\"352.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"352.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- scale_data&#45;&gt;X -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>scale_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M353.78,-134.11C359.07,-146.19 362.96,-161.79 356,-174 350.83,-183.08 342.27,-190.05 333.25,-195.29\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"331.48,-192.26 324.19,-199.96 334.69,-198.49 331.48,-192.26\"/>\n", "<text text-anchor=\"middle\" x=\"380.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_scaled</text>\n", "<text text-anchor=\"middle\" x=\"380.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- get_data -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>get_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M273,-326C273,-326 219,-326 219,-326 213,-326 207,-320 207,-314 207,-314 207,-298 207,-298 207,-292 213,-286 219,-286 219,-286 273,-286 273,-286 279,-286 285,-292 285,-298 285,-298 285,-314 285,-314 285,-320 279,-326 273,-326\"/>\n", "<text text-anchor=\"start\" x=\"221\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">get_data</text>\n", "<text text-anchor=\"start\" x=\"215\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:get_data</text>\n", "<text text-anchor=\"start\" x=\"231.5\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- get_data&#45;&gt;y -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>get_data&#45;&gt;y</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M226.15,-285.76C221.75,-280.43 217.63,-274.35 215,-268 211.19,-258.8 209.6,-248.06 209.07,-238.4\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"212.56,-238.02 208.87,-228.09 205.57,-238.16 212.56,-238.02\"/>\n", "<text text-anchor=\"middle\" x=\"236.5\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"236.5\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- get_data&#45;&gt;X -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>get_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M256.32,-285.98C263.94,-271.94 274.39,-252.66 282.8,-237.17\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"286.03,-238.56 287.72,-228.1 279.88,-235.22 286.03,-238.56\"/>\n", "<text text-anchor=\"middle\" x=\"299.5\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"299.5\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7908db100160>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(get_train_test_split).expand_back(recursive=True)\n", "cf.draw(verbose=True, orientation='TB')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that **the underlying low-level call graph that the CF represents is still\n", "acyclic**, because we distinguish between the value before and after the update.\n", "This shows up in the dataframe representation as two rows, where only one of the\n", "rows contains a call to `scale_data`:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:09.604267Z", "iopub.status.busy": "2024-07-11T14:33:09.603724Z", "iopub.status.idle": "2024-07-11T14:33:09.673520Z", "shell.execute_reply": "2024-07-11T14:33:09.672757Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    | get_data                   | scale_data                   | get_train_test_split                   |\n", "|---:|:---------------------------|:-----------------------------|:---------------------------------------|\n", "|  0 | Call(get_data, hid=73a...) |                              | Call(get_train_test_split, hid=7be...) |\n", "|  1 | Call(get_data, hid=73a...) | Call(scale_data, hid=d6b...) | Call(get_train_test_split, hid=e0a...) |\n"]}], "source": ["print(cf.df()[['get_data', 'scale_data', 'get_train_test_split']].to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pipelines that branch and/or merge\n", "Next, let's train and evaluate two kinds of models: an SVC (support vector\n", "classifier) and a random forest. We then initialize a CF from the `eval_model`\n", "function, and we call `expand_back` to trace back the full computation graph:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:09.676656Z", "iopub.status.busy": "2024-07-11T14:33:09.676426Z", "iopub.status.idle": "2024-07-11T14:33:10.107645Z", "shell.execute_reply": "2024-07-11T14:33:10.106891Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"824pt\" height=\"746pt\"\n", " viewBox=\"0.00 0.00 823.50 746.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 742)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-742 819.5,-742 819.5,4 -4,4\"/>\n", "<!-- y_test -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M42,-394C42,-394 12,-394 12,-394 6,-394 0,-388 0,-382 0,-382 0,-370 0,-370 0,-364 6,-358 12,-358 12,-358 42,-358 42,-358 48,-358 54,-364 54,-370 54,-370 54,-382 54,-382 54,-388 48,-394 42,-394\"/>\n", "<text text-anchor=\"start\" x=\"9.5\" y=\"-378.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"8.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node15\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M135,-134C135,-134 69,-134 69,-134 63,-134 57,-128 57,-122 57,-122 57,-106 57,-106 57,-100 63,-94 69,-94 69,-94 135,-94 135,-94 141,-94 147,-100 147,-106 147,-106 147,-122 147,-122 147,-128 141,-134 135,-134\"/>\n", "<text text-anchor=\"start\" x=\"69\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"65\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"87.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M23.29,-357.92C18.56,-332.85 12.19,-284.86 23,-246 33.94,-206.69 60.2,-167.38 79.45,-142.23\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"82.28,-144.3 85.68,-134.26 76.76,-139.99 82.28,-144.3\"/>\n", "<text text-anchor=\"middle\" x=\"44.5\" y=\"-267\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"44.5\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- max_depth -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>max_depth</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M239.5,-448C239.5,-448 158.5,-448 158.5,-448 152.5,-448 146.5,-442 146.5,-436 146.5,-436 146.5,-424 146.5,-424 146.5,-418 152.5,-412 158.5,-412 158.5,-412 239.5,-412 239.5,-412 245.5,-412 251.5,-418 251.5,-424 251.5,-424 251.5,-436 251.5,-436 251.5,-442 245.5,-448 239.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"167\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">max_depth</text>\n", "<text text-anchor=\"start\" x=\"154.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- train_random_forest -->\n", "<g id=\"node14\" class=\"node\">\n", "<title>train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M390.5,-340C390.5,-340 281.5,-340 281.5,-340 275.5,-340 269.5,-334 269.5,-328 269.5,-328 269.5,-312 269.5,-312 269.5,-306 275.5,-300 281.5,-300 281.5,-300 390.5,-300 390.5,-300 396.5,-300 402.5,-306 402.5,-312 402.5,-312 402.5,-328 402.5,-328 402.5,-334 396.5,-340 390.5,-340\"/>\n", "<text text-anchor=\"start\" x=\"277.5\" y=\"-327.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_random_forest</text>\n", "<text text-anchor=\"start\" x=\"280.5\" y=\"-317\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_random_forest</text>\n", "<text text-anchor=\"start\" x=\"321.5\" y=\"-307\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- max_depth&#45;&gt;train_random_forest -->\n", "<g id=\"edge15\" class=\"edge\">\n", "<title>max_depth&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M205.03,-411.87C211.44,-395.94 223.02,-372.52 240,-358 246.01,-352.86 252.82,-348.39 259.97,-344.51\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"261.78,-347.51 269.16,-339.91 258.65,-341.25 261.78,-347.51\"/>\n", "<text text-anchor=\"middle\" x=\"264.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">max_depth</text>\n", "<text text-anchor=\"middle\" x=\"264.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M512,-448C512,-448 480,-448 480,-448 474,-448 468,-442 468,-436 468,-436 468,-424 468,-424 468,-418 474,-412 480,-412 480,-412 512,-412 512,-412 518,-412 524,-418 524,-424 524,-424 524,-436 524,-436 524,-442 518,-448 512,-448\"/>\n", "<text text-anchor=\"start\" x=\"476\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"477.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_random_forest -->\n", "<g id=\"edge18\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M467.73,-415.62C457.79,-409.96 447.1,-402.64 439,-394 426.63,-380.8 433.24,-370.33 420,-358 414.71,-353.07 408.66,-348.73 402.27,-344.92\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"403.86,-341.79 393.41,-340.04 400.49,-347.93 403.86,-341.79\"/>\n", "<text text-anchor=\"middle\" x=\"460.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"460.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- train_svc -->\n", "<g id=\"node17\" class=\"node\">\n", "<title>train_svc</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M602,-340C602,-340 546,-340 546,-340 540,-340 534,-334 534,-328 534,-328 534,-312 534,-312 534,-306 540,-300 546,-300 546,-300 602,-300 602,-300 608,-300 614,-306 614,-312 614,-312 614,-328 614,-328 614,-334 608,-340 602,-340\"/>\n", "<text text-anchor=\"start\" x=\"547.5\" y=\"-327.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_svc</text>\n", "<text text-anchor=\"start\" x=\"542\" y=\"-317\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_svc</text>\n", "<text text-anchor=\"start\" x=\"559.5\" y=\"-307\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_svc -->\n", "<g id=\"edge17\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M524.13,-416.66C534.16,-411.03 544.76,-403.47 552,-394 561.7,-381.3 567.14,-364.35 570.19,-349.98\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"573.65,-350.54 572.01,-340.07 566.76,-349.27 573.65,-350.54\"/>\n", "<text text-anchor=\"middle\" x=\"589.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"589.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M117,-282C117,-282 87,-282 87,-282 81,-282 75,-276 75,-270 75,-270 75,-258 75,-258 75,-252 81,-246 87,-246 87,-246 117,-246 117,-246 123,-246 129,-252 129,-258 129,-258 129,-270 129,-270 129,-276 123,-282 117,-282\"/>\n", "<text text-anchor=\"start\" x=\"83.5\" y=\"-266.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"83.5\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M102,-245.73C102,-220.88 102,-174.68 102,-144.39\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"105.5,-144.02 102,-134.02 98.5,-144.02 105.5,-144.02\"/>\n", "<text text-anchor=\"middle\" x=\"123.5\" y=\"-213\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"123.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- y -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>y</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M187,-640C187,-640 157,-640 157,-640 151,-640 145,-634 145,-628 145,-628 145,-616 145,-616 145,-610 151,-604 157,-604 157,-604 187,-604 187,-604 193,-604 199,-610 199,-616 199,-616 199,-628 199,-628 199,-634 193,-640 187,-640\"/>\n", "<text text-anchor=\"start\" x=\"168.5\" y=\"-624.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"start\" x=\"153.5\" y=\"-614\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- get_train_test_split -->\n", "<g id=\"node16\" class=\"node\">\n", "<title>get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M227.5,-546C227.5,-546 126.5,-546 126.5,-546 120.5,-546 114.5,-540 114.5,-534 114.5,-534 114.5,-518 114.5,-518 114.5,-512 120.5,-506 126.5,-506 126.5,-506 227.5,-506 227.5,-506 233.5,-506 239.5,-512 239.5,-518 239.5,-518 239.5,-534 239.5,-534 239.5,-540 233.5,-546 227.5,-546\"/>\n", "<text text-anchor=\"start\" x=\"122.5\" y=\"-533.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">get_train_test_split</text>\n", "<text text-anchor=\"start\" x=\"123.5\" y=\"-523\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:get_train_test_split</text>\n", "<text text-anchor=\"start\" x=\"162.5\" y=\"-513\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- y&#45;&gt;get_train_test_split -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>y&#45;&gt;get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M153.66,-603.64C149.33,-598.43 145.3,-592.38 143,-586 139.68,-576.8 139.45,-573.11 143,-564 144.35,-560.55 146.18,-557.22 148.31,-554.07\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"151.07,-556.23 154.48,-546.2 145.56,-551.91 151.07,-556.23\"/>\n", "<text text-anchor=\"middle\" x=\"164.5\" y=\"-578\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"164.5\" y=\"-567\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>X</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M274,-640C274,-640 244,-640 244,-640 238,-640 232,-634 232,-628 232,-628 232,-616 232,-616 232,-610 238,-604 244,-604 244,-604 274,-604 274,-604 280,-604 286,-610 286,-616 286,-616 286,-628 286,-628 286,-634 280,-640 274,-640\"/>\n", "<text text-anchor=\"start\" x=\"254.5\" y=\"-624.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"start\" x=\"240.5\" y=\"-614\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- scale_data -->\n", "<g id=\"node13\" class=\"node\">\n", "<title>scale_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M337,-546C337,-546 273,-546 273,-546 267,-546 261,-540 261,-534 261,-534 261,-518 261,-518 261,-512 267,-506 273,-506 273,-506 337,-506 337,-506 343,-506 349,-512 349,-518 349,-518 349,-534 349,-534 349,-540 343,-546 337,-546\"/>\n", "<text text-anchor=\"start\" x=\"274\" y=\"-533.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">scale_data</text>\n", "<text text-anchor=\"start\" x=\"269\" y=\"-523\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:scale_data</text>\n", "<text text-anchor=\"start\" x=\"290.5\" y=\"-513\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- X&#45;&gt;scale_data -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>X&#45;&gt;scale_data</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M261.91,-603.86C264.34,-592.18 268.48,-576.6 275,-564 276.7,-560.72 278.71,-557.46 280.89,-554.32\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"283.69,-556.41 286.89,-546.31 278.09,-552.21 283.69,-556.41\"/>\n", "<text text-anchor=\"middle\" x=\"296.5\" y=\"-578\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"296.5\" y=\"-567\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X&#45;&gt;get_train_test_split -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>X&#45;&gt;get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M238.4,-603.96C232.22,-598.48 225.61,-592.21 220,-586 211.17,-576.23 202.43,-564.67 195.15,-554.38\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"197.99,-552.33 189.42,-546.11 192.24,-556.32 197.99,-552.33\"/>\n", "<text text-anchor=\"middle\" x=\"241.5\" y=\"-578\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"241.5\" y=\"-567\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- C -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>C</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M680.5,-448C680.5,-448 599.5,-448 599.5,-448 593.5,-448 587.5,-442 587.5,-436 587.5,-436 587.5,-424 587.5,-424 587.5,-418 593.5,-412 599.5,-412 599.5,-412 680.5,-412 680.5,-412 686.5,-412 692.5,-418 692.5,-424 692.5,-424 692.5,-436 692.5,-436 692.5,-442 686.5,-448 680.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"635\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">C</text>\n", "<text text-anchor=\"start\" x=\"595.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- C&#45;&gt;train_svc -->\n", "<g id=\"edge20\" class=\"edge\">\n", "<title>C&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M637.48,-411.76C634.65,-396.6 628.92,-374.45 618,-358 615.51,-354.25 612.53,-350.67 609.32,-347.33\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"611.66,-344.72 601.99,-340.38 606.84,-349.8 611.66,-344.72\"/>\n", "<text text-anchor=\"middle\" x=\"654.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">C</text>\n", "<text text-anchor=\"middle\" x=\"654.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M362.5,-448C362.5,-448 281.5,-448 281.5,-448 275.5,-448 269.5,-442 269.5,-436 269.5,-436 269.5,-424 269.5,-424 269.5,-418 275.5,-412 281.5,-412 281.5,-412 362.5,-412 362.5,-412 368.5,-412 374.5,-418 374.5,-424 374.5,-424 374.5,-436 374.5,-436 374.5,-442 368.5,-448 362.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"284.5\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"277.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_random_forest -->\n", "<g id=\"edge16\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M306.42,-411.65C302.59,-406.35 299.01,-400.26 297,-394 292.12,-378.76 290.68,-372.7 297,-358 298.57,-354.34 300.7,-350.88 303.16,-347.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"305.83,-349.9 309.82,-340.09 300.58,-345.27 305.83,-349.9\"/>\n", "<text text-anchor=\"middle\" x=\"325.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"325.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- kernel -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>kernel</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M803.5,-448C803.5,-448 722.5,-448 722.5,-448 716.5,-448 710.5,-442 710.5,-436 710.5,-436 710.5,-424 710.5,-424 710.5,-418 716.5,-412 722.5,-412 722.5,-412 803.5,-412 803.5,-412 809.5,-412 815.5,-418 815.5,-424 815.5,-424 815.5,-436 815.5,-436 815.5,-442 809.5,-448 803.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"745\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">kernel</text>\n", "<text text-anchor=\"start\" x=\"718.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- kernel&#45;&gt;train_svc -->\n", "<g id=\"edge21\" class=\"edge\">\n", "<title>kernel&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M746.64,-411.71C730.86,-395.88 705.65,-372.74 680,-358 662.6,-348 642.04,-340.05 623.68,-334.13\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"624.62,-330.76 614.03,-331.13 622.54,-337.44 624.62,-330.76\"/>\n", "<text text-anchor=\"middle\" x=\"748.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">kernel</text>\n", "<text text-anchor=\"middle\" x=\"748.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M437.5,-448C437.5,-448 404.5,-448 404.5,-448 398.5,-448 392.5,-442 392.5,-436 392.5,-436 392.5,-424 392.5,-424 392.5,-418 398.5,-412 404.5,-412 404.5,-412 437.5,-412 437.5,-412 443.5,-412 449.5,-418 449.5,-424 449.5,-424 449.5,-436 449.5,-436 449.5,-442 443.5,-448 437.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"400.5\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"402.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_random_forest -->\n", "<g id=\"edge14\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M395.76,-411.65C389.19,-406.44 382.44,-400.39 377,-394 370.6,-386.48 359.45,-366.39 350.39,-349.18\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"353.34,-347.27 345.61,-340.02 347.13,-350.5 353.34,-347.27\"/>\n", "<text text-anchor=\"middle\" x=\"398.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"398.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_svc -->\n", "<g id=\"edge13\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M449.52,-415.7C464.23,-408.44 480.27,-399.78 486,-394 498.74,-381.15 492.25,-370.83 505,-358 510.73,-352.24 517.54,-347.15 524.62,-342.73\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"526.76,-345.54 533.67,-337.5 523.26,-339.48 526.76,-345.54\"/>\n", "<text text-anchor=\"middle\" x=\"526.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"526.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- accuracy -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>accuracy</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M137,-36C137,-36 67,-36 67,-36 61,-36 55,-30 55,-24 55,-24 55,-12 55,-12 55,-6 61,0 67,0 67,0 137,0 137,0 143,0 149,-6 149,-12 149,-12 149,-24 149,-24 149,-30 143,-36 137,-36\"/>\n", "<text text-anchor=\"start\" x=\"75.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"start\" x=\"63\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node12\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M351,-228C351,-228 321,-228 321,-228 315,-228 309,-222 309,-216 309,-216 309,-204 309,-204 309,-198 315,-192 321,-192 321,-192 351,-192 351,-192 357,-192 363,-198 363,-204 363,-204 363,-216 363,-216 363,-222 357,-228 351,-228\"/>\n", "<text text-anchor=\"start\" x=\"318\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"317.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M308.7,-198.03C271.59,-183.13 204.4,-156.13 156.7,-136.98\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"157.72,-133.61 147.14,-133.13 155.11,-140.11 157.72,-133.61\"/>\n", "<text text-anchor=\"middle\" x=\"268.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"268.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- scale_data&#45;&gt;X -->\n", "<g id=\"edge24\" class=\"edge\">\n", "<title>scale_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M315.78,-546.11C321.07,-558.19 324.96,-573.79 318,-586 312.83,-595.08 304.27,-602.05 295.25,-607.29\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"293.48,-604.26 286.19,-611.96 296.69,-610.49 293.48,-604.26\"/>\n", "<text text-anchor=\"middle\" x=\"342.5\" y=\"-578\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_scaled</text>\n", "<text text-anchor=\"middle\" x=\"342.5\" y=\"-567\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_random_forest&#45;&gt;model -->\n", "<g id=\"edge12\" class=\"edge\">\n", "<title>train_random_forest&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M336,-299.94C336,-282.85 336,-257.58 336,-238.34\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"339.5,-238.09 336,-228.09 332.5,-238.09 339.5,-238.09\"/>\n", "<text text-anchor=\"middle\" x=\"357.5\" y=\"-267\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">rf_model</text>\n", "<text text-anchor=\"middle\" x=\"357.5\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;accuracy -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;accuracy</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M102,-93.98C102,-80.34 102,-61.75 102,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"105.5,-46.1 102,-36.1 98.5,-46.1 105.5,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"123.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"middle\" x=\"123.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;y_test -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;y_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M114.47,-517.22C78.77,-511.36 38.98,-501.91 28,-488 9.4,-464.44 13.06,-428.46 18.78,-403.9\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"22.19,-404.69 21.3,-394.14 15.42,-402.94 22.19,-404.69\"/>\n", "<text text-anchor=\"middle\" x=\"49.5\" y=\"-480\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"49.5\" y=\"-469\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;y_train -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M239.9,-508.51C243.98,-507.61 248.04,-506.76 252,-506 311,-494.6 328.7,-506.09 386,-488 412.26,-479.71 439.78,-465.45 460.72,-453.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"462.74,-456.14 469.55,-448.02 459.17,-450.12 462.74,-456.14\"/>\n", "<text text-anchor=\"middle\" x=\"456.5\" y=\"-480\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"456.5\" y=\"-469\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;X_test -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M146.01,-505.96C127.11,-492.4 104.61,-472.32 94,-448 71.54,-396.53 83.87,-329.01 93.68,-292.06\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"97.16,-292.63 96.48,-282.05 90.42,-290.74 97.16,-292.63\"/>\n", "<text text-anchor=\"middle\" x=\"115.5\" y=\"-433\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"115.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;X_train -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M231.48,-505.99C271.26,-491.82 326.61,-471.52 382.99,-448.3\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"384.51,-451.45 392.41,-444.39 381.83,-444.99 384.51,-451.45\"/>\n", "<text text-anchor=\"middle\" x=\"360.5\" y=\"-480\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"360.5\" y=\"-469\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- train_svc&#45;&gt;model -->\n", "<g id=\"edge19\" class=\"edge\">\n", "<title>train_svc&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M533.9,-300.8C488.82,-280.35 416.25,-247.42 372.64,-227.63\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"373.82,-224.32 363.27,-223.37 370.93,-230.69 373.82,-224.32\"/>\n", "<text text-anchor=\"middle\" x=\"514\" y=\"-267\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">svc_model</text>\n", "<text text-anchor=\"middle\" x=\"514\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_data -->\n", "<g id=\"node18\" class=\"node\">\n", "<title>get_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M235,-738C235,-738 181,-738 181,-738 175,-738 169,-732 169,-726 169,-726 169,-710 169,-710 169,-704 175,-698 181,-698 181,-698 235,-698 235,-698 241,-698 247,-704 247,-710 247,-710 247,-726 247,-726 247,-732 241,-738 235,-738\"/>\n", "<text text-anchor=\"start\" x=\"183\" y=\"-725.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">get_data</text>\n", "<text text-anchor=\"start\" x=\"177\" y=\"-715\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:get_data</text>\n", "<text text-anchor=\"start\" x=\"193.5\" y=\"-705\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- get_data&#45;&gt;y -->\n", "<g id=\"edge23\" class=\"edge\">\n", "<title>get_data&#45;&gt;y</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M188.15,-697.76C183.75,-692.43 179.63,-686.35 177,-680 173.19,-670.8 171.6,-660.06 171.07,-650.4\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"174.56,-650.02 170.87,-640.09 167.57,-650.16 174.56,-650.02\"/>\n", "<text text-anchor=\"middle\" x=\"198.5\" y=\"-672\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"198.5\" y=\"-661\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- get_data&#45;&gt;X -->\n", "<g id=\"edge22\" class=\"edge\">\n", "<title>get_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M218.32,-697.98C225.94,-683.94 236.39,-664.66 244.8,-649.17\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"248.03,-650.56 249.72,-640.1 241.88,-647.22 248.03,-650.56\"/>\n", "<text text-anchor=\"middle\" x=\"261.5\" y=\"-672\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"261.5\" y=\"-661\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7908cfc03880>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score\n", "\n", "@op(output_names=[\"svc_model\"])\n", "def train_svc(X_train, y_train, C: float = 1.0, kernel: str = \"linear\"):\n", "    model = SVC(C=C, kernel=kernel)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "@op(output_names=[\"rf_model\"])\n", "def train_random_forest(X_train, y_train, n_estimators: int = 5, max_depth: int = 5):\n", "    model = RandomForestClassifier(n_estimators=n_estimators, max_depth=max_depth)\n", "    model.fit(X_train, y_train)\n", "    return model\n", "\n", "@op(output_names=[\"accuracy\",])\n", "def eval_model(model, X_test, y_test):\n", "    y_pred = model.predict(X_test)\n", "    acc = accuracy_score(y_test, y_pred)\n", "    return acc\n", "\n", "with storage:\n", "    for scale in (True, False):\n", "        X, y = get_data()\n", "        if scale:\n", "            X = scale_data(X=X)\n", "        ### new: training an SVC and a Random Forest\n", "        X_train, X_test, y_train, y_test = get_train_test_split(X=X, y=y)\n", "        svc_model = train_svc(X_train=X_train, y_train=y_train)\n", "        svc_acc = eval_model(model=svc_model, X_test=X_test, y_test=y_test)\n", "        rf_model = train_random_forest(X_train=X_train, y_train=y_train)\n", "        rf_acc = eval_model(model=rf_model, X_test=X_test, y_test=y_test)\n", "\n", "cf = storage.cf(eval_model).expand_back(recursive=True)\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It's quite clear by looking at the CF's drawing what computations we did. As\n", "before, we can obtain a single dataframe that expresses the full computation, \n", "and we can select operations/variables of interest to analyze the results; for\n", "example, we see that random forest generally performs better than SVC on this\n", "dataset."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:10.110618Z", "iopub.status.busy": "2024-07-11T14:33:10.110298Z", "iopub.status.idle": "2024-07-11T14:33:10.184518Z", "shell.execute_reply": "2024-07-11T14:33:10.183763Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    |   accuracy | scale_data                   | train_svc                   | train_random_forest                   |\n", "|---:|-----------:|:-----------------------------|:----------------------------|:--------------------------------------|\n", "|  3 |      0.915 | Call(scale_data, hid=d6b...) |                             | Call(train_random_forest, hid=c42...) |\n", "|  2 |      0.885 |                              |                             | Call(train_random_forest, hid=997...) |\n", "|  0 |      0.82  |                              | Call(train_svc, hid=6a0...) |                                       |\n", "|  1 |      0.82  | Call(scale_data, hid=d6b...) | Call(train_svc, hid=7d9...) |                                       |\n"]}], "source": ["print(cf.df()[['accuracy', 'scale_data', 'train_svc', 'train_random_forest']].sort_values('accuracy', ascending=False).to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So is this the full story of this dataset? We might want to investigate further\n", "by \n", "\n", "- training more SVC and random forest models with different hyperparameters;\n", "- ensembling different types of models to see if this improves performance.\n", "\n", "We do this below by trying out more kernels for SVC and increasing the number of\n", "trees in the random forest. Importantly, when defining the new `eval_ensemble`\n", "operation, we type-annotate the `models` input using the custom `MList` type\n", "constructor. This tells `mandala` to track each element of `models` as an\n", "individual entity in the computation graph."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:10.187836Z", "iopub.status.busy": "2024-07-11T14:33:10.187535Z", "iopub.status.idle": "2024-07-11T14:33:10.726028Z", "shell.execute_reply": "2024-07-11T14:33:10.725410Z"}}, "outputs": [], "source": ["from typing import Any\n", "\n", "@op(output_names=[\"accuracy\"])\n", "def eval_ensemble(models: MList[Any], X_test, y_test):\n", "    y_preds = [model.predict(X_test) for model in models]\n", "    y_pred = np.mean(y_preds, axis=0) > 0.5\n", "    acc = accuracy_score(y_test, y_pred)\n", "    return acc\n", "\n", "with storage:\n", "    for scale in (True, False):\n", "        X, y = get_data()\n", "        if scale:\n", "            X = scale_data(X=X)\n", "        X_train, X_test, y_train, y_test = get_train_test_split(X=X, y=y)\n", "        svc_models = []\n", "        for kernel in ('linear', 'rbf', 'poly'): # new: trying different kernels\n", "            svc_model = train_svc(X_train=X_train, y_train=y_train, kernel=kernel)\n", "            svc_acc = eval_model(model=svc_model, X_test=X_test, y_test=y_test)\n", "            svc_models.append(svc_model)\n", "        rf_models = []\n", "        for n_estimators in (5, 10, 20): # new: trying different numbers of estimators\n", "            rf_model = train_random_forest(X_train=X_train, y_train=y_train, n_estimators=n_estimators)\n", "            rf_acc = eval_model(model=rf_model, X_test=X_test, y_test=y_test)\n", "            rf_models.append(rf_model)\n", "        \n", "        ### new: ensembling\n", "        ensemble_acc = eval_ensemble(models=svc_models + rf_models, X_test=X_test, y_test=y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tidy tools: merging `ComputationFrame`s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To illustrate how CFs admit **tidy tools** &mdash; operations that take as input\n", "computation frames and produce new ones &mdash; we'll use the `|` operator to\n", "merge the CFs for `eval_model` and `eval_ensemble`. \n", "\n", "Merging is similar to concatenation of dataframes: it takes the union of the\n", "sets of values/calls at each node by matching the names of the operations and\n", "variables between the two CFs. Because both our functions use the same names for\n", "analogous inputs (`X_test` and `y_test`) and outputs (`accuracy`), this works\n", "out nicely: "]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:10.729146Z", "iopub.status.busy": "2024-07-11T14:33:10.728908Z", "iopub.status.idle": "2024-07-11T14:33:10.881951Z", "shell.execute_reply": "2024-07-11T14:33:10.881304Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"494pt\" height=\"236pt\"\n", " viewBox=\"0.00 0.00 493.50 236.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 232)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-232 489.5,-232 489.5,4 -4,4\"/>\n", "<!-- y_test -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M227.5,-228C227.5,-228 146.5,-228 146.5,-228 140.5,-228 134.5,-222 134.5,-216 134.5,-216 134.5,-204 134.5,-204 134.5,-198 140.5,-192 146.5,-192 146.5,-192 227.5,-192 227.5,-192 233.5,-192 239.5,-198 239.5,-204 239.5,-204 239.5,-216 239.5,-216 239.5,-222 233.5,-228 227.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"169.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"142.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- eval_ensemble -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M352,-134C352,-134 270,-134 270,-134 264,-134 258,-128 258,-122 258,-122 258,-106 258,-106 258,-100 264,-94 270,-94 270,-94 352,-94 352,-94 358,-94 364,-100 364,-106 364,-106 364,-122 364,-122 364,-128 358,-134 352,-134\"/>\n", "<text text-anchor=\"start\" x=\"268\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_ensemble</text>\n", "<text text-anchor=\"start\" x=\"266\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_ensemble</text>\n", "<text text-anchor=\"start\" x=\"296.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_ensemble -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M230.02,-191.99C240.33,-186.96 250.96,-180.91 260,-174 270.26,-166.15 270.55,-161.77 279,-152 281.81,-148.75 284.76,-145.33 287.69,-141.95\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"290.5,-144.05 294.39,-134.2 285.2,-139.48 290.5,-144.05\"/>\n", "<text text-anchor=\"middle\" x=\"300.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"300.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M220,-134C220,-134 154,-134 154,-134 148,-134 142,-128 142,-122 142,-122 142,-106 142,-106 142,-100 148,-94 154,-94 154,-94 220,-94 220,-94 226,-94 232,-100 232,-106 232,-106 232,-122 232,-122 232,-128 226,-134 220,-134\"/>\n", "<text text-anchor=\"start\" x=\"154\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"150\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"170\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">12 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M163.94,-191.97C158.71,-186.83 153.85,-180.74 151,-174 147.19,-164.99 147.32,-161.06 151,-152 152.4,-148.56 154.29,-145.26 156.47,-142.15\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"159.19,-144.35 162.81,-134.39 153.77,-139.93 159.19,-144.35\"/>\n", "<text text-anchor=\"middle\" x=\"172.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"172.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M350.5,-228C350.5,-228 269.5,-228 269.5,-228 263.5,-228 257.5,-222 257.5,-216 257.5,-216 257.5,-204 257.5,-204 257.5,-198 263.5,-192 269.5,-192 269.5,-192 350.5,-192 350.5,-192 356.5,-192 362.5,-198 362.5,-204 362.5,-204 362.5,-216 362.5,-216 362.5,-222 356.5,-228 350.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"291.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"265.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_ensemble -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M319.68,-191.71C322.26,-186.22 324.69,-180.01 326,-174 328.08,-164.45 327.87,-161.6 326,-152 325.47,-149.29 324.74,-146.52 323.89,-143.79\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"327.1,-142.36 320.39,-134.15 320.52,-144.75 327.1,-142.36\"/>\n", "<text text-anchor=\"middle\" x=\"348.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"348.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M257.39,-192.88C240.13,-186.82 223.65,-179.95 217,-174 207.89,-165.84 201.17,-154.27 196.43,-143.53\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"199.58,-142.01 192.61,-134.03 193.09,-144.61 199.58,-142.01\"/>\n", "<text text-anchor=\"middle\" x=\"238.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"238.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- accuracy -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>accuracy</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M285,-36C285,-36 205,-36 205,-36 199,-36 193,-30 193,-24 193,-24 193,-12 193,-12 193,-6 199,0 205,0 205,0 285,0 285,0 291,0 297,-6 297,-12 297,-12 297,-24 297,-24 297,-30 291,-36 285,-36\"/>\n", "<text text-anchor=\"start\" x=\"218.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"start\" x=\"201\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">14 values (14 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M104,-228C104,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 104,-192 104,-192 110,-192 116,-198 116,-204 116,-204 116,-216 116,-216 116,-222 110,-228 104,-228\"/>\n", "<text text-anchor=\"start\" x=\"40\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">12 values (12 sources)</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M65.97,-191.77C72.51,-179.28 82.82,-162.75 96,-152 106.63,-143.32 119.58,-136.44 132.32,-131.09\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"133.97,-134.2 141.99,-127.29 131.41,-127.69 133.97,-134.2\"/>\n", "<text text-anchor=\"middle\" x=\"120.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"120.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(12 values)</text>\n", "</g>\n", "<!-- models -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>models</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M473.5,-228C473.5,-228 392.5,-228 392.5,-228 386.5,-228 380.5,-222 380.5,-216 380.5,-216 380.5,-204 380.5,-204 380.5,-198 386.5,-192 392.5,-192 392.5,-192 473.5,-192 473.5,-192 479.5,-192 485.5,-198 485.5,-204 485.5,-204 485.5,-216 485.5,-216 485.5,-222 479.5,-228 473.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"411.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">models</text>\n", "<text text-anchor=\"start\" x=\"388.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- models&#45;&gt;eval_ensemble -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>models&#45;&gt;eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M417.42,-191.93C406.04,-179.98 389.88,-164.06 374,-152 368.24,-147.62 361.91,-143.36 355.56,-139.37\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"357.3,-136.34 346.94,-134.13 353.67,-142.32 357.3,-136.34\"/>\n", "<text text-anchor=\"middle\" x=\"418.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">models</text>\n", "<text text-anchor=\"middle\" x=\"418.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_ensemble&#45;&gt;accuracy -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>eval_ensemble&#45;&gt;accuracy</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M297.77,-93.82C289.77,-82.26 279.34,-67.26 270,-54 267.81,-50.89 265.49,-47.62 263.2,-44.4\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"265.91,-42.17 257.25,-36.06 260.21,-46.23 265.91,-42.17\"/>\n", "<text text-anchor=\"middle\" x=\"305.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"middle\" x=\"305.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;accuracy -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>eval_model&#45;&gt;accuracy</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M197.5,-93.91C204.04,-82.25 212.75,-67.11 221,-54 222.9,-50.98 224.95,-47.85 227.01,-44.75\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"230.05,-46.51 232.76,-36.27 224.25,-42.58 230.05,-46.51\"/>\n", "<text text-anchor=\"middle\" x=\"245.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"middle\" x=\"245.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(12 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7908cfc02a40>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(eval_ensemble) | storage.cf(eval_model)\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Operations that aggregate multiple results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As before, let's expand this CF back to see the full computation graph that\n", "leads to it:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:10.886145Z", "iopub.status.busy": "2024-07-11T14:33:10.885438Z", "iopub.status.idle": "2024-07-11T14:33:11.110432Z", "shell.execute_reply": "2024-07-11T14:33:11.109802Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"826pt\" height=\"910pt\"\n", " viewBox=\"0.00 0.00 825.50 910.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 906)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-906 821.5,-906 821.5,4 -4,4\"/>\n", "<!-- y_test -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M98,-612C98,-612 68,-612 68,-612 62,-612 56,-606 56,-600 56,-600 56,-588 56,-588 56,-582 62,-576 68,-576 68,-576 98,-576 98,-576 104,-576 110,-582 110,-588 110,-588 110,-600 110,-600 110,-606 104,-612 98,-612\"/>\n", "<text text-anchor=\"start\" x=\"65.5\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"64.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node17\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M192,-326C192,-326 126,-326 126,-326 120,-326 114,-320 114,-314 114,-314 114,-298 114,-298 114,-292 120,-286 126,-286 126,-286 192,-286 192,-286 198,-286 204,-292 204,-298 204,-298 204,-314 204,-314 204,-320 198,-326 192,-326\"/>\n", "<text text-anchor=\"start\" x=\"126\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"122\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"142\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">12 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M81.08,-575.6C78.34,-546.71 74.61,-487.16 84,-438 88.83,-412.71 92.85,-406.71 105,-384 114.25,-366.71 126.7,-348.7 137.32,-334.45\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"140.39,-336.18 143.65,-326.1 134.82,-331.96 140.39,-336.18\"/>\n", "<text text-anchor=\"middle\" x=\"105.5\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"105.5\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_ensemble -->\n", "<g id=\"node19\" class=\"node\">\n", "<title>eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M117,-134C117,-134 35,-134 35,-134 29,-134 23,-128 23,-122 23,-122 23,-106 23,-106 23,-100 29,-94 35,-94 35,-94 117,-94 117,-94 123,-94 129,-100 129,-106 129,-106 129,-122 129,-122 129,-128 123,-134 117,-134\"/>\n", "<text text-anchor=\"start\" x=\"33\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_ensemble</text>\n", "<text text-anchor=\"start\" x=\"31\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_ensemble</text>\n", "<text text-anchor=\"start\" x=\"61.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_ensemble -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M55.82,-577.34C31.62,-561.14 0,-533.4 0,-499 0,-499 0,-499 0,-209 0,-181.52 19.89,-157.55 39.32,-140.7\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"41.78,-143.21 47.27,-134.15 37.32,-137.81 41.78,-143.21\"/>\n", "<text text-anchor=\"middle\" x=\"21.5\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"21.5\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- max_depth -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>max_depth</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M805.5,-612C805.5,-612 724.5,-612 724.5,-612 718.5,-612 712.5,-606 712.5,-600 712.5,-600 712.5,-588 712.5,-588 712.5,-582 718.5,-576 724.5,-576 724.5,-576 805.5,-576 805.5,-576 811.5,-576 817.5,-582 817.5,-588 817.5,-588 817.5,-600 817.5,-600 817.5,-606 811.5,-612 805.5,-612\"/>\n", "<text text-anchor=\"start\" x=\"733\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">max_depth</text>\n", "<text text-anchor=\"start\" x=\"720.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- train_random_forest -->\n", "<g id=\"node15\" class=\"node\">\n", "<title>train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M630.5,-518C630.5,-518 521.5,-518 521.5,-518 515.5,-518 509.5,-512 509.5,-506 509.5,-506 509.5,-490 509.5,-490 509.5,-484 515.5,-478 521.5,-478 521.5,-478 630.5,-478 630.5,-478 636.5,-478 642.5,-484 642.5,-490 642.5,-490 642.5,-506 642.5,-506 642.5,-512 636.5,-518 630.5,-518\"/>\n", "<text text-anchor=\"start\" x=\"517.5\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_random_forest</text>\n", "<text text-anchor=\"start\" x=\"520.5\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_random_forest</text>\n", "<text text-anchor=\"start\" x=\"561.5\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">6 calls</text>\n", "</g>\n", "<!-- max_depth&#45;&gt;train_random_forest -->\n", "<g id=\"edge21\" class=\"edge\">\n", "<title>max_depth&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M744.69,-575.98C729.13,-563.6 706.71,-547.11 685,-536 674.58,-530.66 663.22,-525.83 651.89,-521.55\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"653,-518.23 642.41,-518.09 650.6,-524.81 653,-518.23\"/>\n", "<text text-anchor=\"middle\" x=\"743.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">max_depth</text>\n", "<text text-anchor=\"middle\" x=\"743.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M535,-612C535,-612 503,-612 503,-612 497,-612 491,-606 491,-600 491,-600 491,-588 491,-588 491,-582 497,-576 503,-576 503,-576 535,-576 535,-576 541,-576 547,-582 547,-588 547,-588 547,-600 547,-600 547,-606 541,-612 535,-612\"/>\n", "<text text-anchor=\"start\" x=\"499\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"500.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_random_forest -->\n", "<g id=\"edge24\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M539.15,-575.78C544.5,-570.49 549.91,-564.36 554,-558 559.94,-548.76 564.61,-537.68 568.08,-527.63\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"571.44,-528.62 571.17,-518.03 564.78,-526.48 571.44,-528.62\"/>\n", "<text text-anchor=\"middle\" x=\"585.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"585.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- train_svc -->\n", "<g id=\"node20\" class=\"node\">\n", "<title>train_svc</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M340,-518C340,-518 284,-518 284,-518 278,-518 272,-512 272,-506 272,-506 272,-490 272,-490 272,-484 278,-478 284,-478 284,-478 340,-478 340,-478 346,-478 352,-484 352,-490 352,-490 352,-506 352,-506 352,-512 346,-518 340,-518\"/>\n", "<text text-anchor=\"start\" x=\"285.5\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_svc</text>\n", "<text text-anchor=\"start\" x=\"280\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_svc</text>\n", "<text text-anchor=\"start\" x=\"297.5\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">6 calls</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_svc -->\n", "<g id=\"edge23\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M514.06,-575.91C509.42,-562.98 501.19,-545.82 488,-536 468.42,-521.43 407.13,-510.85 362.37,-504.83\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"362.6,-501.33 352.23,-503.51 361.69,-508.27 362.6,-501.33\"/>\n", "<text text-anchor=\"middle\" x=\"526.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"526.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M156,-420C156,-420 126,-420 126,-420 120,-420 114,-414 114,-408 114,-408 114,-396 114,-396 114,-390 120,-384 126,-384 126,-384 156,-384 156,-384 162,-384 168,-390 168,-396 168,-396 168,-408 168,-408 168,-414 162,-420 156,-420\"/>\n", "<text text-anchor=\"start\" x=\"122.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"122.5\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M144.3,-383.76C146.84,-370.5 150.41,-351.86 153.39,-336.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"156.9,-336.55 155.35,-326.07 150.03,-335.24 156.9,-336.55\"/>\n", "<text text-anchor=\"middle\" x=\"173.5\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"173.5\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_ensemble -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M131.71,-383.79C123.88,-368.82 112.74,-346.39 105,-326 88.92,-283.65 83.48,-272.83 77,-228 72.93,-199.84 73.15,-167.32 74.11,-144.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"77.61,-144.47 74.6,-134.31 70.62,-144.13 77.61,-144.47\"/>\n", "<text text-anchor=\"middle\" x=\"106.5\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"106.5\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- y -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>y</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M235,-804C235,-804 205,-804 205,-804 199,-804 193,-798 193,-792 193,-792 193,-780 193,-780 193,-774 199,-768 205,-768 205,-768 235,-768 235,-768 241,-768 247,-774 247,-780 247,-780 247,-792 247,-792 247,-798 241,-804 235,-804\"/>\n", "<text text-anchor=\"start\" x=\"216.5\" y=\"-788.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"start\" x=\"201.5\" y=\"-778\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- get_train_test_split -->\n", "<g id=\"node18\" class=\"node\">\n", "<title>get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M275.5,-710C275.5,-710 174.5,-710 174.5,-710 168.5,-710 162.5,-704 162.5,-698 162.5,-698 162.5,-682 162.5,-682 162.5,-676 168.5,-670 174.5,-670 174.5,-670 275.5,-670 275.5,-670 281.5,-670 287.5,-676 287.5,-682 287.5,-682 287.5,-698 287.5,-698 287.5,-704 281.5,-710 275.5,-710\"/>\n", "<text text-anchor=\"start\" x=\"170.5\" y=\"-697.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">get_train_test_split</text>\n", "<text text-anchor=\"start\" x=\"171.5\" y=\"-687\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:get_train_test_split</text>\n", "<text text-anchor=\"start\" x=\"210.5\" y=\"-677\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- y&#45;&gt;get_train_test_split -->\n", "<g id=\"edge17\" class=\"edge\">\n", "<title>y&#45;&gt;get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M201.66,-767.64C197.33,-762.43 193.3,-756.38 191,-750 187.68,-740.8 187.45,-737.11 191,-728 192.35,-724.55 194.18,-721.22 196.31,-718.07\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"199.07,-720.23 202.48,-710.2 193.56,-715.91 199.07,-720.23\"/>\n", "<text text-anchor=\"middle\" x=\"212.5\" y=\"-742\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"212.5\" y=\"-731\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>X</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M322,-804C322,-804 292,-804 292,-804 286,-804 280,-798 280,-792 280,-792 280,-780 280,-780 280,-774 286,-768 292,-768 292,-768 322,-768 322,-768 328,-768 334,-774 334,-780 334,-780 334,-792 334,-792 334,-798 328,-804 322,-804\"/>\n", "<text text-anchor=\"start\" x=\"302.5\" y=\"-788.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"start\" x=\"288.5\" y=\"-778\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- scale_data -->\n", "<g id=\"node14\" class=\"node\">\n", "<title>scale_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M385,-710C385,-710 321,-710 321,-710 315,-710 309,-704 309,-698 309,-698 309,-682 309,-682 309,-676 315,-670 321,-670 321,-670 385,-670 385,-670 391,-670 397,-676 397,-682 397,-682 397,-698 397,-698 397,-704 391,-710 385,-710\"/>\n", "<text text-anchor=\"start\" x=\"322\" y=\"-697.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">scale_data</text>\n", "<text text-anchor=\"start\" x=\"317\" y=\"-687\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:scale_data</text>\n", "<text text-anchor=\"start\" x=\"338.5\" y=\"-677\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- X&#45;&gt;scale_data -->\n", "<g id=\"edge16\" class=\"edge\">\n", "<title>X&#45;&gt;scale_data</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M309.91,-767.86C312.34,-756.18 316.48,-740.6 323,-728 324.7,-724.72 326.71,-721.46 328.89,-718.32\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"331.69,-720.41 334.89,-710.31 326.09,-716.21 331.69,-720.41\"/>\n", "<text text-anchor=\"middle\" x=\"344.5\" y=\"-742\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"344.5\" y=\"-731\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X&#45;&gt;get_train_test_split -->\n", "<g id=\"edge15\" class=\"edge\">\n", "<title>X&#45;&gt;get_train_test_split</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M286.4,-767.96C280.22,-762.48 273.61,-756.21 268,-750 259.17,-740.23 250.43,-728.67 243.15,-718.38\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"245.99,-716.33 237.42,-710.11 240.24,-720.32 245.99,-716.33\"/>\n", "<text text-anchor=\"middle\" x=\"289.5\" y=\"-742\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"289.5\" y=\"-731\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- C -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>C</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M262.5,-612C262.5,-612 181.5,-612 181.5,-612 175.5,-612 169.5,-606 169.5,-600 169.5,-600 169.5,-588 169.5,-588 169.5,-582 175.5,-576 181.5,-576 181.5,-576 262.5,-576 262.5,-576 268.5,-576 274.5,-582 274.5,-588 274.5,-588 274.5,-600 274.5,-600 274.5,-606 268.5,-612 262.5,-612\"/>\n", "<text text-anchor=\"start\" x=\"217\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">C</text>\n", "<text text-anchor=\"start\" x=\"177.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- C&#45;&gt;train_svc -->\n", "<g id=\"edge26\" class=\"edge\">\n", "<title>C&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M231.92,-575.96C239.33,-564.03 250.18,-548.12 262,-536 265.91,-531.99 270.29,-528.08 274.79,-524.41\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"277.19,-526.97 282.93,-518.06 272.89,-521.45 277.19,-526.97\"/>\n", "<text text-anchor=\"middle\" x=\"283.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">C</text>\n", "<text text-anchor=\"middle\" x=\"283.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M682.5,-612C682.5,-612 601.5,-612 601.5,-612 595.5,-612 589.5,-606 589.5,-600 589.5,-600 589.5,-588 589.5,-588 589.5,-582 595.5,-576 601.5,-576 601.5,-576 682.5,-576 682.5,-576 688.5,-576 694.5,-582 694.5,-588 694.5,-588 694.5,-600 694.5,-600 694.5,-606 688.5,-612 682.5,-612\"/>\n", "<text text-anchor=\"start\" x=\"604.5\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"597.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sources)</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_random_forest -->\n", "<g id=\"edge22\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M634.13,-575.85C628.43,-564.16 620.13,-548.58 611,-536 608.49,-532.54 605.67,-529.06 602.76,-525.7\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"605.21,-523.19 595.89,-518.14 600.02,-527.89 605.21,-523.19\"/>\n", "<text text-anchor=\"middle\" x=\"652.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"652.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- kernel -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>kernel</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M385.5,-612C385.5,-612 304.5,-612 304.5,-612 298.5,-612 292.5,-606 292.5,-600 292.5,-600 292.5,-588 292.5,-588 292.5,-582 298.5,-576 304.5,-576 304.5,-576 385.5,-576 385.5,-576 391.5,-576 397.5,-582 397.5,-588 397.5,-588 397.5,-600 397.5,-600 397.5,-606 391.5,-612 385.5,-612\"/>\n", "<text text-anchor=\"start\" x=\"327\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">kernel</text>\n", "<text text-anchor=\"start\" x=\"300.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sources)</text>\n", "</g>\n", "<!-- kernel&#45;&gt;train_svc -->\n", "<g id=\"edge27\" class=\"edge\">\n", "<title>kernel&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M324.87,-575.9C320.09,-570.69 315.62,-564.58 313,-558 309.3,-548.7 308.28,-537.91 308.43,-528.12\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"311.93,-528.18 308.98,-518.01 304.94,-527.8 311.93,-528.18\"/>\n", "<text text-anchor=\"middle\" x=\"334.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">kernel</text>\n", "<text text-anchor=\"middle\" x=\"334.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M460.5,-612C460.5,-612 427.5,-612 427.5,-612 421.5,-612 415.5,-606 415.5,-600 415.5,-600 415.5,-588 415.5,-588 415.5,-582 421.5,-576 427.5,-576 427.5,-576 460.5,-576 460.5,-576 466.5,-576 472.5,-582 472.5,-588 472.5,-588 472.5,-600 472.5,-600 472.5,-606 466.5,-612 460.5,-612\"/>\n", "<text text-anchor=\"start\" x=\"423.5\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"425.5\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_random_forest -->\n", "<g id=\"edge20\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_random_forest</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M439.31,-575.78C437.05,-563.45 436.42,-547.13 445,-536 452.42,-526.37 475.02,-518.51 499.41,-512.55\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"500.23,-515.95 509.18,-510.27 498.65,-509.13 500.23,-515.95\"/>\n", "<text text-anchor=\"middle\" x=\"466.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"466.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_svc -->\n", "<g id=\"edge19\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_svc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M415.49,-579.76C403.76,-573.78 390.3,-566.21 379,-558 367.56,-549.68 366.53,-545.45 356,-536 351.91,-532.33 347.55,-528.54 343.22,-524.83\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"345.42,-522.11 335.53,-518.31 340.9,-527.45 345.42,-522.11\"/>\n", "<text text-anchor=\"middle\" x=\"400.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"400.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- accuracy -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>accuracy</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M174,-36C174,-36 94,-36 94,-36 88,-36 82,-30 82,-24 82,-24 82,-12 82,-12 82,-6 88,0 94,0 94,0 174,0 174,0 180,0 186,-6 186,-12 186,-12 186,-24 186,-24 186,-30 180,-36 174,-36\"/>\n", "<text text-anchor=\"start\" x=\"107.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"start\" x=\"90\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">14 values (14 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node12\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M326.5,-420C326.5,-420 291.5,-420 291.5,-420 285.5,-420 279.5,-414 279.5,-408 279.5,-408 279.5,-396 279.5,-396 279.5,-390 285.5,-384 291.5,-384 291.5,-384 326.5,-384 326.5,-384 332.5,-384 338.5,-390 338.5,-396 338.5,-396 338.5,-408 338.5,-408 338.5,-414 332.5,-420 326.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"291\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"287.5\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">12 values</text>\n", "</g>\n", "<!-- __make_list__ -->\n", "<g id=\"node16\" class=\"node\">\n", "<title>__make_list__</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M325,-326C325,-326 245,-326 245,-326 239,-326 233,-320 233,-314 233,-314 233,-298 233,-298 233,-292 239,-286 245,-286 245,-286 325,-286 325,-286 331,-286 337,-292 337,-298 337,-298 337,-314 337,-314 337,-320 331,-326 325,-326\"/>\n", "<text text-anchor=\"start\" x=\"244\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">__make_list__</text>\n", "<text text-anchor=\"start\" x=\"241\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:__make_list__</text>\n", "<text text-anchor=\"start\" x=\"270.5\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- model&#45;&gt;__make_list__ -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>model&#45;&gt;__make_list__</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M304.6,-383.76C301.18,-370.37 296.36,-351.5 292.36,-335.82\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"295.73,-334.9 289.87,-326.07 288.95,-336.63 295.73,-334.9\"/>\n", "<text text-anchor=\"middle\" x=\"324.5\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">*elts</text>\n", "<text text-anchor=\"middle\" x=\"324.5\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(12 values)</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M279.5,-386.69C267.64,-380.65 253.97,-373.34 242,-366 225.24,-355.73 207.38,-343.25 192.54,-332.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"194.31,-329.38 184.18,-326.27 190.16,-335.01 194.31,-329.38\"/>\n", "<text text-anchor=\"middle\" x=\"266.5\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"266.5\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(12 values)</text>\n", "</g>\n", "<!-- models -->\n", "<g id=\"node13\" class=\"node\">\n", "<title>models</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M133.5,-228C133.5,-228 98.5,-228 98.5,-228 92.5,-228 86.5,-222 86.5,-216 86.5,-216 86.5,-204 86.5,-204 86.5,-198 92.5,-192 98.5,-192 98.5,-192 133.5,-192 133.5,-192 139.5,-192 145.5,-198 145.5,-204 145.5,-204 145.5,-216 145.5,-216 145.5,-222 139.5,-228 133.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"94.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">models</text>\n", "<text text-anchor=\"start\" x=\"97.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- models&#45;&gt;eval_ensemble -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>models&#45;&gt;eval_ensemble</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M108.66,-191.76C102.91,-178.24 94.78,-159.14 88.08,-143.38\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"91.25,-141.9 84.12,-134.07 84.81,-144.64 91.25,-141.9\"/>\n", "<text text-anchor=\"middle\" x=\"122.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">models</text>\n", "<text text-anchor=\"middle\" x=\"122.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- scale_data&#45;&gt;X -->\n", "<g id=\"edge30\" class=\"edge\">\n", "<title>scale_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M363.78,-710.11C369.07,-722.19 372.96,-737.79 366,-750 360.83,-759.08 352.27,-766.05 343.25,-771.29\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"341.48,-768.26 334.19,-775.96 344.69,-774.49 341.48,-768.26\"/>\n", "<text text-anchor=\"middle\" x=\"390.5\" y=\"-742\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_scaled</text>\n", "<text text-anchor=\"middle\" x=\"390.5\" y=\"-731\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_random_forest&#45;&gt;model -->\n", "<g id=\"edge18\" class=\"edge\">\n", "<title>train_random_forest&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M521.97,-477.98C470.44,-459.84 394.06,-432.95 348.15,-416.78\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"349.15,-413.43 338.56,-413.41 346.83,-420.03 349.15,-413.43\"/>\n", "<text text-anchor=\"middle\" x=\"487.5\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">rf_model</text>\n", "<text text-anchor=\"middle\" x=\"487.5\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(6 values)</text>\n", "</g>\n", "<!-- __make_list__&#45;&gt;models -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>__make_list__&#45;&gt;models</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M250.8,-285.98C222.95,-270.49 183.64,-248.62 154.65,-232.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"156.07,-229.28 145.63,-227.48 152.67,-235.4 156.07,-229.28\"/>\n", "<text text-anchor=\"middle\" x=\"237.5\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">list</text>\n", "<text text-anchor=\"middle\" x=\"237.5\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;accuracy -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>eval_model&#45;&gt;accuracy</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M162.88,-285.82C170.71,-242.98 185.53,-136.79 159,-54 158,-50.87 156.6,-47.78 154.98,-44.82\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"157.91,-42.9 149.58,-36.35 152,-46.66 157.91,-42.9\"/>\n", "<text text-anchor=\"middle\" x=\"199.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"middle\" x=\"199.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(12 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;y_test -->\n", "<g id=\"edge12\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;y_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M162.39,-680.12C129.53,-674.06 94.04,-664.79 84,-652 77.51,-643.74 76.23,-632.53 76.96,-622.24\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"80.47,-622.38 78.3,-612.01 73.53,-621.47 80.47,-622.38\"/>\n", "<text text-anchor=\"middle\" x=\"105.5\" y=\"-644\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"105.5\" y=\"-633\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;y_train -->\n", "<g id=\"edge14\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M287.52,-672.74C291.74,-671.77 295.92,-670.85 300,-670 348.06,-659.96 362.41,-667.49 409,-652 434.96,-643.37 462.27,-629.25 483.19,-617.22\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"485.17,-620.12 492.03,-612.04 481.63,-614.08 485.17,-620.12\"/>\n", "<text text-anchor=\"middle\" x=\"479.5\" y=\"-644\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"479.5\" y=\"-633\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;X_test -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M202.87,-669.82C188,-655.51 169.44,-634.6 160,-612 134.46,-550.9 135.61,-471.21 138.42,-430.12\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"141.92,-430.31 139.2,-420.07 134.94,-429.77 141.92,-430.31\"/>\n", "<text text-anchor=\"middle\" x=\"166.5\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"166.5\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- get_train_test_split&#45;&gt;X_train -->\n", "<g id=\"edge13\" class=\"edge\">\n", "<title>get_train_test_split&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M271.17,-669.92C305.97,-655.44 355.07,-634.79 406.14,-612.33\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"407.62,-615.5 415.36,-608.27 404.79,-609.1 407.62,-615.5\"/>\n", "<text text-anchor=\"middle\" x=\"383.5\" y=\"-644\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"383.5\" y=\"-633\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_ensemble&#45;&gt;accuracy -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>eval_ensemble&#45;&gt;accuracy</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M87.74,-93.98C96.48,-79.81 108.52,-60.3 118.12,-44.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"121.18,-46.45 123.45,-36.1 115.22,-42.77 121.18,-46.45\"/>\n", "<text text-anchor=\"middle\" x=\"133.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">accuracy</text>\n", "<text text-anchor=\"middle\" x=\"133.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- train_svc&#45;&gt;model -->\n", "<g id=\"edge25\" class=\"edge\">\n", "<title>train_svc&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M311.39,-477.98C310.96,-464.34 310.36,-445.75 309.88,-430.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"313.36,-429.98 309.55,-420.1 306.37,-430.2 313.36,-429.98\"/>\n", "<text text-anchor=\"middle\" x=\"335\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">svc_model</text>\n", "<text text-anchor=\"middle\" x=\"335\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(6 values)</text>\n", "</g>\n", "<!-- get_data -->\n", "<g id=\"node21\" class=\"node\">\n", "<title>get_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M283,-902C283,-902 229,-902 229,-902 223,-902 217,-896 217,-890 217,-890 217,-874 217,-874 217,-868 223,-862 229,-862 229,-862 283,-862 283,-862 289,-862 295,-868 295,-874 295,-874 295,-890 295,-890 295,-896 289,-902 283,-902\"/>\n", "<text text-anchor=\"start\" x=\"231\" y=\"-889.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">get_data</text>\n", "<text text-anchor=\"start\" x=\"225\" y=\"-879\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:get_data</text>\n", "<text text-anchor=\"start\" x=\"241.5\" y=\"-869\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- get_data&#45;&gt;y -->\n", "<g id=\"edge29\" class=\"edge\">\n", "<title>get_data&#45;&gt;y</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M236.15,-861.76C231.75,-856.43 227.63,-850.35 225,-844 221.19,-834.8 219.6,-824.06 219.07,-814.4\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"222.56,-814.02 218.87,-804.09 215.57,-814.16 222.56,-814.02\"/>\n", "<text text-anchor=\"middle\" x=\"246.5\" y=\"-836\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"246.5\" y=\"-825\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- get_data&#45;&gt;X -->\n", "<g id=\"edge28\" class=\"edge\">\n", "<title>get_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M266.32,-861.98C273.94,-847.94 284.39,-828.66 292.8,-813.17\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"296.03,-814.56 297.72,-804.1 289.88,-811.22 296.03,-814.56\"/>\n", "<text text-anchor=\"middle\" x=\"309.5\" y=\"-836\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"309.5\" y=\"-825\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7908cfc68e20>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = cf.expand_back(recursive=True); cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note the built-in `__make_list__` operation which got automatically\n", "added to the graph. This operation groups the items of the `models` list passed\n", "to `eval_ensemble` into a single object, which is what's actually passed to \n", "`eval_ensemble` in the expanded graph. `__make_list__` is implemented the same\n", "as any other operation, with (roughly) the following semantics:\n", "```python\n", "@op\n", "def __make_list__(*elts:Any) -> list:\n", "    return list(elts)\n", "```\n", "The \"trick\" is that all the variadic inputs `elts_0`, `elts_1`, ... are grouped\n", "under the `*elts`-labeled edge, and are found in the `model` variable. This is \n", "how CFs express the aggregation of multiple values into a single one.\n", "\n", "How does `.df()` behave in the presence of such aggregation? We can take a look by\n", "selecting enough columns to paint a complete picture of each branch of the\n", "overall computation:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:33:11.113455Z", "iopub.status.busy": "2024-07-11T14:33:11.113204Z", "iopub.status.idle": "2024-07-11T14:33:11.284275Z", "shell.execute_reply": "2024-07-11T14:33:11.283689Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    | n_estimators                 | kernel                                     |   accuracy |\n", "|---:|:-----------------------------|:-------------------------------------------|-----------:|\n", "|  1 |                              | rbf                                        |      0.915 |\n", "|  4 | 5                            |                                            |      0.915 |\n", "|  3 |                              | rbf                                        |      0.91  |\n", "|  0 | 20                           |                                            |      0.9   |\n", "|  2 | 10                           |                                            |      0.9   |\n", "|  6 | 20                           |                                            |      0.9   |\n", "|  8 | 10                           |                                            |      0.9   |\n", "|  7 | ValueCollection([20, 10, 5]) | ValueCollection(['linear', 'rbf', 'poly']) |      0.895 |\n", "|  9 | ValueCollection([20, 10, 5]) | ValueCollection(['linear', 'rbf', 'poly']) |      0.895 |\n", "| 12 | 5                            |                                            |      0.885 |\n", "| 10 |                              | poly                                       |      0.835 |\n", "|  5 |                              | linear                                     |      0.82  |\n", "| 11 |                              | linear                                     |      0.82  |\n", "| 13 |                              | poly                                       |      0.82  |\n"]}], "source": ["print(cf.df()[['n_estimators', 'kernel', 'accuracy', ]].sort_values('accuracy', ascending=False).to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Columns where `n_estimators` is not null correspond to the random forest runs,\n", "and similarly columns where `kernel` is not null correspond to the SVC runs.\n", "\n", "But now we also get some columns containing a `ValueCollection` object, which is\n", "the CF's way of distinguishing between depending on a single value of a variable\n", "versus depending on a collection of values. This makes sense: the\n", "`eval_ensemble` calls depend on multiple models, and in turn, on multiple values\n", "in the `n_estimators` and `kernel` variables."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion, limitations & outlook\n", "The CF visualizations are now getting out of hand, so it's a good time to wrap\n", "up! We've seen how `ComputationFrame`s make complex computations\n", "\"self-organizing\", and how they provide a simple grammar of operations to\n", "manipulate and analyze them. However, there is much other work in progress on\n", "natural CF methods that we haven't touched on:\n", "\n", "- ways to efficiently select and filter the data in the CF *before* converting\n", "it to a dataframe;\n", "- operations for fine-grained control over the expansion of the CF, such as ways\n", "to explicitly merge/split nodes in the result, or options to expand only up to a\n", "certain depth, or whether/when to group calls to the same operation in a single\n", "node.\n", "\n", "Another limitation is that, currently, `ComputationFrame`s are not optimized for\n", "large-scale computation graphs.\n", "\n", "There's also much more to `mandala` than what we've covered here. For example,\n", "the incremental computation features that `mandala` provides, which only\n", "recompute `@op`s when the inputs and/or relevant code changes. If you're\n", "interested in learning more, check out the [mandala\n", "documentation](https://amakelov.github.io/mandala/), and feel free to reach out\n", "on [Twitter](https://x.com/AMakelov)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Why \"tidy\"? And some other related work\n", "\n", "### Tidy data vs tidy computations\n", "In many ways, the ideas here are a re-imagining of <PERSON>'s\n", "[Tidy Data](https://www.jstatsoft.org/article/view/v059i10) in the context of\n", "computational data management. In particular, the focus is on computations built\n", "from repeated calls to the same set of functions composed in various ways, which\n", "is a common pattern in machine learning and scientific computing. In analogy\n", "with the tidy data philosophy, the goal of \"tidy computations\" is to eliminate\n", "the code and effort required to organize computations, so that only the code\n", "to compute and analyze the results remains.\n", "\n", "Despite the different setups &mdash; data cleaning versus computation tracking\n", "&mdash; there are many similarities between the two approaches. This is because\n", "in an abstract sense you can think of \"data\" as a kind of \"computation\" that\n", "nature has performed via some \"data-generating process\". The difference stems\n", "from this process typically being unknown, hidden or otherwise hard to model.\n", "Perhaps this is also why the tidy data paper spends some time talking about notions of functional \n", "dependencies and normal forms, which are also relevant to computations. In fact,\n", "tidy data is in <PERSON><PERSON>'s [third normal\n", "form](https://en.wikipedia.org/wiki/Third_normal_form), which is in turn a more\n", "relaxed version of the [<PERSON><PERSON>-<PERSON>dd normal\n", "form](https://en.wikipedia.org/wiki/Boyce%E2%80%93Codd_normal_form) (BCNF). The\n", "BCNF is automatically satisfied by operation nodes in a computation frame when\n", "viewed as relations.\n", "\n", "On the one hand, the explicit knowledge of the data generating process makes the\n", "job of computation tracking easier in an ontological sense. <PERSON><PERSON><PERSON> remarks\n", "that, while easy to disambiguate in concrete examples, the concepts of\n", "\"variable\" and \"observation\" are actually hard to define abstractly. Not so for\n", "computations: variables are inputs/outputs of functions, and observations are\n", "function calls. \n", "\n", "But on the other hand, this detailed knowledge also gives us more complex\n", "situations to handle, such as feedback loops, branching pipelines, and\n", "aggregation/decomposition to name a few. This means we need more expressive\n", "tools and grammars to handle these situations. Furthermore, even if functions\n", "impose a notion of variables and observations, this does not prevent one from\n", "designing function interfaces poorly, which can in turn lead to messy\n", "computations.\n", "\n", "### Graphs, databases, categories\n", "There's a rich history of work in relational databases and graph databases, and\n", "CFs share some similarities with both:\n", "\n", "- A CF can be seen as a relational database, with a table for each operation and\n", "each variable. The operation tables have columns labeled by the inputs/outputs\n", "of the operation, and the values in these columns are pointers to the\n", "corresponding input/output values, which are stored in the (single-column)\n", "variable tables.\n", "- The `.df()` method works by performing an outer join operation (in a specific\n", "order) on the tables containing the full computational history of all final\n", "values in the CF.\n", "- Similarly, a CF can be seen as a graph database, where the nodes are\n", "calls and values, and the variables and operations serve as \"node types\".\n", "- Correspondingly, some operations, such as expansion or finding the\n", "dependencies/dependents of some nodes/values can be seen as graph traversal\n", "operations.\n", "\n", "Finally, the CF data structure can be seen as a kind of \"functor\" from a finite\n", "category (roughly speaking the call graph) to the category of sets. Some\n", "consequences of this perspective &mdash; which combines graphs and databases\n", "&mdash; are presented e.g. in [this\n", "paper](https://compositionality-journal.org/papers/compositionality-4-5/)"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}