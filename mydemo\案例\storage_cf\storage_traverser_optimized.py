"""
Storage执行顺序遍历器优化版本
充分利用mandala1框架的内置功能，避免重复实现
"""

import os
import sys
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref
except ImportError:
    # 如果mandala1不可用，添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref


class StorageTraverserOptimized:
    """优化的存储执行顺序遍历器，充分利用mandala1内置功能"""
    
    def __init__(self):
        pass
        
    def traverse_execution_order(self, storage: Storage) -> List[Dict]:
        """
        遍历函数执行顺序，使用mandala1的内置方法
        
        参数:
            storage: Storage对象
            
        返回:
            包含调用信息的字典列表
        """
        # 1. 使用Storage的内置方法获取所有调用
        all_calls = self._get_calls_using_storage_methods(storage)
        
        if not all_calls:
            return []
        
        # 2. 创建一个包含所有调用的ComputationFrame来利用其分析功能
        cf = self._create_cf_from_calls(storage, all_calls)
        
        # 3. 使用ComputationFrame的方法分析执行顺序
        execution_info = self._analyze_execution_using_cf(storage, cf, all_calls)
            
        return execution_info
    
    def _get_calls_using_storage_methods(self, storage: Storage) -> List[Call]:
        """
        使用Storage的内置方法获取所有调用
        
        参数:
            storage: Storage对象
            
        返回:
            Call对象列表
        """
        all_calls = []
        
        try:
            # 方法1: 直接从storage的call_cache获取
            if hasattr(storage, 'call_cache'):
                cache = storage.call_cache
                if hasattr(cache, 'items'):
                    for call_hid, call in cache.items():
                        if isinstance(call, Call):
                            all_calls.append(call)
                elif hasattr(cache, '_cache') and hasattr(cache._cache, 'items'):
                    for call_hid, call in cache._cache.items():
                        if isinstance(call, Call):
                            all_calls.append(call)
            
            # 方法2: 如果缓存为空，从数据库查询
            if not all_calls and hasattr(storage, 'call_storage'):
                try:
                    df = storage.call_storage.execute_df("SELECT call_history_id FROM calls")
                    if not df.empty:
                        call_hids = df['call_history_id'].unique().tolist()
                        all_calls = storage.mget_call(hids=call_hids, in_memory=True)
                except Exception as e:
                    print(f"从数据库加载调用失败: {e}")
                    
        except Exception as e:
            print(f"获取调用列表失败: {e}")
            
        return all_calls
    
    def _create_cf_from_calls(self, storage: Storage, calls: List[Call]) -> Optional[ComputationFrame]:
        """
        从调用列表创建ComputationFrame以利用其分析功能
        
        参数:
            storage: Storage对象
            calls: Call对象列表
            
        返回:
            ComputationFrame对象或None
        """
        if not calls:
            return None
            
        try:
            # 获取所有输出引用
            all_refs = []
            for call in calls:
                all_refs.extend(call.outputs.values())
            
            if all_refs:
                # 使用第一个引用创建CF，然后扩展
                cf = storage.cf(all_refs[0])
                if len(all_refs) > 1:
                    # 创建包含所有引用的CF
                    cf = storage.cf(all_refs)
                return cf
                
        except Exception as e:
            print(f"创建ComputationFrame失败: {e}")
            
        return None
    
    def _analyze_execution_using_cf(self, storage: Storage, cf: Optional[ComputationFrame], calls: List[Call]) -> List[Dict]:
        """
        使用ComputationFrame的方法分析执行顺序
        
        参数:
            storage: Storage对象
            cf: ComputationFrame对象
            calls: Call对象列表
            
        返回:
            执行信息列表
        """
        execution_info = []
        
        # 按hid排序调用（简单的时间顺序近似）
        sorted_calls = sorted(calls, key=lambda call: call.hid)
        
        for i, call in enumerate(sorted_calls):
            call_info = self._extract_call_info_optimized(storage, call, i, cf)
            execution_info.append(call_info)
            
        return execution_info
    
    def _extract_call_info_optimized(self, storage: Storage, call: Call, 
                                   execution_index: int, cf: Optional[ComputationFrame]) -> Dict[str, Any]:
        """
        使用mandala1的方法提取调用信息
        
        参数:
            storage: Storage对象
            call: Call对象
            execution_index: 执行索引
            cf: ComputationFrame对象
            
        返回:
            调用信息字典
        """
        info = {
            'execution_index': execution_index + 1,
            'function_name': call.op.name,
            'depth': 0,
            'inputs': {},
            'outputs': {},
            'call_id': call.hid[:8] if call.hid else 'unknown',
            'semantic_version': call.semantic_version,
            'content_version': call.content_version
        }
        
        try:
            # 1. 使用storage.unwrap获取实际参数值
            info['inputs'] = self._extract_params_using_storage(storage, call.inputs)
            info['outputs'] = self._extract_params_using_storage(storage, call.outputs)
            
            # 2. 如果有ComputationFrame，使用其方法计算深度
            if cf and call.op.name in cf.fnames:
                try:
                    # 使用CF的上游分析计算深度
                    upstream_cf = cf.upstream(call.op.name)
                    info['depth'] = len(upstream_cf.fnames) - 1  # 简化的深度计算
                except Exception:
                    info['depth'] = 0
            
        except Exception as e:
            print(f"提取调用 {call.op.name} 信息时出错: {e}")
            
        return info
    
    def _extract_params_using_storage(self, storage: Storage, params: Dict[str, Ref]) -> Dict[str, Any]:
        """
        使用Storage的unwrap方法提取参数值
        
        参数:
            storage: Storage对象
            params: 参数字典
            
        返回:
            参数值字典
        """
        result = {}
        
        for param_name, ref in params.items():
            try:
                # 使用storage.unwrap获取实际值
                value = storage.unwrap(ref)
                result[param_name] = self._format_value_optimized(value)
            except Exception:
                # 如果解包失败，使用引用信息
                result[param_name] = f"Ref({ref.hid[:8] if hasattr(ref, 'hid') else 'unknown'})"
                
        return result
    
    def _format_value_optimized(self, value: Any) -> str:
        """
        优化的值格式化方法
        
        参数:
            value: 要格式化的值
            
        返回:
            格式化后的字符串
        """
        if value is None:
            return "None"
        elif isinstance(value, (int, float, bool)):
            return str(value)
        elif isinstance(value, str):
            return f"'{value}'" if len(value) <= 20 else f"'{value[:17]}...'"
        elif isinstance(value, (list, tuple)):
            if len(value) <= 3:
                return str(value)
            else:
                return f"[{len(value)} items]"
        elif isinstance(value, dict):
            if len(value) <= 2:
                return str(value)
            else:
                return f"{{{len(value)} items}}"
        else:
            return f"{type(value).__name__}(...)"
    
    def print_execution_order(self, storage: Storage, show_details: bool = True, 
                            max_calls: int = 20, show_versions: bool = False):
        """
        打印函数执行顺序
        
        参数:
            storage: Storage对象
            show_details: 是否显示详细信息
            max_calls: 最大显示调用数量
            show_versions: 是否显示版本信息
        """
        print("=" * 60)
        print("函数执行顺序遍历结果（优化版本）")
        print("=" * 60)
        
        # 使用优化的遍历方法
        execution_info = self.traverse_execution_order(storage)
        
        if not execution_info:
            print("没有找到函数调用记录")
            return
        
        # 显示Storage基本信息
        print(f"Storage概览:")
        print(f"  - 总调用数: {len(execution_info)}")
        unique_functions = set(info['function_name'] for info in execution_info)
        print(f"  - 唯一函数数: {len(unique_functions)}")
        print(f"  - 函数列表: {', '.join(sorted(unique_functions))}")
        
        # 限制显示数量
        display_calls = execution_info[:max_calls]
        
        print(f"\n执行顺序详情（显示前{len(display_calls)}个调用）:")
        
        for call_info in display_calls:
            depth_prefix = "  " * call_info['depth']
            print(f"{call_info['execution_index']:3d}. {depth_prefix}[深度{call_info['depth']}] {call_info['function_name']}")
            
            if show_details:
                # 显示输入参数
                if call_info['inputs']:
                    inputs_str = ", ".join([f"{k}={v}" for k, v in call_info['inputs'].items()])
                    print(f"     {depth_prefix}├─ 输入: {inputs_str}")
                
                # 显示输出参数
                if call_info['outputs']:
                    outputs_str = ", ".join([f"{k}={v}" for k, v in call_info['outputs'].items()])
                    print(f"     {depth_prefix}├─ 输出: {outputs_str}")
                
                # 显示版本信息
                if show_versions:
                    print(f"     {depth_prefix}├─ 调用ID: {call_info['call_id']}")
                    if call_info['semantic_version']:
                        print(f"     {depth_prefix}├─ 语义版本: {call_info['semantic_version'][:8]}...")
                    if call_info['content_version']:
                        print(f"     {depth_prefix}└─ 内容版本: {call_info['content_version'][:8]}...")
                    else:
                        print(f"     {depth_prefix}└─ 版本: 无")
                else:
                    print(f"     {depth_prefix}└─ 调用ID: {call_info['call_id']}")
        
        if len(execution_info) > max_calls:
            print(f"\n... 还有 {len(execution_info) - max_calls} 个调用未显示")
        
        print(f"\n总计: {len(execution_info)} 个函数调用")
        print("=" * 60)
    
    def get_execution_summary(self, storage: Storage) -> Dict[str, Any]:
        """
        获取执行摘要信息
        
        参数:
            storage: Storage对象
            
        返回:
            执行摘要
        """
        execution_info = self.traverse_execution_order(storage)
        
        summary = {
            'total_calls': len(execution_info),
            'unique_functions': len(set(info['function_name'] for info in execution_info)),
            'function_call_counts': defaultdict(int),
            'depth_distribution': defaultdict(int),
            'execution_timeline': []
        }
        
        for info in execution_info:
            summary['function_call_counts'][info['function_name']] += 1
            summary['depth_distribution'][info['depth']] += 1
            summary['execution_timeline'].append({
                'index': info['execution_index'],
                'function': info['function_name'],
                'depth': info['depth']
            })
        
        return summary


def demo_optimized_storage_traverser():
    """演示优化的Storage执行顺序遍历功能"""
    print("优化的Storage执行顺序遍历功能演示")
    print("=" * 50)
    
    # 创建一些示例函数
    @op
    def add(x, y):
        """加法函数"""
        return x + y
    
    @op
    def multiply(x, y):
        """乘法函数"""
        return x * y
    
    @op
    def power(x, n):
        """幂函数"""
        return x ** n
    
    # 创建存储并执行计算
    storage = Storage()
    
    with storage:
        # 执行一些计算
        a = add(1, 2)
        b = add(3, 4)
        c = multiply(a, b)
        d = power(c, 2)
        e = add(d, 10)
    
    # 在Storage上下文外进行遍历
    traverser = StorageTraverserOptimized()
    traverser.print_execution_order(storage, show_details=True, show_versions=True)
    
    # 显示摘要信息
    print("\n执行摘要:")
    summary = traverser.get_execution_summary(storage)
    for key, value in summary.items():
        if key != 'execution_timeline':
            print(f"  {key}: {dict(value) if isinstance(value, defaultdict) else value}")


if __name__ == "__main__":
    demo_optimized_storage_traverser()
