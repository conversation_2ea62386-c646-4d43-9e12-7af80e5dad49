# model.py 文档

## 文件内容与作用总体说明

`model.py` 文件是 mandala 框架的核心数据模型模块，定义了框架中的基本数据结构和接口。主要包括 `Ref`（引用）、`Call`（调用）、`Op`（操作）等核心类，以及支持各种数据类型的引用类（如 `ListRef`、`DictRef` 等）。该文件还提供了 `@op` 装饰器的实现，以及用于包装和处理不同数据类型的工具函数。这些类和函数构成了 mandala 记忆化和计算图功能的基础。

## 文件内的所有变量的作用与说明

### 全局变量
- `T`: TypeVar，泛型类型变量，用于类型注解
- `_KT`: TypeVar，字典键类型变量
- `_VT`: TypeVar，字典值类型变量

### 类常量
- `Ref` 类中的各种实例变量在初始化时设置
- `Op` 类中的操作配置参数
- `Call` 类中的调用元数据

## 文件内的所有函数的作用与说明

### Ref 类（引用基类）

#### __init__(self, cid: str, hid: str, in_memory: bool, obj: Optional[Any]) -> None

**作用**: 初始化引用对象，设置内容ID、历史ID、内存状态和对象

**输入参数**:
- `cid`: str，内容ID，基于对象内容的哈希
- `hid`: str，历史ID，基于计算历史的哈希
- `in_memory`: bool，对象是否在内存中
- `obj`: Optional[Any]，实际的对象值

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：Ref 是所有引用类型的基类，不应直接实例化

#### with_hid(self, hid: str) -> "Ref"

**作用**: 创建具有新历史ID的引用副本

**输入参数**:
- `hid`: str，新的历史ID

**内部调用的函数**：
- type(self): 获取当前类型并创建新实例

**输出参数**: Ref - 新的引用对象

#### __repr__(self) -> str

**作用**: 返回引用对象的字符串表示

**输入参数**: 无

**内部调用的函数**：
- repr: 获取对象的字符串表示

**输出参数**: str - 对象的字符串表示

#### __hash__(self) -> int

**作用**: 返回引用对象的哈希值，基于历史ID

**输入参数**: 无

**内部调用的函数**：
- hash: 计算字符串哈希

**输出参数**: int - 哈希值

#### detached(self) -> "Ref"

**作用**: 返回引用的分离副本，不包含内存中的对象

**输入参数**: 无

**内部调用的函数**：
- type(self): 获取当前类型并创建新实例

**输出参数**: Ref - 分离的引用副本

### AtomRef 类（原子引用）

#### __repr__(self) -> str

**作用**: 返回原子引用的字符串表示

**输入参数**: 无

**内部调用的函数**：
- repr: 获取对象的字符串表示

**输出参数**: str - 原子引用的字符串表示

### ValuePointer 类（值指针）

#### __init__(self, id: str, obj: Any) -> None

**作用**: 初始化值指针，用于标识不需要序列化的大型对象

**输入参数**:
- `id`: str，对象的人类可读标识符
- `obj`: Any，实际的对象

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：ValuePointer 用于处理大型、不可序列化或存储在其他地方的对象

#### __repr__(self) -> str

**作用**: 返回值指针的字符串表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 值指针的字符串表示

### Op 类（操作）

#### __init__(self, name: str, f: Callable, nout: Union[Literal["var", "auto"], int] = "auto", output_names: Optional[List[str]] = None, version: Optional[int] = 0, ignore_args: Optional[Tuple[str,...]] = None, __structural__: bool = False, __allow_side_effects__: bool = False) -> None

**作用**: 初始化操作对象，包装函数以支持记忆化

**输入参数**:
- `name`: str，操作名称
- `f`: Callable，要包装的函数
- `nout`: Union[Literal["var", "auto"], int]，输出数量配置
- `output_names`: Optional[List[str]]，输出名称列表
- `version`: Optional[int]，操作版本号
- `ignore_args`: Optional[Tuple[str,...]]，哈希时忽略的参数
- `__structural__`: bool，是否为结构化操作
- `__allow_side_effects__`: bool，是否允许副作用

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：Op 是 @op 装饰器创建的核心对象

#### get_call_history_id(self, inputs: Dict[str, Ref], semantic_version: Optional[str] = None) -> str

**作用**: 计算调用的历史ID，结合输入的历史ID、操作名称和语义版本

**输入参数**:
- `inputs`: Dict[str, Ref]，输入引用字典
- `semantic_version`: Optional[str]，语义版本

**内部调用的函数**：
- self._get_hashable_inputs: 获取可哈希的输入
- get_content_hash: 计算内容哈希

**输出参数**: str - 调用的历史ID

#### get_call_content_id(self, inputs: Dict[str, Ref], semantic_version: Optional[str] = None) -> str

**作用**: 计算调用的内容ID，结合输入的内容ID、操作名称和语义版本

**输入参数**:
- `inputs`: Dict[str, Ref]，输入引用字典
- `semantic_version`: Optional[str]，语义版本

**内部调用的函数**：
- self._get_hashable_inputs: 获取可哈希的输入
- get_content_hash: 计算内容哈希

**输出参数**: str - 调用的内容ID

#### get_pre_call_id(self, inputs: Dict[str, Ref]) -> str

**作用**: 计算预调用ID，用于搜索匹配的语义版本

**输入参数**:
- `inputs`: Dict[str, Ref]，输入引用字典

**内部调用的函数**：
- self._get_hashable_inputs: 获取可哈希的输入
- get_content_hash: 计算内容哈希

**输出参数**: str - 预调用ID

#### detached(self) -> "Op"

**作用**: 返回操作的分离副本，不包含函数对象

**输入参数**: 无

**内部调用的函数**：
- Op: 创建新的操作实例

**输出参数**: Op - 分离的操作副本

#### __call__(self, *args, **kwargs) -> Union[Tuple[Ref, ...], Ref]

**作用**: 执行操作调用，如果在存储上下文中则进行记忆化

**输入参数**:
- `args`: 位置参数
- `kwargs`: 关键字参数

**内部调用的函数**：
- Context.current_context: 获取当前上下文
- storage.call: 执行存储调用

**输出参数**: Union[Tuple[Ref, ...], Ref] - 调用结果

**其他说明**：如果不在存储上下文中，直接调用原函数

### Call 类（调用）

#### __init__(self, op: Op, cid: str, hid: str, inputs: Dict[str, Ref], outputs: Dict[str, Ref], semantic_version: Optional[str] = None, content_version: Optional[str] = None) -> None

**作用**: 初始化调用对象，记录操作执行的完整信息

**输入参数**:
- `op`: Op，执行的操作
- `cid`: str，调用的内容ID
- `hid`: str，调用的历史ID
- `inputs`: Dict[str, Ref]，输入引用字典
- `outputs`: Dict[str, Ref]，输出引用字典
- `semantic_version`: Optional[str]，语义版本
- `content_version`: Optional[str]，内容版本

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### __repr__(self) -> str

**作用**: 返回调用对象的字符串表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 调用的字符串表示

### 集合引用类

#### ListRef 类

#### __len__(self) -> int

**作用**: 返回列表的长度

**输入参数**: 无

**内部调用的函数**：
- len: 计算对象长度

**输出参数**: int - 列表长度

#### __getitem__(self, i: int) -> Ref

**作用**: 获取列表中指定索引的元素

**输入参数**:
- `i`: int，索引

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Ref - 指定索引的引用

#### shape(self) -> "ListRef"

**作用**: 返回列表的形状信息（分离的引用）

**输入参数**: 无

**内部调用的函数**：
- ListRef: 创建新的列表引用
- elt.detached: 获取元素的分离引用

**输出参数**: ListRef - 形状信息

### 工具函数

#### wrap_atom(obj: Any, history_id: Optional[str] = None) -> AtomRef

**作用**: 将对象包装为原子引用，这是所有包装操作的核心函数

**输入参数**:
- `obj`: Any，要包装的对象
- `history_id`: Optional[str]，可选的历史ID

**内部调用的函数**：
- get_content_hash: 计算内容哈希
- AtomRef: 创建原子引用

**输出参数**: AtomRef - 包装后的原子引用

**其他说明**：处理 ValuePointer 和普通对象的不同包装逻辑

#### __make_list__(**items: Any) -> ListRef

**作用**: 创建列表引用的结构化操作

**输入参数**:
- `items`: Any，列表元素，以 "elts_0", "elts_1" 等键传入

**内部调用的函数**：
- get_content_hash: 计算内容哈希
- ListRef: 创建列表引用

**输出参数**: ListRef - 创建的列表引用

#### __make_dict__(**kwargs: Any) -> DictRef

**作用**: 创建字典引用的结构化操作

**输入参数**:
- `kwargs`: Any，字典的键值对

**内部调用的函数**：
- get_content_hash: 计算内容哈希
- DictRef: 创建字典引用

**输出参数**: DictRef - 创建的字典引用

#### op(output_names: Union[Optional[List[str]], Callable] = None, nout: Union[Literal["var", "auto"], int] = "auto", ignore_args: Optional[Tuple[str,...]] = None, __structural__: bool = False, __allow_side_effects__: bool = False)

**作用**: @op 装饰器函数，将普通函数转换为记忆化操作

**输入参数**:
- `output_names`: Union[Optional[List[str]], Callable]，输出名称或函数
- `nout`: Union[Literal["var", "auto"], int]，输出数量配置
- `ignore_args`: Optional[Tuple[str,...]]，忽略的参数
- `__structural__`: bool，是否为结构化操作
- `__allow_side_effects__`: bool，是否允许副作用

**内部调用的函数**：
- Op: 创建操作对象
- functools.wraps: 保持函数元数据

**输出参数**: Callable - 装饰后的函数

**其他说明**：支持带参数和不带参数的装饰器使用方式

#### attached(self, obj: Any) -> "Ref"

**作用**: 返回附加了对象的引用副本

**输入参数**:
- `obj`: Any，要附加的对象

**内部调用的函数**：
- type(self): 获取当前类型并创建新实例

**输出参数**: Ref - 附加对象的引用

#### shallow_copy(self) -> "Ref"

**作用**: 创建引用的浅拷贝

**输入参数**: 无

**内部调用的函数**：
- type(self): 获取当前类型并创建新实例

**输出参数**: Ref - 浅拷贝的引用

#### get_output_history_ids(self, call_history_id: str, output_names: List[str]) -> List[str]

**作用**: 获取调用输出的历史ID列表

**输入参数**:
- `call_history_id`: str，调用历史ID
- `output_names`: List[str]，输出名称列表

**内部调用的函数**：
- get_content_hash: 计算内容哈希

**输出参数**: List[str] - 输出历史ID列表

#### get_ordered_outputs(self, output_dict: Dict[str, Any]) -> Tuple[Any, ...]

**作用**: 根据输出名称配置获取有序的输出元组

**输入参数**:
- `output_dict`: Dict[str, Any]，输出字典

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Tuple[Any, ...] - 有序的输出元组

### DictRef 类（字典引用）

#### items(self) -> Iterable[Tuple[str, Ref]]

**作用**: 返回字典的键值对迭代器

**输入参数**: 无

**内部调用的函数**：
- self.obj.items: 获取字典项

**输出参数**: Iterable[Tuple[str, Ref]] - 键值对迭代器

#### values(self) -> Iterable[Ref]

**作用**: 返回字典值的迭代器

**输入参数**: 无

**内部调用的函数**：
- self.obj.values: 获取字典值

**输出参数**: Iterable[Ref] - 值的迭代器

#### shape(self) -> "DictRef"

**作用**: 返回字典的形状信息（分离的引用）

**输入参数**: 无

**内部调用的函数**：
- DictRef: 创建新的字典引用
- item.detached: 获取项的分离引用

**输出参数**: DictRef - 形状信息

### SetRef 类（集合引用）

#### __iter__(self)

**作用**: 返回集合的迭代器

**输入参数**: 无

**内部调用的函数**：
- iter: 创建迭代器

**输出参数**: Iterator - 集合迭代器

#### __contains__(self, elt: Ref) -> bool

**作用**: 检查元素是否在集合中

**输入参数**:
- `elt`: Ref，要检查的元素

**内部调用的函数**：
- 无直接函数调用

**输出参数**: bool - 元素是否在集合中

### 特殊值处理函数

#### Ignore(value: T = None) -> T

**作用**: 创建忽略值标记，用于标记在哈希计算中要忽略的参数

**输入参数**:
- `value`: T，要忽略的值

**内部调用的函数**：
- _Ignore: 创建忽略对象

**输出参数**: T - 忽略值标记

#### NewArgDefault(value: T = None) -> T

**作用**: 创建新参数默认值标记

**输入参数**:
- `value`: T，默认值

**内部调用的函数**：
- _NewArgDefault: 创建新参数默认值对象

**输出参数**: T - 新参数默认值标记

#### unwrap_special_value(obj: ValuePointer | _Ignore | Any) -> Any

**作用**: 解包特殊值对象，获取实际值

**输入参数**:
- `obj`: ValuePointer | _Ignore | Any，特殊值对象

**内部调用的函数**：
- isinstance: 检查对象类型

**输出参数**: Any - 解包后的实际值

### 结构化操作函数

#### __list_getitem__(list: MList[Any], i: Any) -> Any

**作用**: 列表索引操作的结构化实现

**输入参数**:
- `list`: MList[Any]，列表引用
- `i`: Any，索引

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Any - 索引对应的元素

#### __dict_getitem__(dict: MDict[Any, Any], key: Any) -> Any

**作用**: 字典索引操作的结构化实现

**输入参数**:
- `dict`: MDict[Any, Any]，字典引用
- `key`: Any，键

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Any - 键对应的值

#### __make_tuple__(*elts: Any) -> tuple

**作用**: 创建元组的结构化操作

**输入参数**:
- `elts`: Any，元组元素

**内部调用的函数**：
- tuple: 创建元组

**输出参数**: tuple - 创建的元组

#### make_ref_set(resf: Iterable[Ref]) -> SetRef

**作用**: 从引用可迭代对象创建集合引用

**输入参数**:
- `resf`: Iterable[Ref]，引用可迭代对象

**内部调用的函数**：
- __make_set__.f: 调用集合创建函数

**输出参数**: SetRef - 创建的集合引用

#### recurse_on_ref_collections(f: Callable, obj: Any, **kwargs: Any) -> Any

**作用**: 在引用集合上递归应用函数

**输入参数**:
- `f`: Callable，要应用的函数
- `obj`: Any，目标对象
- `kwargs`: Any，额外参数

**内部调用的函数**：
- isinstance: 检查对象类型
- f: 递归调用函数

**输出参数**: Any - 应用函数后的结果

### 集合类

#### RefCollection 类

#### __init__(self, refs: Iterable[Ref])

**作用**: 初始化引用集合

**输入参数**:
- `refs`: Iterable[Ref]，引用可迭代对象

**内部调用的函数**：
- sorted: 排序引用

**输出参数**: 无返回值

#### CallCollection 类

#### __init__(self, calls: Iterable[Call])

**作用**: 初始化调用集合

**输入参数**:
- `calls`: Iterable[Call]，调用可迭代对象

**内部调用的函数**：
- sorted: 排序调用

**输出参数**: 无返回值
