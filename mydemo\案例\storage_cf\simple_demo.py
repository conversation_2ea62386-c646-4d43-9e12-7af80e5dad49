"""
栈重放简单演示程序
展示如何使用StackReplayAnalyzer分析函数执行历史
"""

import os
import sys
from pathlib import Path

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from mandala1.storage import Storage
from mandala1.model import op
from stack_replay import StackReplayAnalyzer


# 定义一些示例函数用于演示
@op
def load_data(filename: str) -> list:
    """加载数据"""
    print(f"📁 加载数据: {filename}")
    return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]


@op
def filter_data(data: list, threshold: int = 5) -> list:
    """过滤数据"""
    print(f"🔍 过滤数据，阈值: {threshold}")
    return [x for x in data if x > threshold]


@op
def transform_data(data: list, multiplier: int = 2) -> list:
    """转换数据"""
    print(f"🔄 转换数据，乘数: {multiplier}")
    return [x * multiplier for x in data]


@op
def aggregate_data(data: list) -> dict:
    """聚合数据"""
    print(f"📊 聚合数据")
    return {
        "sum": sum(data),
        "count": len(data),
        "avg": sum(data) / len(data) if data else 0,
        "max": max(data) if data else 0,
        "min": min(data) if data else 0
    }


@op
def generate_report(stats: dict, title: str = "数据报告") -> str:
    """生成报告"""
    print(f"📝 生成报告: {title}")
    report = f"""
{title}
{'='*len(title)}
总和: {stats['sum']}
数量: {stats['count']}
平均值: {stats['avg']:.2f}
最大值: {stats['max']}
最小值: {stats['min']}
"""
    return report.strip()


@op
def save_report(report: str, filename: str) -> bool:
    """保存报告"""
    print(f"💾 保存报告到: {filename}")
    # 模拟保存操作
    return True


def create_computation_pipeline():
    """创建一个完整的计算管道"""
    print("🚀 开始执行计算管道...")
    
    # 创建存储
    storage = Storage()
    
    with storage:
        # 执行计算管道
        print("\n📋 执行步骤:")
        
        # 步骤1: 加载数据
        raw_data = load_data("sample_data.csv")
        
        # 步骤2: 过滤数据 (执行多次，不同参数)
        filtered_data_1 = filter_data(raw_data, threshold=3)
        filtered_data_2 = filter_data(raw_data, threshold=7)
        
        # 步骤3: 转换数据
        transformed_data_1 = transform_data(filtered_data_1, multiplier=2)
        transformed_data_2 = transform_data(filtered_data_2, multiplier=3)
        
        # 步骤4: 聚合数据
        stats_1 = aggregate_data(transformed_data_1)
        stats_2 = aggregate_data(transformed_data_2)
        
        # 步骤5: 生成报告
        report_1 = generate_report(stats_1, "低阈值数据报告")
        report_2 = generate_report(stats_2, "高阈值数据报告")
        
        # 步骤6: 保存报告
        save_result_1 = save_report(report_1, "low_threshold_report.txt")
        save_result_2 = save_report(report_2, "high_threshold_report.txt")
        
        print("\n✅ 计算管道执行完成!")
        
        # 返回存储和最终结果
        return storage, [save_result_1, save_result_2]


def demonstrate_stack_replay():
    """演示栈重放功能"""
    print("🎯 栈重放（Stack Replay）演示")
    print("="*60)
    
    # 1. 执行计算管道
    storage, results = create_computation_pipeline()
    
    # 2. 创建栈重放分析器
    analyzer = StackReplayAnalyzer(storage)
    
    # 3. 获取完整的计算框架
    print("\n🔍 构建计算框架...")
    cf = storage.cf(save_report).expand_all()
    
    print(f"✅ 计算框架构建完成:")
    print(f"   • 函数节点: {len(cf.fnames)}")
    print(f"   • 变量节点: {len(cf.vnames)}")
    print(f"   • 边数: {len(cf.edges())}")
    
    # 4. 捕获执行信息
    execution_info = analyzer.capture_execution_info(cf)
    
    # 5. 打印ComputationFrame的层级遍历
    analyzer.print_computation_frame_hierarchy(cf, detailed=True)
    
    # 6. 打印Storage的执行顺序遍历
    analyzer.print_storage_execution_order(execution_info, detailed=True)
    
    # 7. 生成执行摘要
    summary = analyzer.generate_execution_summary(execution_info)
    print(f"\n{summary}")
    
    # 8. 尝试生成可视化
    try:
        svg_path = Path(__file__).parent / "computation_graph.svg"
        cf.draw(verbose=True, path=str(svg_path))
        print(f"\n🎨 计算图已保存到: {svg_path}")
    except Exception as e:
        print(f"\n❌ 无法生成可视化: {e}")
    
    return analyzer, execution_info


def demonstrate_simple_case():
    """演示简单情况"""
    print("\n" + "="*60)
    print("🔬 简单案例演示")
    print("="*60)
    
    storage = Storage()
    
    with storage:
        # 简单的线性计算
        data = load_data("simple.csv")
        filtered = filter_data(data, threshold=5)
        result = aggregate_data(filtered)
        
        print(f"\n📊 简单计算结果: {result}")
    
    # 分析简单案例
    analyzer = StackReplayAnalyzer(storage)
    cf = storage.cf(aggregate_data).expand_all()
    
    print(f"\n🔍 简单案例分析:")
    print(f"   • 函数节点: {len(cf.fnames)}")
    print(f"   • 变量节点: {len(cf.vnames)}")
    
    # 捕获和显示执行信息
    execution_info = analyzer.capture_execution_info(cf)
    
    # 简化显示
    analyzer.print_computation_frame_hierarchy(cf, detailed=False)
    analyzer.print_storage_execution_order(execution_info, detailed=False)
    
    return analyzer, execution_info


if __name__ == "__main__":
    print("🎬 栈重放演示程序启动")
    print("="*80)
    
    # 演示完整的计算管道
    analyzer1, info1 = demonstrate_stack_replay()
    
    # 演示简单案例
    analyzer2, info2 = demonstrate_simple_case()
    
    print("\n🎉 演示完成!")
    print("="*80)
    print("📝 总结:")
    print("   • 成功捕获了完整的函数执行信息")
    print("   • 实现了ComputationFrame的高可读性层级遍历")
    print("   • 实现了Storage的函数执行顺序遍历")
    print("   • 展示了函数间的依赖关系和数据流")
    print("   • 提供了详细的执行历史分析")
