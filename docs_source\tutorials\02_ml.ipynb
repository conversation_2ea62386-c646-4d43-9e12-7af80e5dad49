{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Random forest ML project\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/tutorials/02_ml.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "This tutorial will show you how `mandala` works in a small random forest\n", "ML project. You'll see how **queriable & composable** memoization is a simple way\n", "to achieve the main goals of scientific data management:\n", "\n", "- **iterate in the simplest way** by just dumping more\n", "logic/parameters/experiments on top of the code you already ran. Memoization\n", "automatically takes care of loading past results, skipping over past\n", "computations, and merging results across compatible versions of your code.\n", "\n", "- **explore the interdependencies of all saved results incrementally and\n", "declaratively** with *computation frames*, generalized dataframes that operate\n", "over memoized computation graphs. Expand the computational history of artifacts\n", "backward (to what produced them) and/or forward (to experiments that used them),\n", "perform high-level operations over slices of storage, and generate dataframes of\n", "results for further analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports & setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:34.172674Z", "iopub.status.busy": "2024-07-11T14:32:34.172414Z", "iopub.status.idle": "2024-07-11T14:32:34.184110Z", "shell.execute_reply": "2024-07-11T14:32:34.183663Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:34.186455Z", "iopub.status.busy": "2024-07-11T14:32:34.186218Z", "iopub.status.idle": "2024-07-11T14:32:36.319097Z", "shell.execute_reply": "2024-07-11T14:32:36.318460Z"}}, "outputs": [], "source": ["from typing import Tuple\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.datasets import load_digits\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "# recommended way to import mandala functionality\n", "from mandala.imports import *\n", "\n", "np.random.seed(0)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:36.322449Z", "iopub.status.busy": "2024-07-11T14:32:36.322157Z", "iopub.status.idle": "2024-07-11T14:32:36.350871Z", "shell.execute_reply": "2024-07-11T14:32:36.350243Z"}}, "outputs": [], "source": ["@op # memoizing decorator\n", "def generate_dataset(random_seed=42):\n", "    print(f\"Generating dataset...\")\n", "    X, y = load_digits(return_X_y=True)\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=random_seed)\n", "    return X_train, X_test, y_train, y_test\n", "\n", "@op\n", "def train_model(X_train, y_train):\n", "    print(f\"Training model...\")\n", "    model = RandomForestClassifier(n_estimators=1)\n", "    model.fit(X_train, y_train)\n", "    return model, round(model.score(X_train, y_train), 2)\n", "\n", "@op\n", "def eval_model(model, X_test, y_test):\n", "    print(f\"Evaluating model...\")\n", "    return round(model.score(X_test, y_test), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running and iterating on the pipeline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run the pipeline once with default settings"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:36.354347Z", "iopub.status.busy": "2024-07-11T14:32:36.354079Z", "iopub.status.idle": "2024-07-11T14:32:36.443064Z", "shell.execute_reply": "2024-07-11T14:32:36.442280Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating dataset...\n", "Training model...\n", "Evaluating model...\n", "Train accuracy: AtomRef(0.91, hid=8f5...),\n", "Test accuracy: AtomRef(0.76, hid=57f...)\n"]}], "source": ["# in-memory storage for all results in this notebook; use `db_path` to persist\n", "# to disk\n", "storage = Storage() \n", "\n", "with storage: # block to make all @ops called inside read/write to a given storage\n", "    X_train, X_test, y_train, y_test = generate_dataset()\n", "    model, train_acc = train_model(X_train, y_train)\n", "    test_acc = eval_model(model, X_test, y_test)\n", "    print(f\"Train accuracy: {train_acc},\\nTest accuracy: {test_acc}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now all three calls are saved to the storage. `@op`s return **value \n", "references**, which wrap a Python object with some storage metadata needed to\n", "make the memoization **compose**. To get the underlying object, call\n", "`storage.unwrap(ref)`.\n", "\n", "Thanks to that metadata, when we re-run memoized code, the storage recognizes\n", "step-by-step that all work has already been done, and only loads *references* to\n", "the results (not the Python objects themselves):"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:36.484163Z", "iopub.status.busy": "2024-07-11T14:32:36.483932Z", "iopub.status.idle": "2024-07-11T14:32:36.521972Z", "shell.execute_reply": "2024-07-11T14:32:36.520864Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train accuracy: AtomRef(hid=8f5..., in_memory=False),\n", "Test accuracy: AtomRef(hid=57f..., in_memory=False)\n"]}], "source": ["with storage: # same code, but now it only loads pointers to saved results\n", "    X_train, X_test, y_train, y_test = generate_dataset()\n", "    model, train_acc = train_model(X_train, y_train)\n", "    test_acc = eval_model(model, X_test, y_test)\n", "    print(f\"Train accuracy: {train_acc},\\nTest accuracy: {test_acc}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterate directly on top of memoized code and change memoized functions backward-compatibly\n", "This also makes it easy to iterate on a project by just adding stuff on top of\n", "already memoized code. For example, let's add a new parameter to `train_model`\n", "in a way compatible with our current results:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:36.526599Z", "iopub.status.busy": "2024-07-11T14:32:36.525746Z", "iopub.status.idle": "2024-07-11T14:32:37.143275Z", "shell.execute_reply": "2024-07-11T14:32:37.142181Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running with 1 trees...\n", "    Train accuracy=AtomRef(hid=8f5..., in_memory=False),\n", "    Test accuracy=AtomRef(hid=57f..., in_memory=False)\n", "Running with 10 trees...\n", "Training model...\n", "Evaluating model...\n", "    Train accuracy=AtomRef(1.0, hid=760...),\n", "    Test accuracy=AtomRef(0.94, hid=600...)\n", "Running with 100 trees...\n", "Training model...\n", "Evaluating model...\n", "    Train accuracy=AtomRef(1.0, hid=ab0...),\n", "    Test accuracy=AtomRef(0.98, hid=45f...)\n"]}], "source": ["@op\n", "def train_model(X_train, y_train, n_estimators=NewArgDefault(1)):\n", "    print(f\"Training model...\")\n", "    model = RandomForestClassifier(n_estimators=n_estimators)\n", "    model.fit(X_train, y_train)\n", "    return model, round(model.score(X_train, y_train), 2)\n", "\n", "with storage: \n", "    X_train, X_test, y_train, y_test = generate_dataset()\n", "    for n_estimators in [1, 10, 100]:\n", "        print(f\"Running with {n_estimators} trees...\")\n", "        model, train_acc = train_model(X_train, y_train, n_estimators=n_estimators)\n", "        test_acc = eval_model(model, X_test, y_test)\n", "        print(f\"    Train accuracy={train_acc},\\n    Test accuracy={test_acc}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When we add a new argument with a default value wrapped as `NewArgDefault(obj)`,\n", "this ensures **backward compatibility**. `mandala` will ignore this parameter\n", "when its value equals `obj`, and **fall back to memoized calls that don't\n", "provide this argument**. \n", "\n", "This is why `Training model...` got printed **only two times**, and why the\n", "results for `n_estimators=1` are not in memory (`in_memory=False`)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pros and cons of memoization\n", "**Composable memoization is a powerful imperative query interface**: *if* you\n", "have the memoized code in front of you, **the code becomes its own storage\n", "interface**. You can just retrace it and get references to any intermediate\n", "results you want to look at. This is very flexible, because you can add control\n", "flow logic to restrict the \"query\". Retracing is cheap, because large objects\n", "are not loaded from storage, letting you narrow down your \"query\" before\n", "high-bandwidth interaction with the backend.\n", "\n", "However, **the full memoized code may not always be in front of you**!\n", "Especially in larger projects, where it's easy to lose track of what has already\n", "been computed, there's a need for a complementary storage interface based on\n", "**declarative** principles. We discuss this next."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Querying the storage with computation frames \n", "In cases when memoization-based querying is not enough, `mandala` offers the\n", "`ComputationFrame` class. A computation frame is a generalization of the\n", "familiar `pandas` dataframe in two main ways:\n", "\n", "- the \"columns\" of a computation frame represent a **computational graph**, made\n", "of variables and operations on them.\n", "- the \"rows\" of a computation frame are **computations that (partially) follow\n", "the structure of the computational graph**.\n", "\n", "It's best to illustrate this with some examples:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:37.149934Z", "iopub.status.busy": "2024-07-11T14:32:37.149349Z", "iopub.status.idle": "2024-07-11T14:32:37.508439Z", "shell.execute_reply": "2024-07-11T14:32:37.507790Z"}}, "outputs": [{"data": {"text/plain": ["ComputationFrame with:\n", "    5 variable(s) (10 unique refs)\n", "    1 operation(s) (3 unique calls)\n", "Computational graph:\n", "    var_0@output_0, var_1@output_1 = train_model(X_train=X_train, n_estimators=n_estimators, y_train=y_train)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["cf = storage.cf(train_model); cf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We just got a computation frame (CF) corresponding to a very simple computation\n", "graph: it only has one operation, `train_model`, with its associated inputs and\n", "outputs. **Output variables are appended with the name of the function output\n", "they are connected to**, in order to remove ambiguity in cases when not all\n", "outputs of an operation are present in the computational graph.\n", "\n", "The printout also describes the overall number of `Ref`s and `Call`s represented\n", "by this CF. Much like in `pandas`, we can rename the \"columns\" of our\n", "computation frame for readability:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:37.512161Z", "iopub.status.busy": "2024-07-11T14:32:37.511604Z", "iopub.status.idle": "2024-07-11T14:32:37.544744Z", "shell.execute_reply": "2024-07-11T14:32:37.543754Z"}}, "outputs": [{"data": {"text/plain": ["ComputationFrame with:\n", "    5 variable(s) (10 unique refs)\n", "    1 operation(s) (3 unique calls)\n", "Computational graph:\n", "    train_acc@output_0, model@output_1 = train_model(X_train=X_train, n_estimators=n_estimators, y_train=y_train)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cf.rename(vars={'var_1': 'model', 'var_0': 'train_acc'}, inplace=True); cf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can directly extract a dataframe from the CF:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:37.548295Z", "iopub.status.busy": "2024-07-11T14:32:37.547907Z", "iopub.status.idle": "2024-07-11T14:32:37.651207Z", "shell.execute_reply": "2024-07-11T14:32:37.650162Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    | y_train           | X_train                         |   n_estimators | train_model                   |   model | train_acc                               |\n", "|---:|:------------------|:--------------------------------|---------------:|:------------------------------|--------:|:----------------------------------------|\n", "|  0 | [6 0 0 ... 2 7 1] | [[ 0.  0.  3. ... 13.  4.  0.]  |            nan | Call(train_model, hid=ac0...) |    0.91 | RandomForestClassifier(n_estimators=1)  |\n", "|    |                   |  [ 0.  0.  9. ...  3.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  0. ...  6.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  ...                            |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  9. ... 16.  2.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  1. ...  0.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  1. ...  1.  0.  0.]] |                |                               |         |                                         |\n", "|  1 | [6 0 0 ... 2 7 1] | [[ 0.  0.  3. ... 13.  4.  0.]  |            100 | Call(train_model, hid=255...) |    1    | RandomForestClassifier()                |\n", "|    |                   |  [ 0.  0.  9. ...  3.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  0. ...  6.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  ...                            |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  9. ... 16.  2.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  1. ...  0.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  1. ...  1.  0.  0.]] |                |                               |         |                                         |\n", "|  2 | [6 0 0 ... 2 7 1] | [[ 0.  0.  3. ... 13.  4.  0.]  |             10 | Call(train_model, hid=5f7...) |    1    | RandomForestClassifier(n_estimators=10) |\n", "|    |                   |  [ 0.  0.  9. ...  3.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  0. ...  6.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  ...                            |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  9. ... 16.  2.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  1. ...  0.  0.  0.]  |                |                               |         |                                         |\n", "|    |                   |  [ 0.  0.  1. ...  1.  0.  0.]] |                |                               |         |                                         |\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/workspace/current/conda_envs/mandala_3.10/lib/python3.10/site-packages/tabulate/__init__.py:107: FutureWarning: elementwise comparison failed; returning scalar instead, but in the future will perform elementwise comparison\n", "  (len(row) >= 1 and row[0] == SEPARATING_LINE)\n", "/home/<USER>/workspace/current/conda_envs/mandala_3.10/lib/python3.10/site-packages/tabulate/__init__.py:108: FutureWarning: elementwise comparison failed; returning scalar instead, but in the future will perform elementwise comparison\n", "  or (len(row) >= 2 and row[1] == SEPARATING_LINE)\n"]}], "source": ["print(cf.df().to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We get back a table, where each row corresponds to a call to `train_model`.\n", "Furthermore, the `Call` objects themselves (which contain metadata about the\n", "call) appear in the column dedicated to the single operation in the graph.\n", "\n", "We see that in the `n_estimators` column we have the values `[NaN, 10.0, 100.0]`\n", "(in some order), reflecting the fact that we made 1 call to `train_model` before\n", "introducing the `n_estimators` argument, and 2 afterwards."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exploring storage by expanding the CF\n", "This CF gets much more interesting and useful when we can look into where the\n", "inputs to `train_model` came from, and what the outputs were used for. We can\n", "add the history of particular variables by calling `expand_back` on the CF, and \n", "similarly `expand_forward` shows the operations that consume given variables: "]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:37.655090Z", "iopub.status.busy": "2024-07-11T14:32:37.654732Z", "iopub.status.idle": "2024-07-11T14:32:37.902968Z", "shell.execute_reply": "2024-07-11T14:32:37.902399Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Expanding back the inputs:\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"263pt\" height=\"428pt\"\n", " viewBox=\"0.00 0.00 263.00 428.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 424)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-424 259,-424 259,4 -4,4\"/>\n", "<!-- random_seed -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M106,-420C106,-420 25,-420 25,-420 19,-420 13,-414 13,-408 13,-408 13,-396 13,-396 13,-390 19,-384 25,-384 25,-384 106,-384 106,-384 112,-384 118,-390 118,-396 118,-396 118,-408 118,-408 118,-414 112,-420 106,-420\"/>\n", "<text text-anchor=\"start\" x=\"26\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"21\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M111.5,-326C111.5,-326 19.5,-326 19.5,-326 13.5,-326 7.5,-320 7.5,-314 7.5,-314 7.5,-298 7.5,-298 7.5,-292 13.5,-286 19.5,-286 19.5,-286 111.5,-286 111.5,-286 117.5,-286 123.5,-292 123.5,-298 123.5,-298 123.5,-314 123.5,-314 123.5,-320 117.5,-326 111.5,-326\"/>\n", "<text text-anchor=\"start\" x=\"15.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"15.5\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"51\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M65.5,-383.76C65.5,-370.5 65.5,-351.86 65.5,-336.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"69,-336.07 65.5,-326.07 62,-336.07 69,-336.07\"/>\n", "<text text-anchor=\"middle\" x=\"95\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"95\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M243,-228C243,-228 162,-228 162,-228 156,-228 150,-222 150,-216 150,-216 150,-204 150,-204 150,-198 156,-192 162,-192 162,-192 243,-192 243,-192 249,-192 255,-198 255,-204 255,-204 255,-216 255,-216 255,-222 249,-228 243,-228\"/>\n", "<text text-anchor=\"start\" x=\"165\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"158\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M137,-134C137,-134 70,-134 70,-134 64,-134 58,-128 58,-122 58,-122 58,-106 58,-106 58,-100 64,-94 70,-94 70,-94 137,-94 137,-94 143,-94 149,-100 149,-106 149,-106 149,-122 149,-122 149,-128 143,-134 137,-134\"/>\n", "<text text-anchor=\"start\" x=\"69.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"66\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"89\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">3 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M188.17,-191.86C178.09,-180.17 164.04,-164.59 150.5,-152 146.41,-148.2 141.97,-144.36 137.5,-140.67\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"139.48,-137.78 129.5,-134.23 135.09,-143.23 139.48,-137.78\"/>\n", "<text text-anchor=\"middle\" x=\"199\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"199\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M45,-228C45,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 45,-192 45,-192 51,-192 57,-198 57,-204 57,-204 57,-216 57,-216 57,-222 51,-228 45,-228\"/>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"10\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M34.56,-191.89C39.33,-179.92 46.79,-164 56.5,-152 59.6,-148.17 63.17,-144.48 66.93,-141.02\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"69.5,-143.43 74.81,-134.26 64.94,-138.11 69.5,-143.43\"/>\n", "<text text-anchor=\"middle\" x=\"78\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"78\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_acc -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>train_acc</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M82.5,-36C82.5,-36 12.5,-36 12.5,-36 6.5,-36 0.5,-30 0.5,-24 0.5,-24 0.5,-12 0.5,-12 0.5,-6 6.5,0 12.5,0 12.5,0 82.5,0 82.5,0 88.5,0 94.5,-6 94.5,-12 94.5,-12 94.5,-24 94.5,-24 94.5,-30 88.5,-36 82.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"21\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_acc</text>\n", "<text text-anchor=\"start\" x=\"8.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M194.5,-36C194.5,-36 124.5,-36 124.5,-36 118.5,-36 112.5,-30 112.5,-24 112.5,-24 112.5,-12 112.5,-12 112.5,-6 118.5,0 124.5,0 124.5,0 194.5,0 194.5,0 200.5,0 206.5,-6 206.5,-12 206.5,-12 206.5,-24 206.5,-24 206.5,-30 200.5,-36 194.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"141.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"120.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M119.5,-228C119.5,-228 87.5,-228 87.5,-228 81.5,-228 75.5,-222 75.5,-216 75.5,-216 75.5,-204 75.5,-204 75.5,-198 81.5,-192 87.5,-192 87.5,-192 119.5,-192 119.5,-192 125.5,-192 131.5,-198 131.5,-204 131.5,-204 131.5,-216 131.5,-216 131.5,-222 125.5,-228 119.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"83.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"85\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M103.5,-191.76C103.5,-178.5 103.5,-159.86 103.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"107,-144.07 103.5,-134.07 100,-144.07 107,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"125\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"125\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_train -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M46.75,-286C42.38,-280.57 38.22,-274.39 35.5,-268 31.55,-258.7 29.63,-247.83 28.75,-238.09\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"32.24,-237.87 28.16,-228.1 25.26,-238.28 32.24,-237.87\"/>\n", "<text text-anchor=\"middle\" x=\"57\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"57\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;y_train -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M73.19,-285.98C78.81,-272.07 86.51,-253.03 92.74,-237.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"96.09,-238.68 96.59,-228.1 89.6,-236.06 96.09,-238.68\"/>\n", "<text text-anchor=\"middle\" x=\"111\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_2</text>\n", "<text text-anchor=\"middle\" x=\"111\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;train_acc -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>train_model&#45;&gt;train_acc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M92.17,-93.98C83.73,-79.81 72.1,-60.3 62.83,-44.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"65.81,-42.9 57.69,-36.1 59.8,-46.48 65.81,-42.9\"/>\n", "<text text-anchor=\"middle\" x=\"103\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"103\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M117.09,-93.64C120.9,-88.02 124.96,-81.83 128.5,-76 134.53,-66.08 140.73,-54.91 145.98,-45.1\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"149.11,-46.68 150.7,-36.2 142.92,-43.4 149.11,-46.68\"/>\n", "<text text-anchor=\"middle\" x=\"162\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"162\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7578e6bb3040>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Expanding forward the outputs:\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"359pt\" height=\"428pt\"\n", " viewBox=\"0.00 0.00 359.00 428.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 424)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-424 355,-424 355,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M160.5,-36C160.5,-36 90.5,-36 90.5,-36 84.5,-36 78.5,-30 78.5,-24 78.5,-24 78.5,-12 78.5,-12 78.5,-6 84.5,0 90.5,0 90.5,0 160.5,0 160.5,0 166.5,0 172.5,-6 172.5,-12 172.5,-12 172.5,-24 172.5,-24 172.5,-30 166.5,-36 160.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"109.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"86.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420\"/>\n", "<text text-anchor=\"start\" x=\"15\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M209,-326C209,-326 142,-326 142,-326 136,-326 130,-320 130,-314 130,-314 130,-298 130,-298 130,-292 136,-286 142,-286 142,-286 209,-286 209,-286 215,-286 221,-292 221,-298 221,-298 221,-314 221,-314 221,-320 215,-326 209,-326\"/>\n", "<text text-anchor=\"start\" x=\"141.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"138\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"161\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">3 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M69.33,-383.81C81.37,-371.96 98.25,-356.21 114.5,-344 120.29,-339.65 126.61,-335.36 132.92,-331.33\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"134.83,-334.26 141.48,-326.01 131.14,-328.32 134.83,-334.26\"/>\n", "<text text-anchor=\"middle\" x=\"143\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"143\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M216,-420C216,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 216,-384 216,-384 222,-384 228,-390 228,-396 228,-396 228,-408 228,-408 228,-414 222,-420 216,-420\"/>\n", "<text text-anchor=\"start\" x=\"155\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-383.76C175.5,-370.5 175.5,-351.86 175.5,-336.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179,-336.07 175.5,-326.07 172,-336.07 179,-336.07\"/>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_acc -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>train_acc</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M148,-228C148,-228 103,-228 103,-228 97,-228 91,-222 91,-216 91,-216 91,-204 91,-204 91,-198 97,-192 103,-192 103,-192 148,-192 148,-192 154,-192 160,-198 160,-204 160,-204 160,-216 160,-216 160,-222 154,-228 148,-228\"/>\n", "<text text-anchor=\"start\" x=\"99\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_acc</text>\n", "<text text-anchor=\"start\" x=\"107\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M158.5,-134C158.5,-134 92.5,-134 92.5,-134 86.5,-134 80.5,-128 80.5,-122 80.5,-122 80.5,-106 80.5,-106 80.5,-100 86.5,-94 92.5,-94 92.5,-94 158.5,-94 158.5,-94 164.5,-94 170.5,-100 170.5,-106 170.5,-106 170.5,-122 170.5,-122 170.5,-128 164.5,-134 158.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"92.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"88.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"111\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">3 calls</text>\n", "</g>\n", "<!-- train_acc&#45;&gt;eval_model -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>train_acc&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M125.5,-191.76C125.5,-178.5 125.5,-159.86 125.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"129,-144.07 125.5,-134.07 122,-144.07 129,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M260.5,-228C260.5,-228 190.5,-228 190.5,-228 184.5,-228 178.5,-222 178.5,-216 178.5,-216 178.5,-204 178.5,-204 178.5,-198 184.5,-192 190.5,-192 190.5,-192 260.5,-192 260.5,-192 266.5,-192 272.5,-198 272.5,-204 272.5,-204 272.5,-216 272.5,-216 272.5,-222 266.5,-228 260.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"207.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"186.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M339,-420C339,-420 258,-420 258,-420 252,-420 246,-414 246,-408 246,-408 246,-396 246,-396 246,-390 252,-384 258,-384 258,-384 339,-384 339,-384 345,-384 351,-390 351,-396 351,-396 351,-408 351,-408 351,-414 345,-420 339,-420\"/>\n", "<text text-anchor=\"start\" x=\"278.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"254\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M276.23,-383.98C257.4,-369.59 230.16,-348.78 208.76,-332.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"210.64,-329.45 200.57,-326.16 206.39,-335.02 210.64,-329.45\"/>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;var_0 -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>eval_model&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M125.5,-93.98C125.5,-80.34 125.5,-61.75 125.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"129,-46.1 125.5,-36.1 122,-46.1 129,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;train_acc -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>train_model&#45;&gt;train_acc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M165.38,-285.98C157.91,-271.94 147.66,-252.66 139.42,-237.17\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"142.38,-235.28 134.59,-228.1 136.2,-238.57 142.38,-235.28\"/>\n", "<text text-anchor=\"middle\" x=\"176\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"176\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M190.07,-285.92C194.04,-280.31 198.15,-274.04 201.5,-268 206.79,-258.45 211.66,-247.49 215.61,-237.75\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"218.99,-238.74 219.38,-228.16 212.47,-236.19 218.99,-238.74\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7578e6bb2c80>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print('Expanding back the inputs:')\n", "cf.expand_back(varnames=[\"X_train\", \"y_train\"]).draw(verbose=True)\n", "print('\\nExpanding forward the outputs:')\n", "cf.expand_forward(varnames=['model', 'train_acc']).draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can perform a full expansion (until we can't go back or forward) with the\n", "`.expand()` method:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:37.905600Z", "iopub.status.busy": "2024-07-11T14:32:37.905333Z", "iopub.status.idle": "2024-07-11T14:32:38.004026Z", "shell.execute_reply": "2024-07-11T14:32:38.003168Z"}}, "outputs": [{"data": {"text/plain": ["ComputationFrame with:\n", "    7 variable(s) (14 unique refs)\n", "    3 operation(s) (7 unique calls)\n", "Computational graph:\n", "    X_train@output_0, y_train@output_2 = generate_dataset(random_seed=random_seed)\n", "    train_acc@output_0, model@output_1 = train_model(X_train=X_train, n_estimators=n_estimators, y_train=y_train)\n", "    var_0@output_0 = eval_model(model=train_acc)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["expanded_cf = cf.expand_all()\n", "expanded_cf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the extracted CF represents a \"partial\" computation graph: the\n", "variables for `X_test`and `y_test` were not reached during this traversal\n", "(though they can be added). Finally, we can (again) extract a dataframe from\n", "this CF:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:38.006887Z", "iopub.status.busy": "2024-07-11T14:32:38.006656Z", "iopub.status.idle": "2024-07-11T14:32:38.105977Z", "shell.execute_reply": "2024-07-11T14:32:38.105109Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    |   random_seed | generate_dataset                   |   n_estimators | train_model                   |   model | train_acc                               | eval_model                   |   var_0 |\n", "|---:|--------------:|:-----------------------------------|---------------:|:------------------------------|--------:|:----------------------------------------|:-----------------------------|--------:|\n", "|  0 |            42 | Call(generate_dataset, hid=c3f...) |            nan | Call(train_model, hid=ac0...) |    0.91 | RandomForestClassifier(n_estimators=1)  | Call(eval_model, hid=c5a...) |    0.76 |\n", "|  1 |            42 | Call(generate_dataset, hid=c3f...) |            100 | Call(train_model, hid=255...) |    1    | RandomForestClassifier()                | Call(eval_model, hid=07b...) |    0.98 |\n", "|  2 |            42 | Call(generate_dataset, hid=c3f...) |             10 | Call(train_model, hid=5f7...) |    1    | RandomForestClassifier(n_estimators=10) | Call(eval_model, hid=ed5...) |    0.94 |\n"]}], "source": ["df = expanded_cf.df().drop(columns=['X_train', 'y_train']) # drop to avoid a bit of clutter\n", "print(df.to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using computation frames for high-level operations\n", "Finally, we can illustrate the use of computation frames for easy declarative\n", "operations over the storage, even in the presence of highly heterogeneous\n", "experiments. To make this more interesting, let's train some more models:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:38.109055Z", "iopub.status.busy": "2024-07-11T14:32:38.108821Z", "iopub.status.idle": "2024-07-11T14:32:39.086492Z", "shell.execute_reply": "2024-07-11T14:32:39.085841Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training model...\n", "Training model...\n", "Training model...\n", "Training model...\n"]}], "source": ["@op\n", "def train_model(X_train, y_train,\n", "                n_estimators=NewArgDefault(1),\n", "                max_depth=NewArgDefault(None) # one more backward-compatible argument\n", "                ):\n", "    print(f\"Training model...\")\n", "    model = RandomForestClassifier(n_estimators=n_estimators, max_depth=max_depth)\n", "    model.fit(X_train, y_train)\n", "    return model, round(model.score(X_train, y_train), 2)\n", "\n", "with storage: \n", "    X_train, X_test, y_train, y_test = generate_dataset()\n", "    for n_estimators in [10, 100]:\n", "        for max_depth in [1, 2]:\n", "            model, train_acc = train_model(X_train, y_train, n_estimators=n_estimators, max_depth=max_depth)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We left out the call to `eval_model` on purpose to illustrate how CFs handle\n", "heterogeneous and partial computations. \n", "\n", "As before, we build a big CF by expanding backward and forward from\n", "`train_model`, and then extract a dataframe:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:39.089262Z", "iopub.status.busy": "2024-07-11T14:32:39.089052Z", "iopub.status.idle": "2024-07-11T14:32:39.386353Z", "shell.execute_reply": "2024-07-11T14:32:39.385631Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    |   max_depth |   n_estimators |   random_seed | generate_dataset                   | train_model                   |   train_acc | model                                                | eval_model                   |   eval_acc |\n", "|---:|------------:|---------------:|--------------:|:-----------------------------------|:------------------------------|------------:|:-----------------------------------------------------|:-----------------------------|-----------:|\n", "|  0 |         nan |            nan |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=ac0...) |        0.91 | RandomForestClassifier(n_estimators=1)               | Call(eval_model, hid=c5a...) |       0.76 |\n", "|  1 |         nan |            100 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=255...) |        1    | RandomForestClassifier()                             | Call(eval_model, hid=07b...) |       0.98 |\n", "|  2 |           2 |             10 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=e61...) |        0.71 | RandomForestClassifier(max_depth=2, n_estimators=10) |                              |     nan    |\n", "|  3 |           1 |             10 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=f56...) |        0.59 | RandomForestClassifier(max_depth=1, n_estimators=10) |                              |     nan    |\n", "|  4 |           2 |            100 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=fe1...) |        0.84 | RandomForestClassifier(max_depth=2)                  |                              |     nan    |\n", "|  5 |         nan |             10 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=5f7...) |        1    | RandomForestClassifier(n_estimators=10)              | Call(eval_model, hid=ed5...) |       0.94 |\n", "|  6 |           1 |            100 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=561...) |        0.7  | RandomForestClassifier(max_depth=1)                  |                              |     nan    |\n"]}], "source": ["cf = storage.cf(train_model).expand_all().rename(vars={'var_0': 'model', 'var_1': 'train_acc', 'var_2': 'eval_acc'})\n", "# use `lazy_vars` to avoid loading the large arrays which we don't need\n", "df = cf.df(lazy_vars=['X_train', 'y_train'], join_how='outer').drop(columns=['X_train', 'y_train'])\n", "print(df.to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that the various missing computational paths show up as `NaN`s in the \n", "table, under either variables (e.g. `max_depth`) or operations (e.g.\n", "`eval_model`). \n", "\n", "Suppose we want the models where train accuracy was above 0.8 and max depth was 2. \n", "We can do this with familiar dataframe operations:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:39.389384Z", "iopub.status.busy": "2024-07-11T14:32:39.389172Z", "iopub.status.idle": "2024-07-11T14:32:39.422889Z", "shell.execute_reply": "2024-07-11T14:32:39.422161Z"}}, "outputs": [{"data": {"text/plain": ["[RandomForestClassifier(max_depth=2)]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.query('train_acc > 0.8 and max_depth == 2').model.tolist()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Similarly, we can manipulate storage by e.g. deleting calls based on this\n", "dataframe. For example, we can delete all calls to `train_model` where \n", "`train_acc < 0.8`:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:39.425936Z", "iopub.status.busy": "2024-07-11T14:32:39.425724Z", "iopub.status.idle": "2024-07-11T14:32:39.847455Z", "shell.execute_reply": "2024-07-11T14:32:39.846646Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[17:32:39] </span><span style=\"color: #000080; text-decoration-color: #000080\">INFO    </span> Dropped <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> calls <span style=\"font-weight: bold\">(</span>and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> from cache<span style=\"font-weight: bold\">)</span>.                                              <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">storage.py:350</span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[17:32:39]\u001b[0m\u001b[2;36m \u001b[0m\u001b[34mINFO    \u001b[0m Dropped \u001b[1;36m3\u001b[0m calls \u001b[1m(\u001b[0mand \u001b[1;36m3\u001b[0m from cache\u001b[1m)\u001b[0m.                                              \u001b[2mstorage.py\u001b[0m\u001b[2m:\u001b[0m\u001b[2m350\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["|    |   max_depth |   n_estimators |   random_seed | generate_dataset                   | train_model                   |   model | train_acc                               | eval_model                   |   eval_acc |\n", "|---:|------------:|---------------:|--------------:|:-----------------------------------|:------------------------------|--------:|:----------------------------------------|:-----------------------------|-----------:|\n", "|  0 |         nan |            nan |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=ac0...) |    0.91 | RandomForestClassifier(n_estimators=1)  | Call(eval_model, hid=c5a...) |       0.76 |\n", "|  1 |         nan |            100 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=255...) |    1    | RandomForestClassifier()                | Call(eval_model, hid=07b...) |       0.98 |\n", "|  2 |           2 |            100 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=fe1...) |    0.84 | RandomForestClassifier(max_depth=2)     |                              |     nan    |\n", "|  3 |         nan |             10 |            42 | Call(generate_dataset, hid=c3f...) | Call(train_model, hid=5f7...) |    1    | RandomForestClassifier(n_estimators=10) | Call(eval_model, hid=ed5...) |       0.94 |\n"]}], "source": ["storage.drop_calls(df.query('train_acc < 0.8').train_model, delete_dependents=True)\n", "# now check that the dropped calls are gone\n", "cf = storage.cf(train_model).expand_all().rename(vars={'var_1': 'model', 'var_0': 'train_acc', 'var_2': 'eval_acc'})\n", "df = cf.df(lazy_vars=['X_train', 'y_train']).drop(columns=['X_train', 'y_train'])\n", "print(df.to_markdown())"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:39.851168Z", "iopub.status.busy": "2024-07-11T14:32:39.850609Z", "iopub.status.idle": "2024-07-11T14:32:39.975801Z", "shell.execute_reply": "2024-07-11T14:32:39.975119Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"386pt\" height=\"620pt\"\n", " viewBox=\"0.00 0.00 386.00 620.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 616)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-616 382,-616 382,4 -4,4\"/>\n", "<!-- random_seed -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M106,-612C106,-612 25,-612 25,-612 19,-612 13,-606 13,-600 13,-600 13,-588 13,-588 13,-582 19,-576 25,-576 25,-576 106,-576 106,-576 112,-576 118,-582 118,-588 118,-588 118,-600 118,-600 118,-606 112,-612 106,-612\"/>\n", "<text text-anchor=\"start\" x=\"26\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"21\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M111.5,-518C111.5,-518 19.5,-518 19.5,-518 13.5,-518 7.5,-512 7.5,-506 7.5,-506 7.5,-490 7.5,-490 7.5,-484 13.5,-478 19.5,-478 19.5,-478 111.5,-478 111.5,-478 117.5,-478 123.5,-484 123.5,-490 123.5,-490 123.5,-506 123.5,-506 123.5,-512 117.5,-518 111.5,-518\"/>\n", "<text text-anchor=\"start\" x=\"15.5\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"15.5\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"51\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M65.5,-575.76C65.5,-562.5 65.5,-543.86 65.5,-528.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"69,-528.07 65.5,-518.07 62,-528.07 69,-528.07\"/>\n", "<text text-anchor=\"middle\" x=\"95\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"95\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_acc -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>eval_acc</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M147.5,-36C147.5,-36 77.5,-36 77.5,-36 71.5,-36 65.5,-30 65.5,-24 65.5,-24 65.5,-12 65.5,-12 65.5,-6 71.5,0 77.5,0 77.5,0 147.5,0 147.5,0 153.5,0 159.5,-6 159.5,-12 159.5,-12 159.5,-24 159.5,-24 159.5,-30 153.5,-36 147.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"87\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_acc</text>\n", "<text text-anchor=\"start\" x=\"73.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M243,-420C243,-420 162,-420 162,-420 156,-420 150,-414 150,-408 150,-408 150,-396 150,-396 150,-390 156,-384 162,-384 162,-384 243,-384 243,-384 249,-384 255,-390 255,-396 255,-396 255,-408 255,-408 255,-414 249,-420 243,-420\"/>\n", "<text text-anchor=\"start\" x=\"165\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"158\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M202,-326C202,-326 135,-326 135,-326 129,-326 123,-320 123,-314 123,-314 123,-298 123,-298 123,-292 129,-286 135,-286 135,-286 202,-286 202,-286 208,-286 214,-292 214,-298 214,-298 214,-314 214,-314 214,-320 208,-326 202,-326\"/>\n", "<text text-anchor=\"start\" x=\"134.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"154\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M196.26,-383.76C191.42,-370.37 184.6,-351.5 178.93,-335.82\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"182.09,-334.29 175.4,-326.07 175.51,-336.67 182.09,-334.29\"/>\n", "<text text-anchor=\"middle\" x=\"218\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"218\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M45,-420C45,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 45,-384 45,-384 51,-384 57,-390 57,-396 57,-396 57,-408 57,-408 57,-414 51,-420 45,-420\"/>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"10\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M38.59,-383.75C46.66,-371.25 59.01,-354.72 73.5,-344 85.41,-335.19 99.68,-328.19 113.5,-322.75\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"114.81,-326 122.97,-319.23 112.37,-319.44 114.81,-326\"/>\n", "<text text-anchor=\"middle\" x=\"95\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"95\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- max_depth -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>max_depth</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M366,-420C366,-420 285,-420 285,-420 279,-420 273,-414 273,-408 273,-408 273,-396 273,-396 273,-390 279,-384 285,-384 285,-384 366,-384 366,-384 372,-384 378,-390 378,-396 378,-396 378,-408 378,-408 378,-414 372,-420 366,-420\"/>\n", "<text text-anchor=\"start\" x=\"293.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">max_depth</text>\n", "<text text-anchor=\"start\" x=\"281\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- max_depth&#45;&gt;train_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>max_depth&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M305.69,-383.97C291.06,-371.89 270.32,-355.79 250.5,-344 241.94,-338.91 232.56,-334.06 223.3,-329.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"224.66,-326.41 214.11,-325.38 221.71,-332.77 224.66,-326.41\"/>\n", "<text text-anchor=\"middle\" x=\"307\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">max_depth</text>\n", "<text text-anchor=\"middle\" x=\"307\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_acc -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>train_acc</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M147.5,-228C147.5,-228 77.5,-228 77.5,-228 71.5,-228 65.5,-222 65.5,-216 65.5,-216 65.5,-204 65.5,-204 65.5,-198 71.5,-192 77.5,-192 77.5,-192 147.5,-192 147.5,-192 153.5,-192 159.5,-198 159.5,-204 159.5,-204 159.5,-216 159.5,-216 159.5,-222 153.5,-228 147.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"86\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_acc</text>\n", "<text text-anchor=\"start\" x=\"73.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (1 sinks)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M145.5,-134C145.5,-134 79.5,-134 79.5,-134 73.5,-134 67.5,-128 67.5,-122 67.5,-122 67.5,-106 67.5,-106 67.5,-100 73.5,-94 79.5,-94 79.5,-94 145.5,-94 145.5,-94 151.5,-94 157.5,-100 157.5,-106 157.5,-106 157.5,-122 157.5,-122 157.5,-128 151.5,-134 145.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"79.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"75.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"98\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">3 calls</text>\n", "</g>\n", "<!-- train_acc&#45;&gt;eval_model -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>train_acc&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M112.5,-191.76C112.5,-178.5 112.5,-159.86 112.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"116,-144.07 112.5,-134.07 109,-144.07 116,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"134\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"134\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M259.5,-228C259.5,-228 189.5,-228 189.5,-228 183.5,-228 177.5,-222 177.5,-216 177.5,-216 177.5,-204 177.5,-204 177.5,-198 183.5,-192 189.5,-192 189.5,-192 259.5,-192 259.5,-192 265.5,-192 271.5,-198 271.5,-204 271.5,-204 271.5,-216 271.5,-216 271.5,-222 265.5,-228 259.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"206.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"185.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M119.5,-420C119.5,-420 87.5,-420 87.5,-420 81.5,-420 75.5,-414 75.5,-408 75.5,-408 75.5,-396 75.5,-396 75.5,-390 81.5,-384 87.5,-384 87.5,-384 119.5,-384 119.5,-384 125.5,-384 131.5,-390 131.5,-396 131.5,-396 131.5,-408 131.5,-408 131.5,-414 125.5,-420 119.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"83.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"85\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M111.42,-383.89C117.16,-372.22 125.46,-356.65 134.5,-344 136.95,-340.57 139.7,-337.11 142.53,-333.76\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"145.21,-336.02 149.21,-326.2 139.96,-331.38 145.21,-336.02\"/>\n", "<text text-anchor=\"middle\" x=\"156\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"156\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;eval_acc -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>eval_model&#45;&gt;eval_acc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M112.5,-93.98C112.5,-80.34 112.5,-61.75 112.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"116,-46.1 112.5,-36.1 109,-46.1 116,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"134\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"134\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_train -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M46.75,-478C42.38,-472.57 38.22,-466.39 35.5,-460 31.55,-450.7 29.63,-439.83 28.75,-430.09\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"32.24,-429.87 28.16,-420.1 25.26,-430.28 32.24,-429.87\"/>\n", "<text text-anchor=\"middle\" x=\"57\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"57\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;y_train -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M73.19,-477.98C78.81,-464.07 86.51,-445.03 92.74,-429.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"96.09,-430.68 96.59,-420.1 89.6,-428.06 96.09,-430.68\"/>\n", "<text text-anchor=\"middle\" x=\"111\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_2</text>\n", "<text text-anchor=\"middle\" x=\"111\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;train_acc -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>train_model&#45;&gt;train_acc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M157.17,-285.98C148.73,-271.81 137.1,-252.3 127.83,-236.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"130.81,-234.9 122.69,-228.1 124.8,-238.48 130.81,-234.9\"/>\n", "<text text-anchor=\"middle\" x=\"167\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"167\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M181.19,-285.94C184.91,-280.22 188.94,-273.9 192.5,-268 198.55,-257.99 204.91,-246.8 210.36,-237\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"213.49,-238.57 215.26,-228.12 207.36,-235.19 213.49,-238.57\"/>\n", "<text text-anchor=\"middle\" x=\"226\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"226\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x7578e6b7ef50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf.draw(verbose=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.8", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}, "vscode": {"interpreter": {"hash": "30c0510467e0bc33a523a84a8acb20ce0730b8eb0ee254a4b0039140f094f217"}}}, "nbformat": 4, "nbformat_minor": 2}