"""
栈重放（Stack Replay）- 时间旅行器
使用mandala1实现程序运行后的完整函数执行信息捕获和遍历打印
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple, Optional
from datetime import datetime
import pandas as pd

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from mandala1.storage import Storage
from mandala1.model import op
from mandala1.cf import ComputationFrame


class StackReplayAnalyzer:
    """栈重放分析器 - 捕获和分析函数执行历史"""
    
    def __init__(self, storage: Storage):
        self.storage = storage
        self.execution_history = []
        self.function_call_tree = {}
        
    def capture_execution_info(self, cf: ComputationFrame) -> Dict[str, Any]:
        """捕获完整的函数执行信息"""
        print("\n🎯 === 捕获函数执行信息 ===")
        
        execution_info = {
            "timestamp": datetime.now().isoformat(),
            "functions": {},
            "variables": {},
            "call_hierarchy": {},
            "execution_order": [],
            "data_flow": {}
        }
        
        # 1. 捕获函数信息
        print(f"📋 发现 {len(cf.fnames)} 个函数:")
        for fname in cf.fnames:
            func_info = self._capture_function_info(cf, fname)
            execution_info["functions"][fname] = func_info
            print(f"  ✅ {fname}: {func_info['call_count']} 次调用")
        
        # 2. 捕获变量信息
        print(f"\n📊 发现 {len(cf.vnames)} 个变量:")
        for vname in cf.vnames:
            var_info = self._capture_variable_info(cf, vname)
            execution_info["variables"][vname] = var_info
            print(f"  ✅ {vname}: {var_info['ref_count']} 个引用")
        
        # 3. 分析调用层级
        hierarchy = self._analyze_call_hierarchy(cf)
        execution_info["call_hierarchy"] = hierarchy
        
        # 4. 分析执行顺序
        exec_order = self._analyze_execution_order(cf)
        execution_info["execution_order"] = exec_order
        
        # 5. 分析数据流
        data_flow = self._analyze_data_flow(cf)
        execution_info["data_flow"] = data_flow
        
        return execution_info
    
    def _capture_function_info(self, cf: ComputationFrame, fname: str) -> Dict[str, Any]:
        """捕获单个函数的详细信息"""
        func_info = {
            "name": fname,
            "call_count": 0,
            "calls": [],
            "inputs": set(),
            "outputs": set()
        }
        
        # 获取函数调用表
        func_table = cf.get_func_table(fname)
        func_info["call_count"] = len(func_table)
        
        # 分析每次调用
        for _, call_row in func_table.iterrows():
            call_data = {
                "call_id": call_row.get("hid", "unknown"),
                "inputs": {},
                "outputs": {}
            }
            
            # 提取输入参数
            for col in func_table.columns:
                if col.startswith("inputs."):
                    param_name = col.replace("inputs.", "")
                    param_value = call_row[col]
                    call_data["inputs"][param_name] = str(param_value)
                    func_info["inputs"].add(param_name)
                elif col.startswith("outputs."):
                    output_name = col.replace("outputs.", "")
                    output_value = call_row[col]
                    call_data["outputs"][output_name] = str(output_value)
                    func_info["outputs"].add(output_name)
            
            func_info["calls"].append(call_data)
        
        # 转换set为list以便序列化
        func_info["inputs"] = list(func_info["inputs"])
        func_info["outputs"] = list(func_info["outputs"])
        
        return func_info
    
    def _capture_variable_info(self, cf: ComputationFrame, vname: str) -> Dict[str, Any]:
        """捕获单个变量的详细信息"""
        var_info = {
            "name": vname,
            "ref_count": 0,
            "refs": [],
            "creators": set(),
            "consumers": set()
        }
        
        # 获取变量的所有引用
        if vname in cf.vs:
            var_refs = cf.vs[vname]
            var_info["ref_count"] = len(var_refs)
            
            for ref_hid in var_refs:
                ref_data = {
                    "ref_id": ref_hid,
                    "creator": None,
                    "consumers": []
                }
                
                # 查找创建者
                if ref_hid in cf.creator:
                    creator_call_hid = cf.creator[ref_hid]
                    if creator_call_hid in cf.callinv:
                        creator_funcs = cf.callinv[creator_call_hid]
                        ref_data["creator"] = list(creator_funcs)[0] if creator_funcs else None
                        if ref_data["creator"]:
                            var_info["creators"].add(ref_data["creator"])
                
                # 查找消费者
                if ref_hid in cf.consumers:
                    consumer_call_hids = cf.consumers[ref_hid]
                    for consumer_call_hid in consumer_call_hids:
                        if consumer_call_hid in cf.callinv:
                            consumer_funcs = cf.callinv[consumer_call_hid]
                            for consumer_func in consumer_funcs:
                                ref_data["consumers"].append(consumer_func)
                                var_info["consumers"].add(consumer_func)
                
                var_info["refs"].append(ref_data)
        
        # 转换set为list
        var_info["creators"] = list(var_info["creators"])
        var_info["consumers"] = list(var_info["consumers"])
        
        return var_info
    
    def _analyze_call_hierarchy(self, cf: ComputationFrame) -> Dict[str, Any]:
        """分析函数调用层级关系"""
        hierarchy = {
            "levels": [],
            "dependencies": {},
            "max_depth": 0
        }
        
        # 构建依赖图
        func_deps = {}
        for fname in cf.fnames:
            deps = set()
            # 查找函数的输入依赖
            if fname in cf.inp:
                for var_name in cf.inp[fname]:
                    # 查找创建这个变量的函数
                    if var_name in cf.vs:
                        for ref_hid in cf.vs[var_name]:
                            if ref_hid in cf.creator:
                                creator_call_hid = cf.creator[ref_hid]
                                if creator_call_hid in cf.callinv:
                                    creator_funcs = cf.callinv[creator_call_hid]
                                    deps.update(creator_funcs)
            func_deps[fname] = deps
        
        # 计算层级
        assigned_levels = {}
        current_level = 0
        
        while len(assigned_levels) < len(cf.fnames):
            level_funcs = []
            for fname in cf.fnames:
                if fname not in assigned_levels:
                    # 检查所有依赖是否已分配层级
                    deps_satisfied = all(
                        dep in assigned_levels for dep in func_deps[fname]
                    )
                    if deps_satisfied:
                        level_funcs.append(fname)
                        assigned_levels[fname] = current_level
            
            if level_funcs:
                hierarchy["levels"].append({
                    "level": current_level,
                    "functions": level_funcs
                })
                current_level += 1
            else:
                # 防止无限循环
                break
        
        hierarchy["max_depth"] = current_level
        hierarchy["dependencies"] = func_deps
        
        return hierarchy
    
    def _analyze_execution_order(self, cf: ComputationFrame) -> List[Dict[str, Any]]:
        """分析函数执行顺序"""
        execution_order = []
        
        # 收集所有调用信息
        all_calls = []
        for fname in cf.fnames:
            func_table = cf.get_func_table(fname)
            for _, call_row in func_table.iterrows():
                call_info = {
                    "function": fname,
                    "call_id": call_row.get("hid", "unknown"),
                    "timestamp": call_row.get("timestamp", None)
                }
                all_calls.append(call_info)
        
        # 按时间戳排序（如果有的话）
        if all_calls and all_calls[0]["timestamp"] is not None:
            all_calls.sort(key=lambda x: x["timestamp"])
        
        return all_calls
    
    def _analyze_data_flow(self, cf: ComputationFrame) -> Dict[str, Any]:
        """分析数据流向"""
        data_flow = {
            "edges": [],
            "flow_paths": {},
            "data_dependencies": {}
        }
        
        # 分析所有边
        for src, dst, label in cf.edges():
            edge_info = {
                "source": src,
                "target": dst,
                "label": label,
                "type": "var_to_func" if src in cf.vnames else "func_to_var"
            }
            data_flow["edges"].append(edge_info)
        
        return data_flow

    def print_computation_frame_hierarchy(self, cf: ComputationFrame, detailed: bool = True):
        """高可读性打印ComputationFrame的计算图遍历，显示函数层级关系"""
        print("\n" + "="*80)
        print("🌳 ComputationFrame 计算图层级遍历")
        print("="*80)

        # 基本统计信息
        print(f"📊 图统计信息:")
        print(f"   • 函数节点: {len(cf.fnames)} 个")
        print(f"   • 变量节点: {len(cf.vnames)} 个")
        print(f"   • 总边数: {len(cf.edges())} 条")
        print(f"   • 源节点: {len(cf.sources)} 个")
        print(f"   • 汇节点: {len(cf.sinks)} 个")

        # 分析层级结构
        hierarchy = self._analyze_call_hierarchy(cf)

        print(f"\n🏗️ 函数调用层级结构 (共 {hierarchy['max_depth']} 层):")

        for level_info in hierarchy["levels"]:
            level = level_info["level"]
            functions = level_info["functions"]

            print(f"\n📍 层级 {level} ({len(functions)} 个函数):")

            for i, fname in enumerate(functions, 1):
                # 获取函数调用统计
                func_table = cf.get_func_table(fname)
                call_count = len(func_table)

                print(f"   {i}. 🔧 {fname}")
                print(f"      📞 调用次数: {call_count}")

                if detailed and call_count > 0:
                    # 显示依赖关系
                    deps = hierarchy["dependencies"].get(fname, set())
                    if deps:
                        print(f"      ⬇️  依赖函数: {', '.join(sorted(deps))}")

                    # 显示输入输出变量
                    inputs = cf.inp.get(fname, {})
                    outputs = cf.out.get(fname, {})

                    if inputs:
                        input_vars = list(inputs.keys())[:5]  # 限制显示数量
                        print(f"      📥 输入变量: {', '.join(input_vars)}")
                        if len(inputs) > 5:
                            print(f"                  ... 还有 {len(inputs) - 5} 个")

                    if outputs:
                        output_vars = list(outputs.keys())[:5]
                        print(f"      📤 输出变量: {', '.join(output_vars)}")
                        if len(outputs) > 5:
                            print(f"                  ... 还有 {len(outputs) - 5} 个")

                    # 显示前几次调用的详细信息
                    if detailed and call_count > 0:
                        print(f"      📋 调用详情:")
                        for idx, (_, call_row) in enumerate(func_table.head(3).iterrows()):
                            print(f"         调用 {idx + 1}:")

                            # 显示输入参数
                            input_params = {}
                            output_params = {}
                            for col in func_table.columns:
                                if col.startswith("inputs."):
                                    param_name = col.replace("inputs.", "")
                                    param_value = call_row[col]
                                    input_params[param_name] = str(param_value)[:50]  # 限制长度
                                elif col.startswith("outputs."):
                                    output_name = col.replace("outputs.", "")
                                    output_value = call_row[col]
                                    output_params[output_name] = str(output_value)[:50]

                            if input_params:
                                print(f"           📥 输入: {input_params}")
                            if output_params:
                                print(f"           📤 输出: {output_params}")

                        if call_count > 3:
                            print(f"         ... 还有 {call_count - 3} 次调用")

        # 显示数据流分析
        if detailed:
            self._print_data_flow_analysis(cf)

    def _print_data_flow_analysis(self, cf: ComputationFrame):
        """打印数据流分析"""
        print(f"\n🌊 数据流分析:")

        # 分析关键路径
        sources = list(cf.sources)[:5]  # 限制显示数量
        sinks = list(cf.sinks)[:5]

        if sources:
            print(f"   🎯 源节点 (数据起点): {', '.join(sources)}")
        if sinks:
            print(f"   🏁 汇节点 (数据终点): {', '.join(sinks)}")

        # 显示边的统计
        edges = cf.edges()
        var_to_func_edges = [e for e in edges if e[0] in cf.vnames and e[1] in cf.fnames]
        func_to_var_edges = [e for e in edges if e[0] in cf.fnames and e[1] in cf.vnames]

        print(f"   📊 边统计:")
        print(f"      变量→函数: {len(var_to_func_edges)} 条")
        print(f"      函数→变量: {len(func_to_var_edges)} 条")

        # 显示一些关键的数据流路径
        print(f"   🔗 关键数据流 (前5条):")
        for i, (src, dst, label) in enumerate(edges[:5], 1):
            src_type = "变量" if src in cf.vnames else "函数"
            dst_type = "变量" if dst in cf.vnames else "函数"
            print(f"      {i}. {src} ({src_type}) --[{label}]--> {dst} ({dst_type})")

    def print_storage_execution_order(self, execution_info: Dict[str, Any], detailed: bool = True):
        """高可读性打印Storage的函数执行顺序遍历，显示函数层级关系"""
        print("\n" + "="*80)
        print("⏰ Storage 函数执行顺序遍历")
        print("="*80)

        print(f"📅 执行时间: {execution_info['timestamp']}")
        print(f"📊 执行统计:")
        print(f"   • 函数总数: {len(execution_info['functions'])}")
        print(f"   • 变量总数: {len(execution_info['variables'])}")
        print(f"   • 调用总数: {sum(f['call_count'] for f in execution_info['functions'].values())}")

        # 按层级显示函数执行
        hierarchy = execution_info["call_hierarchy"]
        print(f"\n🏗️ 按层级显示函数执行 (共 {hierarchy['max_depth']} 层):")

        for level_info in hierarchy["levels"]:
            level = level_info["level"]
            functions = level_info["functions"]

            print(f"\n📍 执行层级 {level}:")
            print(f"   🔧 本层级函数: {', '.join(functions)}")

            for fname in functions:
                func_info = execution_info["functions"][fname]
                print(f"\n   📋 {fname} 详细执行信息:")
                print(f"      📞 总调用次数: {func_info['call_count']}")

                if func_info["inputs"]:
                    print(f"      📥 输入参数: {', '.join(func_info['inputs'])}")
                if func_info["outputs"]:
                    print(f"      📤 输出结果: {', '.join(func_info['outputs'])}")

                # 显示依赖关系
                deps = hierarchy["dependencies"].get(fname, set())
                if deps:
                    print(f"      ⬇️  依赖函数: {', '.join(sorted(deps))}")

                if detailed and func_info["calls"]:
                    print(f"      📝 执行历史 (前3次):")
                    for i, call in enumerate(func_info["calls"][:3], 1):
                        print(f"         执行 {i} (ID: {call['call_id']}):")
                        if call["inputs"]:
                            formatted_inputs = {k: v[:30] + "..." if len(v) > 30 else v
                                              for k, v in call["inputs"].items()}
                            print(f"           📥 输入: {formatted_inputs}")
                        if call["outputs"]:
                            formatted_outputs = {k: v[:30] + "..." if len(v) > 30 else v
                                               for k, v in call["outputs"].items()}
                            print(f"           📤 输出: {formatted_outputs}")

                    if len(func_info["calls"]) > 3:
                        remaining_calls = len(func_info["calls"]) - 3
                        print(f"         ... 还有 {remaining_calls} 次执行")

        # 显示时间顺序执行
        if detailed and execution_info["execution_order"]:
            self._print_chronological_execution(execution_info["execution_order"])

    def _print_chronological_execution(self, execution_order: List[Dict[str, Any]]):
        """打印按时间顺序的执行历史"""
        print(f"\n⏱️ 按时间顺序的执行历史 (前10次):")

        for i, call_info in enumerate(execution_order[:10], 1):
            timestamp = call_info.get("timestamp", "未知时间")
            print(f"   {i:2d}. {call_info['function']} (ID: {call_info['call_id']}) - {timestamp}")

        if len(execution_order) > 10:
            print(f"       ... 还有 {len(execution_order) - 10} 次调用")

    def generate_execution_summary(self, execution_info: Dict[str, Any]) -> str:
        """生成执行摘要报告"""
        summary = []
        summary.append("📊 栈重放执行摘要报告")
        summary.append("=" * 50)

        # 基本统计
        total_functions = len(execution_info['functions'])
        total_calls = sum(f['call_count'] for f in execution_info['functions'].values())
        total_variables = len(execution_info['variables'])

        summary.append(f"执行时间: {execution_info['timestamp']}")
        summary.append(f"函数总数: {total_functions}")
        summary.append(f"调用总数: {total_calls}")
        summary.append(f"变量总数: {total_variables}")

        # 层级信息
        hierarchy = execution_info["call_hierarchy"]
        summary.append(f"调用层级: {hierarchy['max_depth']} 层")

        # 最活跃的函数
        most_active = max(execution_info['functions'].items(),
                         key=lambda x: x[1]['call_count'])
        summary.append(f"最活跃函数: {most_active[0]} ({most_active[1]['call_count']} 次调用)")

        return "\n".join(summary)
