# deps/model.py 文档

## 文件内容与作用总体说明

`deps/model.py` 文件是 mandala 框架依赖跟踪模块的核心数据模型，定义了依赖图中的各种节点类型和依赖图本身。该文件提供了表示可调用对象、全局变量、终端节点等不同类型依赖的抽象类和具体实现，以及用于构建和操作依赖图的 DependencyGraph 类。这些模型支持代码版本控制、依赖分析和可视化功能。

## 文件内的所有变量的作用与说明

### 导入的模块和类型
- `textwrap`: 文本包装模块，用于格式化输出
- `ABC`, `abstractmethod`: 抽象基类相关
- `types`: Python 类型模块
- `get_content_hash`: 内容哈希计算函数
- `write_output`: 输出写入函数
- `Ref`: 引用类型
- `DepKey`: 依赖键类型
- `load_obj`: 对象加载函数
- `get_runtime_description`: 运行时描述获取函数
- `extract_code`: 代码提取函数
- `unknown_function`: 未知函数占位符
- `UNKNOWN_GLOBAL_VAR`: 未知全局变量占位符

### 类实例变量
各个类的实例变量在相应的类方法中详细说明。

## 文件内的所有函数的作用与说明

### Node 类（抽象基类）

#### __init__(self, module_name: str, obj_name: str, representation: Any)

**作用**: 初始化节点对象，设置模块名、对象名和表示

**输入参数**:
- `module_name`: str，模块名称
- `obj_name`: str，对象名称
- `representation`: Any，对象的表示形式

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### key(self) -> DepKey

**作用**: 获取节点的依赖键，由模块名和对象名组成

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: DepKey - 依赖键元组

#### present_key(self) -> str

**作用**: 抽象方法，返回节点键的可读表示

**输入参数**: 无

**内部调用的函数**：
- 子类实现

**输出参数**: str - 可读的键表示

#### represent(obj: Any) -> Any

**作用**: 静态抽象方法，将对象转换为表示形式

**输入参数**:
- `obj`: Any，要表示的对象

**内部调用的函数**：
- 子类实现

**输出参数**: Any - 对象的表示形式

#### content(self) -> Any

**作用**: 抽象方法，返回节点的内容

**输入参数**: 无

**内部调用的函数**：
- 子类实现

**输出参数**: Any - 节点内容

#### readable_content(self) -> str

**作用**: 抽象方法，返回节点的可读内容

**输入参数**: 无

**内部调用的函数**：
- 子类实现

**输出参数**: str - 可读内容

#### content_hash(self) -> str

**作用**: 抽象属性，返回内容的哈希值

**输入参数**: 无

**内部调用的函数**：
- 子类实现

**输出参数**: str - 内容哈希

#### load_obj(self, skip_missing: bool, skip_silently: bool) -> Any

**作用**: 加载节点对应的对象，支持错误处理和回退机制

**输入参数**:
- `skip_missing`: bool，是否跳过缺失的对象
- `skip_silently`: bool，是否静默跳过

**内部调用的函数**：
- `load_obj`: 加载对象函数
- `logger.debug`: 记录调试信息
- `logger.warning`: 记录警告信息

**输出参数**: Any - 加载的对象或回退对象

**其他说明**：如果对象不存在且允许跳过，会使用 FALLBACK_OBJ

### CallableNode 类（可调用节点）

#### __init__(self, module_name: str, obj_name: str, representation: Optional[str], runtime_description: str)

**作用**: 初始化可调用节点，设置模块名、对象名、表示和运行时描述

**输入参数**:
- `module_name`: str，模块名称
- `obj_name`: str，对象名称
- `representation`: Optional[str]，源代码表示
- `runtime_description`: str，运行时描述

**内部调用的函数**：
- `self._set_representation`: 设置表示

**输出参数**: 无返回值

#### from_obj(obj: Any, dep_key: DepKey) -> "CallableNode"

**作用**: 从对象创建可调用节点

**输入参数**:
- `obj`: Any，可调用对象
- `dep_key`: DepKey，依赖键

**内部调用的函数**：
- `CallableNode.represent`: 获取对象表示
- `extract_code`: 提取代码对象
- `get_runtime_description`: 获取运行时描述

**输出参数**: CallableNode - 新创建的可调用节点

#### from_runtime(module_name: str, obj_name: str, code_obj: types.CodeType) -> "CallableNode"

**作用**: 从运行时代码对象创建可调用节点

**输入参数**:
- `module_name`: str，模块名称
- `obj_name`: str，对象名称
- `code_obj`: types.CodeType，代码对象

**内部调用的函数**：
- `get_runtime_description`: 获取运行时描述

**输出参数**: CallableNode - 新创建的可调用节点

#### _set_representation(self, value: str)

**作用**: 设置表示并计算内容哈希

**输入参数**:
- `value`: str，表示字符串

**内部调用的函数**：
- `get_content_hash`: 计算内容哈希

**输出参数**: 无返回值

#### is_method(self) -> bool

**作用**: 检查是否为方法（对象名包含点号）

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: bool - 是否为方法

#### present_key(self) -> str

**作用**: 返回可调用对象的可读键表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 可读的键表示

#### class_name(self) -> str

**作用**: 获取方法所属的类名

**输入参数**: 无

**内部调用的函数**：
- str.split: 分割对象名
- str.join: 连接类名部分

**输出参数**: str - 类名

**其他说明**：仅适用于方法，断言 is_method 为 True

#### represent(obj: Union[types.FunctionType, types.CodeType, Callable], allow_fallback: bool = False) -> str

**作用**: 获取可调用对象的源代码表示

**输入参数**:
- `obj`: Union[types.FunctionType, types.CodeType, Callable]，可调用对象
- `allow_fallback`: bool，是否允许回退

**内部调用的函数**：
- `inspect.getsource`: 获取源代码
- `logger.warning`: 记录警告

**输出参数**: str - 源代码字符串

**其他说明**：
- 处理 Op 对象，提取其函数部分
- 去除行尾空白以规范化源代码
- 支持回退到默认函数源代码

### GlobalVarNode 类（全局变量节点）

#### __init__(self, module_name: str, obj_name: str, representation: Tuple[str, str])

**作用**: 初始化全局变量节点

**输入参数**:
- `module_name`: str，模块名称
- `obj_name`: str，对象名称
- `representation`: Tuple[str, str]，表示元组（内容哈希，截断的repr）

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### from_obj(obj: Any, dep_key: DepKey, skip_unhashable: bool = False, skip_silently: bool = False) -> "GlobalVarNode"

**作用**: 从对象创建全局变量节点

**输入参数**:
- `obj`: Any，全局变量对象
- `dep_key`: DepKey，依赖键
- `skip_unhashable`: bool，是否跳过不可哈希的对象
- `skip_silently`: bool，是否静默跳过

**内部调用的函数**：
- `GlobalVarNode.represent`: 获取对象表示

**输出参数**: GlobalVarNode - 新创建的全局变量节点

#### represent(obj: Any, skip_unhashable: bool = True, skip_silently: bool = False) -> Tuple[str, str]

**作用**: 获取全局变量的哈希和截断表示

**输入参数**:
- `obj`: Any，要表示的对象
- `skip_unhashable`: bool，是否跳过不可哈希的对象
- `skip_silently`: bool，是否静默跳过

**内部调用的函数**：
- `textwrap.shorten`: 截断文本
- `get_content_hash`: 计算内容哈希
- `logger.debug`: 记录调试信息
- `logger.warning`: 记录警告信息

**输出参数**: Tuple[str, str] - 内容哈希和截断表示的元组

**其他说明**：
- 如果对象是 Ref，重用其内容哈希
- 支持处理不可哈希对象的错误情况
- 返回元组包含哈希值和调试用的截断表示

#### present_key(self) -> str

**作用**: 返回全局变量的可读键表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 可读的键表示

### TerminalData 类（终端数据）

#### __init__(self, op_internal_name: str, op_version: int, call_content_version: str, call_semantic_version: str, dep_key: DepKey)

**作用**: 初始化终端数据，存储操作和调用的版本信息

**输入参数**:
- `op_internal_name`: str，操作内部名称
- `op_version`: int，操作版本
- `call_content_version`: str，调用内容版本
- `call_semantic_version`: str，调用语义版本
- `dep_key`: DepKey，依赖键

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

### TerminalNode 类（终端节点）

#### __init__(self, module_name: str, obj_name: str, representation: TerminalData)

**作用**: 初始化终端节点

**输入参数**:
- `module_name`: str，模块名称
- `obj_name`: str，对象名称
- `representation`: TerminalData，终端数据表示

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

### DependencyGraph 类（依赖图）

#### __init__(self)

**作用**: 初始化依赖图，创建空的节点、根和边集合

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### get_trace_state(self) -> Tuple[DepKey, Dict[DepKey, Node]]

**作用**: 获取跟踪状态，返回根组件和所有节点

**输入参数**: 无

**内部调用的函数**：
- list: 转换集合为列表

**输出参数**: Tuple[DepKey, Dict[DepKey, Node]] - 根组件和节点字典

**其他说明**：要求恰好有一个根节点

#### show(self, path: Optional[Path] = None, how: str = "none")

**作用**: 显示依赖图的可视化

**输入参数**:
- `path`: Optional[Path]，输出路径
- `how`: str，显示方式

**内部调用的函数**：
- `to_dot`: 转换为 DOT 格式
- `write_output`: 写入输出

**输出参数**: 可视化输出结果

#### __repr__(self) -> str

**作用**: 返回依赖图的字符串表示

**输入参数**: 无

**内部调用的函数**：
- `to_string`: 转换为字符串

**输出参数**: str - 依赖图的字符串表示

#### add_node(self, node: Node)

**作用**: 向依赖图添加节点

**输入参数**:
- `node`: Node，要添加的节点

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### add_edge(self, source: Node, target: Node)

**作用**: 向依赖图添加边，如果节点不存在则自动添加

**输入参数**:
- `source`: Node，源节点
- `target`: Node，目标节点

**内部调用的函数**：
- `self.add_node`: 添加节点

**输出参数**: 无返回值

### 设计模式和架构

#### 抽象基类模式
- Node 作为抽象基类定义了所有节点的通用接口
- 子类实现具体的表示和内容处理逻辑

#### 工厂模式
- from_obj 和 from_runtime 静态方法作为工厂方法创建节点

#### 策略模式
- 不同类型的节点有不同的表示策略
- 支持可配置的错误处理策略

#### 组合模式
- DependencyGraph 组合多个节点和边
- 支持复杂的图结构操作
