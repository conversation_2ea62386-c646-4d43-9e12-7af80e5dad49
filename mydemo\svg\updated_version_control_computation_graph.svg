<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="359pt" height="620pt"
 viewBox="0.00 0.00 359.00 620.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 616)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-616 355,-616 355,4 -4,4"/>
<!-- operation -->
<g id="node1" class="node">
<title>operation</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-612C93,-612 12,-612 12,-612 6,-612 0,-606 0,-600 0,-600 0,-588 0,-588 0,-582 6,-576 12,-576 12,-576 93,-576 93,-576 99,-576 105,-582 105,-588 105,-588 105,-600 105,-600 105,-606 99,-612 93,-612"/>
<text text-anchor="start" x="25" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">operation</text>
<text text-anchor="start" x="8" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- enhanced_process_numbers -->
<g id="node7" class="node">
<title>enhanced_process_numbers</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M256,-518C256,-518 95,-518 95,-518 89,-518 83,-512 83,-506 83,-506 83,-490 83,-490 83,-484 89,-478 95,-478 95,-478 256,-478 256,-478 262,-478 268,-484 268,-490 268,-490 268,-506 268,-506 268,-512 262,-518 256,-518"/>
<text text-anchor="start" x="91" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">enhanced_process_numbers</text>
<text text-anchor="start" x="101" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:enhanced_process_numbers</text>
<text text-anchor="start" x="161" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- operation&#45;&gt;enhanced_process_numbers -->
<g id="edge3" class="edge">
<title>operation&#45;&gt;enhanced_process_numbers</title>
<path fill="none" stroke="#002b36" d="M74.77,-575.98C93.6,-561.59 120.84,-540.78 142.24,-524.42"/>
<polygon fill="#002b36" stroke="#002b36" points="144.61,-527.02 150.43,-518.16 140.36,-521.45 144.61,-527.02"/>
<text text-anchor="middle" x="147" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">operation</text>
<text text-anchor="middle" x="147" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- var_1 -->
<g id="node2" class="node">
<title>var_1</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M190.5,-228C190.5,-228 160.5,-228 160.5,-228 154.5,-228 148.5,-222 148.5,-216 148.5,-216 148.5,-204 148.5,-204 148.5,-198 154.5,-192 160.5,-192 160.5,-192 190.5,-192 190.5,-192 196.5,-192 202.5,-198 202.5,-204 202.5,-204 202.5,-216 202.5,-216 202.5,-222 196.5,-228 190.5,-228"/>
<text text-anchor="start" x="159.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_1</text>
<text text-anchor="start" x="157" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- final_computation -->
<g id="node9" class="node">
<title>final_computation</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M223,-134C223,-134 128,-134 128,-134 122,-134 116,-128 116,-122 116,-122 116,-106 116,-106 116,-100 122,-94 128,-94 128,-94 223,-94 223,-94 229,-94 235,-100 235,-106 235,-106 235,-122 235,-122 235,-128 229,-134 223,-134"/>
<text text-anchor="start" x="124" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">final_computation</text>
<text text-anchor="start" x="125" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:final_computation</text>
<text text-anchor="start" x="161" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- var_1&#45;&gt;final_computation -->
<g id="edge7" class="edge">
<title>var_1&#45;&gt;final_computation</title>
<path fill="none" stroke="#002b36" d="M175.5,-191.76C175.5,-178.5 175.5,-159.86 175.5,-144.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-144.07 175.5,-134.07 172,-144.07 179,-144.07"/>
<text text-anchor="middle" x="197" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">value</text>
<text text-anchor="middle" x="197" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- numbers -->
<g id="node3" class="node">
<title>numbers</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M216,-612C216,-612 135,-612 135,-612 129,-612 123,-606 123,-600 123,-600 123,-588 123,-588 123,-582 129,-576 135,-576 135,-576 216,-576 216,-576 222,-576 228,-582 228,-588 228,-588 228,-600 228,-600 228,-606 222,-612 216,-612"/>
<text text-anchor="start" x="149.5" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">numbers</text>
<text text-anchor="start" x="131" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- numbers&#45;&gt;enhanced_process_numbers -->
<g id="edge2" class="edge">
<title>numbers&#45;&gt;enhanced_process_numbers</title>
<path fill="none" stroke="#002b36" d="M175.5,-575.76C175.5,-562.5 175.5,-543.86 175.5,-528.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-528.07 175.5,-518.07 172,-528.07 179,-528.07"/>
<text text-anchor="middle" x="197" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">numbers</text>
<text text-anchor="middle" x="197" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- version -->
<g id="node4" class="node">
<title>version</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M339,-612C339,-612 258,-612 258,-612 252,-612 246,-606 246,-600 246,-600 246,-588 246,-588 246,-582 252,-576 258,-576 258,-576 339,-576 339,-576 345,-576 351,-582 351,-588 351,-588 351,-600 351,-600 351,-606 345,-612 339,-612"/>
<text text-anchor="start" x="277" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">version</text>
<text text-anchor="start" x="254" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- version&#45;&gt;enhanced_process_numbers -->
<g id="edge4" class="edge">
<title>version&#45;&gt;enhanced_process_numbers</title>
<path fill="none" stroke="#002b36" d="M276.23,-575.98C257.4,-561.59 230.16,-540.78 208.76,-524.42"/>
<polygon fill="#002b36" stroke="#002b36" points="210.64,-521.45 200.57,-518.16 206.39,-527.02 210.64,-521.45"/>
<text text-anchor="middle" x="273" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">version</text>
<text text-anchor="middle" x="273" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- var_2 -->
<g id="node5" class="node">
<title>var_2</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M210.5,-36C210.5,-36 140.5,-36 140.5,-36 134.5,-36 128.5,-30 128.5,-24 128.5,-24 128.5,-12 128.5,-12 128.5,-6 134.5,0 140.5,0 140.5,0 210.5,0 210.5,0 216.5,0 222.5,-6 222.5,-12 222.5,-12 222.5,-24 222.5,-24 222.5,-30 216.5,-36 210.5,-36"/>
<text text-anchor="start" x="159.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_2</text>
<text text-anchor="start" x="136.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sinks)</text>
</g>
<!-- var_0 -->
<g id="node6" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M190.5,-420C190.5,-420 160.5,-420 160.5,-420 154.5,-420 148.5,-414 148.5,-408 148.5,-408 148.5,-396 148.5,-396 148.5,-390 154.5,-384 160.5,-384 160.5,-384 190.5,-384 190.5,-384 196.5,-384 202.5,-390 202.5,-396 202.5,-396 202.5,-408 202.5,-408 202.5,-414 196.5,-420 190.5,-420"/>
<text text-anchor="start" x="159.5" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="157" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- aggregate_results -->
<g id="node8" class="node">
<title>aggregate_results</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M223.5,-326C223.5,-326 127.5,-326 127.5,-326 121.5,-326 115.5,-320 115.5,-314 115.5,-314 115.5,-298 115.5,-298 115.5,-292 121.5,-286 127.5,-286 127.5,-286 223.5,-286 223.5,-286 229.5,-286 235.5,-292 235.5,-298 235.5,-298 235.5,-314 235.5,-314 235.5,-320 229.5,-326 223.5,-326"/>
<text text-anchor="start" x="123.5" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">aggregate_results</text>
<text text-anchor="start" x="124.5" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:aggregate_results</text>
<text text-anchor="start" x="161" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- var_0&#45;&gt;aggregate_results -->
<g id="edge5" class="edge">
<title>var_0&#45;&gt;aggregate_results</title>
<path fill="none" stroke="#002b36" d="M175.5,-383.76C175.5,-370.5 175.5,-351.86 175.5,-336.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-336.07 175.5,-326.07 172,-336.07 179,-336.07"/>
<text text-anchor="middle" x="197" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="197" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- enhanced_process_numbers&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>enhanced_process_numbers&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M175.5,-477.98C175.5,-464.34 175.5,-445.75 175.5,-430.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-430.1 175.5,-420.1 172,-430.1 179,-430.1"/>
<text text-anchor="middle" x="197" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- aggregate_results&#45;&gt;var_1 -->
<g id="edge6" class="edge">
<title>aggregate_results&#45;&gt;var_1</title>
<path fill="none" stroke="#002b36" d="M175.5,-285.98C175.5,-272.34 175.5,-253.75 175.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-238.1 175.5,-228.1 172,-238.1 179,-238.1"/>
<text text-anchor="middle" x="197" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- final_computation&#45;&gt;var_2 -->
<g id="edge8" class="edge">
<title>final_computation&#45;&gt;var_2</title>
<path fill="none" stroke="#002b36" d="M175.5,-93.98C175.5,-80.34 175.5,-61.75 175.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-46.1 175.5,-36.1 172,-46.1 179,-46.1"/>
<text text-anchor="middle" x="197" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
</g>
</svg>
