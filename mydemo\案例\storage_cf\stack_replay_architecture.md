# 栈重放（Stack Replay）架构设计

## 概述

基于mandala1框架实现栈重放功能，能够捕获完整的函数执行信息，并提供两种遍历方式：
1. **ComputationFrame遍历**：计算图结构遍历，展示函数层级关系
2. **Storage执行顺序遍历**：按时间顺序遍历函数调用，展示执行流程

## 核心架构

### 1. 利用mandala1现有功能

#### 1.1 Storage类功能
- **@op装饰器**：自动捕获函数调用信息
- **Call对象**：存储函数调用的输入输出参数
- **Ref对象**：存储函数返回值的引用
- **调用缓存**：自动记忆化，避免重复计算

#### 1.2 ComputationFrame类功能
- **计算图构建**：自动构建函数依赖关系图
- **图扩展方法**：expand_back、expand_forward、expand_all
- **节点遍历**：获取函数节点和变量节点
- **拓扑排序**：topsort_modulo_sccs方法

### 2. 栈重放功能设计

#### 2.1 ComputationFrame遍历器（CFTraverser）
```python
class CFTraverser:
    """计算图遍历器，实现计算图的层级遍历"""
    
    def traverse_computation_graph(self, cf: ComputationFrame) -> List[Dict]:
        """遍历计算图，返回层级结构信息"""
        # 1. 获取拓扑排序的节点顺序
        # 2. 计算每个节点的深度
        # 3. 提取函数名信息
        # 4. 构建层级结构
        
    def print_computation_graph(self, cf: ComputationFrame):
        """打印计算图的层级结构"""
        # 高可读性的格式化输出
```

#### 2.2 Storage执行顺序遍历器（StorageTraverser）
```python
class StorageTraverser:
    """存储执行顺序遍历器，按时间顺序遍历函数调用"""
    
    def traverse_execution_order(self, storage: Storage) -> List[Dict]:
        """遍历函数执行顺序，返回调用信息"""
        # 1. 获取所有Call对象
        # 2. 按调用顺序排序
        # 3. 计算调用深度
        # 4. 提取输入输出参数
        
    def print_execution_order(self, storage: Storage):
        """打印函数执行顺序"""
        # 高可读性的格式化输出
```

### 3. 深度计算策略

#### 3.1 ComputationFrame深度计算
- 基于计算图的拓扑结构
- 从源节点开始，逐层计算深度
- 考虑函数依赖关系

#### 3.2 Storage执行深度计算
- 基于函数调用栈的实际深度
- 跟踪函数调用的嵌套层级
- 记录调用时的上下文信息

### 4. 输出格式设计

#### 4.1 ComputationFrame输出格式
```
计算图遍历结果：
├─ 深度0: 函数A
│  ├─ 调用次数: 3
│  └─ 输出变量: var1, var2
├─ 深度1: 函数B
│  ├─ 调用次数: 2
│  ├─ 依赖: 函数A
│  └─ 输出变量: var3
└─ 深度2: 函数C
   ├─ 调用次数: 1
   ├─ 依赖: 函数B
   └─ 输出变量: var4
```

#### 4.2 Storage执行顺序输出格式
```
函数执行顺序：
1. [深度0] 函数A(x=1, y=2) -> 返回值: 3
2. [深度1] 函数B(z=3, w=4) -> 返回值: 7
3. [深度0] 函数A(x=5, y=6) -> 返回值: 11
4. [深度2] 函数C(a=7, b=11) -> 返回值: 18
```

## 实现计划

### 阶段1：核心遍历器实现
1. 实现CFTraverser类
2. 实现StorageTraverser类
3. 实现深度计算算法

### 阶段2：输出格式化
1. 设计高可读性的输出格式
2. 实现格式化打印功能
3. 添加详细信息显示选项

### 阶段3：演示程序
1. 创建完整的演示程序
2. 展示不同复杂度的计算场景
3. 验证功能正确性

## 技术要点

### 1. 不重复实现框架功能
- 充分利用mandala1的现有API
- 避免重新实现计算图构建逻辑
- 复用Storage的调用跟踪机制

### 2. 高可读性输出
- 使用树形结构显示层级关系
- 清晰的缩进和符号标识
- 关键信息突出显示

### 3. 性能考虑
- 避免重复计算
- 合理使用缓存机制
- 支持大规模计算图的遍历

## 依赖关系

```
栈重放功能
├─ mandala1.Storage (存储和调用管理)
├─ mandala1.ComputationFrame (计算图操作)
├─ mandala1.model.Call (调用信息)
├─ mandala1.model.Ref (引用管理)
└─ mandala1.model.Op (@op装饰器)
```
