<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="482pt" height="620pt"
 viewBox="0.00 0.00 482.00 620.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 616)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-616 478,-616 478,4 -4,4"/>
<!-- operation -->
<g id="node1" class="node">
<title>operation</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M339,-612C339,-612 258,-612 258,-612 252,-612 246,-606 246,-600 246,-600 246,-588 246,-588 246,-582 252,-576 258,-576 258,-576 339,-576 339,-576 345,-576 351,-582 351,-588 351,-588 351,-600 351,-600 351,-606 345,-612 339,-612"/>
<text text-anchor="start" x="271" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">operation</text>
<text text-anchor="start" x="254" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- process_numbers -->
<g id="node10" class="node">
<title>process_numbers</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M395,-518C395,-518 298,-518 298,-518 292,-518 286,-512 286,-506 286,-506 286,-490 286,-490 286,-484 292,-478 298,-478 298,-478 395,-478 395,-478 401,-478 407,-484 407,-490 407,-490 407,-506 407,-506 407,-512 401,-518 395,-518"/>
<text text-anchor="start" x="294" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">process_numbers</text>
<text text-anchor="start" x="295.5" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:process_numbers</text>
<text text-anchor="start" x="332" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- operation&#45;&gt;process_numbers -->
<g id="edge9" class="edge">
<title>operation&#45;&gt;process_numbers</title>
<path fill="none" stroke="#002b36" d="M307.3,-575.76C314.21,-562.24 323.96,-543.14 332.01,-527.38"/>
<polygon fill="#002b36" stroke="#002b36" points="335.33,-528.57 336.76,-518.07 329.1,-525.39 335.33,-528.57"/>
<text text-anchor="middle" x="349" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">operation</text>
<text text-anchor="middle" x="349" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- enhanced_process_numbers -->
<g id="node11" class="node">
<title>enhanced_process_numbers</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M256,-518C256,-518 95,-518 95,-518 89,-518 83,-512 83,-506 83,-506 83,-490 83,-490 83,-484 89,-478 95,-478 95,-478 256,-478 256,-478 262,-478 268,-484 268,-490 268,-490 268,-506 268,-506 268,-512 262,-518 256,-518"/>
<text text-anchor="start" x="91" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">enhanced_process_numbers</text>
<text text-anchor="start" x="101" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:enhanced_process_numbers</text>
<text text-anchor="start" x="161" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- operation&#45;&gt;enhanced_process_numbers -->
<g id="edge10" class="edge">
<title>operation&#45;&gt;enhanced_process_numbers</title>
<path fill="none" stroke="#002b36" d="M276.23,-575.98C257.4,-561.59 230.16,-540.78 208.76,-524.42"/>
<polygon fill="#002b36" stroke="#002b36" points="210.64,-521.45 200.57,-518.16 206.39,-527.02 210.64,-521.45"/>
<text text-anchor="middle" x="274" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">operation</text>
<text text-anchor="middle" x="274" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- numbers -->
<g id="node2" class="node">
<title>numbers</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-612C93,-612 12,-612 12,-612 6,-612 0,-606 0,-600 0,-600 0,-588 0,-588 0,-582 6,-576 12,-576 12,-576 93,-576 93,-576 99,-576 105,-582 105,-588 105,-588 105,-600 105,-600 105,-606 99,-612 93,-612"/>
<text text-anchor="start" x="26.5" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">numbers</text>
<text text-anchor="start" x="8" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- numbers&#45;&gt;enhanced_process_numbers -->
<g id="edge8" class="edge">
<title>numbers&#45;&gt;enhanced_process_numbers</title>
<path fill="none" stroke="#002b36" d="M74.77,-575.98C93.6,-561.59 120.84,-540.78 142.24,-524.42"/>
<polygon fill="#002b36" stroke="#002b36" points="144.61,-527.02 150.43,-518.16 140.36,-521.45 144.61,-527.02"/>
<text text-anchor="middle" x="147" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">numbers</text>
<text text-anchor="middle" x="147" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- value -->
<g id="node3" class="node">
<title>value</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M337.5,-228C337.5,-228 307.5,-228 307.5,-228 301.5,-228 295.5,-222 295.5,-216 295.5,-216 295.5,-204 295.5,-204 295.5,-198 301.5,-192 307.5,-192 307.5,-192 337.5,-192 337.5,-192 343.5,-192 349.5,-198 349.5,-204 349.5,-204 349.5,-216 349.5,-216 349.5,-222 343.5,-228 337.5,-228"/>
<text text-anchor="start" x="307" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">value</text>
<text text-anchor="start" x="304" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">6 values</text>
</g>
<!-- final_computation -->
<g id="node13" class="node">
<title>final_computation</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M419,-134C419,-134 324,-134 324,-134 318,-134 312,-128 312,-122 312,-122 312,-106 312,-106 312,-100 318,-94 324,-94 324,-94 419,-94 419,-94 425,-94 431,-100 431,-106 431,-106 431,-122 431,-122 431,-128 425,-134 419,-134"/>
<text text-anchor="start" x="320" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">final_computation</text>
<text text-anchor="start" x="321" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:final_computation</text>
<text text-anchor="start" x="357" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">6 calls</text>
</g>
<!-- value&#45;&gt;final_computation -->
<g id="edge3" class="edge">
<title>value&#45;&gt;final_computation</title>
<path fill="none" stroke="#002b36" d="M327.83,-191.77C331.73,-180.18 337.54,-164.76 344.5,-152 346.19,-148.9 348.1,-145.77 350.11,-142.71"/>
<polygon fill="#002b36" stroke="#002b36" points="353.1,-144.55 355.93,-134.34 347.35,-140.55 353.1,-144.55"/>
<text text-anchor="middle" x="366" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">value</text>
<text text-anchor="middle" x="366" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(6 values)</text>
</g>
<!-- version -->
<g id="node4" class="node">
<title>version</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M216,-612C216,-612 135,-612 135,-612 129,-612 123,-606 123,-600 123,-600 123,-588 123,-588 123,-582 129,-576 135,-576 135,-576 216,-576 216,-576 222,-576 228,-582 228,-588 228,-588 228,-600 228,-600 228,-606 222,-612 216,-612"/>
<text text-anchor="start" x="154" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">version</text>
<text text-anchor="start" x="131" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- version&#45;&gt;enhanced_process_numbers -->
<g id="edge11" class="edge">
<title>version&#45;&gt;enhanced_process_numbers</title>
<path fill="none" stroke="#002b36" d="M175.5,-575.76C175.5,-562.5 175.5,-543.86 175.5,-528.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-528.07 175.5,-518.07 172,-528.07 179,-528.07"/>
<text text-anchor="middle" x="197" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">version</text>
<text text-anchor="middle" x="197" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- method -->
<g id="node5" class="node">
<title>method</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M424,-420C424,-420 343,-420 343,-420 337,-420 331,-414 331,-408 331,-408 331,-396 331,-396 331,-390 337,-384 343,-384 343,-384 424,-384 424,-384 430,-384 436,-390 436,-396 436,-396 436,-408 436,-408 436,-414 430,-420 424,-420"/>
<text text-anchor="start" x="361.5" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">method</text>
<text text-anchor="start" x="339" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- aggregate_results -->
<g id="node12" class="node">
<title>aggregate_results</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M370.5,-326C370.5,-326 274.5,-326 274.5,-326 268.5,-326 262.5,-320 262.5,-314 262.5,-314 262.5,-298 262.5,-298 262.5,-292 268.5,-286 274.5,-286 274.5,-286 370.5,-286 370.5,-286 376.5,-286 382.5,-292 382.5,-298 382.5,-298 382.5,-314 382.5,-314 382.5,-320 376.5,-326 370.5,-326"/>
<text text-anchor="start" x="270.5" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">aggregate_results</text>
<text text-anchor="start" x="271.5" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:aggregate_results</text>
<text text-anchor="start" x="308" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">6 calls</text>
</g>
<!-- method&#45;&gt;aggregate_results -->
<g id="edge6" class="edge">
<title>method&#45;&gt;aggregate_results</title>
<path fill="none" stroke="#002b36" d="M372.31,-383.76C363.37,-369.99 350.68,-350.42 340.34,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="343.26,-332.56 334.88,-326.07 337.38,-336.37 343.26,-332.56"/>
<text text-anchor="middle" x="381" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">method</text>
<text text-anchor="middle" x="381" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- factor -->
<g id="node6" class="node">
<title>factor</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M461,-228C461,-228 380,-228 380,-228 374,-228 368,-222 368,-216 368,-216 368,-204 368,-204 368,-198 374,-192 380,-192 380,-192 461,-192 461,-192 467,-192 473,-198 473,-204 473,-204 473,-216 473,-216 473,-222 467,-228 461,-228"/>
<text text-anchor="start" x="403.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">factor</text>
<text text-anchor="start" x="376" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">5 values (5 sources)</text>
</g>
<!-- factor&#45;&gt;final_computation -->
<g id="edge2" class="edge">
<title>factor&#45;&gt;final_computation</title>
<path fill="none" stroke="#002b36" d="M411.51,-191.76C404.47,-178.24 394.51,-159.14 386.29,-143.38"/>
<polygon fill="#002b36" stroke="#002b36" points="389.17,-141.32 381.44,-134.07 382.96,-144.56 389.17,-141.32"/>
<text text-anchor="middle" x="423" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">factor</text>
<text text-anchor="middle" x="423" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(5 values)</text>
</g>
<!-- numbers_0 -->
<g id="node7" class="node">
<title>numbers_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M462,-612C462,-612 381,-612 381,-612 375,-612 369,-606 369,-600 369,-600 369,-588 369,-588 369,-582 375,-576 381,-576 381,-576 462,-576 462,-576 468,-576 474,-582 474,-588 474,-588 474,-600 474,-600 474,-606 468,-612 462,-612"/>
<text text-anchor="start" x="389" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">numbers_0</text>
<text text-anchor="start" x="377" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- numbers_0&#45;&gt;process_numbers -->
<g id="edge13" class="edge">
<title>numbers_0&#45;&gt;process_numbers</title>
<path fill="none" stroke="#002b36" d="M407.74,-575.76C396.65,-561.86 380.85,-542.06 368.08,-526.05"/>
<polygon fill="#002b36" stroke="#002b36" points="370.69,-523.71 361.72,-518.07 365.22,-528.07 370.69,-523.71"/>
<text text-anchor="middle" x="414" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">numbers</text>
<text text-anchor="middle" x="414" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- var_0 -->
<g id="node8" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M406.5,-36C406.5,-36 336.5,-36 336.5,-36 330.5,-36 324.5,-30 324.5,-24 324.5,-24 324.5,-12 324.5,-12 324.5,-6 330.5,0 336.5,0 336.5,0 406.5,0 406.5,0 412.5,0 418.5,-6 418.5,-12 418.5,-12 418.5,-24 418.5,-24 418.5,-30 412.5,-36 406.5,-36"/>
<text text-anchor="start" x="355.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="332.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">6 values (6 sinks)</text>
</g>
<!-- data -->
<g id="node9" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M301,-420C301,-420 220,-420 220,-420 214,-420 208,-414 208,-408 208,-408 208,-396 208,-396 208,-390 214,-384 220,-384 220,-384 301,-384 301,-384 307,-384 313,-390 313,-396 313,-396 313,-408 313,-408 313,-414 307,-420 301,-420"/>
<text text-anchor="start" x="248" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="216" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">6 values (1 sources)</text>
</g>
<!-- data&#45;&gt;aggregate_results -->
<g id="edge5" class="edge">
<title>data&#45;&gt;aggregate_results</title>
<path fill="none" stroke="#002b36" d="M271.87,-383.76C280.96,-369.99 293.86,-350.42 304.37,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="307.34,-336.35 309.92,-326.07 301.49,-332.49 307.34,-336.35"/>
<text text-anchor="middle" x="319" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="319" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(6 values)</text>
</g>
<!-- process_numbers&#45;&gt;data -->
<g id="edge12" class="edge">
<title>process_numbers&#45;&gt;data</title>
<path fill="none" stroke="#002b36" d="M329.1,-477.98C315.89,-463.54 297.62,-443.57 283.25,-427.87"/>
<polygon fill="#002b36" stroke="#002b36" points="285.48,-425.11 276.14,-420.1 280.31,-429.84 285.48,-425.11"/>
<text text-anchor="middle" x="333" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="333" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- enhanced_process_numbers&#45;&gt;data -->
<g id="edge7" class="edge">
<title>enhanced_process_numbers&#45;&gt;data</title>
<path fill="none" stroke="#002b36" d="M192.7,-477.98C205.75,-463.54 223.82,-443.57 238.01,-427.87"/>
<polygon fill="#002b36" stroke="#002b36" points="240.93,-429.86 245.04,-420.1 235.74,-425.17 240.93,-429.86"/>
<text text-anchor="middle" x="250" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="250" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- aggregate_results&#45;&gt;value -->
<g id="edge4" class="edge">
<title>aggregate_results&#45;&gt;value</title>
<path fill="none" stroke="#002b36" d="M322.5,-285.98C322.5,-272.34 322.5,-253.75 322.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="326,-238.1 322.5,-228.1 319,-238.1 326,-238.1"/>
<text text-anchor="middle" x="344" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="344" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(6 values)</text>
</g>
<!-- final_computation&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>final_computation&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M371.5,-93.98C371.5,-80.34 371.5,-61.75 371.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="375,-46.1 371.5,-36.1 368,-46.1 375,-46.1"/>
<text text-anchor="middle" x="393" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="393" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(6 values)</text>
</g>
</g>
</svg>
