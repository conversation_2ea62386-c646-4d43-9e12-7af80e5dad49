---
description: 
globs: 
alwaysApply: true
---
## 3.1 关键原则
以下为任务计划示例内容，请生成务计划时按时此格式。
文件列表以域划分，域内的排序从依赖度从低到高排序
## 3.2 任务计划表

## 项目概述

本项目旨在实现一个通用的系统功能，允许用户通过特定机制捕获和处理关键信息，并提供交互式界面进行分析和操作。

### 核心功能

1. **数据捕获与存储**  
   - 使用 `[framework1]` 框架捕获运行时的核心数据 `[data_structure1]`  
   - 保存关键上下文信息、输入和输出参数  
   - 使用 `[tool1]` 管理运行上下文  

2. **数据结构转换与可视化**  
   - 使用 `[library1]` 将 `[data_structure1]` 转换为目标结构 `[structure1]`  
   - 使用 `[library2]` 实现交互式可视化界面  
   - 支持界面操作，如展开/折叠、拖拽等功能  

3. **核心逻辑操作与交互**  
   - 支持特定节点的回放或重执行功能  
   - 允许修改参数后重新执行相关操作  
   - 支持增加/删除节点或相关元素  

4. **输出优化**  
   - 实现增强的输出显示功能（如彩色打印）  
   - 优化输入输出对比显示效果  
   - 提高日志或信息的可读性  

---

## 复杂度评估

根据功能需求和技术复杂度，本项目评估为 **Level [X]** 复杂度：  
- 需要集成多个第三方框架或工具（如 `[framework1]`, `[library1]`, `[library2]`）。  
- 涉及复杂的数据结构转换（如 `[data_structure1]` 到 `[structure1]` 的映射）。  
- 需要实现交互式界面（需处理用户输入与业务逻辑的解耦）。  
- 需要处理核心逻辑的执行或回放机制（需保证一致性与安全性）。  

---

### 按架构分层分类的开发文档进度跟踪表

#### **1. 领域层（Domain Layer）**
| 文件路径（领域对象）                            | 开发文档状态      | 测试状态    | 用例状态    | 备注                                                                 |
| ------------------------------------- | ----------- | ------- | ------- | ------------------------------------------------------------------ |
| `/core/domain/entities/[entity1].py`  | 进行中         | 未开始     | 未开始     | 需定义 `[entity1]` 的核心属性和验证逻辑。                                        |
| `/core/domain/services/[service1].py` | 未开始         | 未开始     | 未开始     | 负责处理 `[entity1]` 的核心逻辑。                                            |

---

#### **2. 应用层（Application Layer）**
| 文件路径（领域对象）                                   | 开发文档状态    | 测试状态    | 用例状态    | 备注                                                                 |
| -------------------------------------------- | --------- | ------- | ------- | ------------------------------------------------------------------ |
| `/core/application/use_cases/[use_case1].py` | 未开始       | 未开始     | 未开始     | 依赖 `[entity1]` 实体完成。                                               |
| `/core/application/ports/[port1].py`         | 未开始       | 未开始     | 未开始     | 定义接口，需在基础设施层实现。                                                    |

---

#### **3. 适配器层（Interface Adapters Layer）**
| 文件路径（领域对象）                                        | 开发文档状态    | 测试状态    | 用例状态    | 备注                                                                 |
| ------------------------------------------------- | --------- | ------- | ------- | ------------------------------------------------------------------ |
| `/core/interface_adapters/input/[parser1].py`     | 未开始       | 未开始     | 未开始     | 输入参数解析，需依赖应用层接口。                                                   |
| `/core/interface_adapters/output/[formatter1].py` | 未开始       | 未开始     | 未开始     | 实现输出增强功能，需与基础设施层工具集成。                                              |

---

#### **4. 基础设施层（Infrastructure Layer）**
| 文件路径（领域对象）                               | 开发文档状态    | 测试状态    | 用例状态    | 备注                                                                 |
| ---------------------------------------- | --------- | ------- | ------- | ------------------------------------------------------------------ |
| `/core/infrastructure/gui/[adapter1].py` | 未开始          | 未开始       | 未开始     | 实现 `[library2]` 的适配器，需与领域层解耦。                            |


---

## 实现计划

### 阶段一：领域模型设计

1. **设计 `[entity1]` 实体及相关值对象**  
   - **文件**：`/core/domain/entities/[entity1].py`  
   - **开发文档**：`/dev_docs/domain/entities/[entity1].md`  
   - **进度**：进行中（需完成 `[method1]` 和 `[method2]` 方法的文档与测试）。  

2. **设计核心操作相关的领域服务**  
   - **文件**：`/core/domain/services/[service1].py`  
   - **开发文档**：`/dev_docs/domain/services/[service1].md`  
   - **进度**：未开始（需等待 `[entity1]` 实体完成）。  

---

## 风险与挑战

1. **复杂逻辑执行的性能问题**  
   - **解决方案**：在 `[service1]` 服务中引入优化机制（如缓存，需在开发文档中记录）。  
2. **交互操作的复杂性**  
   - **解决方案**：通过 `[tracking_tool]` 记录用户交互设计的原型和迭代方案。  
3. **多框架集成的兼容性**  
   - **解决方案**：在基础设施层通过适配器隔离第三方库（如 `[adapter1]`）。  

---

## 文档管理规范

1. **开发文档更新规则**：  
   - 每个领域对象开发完成后，必须同步更新 `dev_docs` 中的文档（包括 `API说明` 和 `使用示例`）。  
   - 若领域对象被重构或删除，需同步删除或更新对应文档。  

2. **进度同步机制**：  
   - 每日更新 `开发文档进度跟踪表`，标记完成状态和依赖关系。  
   - 使用 `[tracking_tool]/progress.md` 统一汇总所有文件的进度。  

---

## 下一步行动

1. **领域层核心实体开发**  
   - 完成 `[entity1]` 的 `[method2]` 方法逻辑。  
   - 编写其开发文档（`dev_docs/domain/entities/[entity1].md`）。  
   - 实现测试用例（`tdd_files/domain/entities/test_[entity1].py`）。  

2. **进入设计阶段，规划交互逻辑**  
   - 在 `[tracking_tool]/design_prototypes/[prototype1].md` 中记录原型方案。  
   - 确保设计与领域层解耦（如通过接口 `[port1]`）。  

3. **基础设施层工具评估**  
   - 确认 `[library1]` 和 `[library2]` 的兼容性（记录到 `[tracking_tool]/decision_records.md`）。  
