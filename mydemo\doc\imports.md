# imports.py 文档

## 文件内容与作用总体说明

`imports.py` 文件是 mandala 框架的主要导入接口模块，提供了用户使用框架时需要的核心类和函数的统一导入入口。该文件将框架内部的各个模块中的重要组件重新导出，使用户可以通过简单的 `from mandala.imports import *` 语句获得所有必要的功能。这种设计简化了用户的使用体验，隐藏了内部模块的复杂性。

## 文件内的所有变量的作用与说明

### 核心导入
- `Storage`: 存储类，来自 storage 模块，是框架的核心存储管理器
- `noop`: 空操作函数，来自 storage 模块，用于测试和调试
- `op`: 操作装饰器，来自 model 模块，用于创建记忆化函数
- `Ignore`: 忽略类，来自 model 模块，用于标记要忽略的参数
- `NewArgDefault`: 新参数默认值类，来自 model 模块，用于参数默认值处理
- `wrap_atom`: 原子包装函数，来自 model 模块，用于将对象包装为引用
- `ValuePointer`: 值指针类，来自 model 模块，用于处理大型对象的引用
- `MList`: mandala 列表类型，来自 tps 模块，用于类型注解
- `MDict`: mandala 字典类型，来自 tps 模块，用于类型注解
- `track`: 跟踪装饰器，来自 deps.tracers.dec_impl 模块，用于依赖跟踪
- `sess`: Session 对象，来自 common_imports 模块，用于调试

## 文件内的所有函数的作用与说明

### 工具函数

#### pprint_dict(d) -> str

**作用**: 美化打印字典的内容，将字典格式化为易读的字符串

**输入参数**:
- `d`: dict，要打印的字典

**内部调用的函数**：
- 字符串格式化和连接操作

**输出参数**: str - 格式化后的字典字符串

**其他说明**：
- 每个键值对占一行
- 使用缩进提高可读性
- 格式：`    key: value`

### 导入组织说明

#### 存储相关
- `Storage`: 主要的存储管理类，用户创建存储实例
- `noop`: 空操作函数，主要用于测试

#### 操作相关
- `op`: 最重要的装饰器，用于创建记忆化函数
- `Ignore`: 用于标记在哈希计算中要忽略的参数
- `NewArgDefault`: 处理新参数的默认值
- `wrap_atom`: 手动包装对象为原子引用
- `ValuePointer`: 处理不需要序列化的大型对象

#### 类型相关
- `MList`: mandala 专用的列表类型注解
- `MDict`: mandala 专用的字典类型注解

#### 依赖跟踪
- `track`: 用于跟踪函数依赖的装饰器

#### 调试工具
- `sess`: 调试会话对象

### 使用模式

#### 基本使用
```python
from mandala.imports import Storage, op

storage = Storage()

@op
def my_function(x):
    return x + 1

with storage:
    result = my_function(5)
```

#### 类型注解使用
```python
from mandala.imports import op, MList, MDict

@op
def process_data(items: MList[int]) -> MDict[str, int]:
    return {"count": len(items), "sum": sum(items)}
```

#### 高级功能使用
```python
from mandala.imports import op, Ignore, ValuePointer, track

@track
def config_function():
    return {"setting": "value"}

@op
def advanced_function(data, batch_size: Ignore = 32):
    # batch_size 不会影响记忆化
    return process_in_batches(data, batch_size)

# 使用 ValuePointer 处理大型对象
large_model = ValuePointer("my_model", actual_model_object)
```

### 设计原则

#### 简化接口
- 用户只需要一个导入语句就能获得所有核心功能
- 隐藏内部模块的复杂结构
- 提供一致的命名空间

#### 功能完整性
- 包含所有用户常用的类和函数
- 覆盖基本使用到高级功能的需求
- 支持完整的工作流程

#### 向后兼容
- 稳定的导入接口
- 即使内部重构也不影响用户代码
- 清晰的版本管理

### 模块依赖关系

```
imports.py
├── storage.py (Storage, noop)
├── model.py (op, Ignore, NewArgDefault, wrap_atom, ValuePointer)
├── tps.py (MList, MDict)
├── deps/tracers/dec_impl.py (track)
└── common_imports.py (sess)
```

### 扩展性

#### 添加新功能
当框架添加新的核心功能时，可以在此文件中添加相应的导入，保持用户接口的一致性。

#### 版本管理
通过控制此文件的导出内容，可以管理不同版本之间的兼容性。

#### 别名支持
可以为复杂的类名或函数名提供简化的别名，提高用户体验。

### 最佳实践

#### 用户导入
推荐用户使用：
```python
from mandala.imports import *
```
或者选择性导入：
```python
from mandala.imports import Storage, op, MList
```

#### 避免直接导入
不推荐用户直接从内部模块导入：
```python
# 不推荐
from mandala.storage import Storage
from mandala.model import op
```

这样可以确保用户代码的稳定性和可维护性。
