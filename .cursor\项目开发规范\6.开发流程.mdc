---
description: 
globs: 
alwaysApply: true
---
# 关键原则
- 与  《5.memory_bank的使用规范》结合使用
- 每个开启新的任务计划时（使用van模式时），为避免文档的混淆，需要确认后删除/memory_bank下的所有文档


# 重要名词 
任务计划：.md文件，用于指导开发文档的文档，内容包括但不限于开发内容、开发进度等。 
开发文档：.md文件，用于指导领域对象的开发，内容包括但不限于每个领域对象要实现的功能、镜像对应的文件等。
领域对象：.py文件，根据开发文档，使用Python实现的所需要的功能。
测试用例：.py文件，测试驱动开发（TDD）的用例，用于验证领域对象的基本功能
场景用例: .py文件，用例驱动验证（CDD)，用于验证领域对象的综合功能
# 重要原则 
本开发流程的所有内容, 包括开发文档，测试用例，场景用例，非必要不得作任何修改。 
# 领域层开发模式设计（DDD + TDD + CDD）

本文档描述了一种结合领域驱动开发（DDD）、测试驱动开发（TDD）和用例驱动验证（CDD）的开发模式，旨在通过多维度方法构建高质量的领域层代码，确保业务逻辑的独立性、可测试性和可维护性。

## 1. 开发模式核心组成部分

### 1.1 领域建模（DDD）
**目标**：通过领域驱动设计，构建与业务语言一致的模型，聚焦 `[模块/实体名称]` 的核心业务逻辑。  
**输入**：
- 业务需求文档（如用户故事、用例描述）。
- 领域专家访谈记录或工作坊成果。  
**输出**：
- 定义领域对象：包括实体（Entity）、值对象（Value Object）、聚合根（Aggregate Root）。
- 定义领域服务接口及核心业务规则。
- 事件模型：定义领域事件以支持事件驱动架构。  
**示例**：
- 定义 `[模块/实体名称]` 实体，包含属性 `[属性1]` 和 `[属性2]`，并规定相关验证规则。
- 定义 `[聚合根名称]` 聚合根，管理状态转换（如 `[状态1]` 到 `[状态2]`）。
- 定义 `[服务名称]` 接口，处理特定业务逻辑。  
**约束**：
- 领域层代码必须与技术实现解耦，不依赖外部框架、数据库或基础设施。
- 业务规则需以领域语言表达，独立于具体实现细节。
- 确保模型边界清晰，遵循聚合根一致性规则。

### 1.2 测试驱动开发（TDD）
**目标**：通过测试先行，确保 `[模块/实体名称]` 领域逻辑的正确性和可维护性。  
**步骤**：
1. **编写测试用例**：针对核心业务逻辑编写单元测试，聚焦领域对象和服务的行为，覆盖正常场景和边界条件。
   ```python
   import pytest

   def test_invalid_[属性1]_format():
       with pytest.raises(ValueError):
           [模块/实体名称](mdc:[属性1]="invalid", [属性2]="valid")

   def test_valid_[属性1]_format():
       obj = [模块/实体名称](mdc:[属性1]="valid_value", [属性2]="valid")
       assert obj.[属性1] == "valid_value"
   ```
2. **实现最小代码**：编写领域对象或服务的最小实现，使测试通过。
   ```python
   class [模块/实体名称]:
       def __init__(self, [属性1]: str, [属性2]: str):
           if not self._validate_[属性1](mdc:[属性1]):
               raise ValueError("Invalid [属性1] format")
           self.[属性1] = [属性1]
           self.[属性2] = [属性2]

       def _validate_[属性1](mdc:self, [属性1]: str) -> bool:
           return [验证逻辑]
   ```
3. **重构**：优化代码结构，消除重复逻辑，确保代码可读性和可扩展性，同时保持测试通过。  
**约束**：
- 测试用例需覆盖核心业务规则和边界条件（如无效输入、状态转换）。
- 测试代码应与领域逻辑保持一致，避免测试实现细节。
- 每个领域对象对应一个测试文档。

### 1.3 用例驱动验证（CDD）
**目标**：通过关键与更复杂的业务场景验证 `[模块/实体名称]` 领域模型是否能正确处理实际需求。  
**输入**：
- 关键业务场景描述（如“[场景描述1]”、“[场景描述2]”）。  
**输出**：
- 验证领域层能否独立完成业务逻辑，输出验证结果。  
**示例**：
- **场景 1：[场景名称1]**  
  通过模拟无效输入（如 `[无效输入值]`），验证 `[模块/实体名称]` 实体抛出 `ValueError`。
  ```python
  def test_[场景名称1]_failure():
      with pytest.raises(ValueError):
          [模块/实体名称](mdc:[属性1]="invalid", [属性2]="valid")
  ```
- **场景 2：[场景名称2]**  
  通过模拟业务流程，验证 `[模块/实体名称]` 状态或行为符合预期。
  ```python
  def test_[场景名称2]_success():
      obj = [模块/实体名称](mdc:[属性1]="valid", [属性2]="valid")
      obj.[核心业务方法]()
      assert obj.[状态/属性] == "[预期值]"
  ```
**约束**：
- 用例需覆盖核心业务规则（如权限校验、数据有效性、状态转换）。
- 验证过程不得引入外部依赖，保持领域层纯净。
- 用例应与领域专家确认，确保与业务语言一致。
- 每个领域对象对应一个验证用例文档。
- 每个用例的运行代码放在__main__中，确保可以独立运行。

## 2. 综合开发模式流程
以下是结合 DDD、TDD 和 CDD 的完整功能开发流程，强调步骤的顺序性和反馈循环，确保功能实现和验证的闭环：

1. **领域建模（DDD）**：
   - 与领域专家协作，识别 `[模块/实体名称]` 的核心领域对象、业务规则和边界。
   - 确定业务逻辑边界，明确实体、值对象、聚合根及其职责。
   - 输出领域模型和服务接口。
2. **编写 TDD 测试用例**：
   - 针对 `[模块/实体名称]` 的核心逻辑，编写单元测试用例。
   - 确保测试覆盖正常场景和边界条件。
1. **编写 CDD 验证用例**：
   - 基于 `[模块/实体名称]` 的关键业务场景，编写验证用例，确保覆盖核心业务规则。
   - 与领域专家确认用例的准确性和完整性。
1. **实现功能（TDD 最小代码）**：
   - 针对 TDD 测试用例，编写最小代码实现功能。
   - 运行 TDD 测试用例，检查是否通过。
5. **修复 TDD 实现**（如未通过）：
   - 如果 TDD 测试未通过，分析失败原因，修复代码实现。
   - 重复运行测试，直到所有 TDD 测试用例通过。
1. **验证 CDD 用例**：
   - 运行 CDD 验证用例，检查 `[模块/实体名称]` 领域模型是否满足业务场景需求。
   - 记录验证结果，分析是否通过。
7. **修复 CDD 实现**（如未通过）：
   - 如果 CDD 验证未通过，分析失败原因，调整领域模型或代码实现。
   - 重复运行 CDD 验证，直到所有用例通过。
1. **迭代优化**：
   - 根据反馈（领域专家、测试结果）迭代优化 `[模块/实体名称]` 的领域模型和代码。
   - 持续重构，确保代码质量和业务一致性。
   - 如果有新的需求或变更，回到步骤 1 重新建模。

## 3. 开发模式优势
- **业务一致性**：通过 DDD 和 CDD，确保 `[模块/实体名称]` 领域模型与业务语言高度一致。
- **代码质量**：通过 TDD，确保 `[模块/实体名称]` 领域逻辑的可测试性和可维护性。
- **迭代灵活性**：多维度验证（TDD、CDD）支持快速迭代和需求变更。

## 4. 流程图描述

### 4.1 需求文档
```mermaid
flowchart TD
    A0([需求收集]) --> A1["A1: 原始需求分析
    - 输入: 用户故事/业务文档
    - 输出: /req_docs/需求总结文档.md"]
    
    
    
    A1 --> B0([项目启动阶段])
```

### 4.2 项目启动

```mermaid
flowchart TD
    B0([项目启动])
    --> B2["B2: van全局分析
    - 输入: /req_docs/需求总结文档.md
    - 输出: /architectures/项目系统分析.md
    - 输出: /architectures/项目总览.md"]
    
    B2 --> B3["B3: plan任务规划
    - 输入: architectures/项目系统分析.md
    - 输出: /task_plans/任务计划.md"]
    
    B3 --> B4["B4: implement 生成开发文档
    - 输入: /task_plans/任务计划.md
    - 输出: /dev_docs/领域/领域对象文档"]
    
    B4 --> B5["B5: implement 生成测试用例
    - 输入: /core/领域/领域对象文档
    - 输出: /tdd_files/镜像测试用例
    - 输出: /cdd_files/镜像业务用例"]
    
    B5 --> C0([开发阶段])
```
 

### 4.3 开发阶段

```mermaid
flowchart TD
    C0([开发阶段])
    --> C1["C1: van 用户指定模块分析
    - 输入: 任务计划.md
    - 输出: /memory_bank/[模块 or 功能 or 文件] /van/分析结果.md
      更新 任务计划.md"]
    
    C1 --> C2{"C2: 需调整计划?"}
    C2 -->|是| C3["C3: 更新全局计划
    - 输入: 新的模块分析结果
    - 输出: /memory_bank/[模块 or 功能 or 文件] /van/调整计划.md
    - 输出: 更新 任务计划.md"]
    C2 -->|否| C4{"C4: 需补充用例?"}
    C3 --> C4
    C4 -->|是| C5["C5: 更新验证用例
    - 输入: /memory_bank/[模块 or 功能 or 文件] /van/调整计划.md
    - 输出: 更新 cdd_files/业务用例"]
    C4 -->|否| C6["C6: 任务复杂度分级
    - 输出: L1-L4评级"]
    C5 --> C6
    
    C6 --> C7{"C7: 复杂度≤L2?"}
    C7 -->|否| C8["C8: creative设计
    - 输入: 复杂需求
    - 输出: /memory_bank/[模块 or 功能 or 文件]/creative/创意方案/.md"]
    C7 -->|是| C9["C9: 实现领域对象
    - 输入: 
        · /dev_docs/镜像文档
        · /tdd_files/镜像测试用例
        · /cdd_files/镜像业务用例
    - 输出: /core/领域/领域对象代码"]
    C8 --> C9
    
    C9 --> C10["C10: 自动化验证
    - 输入: 
        · 实现代码
        · /tdd_files/镜像测试用例
        · /cdd_files/镜像业务用例
    - 输出: 无"]
    C10 --> C11{"C11: 验证通过?"}
    C11 -->|是| C12["C12: 更新进度
    - 输入: 验证结果
    - 输出: 
        · 更新 任务计划.md"]
    C11 -->|否| C13["C13: 问题修复
    - 输出: 修复方案"]
    C13 --> C9
    
    C12 --> C14{"C14: 当前模块全部完成?"}
    %% 保持指向修复后的C9
    C14 -->|否| C9
    C14 -->|是| C15["C15: 架构合规检查
    - 输入: 
        · 实现代码
        · /architectures/架构规范文档
    - 输出: 架构审计报告"]
    
    C15 --> C16["C16: reflect审查
    - 输入: 
        · 审计报告
        · /dev_docs/开发文档
    - 输出: /memory_bank/模块/reflect/review_report.md"]
    
    C16 --> C17{"C17: 审查通过?"}
    C17 -->|是| C18["C18: archive归档
    - 输入: 
        · 所有开发文档
        · 测试报告
        · 审查报告
    - 输出: /memory_bank/模块/archive/文档包"]
    C17 -->|否| C19["C19: 重新设计
    - 输出: 修改方案→返回C8"]
```
