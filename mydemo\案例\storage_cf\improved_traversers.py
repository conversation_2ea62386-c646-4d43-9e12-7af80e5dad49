"""
改进的遍历器实现
展示如何更充分地利用mandala1的内置功能
"""

import os
import sys
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref
except ImportError:
    # 如果mandala1不可用，添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref


class ImprovedCFTraverser:
    """改进的ComputationFrame遍历器，更充分利用mandala1内置功能"""
    
    def traverse_computation_graph_improved(self, cf: ComputationFrame) -> List[Dict]:
        """
        使用mandala1内置方法的改进遍历
        """
        if not cf.fnames:
            return []
            
        result = []
        
        # 1. 使用mandala1的拓扑排序
        try:
            topo_order = cf.topsort_modulo_sccs()
        except Exception:
            topo_order = list(cf.fnames)
        
        # 2. 使用calls_by_func获取调用信息
        try:
            calls_dict = cf.calls_by_func()
        except Exception:
            calls_dict = {}
        
        # 3. 为每个函数提取信息
        for fname in cf.fnames:
            node_info = self._extract_info_using_builtin_methods(cf, fname, calls_dict)
            result.append(node_info)
            
        # 4. 按深度排序
        result.sort(key=lambda x: (x['depth'], x['function_name']))
        return result
    
    def _extract_info_using_builtin_methods(self, cf: ComputationFrame, fname: str, 
                                          calls_dict: Dict) -> Dict[str, Any]:
        """
        使用mandala1内置方法提取节点信息
        """
        info = {
            'function_name': fname,
            'depth': 0,
            'call_count': 0,
            'func_table_info': None,
            'neighbors_info': {},
            'calls_sample': []
        }
        
        try:
            # 1. 使用get_func_table获取详细信息
            func_table = cf.get_func_table(fname)
            if not func_table.empty:
                info['func_table_info'] = {
                    'shape': func_table.shape,
                    'columns': list(func_table.columns),
                    'input_cols': [col for col in func_table.columns if not col.startswith('output_')],
                    'output_cols': [col for col in func_table.columns if col.startswith('output_')]
                }
            
            # 2. 使用in_neighbors和out_neighbors
            info['neighbors_info'] = {
                'in_neighbors': list(cf.in_neighbors(fname)),
                'out_neighbors': list(cf.out_neighbors(fname)),
                'func_dependencies': [n for n in cf.in_neighbors(fname) if n in cf.fnames],
                'var_inputs': [n for n in cf.in_neighbors(fname) if n in cf.vnames],
                'var_outputs': [n for n in cf.out_neighbors(fname) if n in cf.vnames]
            }
            
            # 3. 计算深度（基于函数依赖）
            func_deps = info['neighbors_info']['func_dependencies']
            if not func_deps:
                info['depth'] = 0
            else:
                # 简化的深度计算：依赖函数的最大深度 + 1
                max_dep_depth = 0
                for dep in func_deps:
                    try:
                        upstream_cf = cf.upstream(dep)
                        dep_depth = len(upstream_cf.fnames) - 1
                        max_dep_depth = max(max_dep_depth, dep_depth)
                    except Exception:
                        pass
                info['depth'] = max_dep_depth + 1
            
            # 4. 使用calls_by_func获取调用信息
            if fname in calls_dict:
                calls = calls_dict[fname]
                info['call_count'] = len(calls)
                
                # 获取前几个调用作为示例
                for call in list(calls)[:2]:
                    call_info = {
                        'call_id': call.hid[:8] if hasattr(call, 'hid') else 'unknown',
                        'inputs': list(call.inputs.keys()),
                        'outputs': list(call.outputs.keys())
                    }
                    info['calls_sample'].append(call_info)
                    
        except Exception as e:
            print(f"提取 {fname} 信息时出错: {e}")
            
        return info
    
    def print_improved_computation_graph(self, cf: ComputationFrame):
        """
        打印改进的计算图遍历结果
        """
        print("=" * 60)
        print("改进的计算图遍历结果")
        print("（充分利用mandala1内置功能）")
        print("=" * 60)
        
        # 使用mandala1的统计功能
        try:
            func_stats = cf.get_func_stats()
            print(f"函数统计: {func_stats}")
        except Exception:
            pass
        
        try:
            var_stats = cf.get_var_stats()
            print(f"变量统计: {var_stats}")
        except Exception:
            pass
        
        # 遍历结果
        traversal_result = self.traverse_computation_graph_improved(cf)
        
        if not traversal_result:
            print("计算图中没有函数节点")
            return
        
        # 按深度分组显示
        layers = defaultdict(list)
        for node_info in traversal_result:
            layers[node_info['depth']].append(node_info)
        
        for depth in sorted(layers.keys()):
            print(f"\n深度 {depth}:")
            nodes_at_depth = layers[depth]
            
            for i, node_info in enumerate(nodes_at_depth):
                is_last = (i == len(nodes_at_depth) - 1)
                prefix = "└─ " if is_last else "├─ "
                
                print(f"{prefix}函数: {node_info['function_name']}")
                
                detail_prefix = "   " if is_last else "│  "
                print(f"{detail_prefix}├─ 调用次数: {node_info['call_count']}")
                
                # 显示函数表信息
                if node_info['func_table_info']:
                    ft_info = node_info['func_table_info']
                    print(f"{detail_prefix}├─ 函数表: {ft_info['shape']}")
                    if ft_info['input_cols']:
                        print(f"{detail_prefix}├─ 输入列: {ft_info['input_cols']}")
                    if ft_info['output_cols']:
                        print(f"{detail_prefix}├─ 输出列: {ft_info['output_cols']}")
                
                # 显示邻居信息
                neighbors = node_info['neighbors_info']
                if neighbors['func_dependencies']:
                    print(f"{detail_prefix}├─ 依赖函数: {neighbors['func_dependencies']}")
                if neighbors['var_inputs']:
                    print(f"{detail_prefix}├─ 输入变量: {neighbors['var_inputs'][:3]}")
                if neighbors['var_outputs']:
                    print(f"{detail_prefix}├─ 输出变量: {neighbors['var_outputs'][:3]}")
                
                # 显示调用示例
                if node_info['calls_sample']:
                    print(f"{detail_prefix}└─ 调用示例:")
                    for j, call_info in enumerate(node_info['calls_sample']):
                        is_last_call = (j == len(node_info['calls_sample']) - 1)
                        call_symbol = "└─" if is_last_call else "├─"
                        print(f"{detail_prefix}   {call_symbol} {call_info['call_id']}: {call_info['inputs']} -> {call_info['outputs']}")
                else:
                    print(f"{detail_prefix}└─ 调用示例: 无")
        
        print(f"\n总计: {len(traversal_result)} 个函数节点，{len(layers)} 个深度层级")
        print("=" * 60)


class ImprovedStorageTraverser:
    """改进的Storage遍历器，更充分利用mandala1内置功能"""
    
    def traverse_execution_improved(self, storage: Storage) -> List[Dict]:
        """
        使用mandala1内置方法的改进执行顺序遍历
        """
        # 1. 尝试创建一个包含所有调用的ComputationFrame
        cf = self._create_comprehensive_cf(storage)
        
        if cf is None:
            return []
        
        # 2. 使用calls_by_func获取所有调用
        try:
            calls_dict = cf.calls_by_func()
        except Exception:
            return []
        
        # 3. 收集所有调用并排序
        all_calls = []
        for fname, calls in calls_dict.items():
            all_calls.extend(calls)
        
        # 按hid排序（时间顺序的近似）
        all_calls.sort(key=lambda call: call.hid)
        
        # 4. 提取执行信息
        execution_info = []
        for i, call in enumerate(all_calls):
            call_info = self._extract_call_info_improved(storage, call, i, cf)
            execution_info.append(call_info)
            
        return execution_info
    
    def _create_comprehensive_cf(self, storage: Storage) -> Optional[ComputationFrame]:
        """
        创建包含尽可能多调用信息的ComputationFrame
        """
        try:
            # 尝试从storage的缓存获取一些引用
            if hasattr(storage, 'ref_cache'):
                cache = storage.ref_cache
                if hasattr(cache, 'items'):
                    refs = list(cache.items())
                    if refs:
                        # 使用第一个引用创建CF
                        first_ref = refs[0][1]
                        cf = storage.cf(first_ref)
                        return cf.expand_all()
                        
        except Exception as e:
            print(f"创建综合CF失败: {e}")
            
        return None
    
    def _extract_call_info_improved(self, storage: Storage, call: Call, 
                                  execution_index: int, cf: ComputationFrame) -> Dict[str, Any]:
        """
        使用mandala1方法提取调用信息
        """
        info = {
            'execution_index': execution_index + 1,
            'function_name': call.op.name,
            'depth': 0,
            'inputs': {},
            'outputs': {},
            'call_id': call.hid[:8] if call.hid else 'unknown'
        }
        
        try:
            # 1. 使用storage.unwrap获取参数值
            for param_name, ref in call.inputs.items():
                try:
                    value = storage.unwrap(ref)
                    info['inputs'][param_name] = self._format_value(value)
                except Exception:
                    info['inputs'][param_name] = f"Ref({ref.hid[:8]})"
            
            for param_name, ref in call.outputs.items():
                try:
                    value = storage.unwrap(ref)
                    info['outputs'][param_name] = self._format_value(value)
                except Exception:
                    info['outputs'][param_name] = f"Ref({ref.hid[:8]})"
            
            # 2. 使用CF的upstream分析计算深度
            if cf and call.op.name in cf.fnames:
                try:
                    upstream_cf = cf.upstream(call.op.name)
                    info['depth'] = len(upstream_cf.fnames) - 1
                except Exception:
                    info['depth'] = 0
                    
        except Exception as e:
            print(f"提取调用信息失败: {e}")
            
        return info
    
    def _format_value(self, value: Any) -> str:
        """格式化值用于显示"""
        if value is None:
            return "None"
        elif isinstance(value, (int, float, bool)):
            return str(value)
        elif isinstance(value, str):
            return f"'{value}'" if len(value) <= 20 else f"'{value[:17]}...'"
        elif isinstance(value, (list, tuple)):
            return str(value) if len(value) <= 3 else f"[{len(value)} items]"
        elif isinstance(value, dict):
            return str(value) if len(value) <= 2 else f"{{{len(value)} items}}"
        else:
            return f"{type(value).__name__}(...)"
    
    def print_improved_execution_order(self, storage: Storage):
        """
        打印改进的执行顺序遍历结果
        """
        print("=" * 60)
        print("改进的执行顺序遍历结果")
        print("（充分利用mandala1内置功能）")
        print("=" * 60)
        
        execution_info = self.traverse_execution_improved(storage)
        
        if not execution_info:
            print("没有找到函数调用记录")
            return
        
        # 显示摘要信息
        unique_functions = set(info['function_name'] for info in execution_info)
        print(f"执行摘要:")
        print(f"  - 总调用数: {len(execution_info)}")
        print(f"  - 唯一函数数: {len(unique_functions)}")
        print(f"  - 函数列表: {', '.join(sorted(unique_functions))}")
        
        print(f"\n执行顺序详情:")
        
        for call_info in execution_info[:15]:  # 限制显示数量
            depth_prefix = "  " * call_info['depth']
            print(f"{call_info['execution_index']:3d}. {depth_prefix}[深度{call_info['depth']}] {call_info['function_name']}")
            
            # 显示输入参数
            if call_info['inputs']:
                inputs_str = ", ".join([f"{k}={v}" for k, v in call_info['inputs'].items()])
                print(f"     {depth_prefix}├─ 输入: {inputs_str}")
            
            # 显示输出参数
            if call_info['outputs']:
                outputs_str = ", ".join([f"{k}={v}" for k, v in call_info['outputs'].items()])
                print(f"     {depth_prefix}└─ 输出: {outputs_str}")
            else:
                print(f"     {depth_prefix}└─ 输出: (无)")
        
        if len(execution_info) > 15:
            print(f"\n... 还有 {len(execution_info) - 15} 个调用未显示")
        
        print(f"\n总计: {len(execution_info)} 个函数调用")
        print("=" * 60)


def demo_improved_traversers():
    """演示改进的遍历器功能"""
    print("🚀 改进的栈重放功能演示")
    print("充分利用mandala1内置功能")
    print("=" * 60)
    
    # 创建示例函数
    @op
    def add(x, y):
        return x + y
    
    @op
    def multiply(x, y):
        return x * y
    
    @op
    def power(x, n):
        return x ** n
    
    # 执行计算
    storage = Storage()
    
    with storage:
        a = add(1, 2)
        b = add(3, 4)
        c = multiply(a, b)
        d = power(c, 2)
    
    # 创建计算框架
    cf = storage.cf(d).expand_back(recursive=True)
    
    # 使用改进的遍历器
    cf_traverser = ImprovedCFTraverser()
    cf_traverser.print_improved_computation_graph(cf)
    
    storage_traverser = ImprovedStorageTraverser()
    storage_traverser.print_improved_execution_order(storage)


if __name__ == "__main__":
    demo_improved_traversers()
