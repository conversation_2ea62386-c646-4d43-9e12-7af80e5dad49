"""
Storage 深度理解用例
展示 Storage 的完整生命周期和核心功能

功能覆盖：
1. Storage的创建和配置
2. 版本管理和依赖跟踪
3. 缓存机制和数据持久化
4. 函数调用的记忆化
5. 引用管理和对象存储
6. 数据库操作和事务管理
7. 完整的程序运行周期观察

基于mandala1框架，不创建新类，充分利用现有功能
"""

import os
import sys
import time
import tempfile
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.model import Call, Ref
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from mandala1.imports import Storage, op
    from mandala1.model import Call, Ref


class StorageDeepUnderstanding:
    """Storage深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.current_phase = 0
        self.phase_results = {}
        
    def print_phase_header(self, phase_num: int, title: str, description: str):
        """打印阶段标题"""
        self.current_phase = phase_num
        print("\n" + "=" * 80)
        print(f"💾 阶段 {phase_num}: {title}")
        print("=" * 80)
        print(f"📝 描述: {description}")
        print("-" * 80)
    
    def print_storage_info(self, storage: Storage, title: str = "Storage信息"):
        """打印Storage的详细信息"""
        print(f"\n📊 {title}:")
        
        # 基本信息
        print(f"  🗄️  数据库路径: {storage.db.db_path}")
        print(f"  🔄 版本控制: {'启用' if storage.versioned else '禁用'}")
        print(f"  📁 溢出目录: {storage.overflow_dir if storage.overflow_dir else '未设置'}")
        print(f"  📏 溢出阈值: {storage.overflow_threshold_MB}MB")
        
        # 缓存信息
        try:
            call_cache_size = len(storage.call_cache.cache) if hasattr(storage.call_cache, 'cache') else 0
            atoms_cache_size = len(storage.atoms.cache) if hasattr(storage.atoms, 'cache') else 0
            ops_cache_size = len(storage.ops.cache) if hasattr(storage.ops, 'cache') else 0
            
            print(f"  🔄 调用缓存: {call_cache_size} 个条目")
            print(f"  ⚛️  原子缓存: {atoms_cache_size} 个条目")
            print(f"  ⚙️  操作缓存: {ops_cache_size} 个条目")
        except Exception as e:
            print(f"  ❌ 缓存信息获取失败: {e}")
        
        # 版本信息
        if storage.versioned:
            try:
                versioner = storage.versioner
                print(f"  📦 依赖包: {versioner.deps_package if hasattr(versioner, 'deps_package') else '未设置'}")
                print(f"  🔍 跟踪全局变量: {'是' if storage._track_globals else '否'}")
                print(f"  ⚠️  严格跟踪: {'是' if storage._strict_tracing else '否'}")
            except Exception as e:
                print(f"  ❌ 版本信息获取失败: {e}")
    
    def demonstrate_storage_creation(self):
        """阶段1: 演示Storage的创建和配置"""
        self.print_phase_header(1, "Storage创建与配置", 
                               "展示如何创建和配置不同类型的Storage")
        
        print("🔧 Storage创建演示:")
        
        # 1. 内存存储
        print("\n1️⃣ 内存存储:")
        memory_storage = Storage()
        self.print_storage_info(memory_storage, "内存Storage")
        
        # 2. 持久化存储
        print("\n2️⃣ 持久化存储:")
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "demo_storage.db")
        
        persistent_storage = Storage(
            db_path=db_path,
            deps_path='__main__',  # 启用版本控制
            overflow_dir=os.path.join(self.temp_dir, "overflow"),
            overflow_threshold_MB=1.0,  # 1MB阈值用于演示
            track_globals=True,
            strict_tracing=False
        )
        self.print_storage_info(persistent_storage, "持久化Storage")
        
        # 3. 配置对比
        print("\n3️⃣ 配置对比:")
        print("  内存存储 vs 持久化存储:")
        print(f"    数据库: 内存 vs 文件({db_path})")
        print(f"    版本控制: 禁用 vs 启用")
        print(f"    溢出处理: 禁用 vs 启用")
        
        self.storage = persistent_storage
        self.phase_results[1] = {
            'memory_storage': memory_storage,
            'persistent_storage': persistent_storage,
            'db_path': db_path
        }
        
        return persistent_storage
    
    def demonstrate_memoization(self, storage: Storage):
        """阶段2: 演示函数记忆化和缓存机制"""
        self.print_phase_header(2, "函数记忆化与缓存", 
                               "展示Storage如何实现函数调用的记忆化和缓存")
        
        # 定义测试函数
        @op
        def expensive_computation(n: int, delay: float = 0.1):
            """模拟耗时计算"""
            print(f"    🔄 执行耗时计算: n={n}, delay={delay}")
            time.sleep(delay)
            return n ** 2 + n + 1
        
        @op
        def data_processing(data: list, operation: str = "sum"):
            """数据处理函数"""
            print(f"    🔄 处理数据: {len(data)} 个元素, 操作={operation}")
            time.sleep(0.05)
            if operation == "sum":
                return sum(data)
            elif operation == "mean":
                return sum(data) / len(data) if data else 0
            elif operation == "max":
                return max(data) if data else 0
            return data
        
        print("🔧 记忆化演示:")
        
        # 1. 首次调用（实际执行）
        print("\n1️⃣ 首次调用（实际执行）:")
        with storage:
            start_time = time.time()
            result1 = expensive_computation(5, 0.2)
            result2 = data_processing([1, 2, 3, 4, 5], "sum")
            first_call_time = time.time() - start_time
            
            print(f"  ✅ 计算结果1: {storage.unwrap(result1)}")
            print(f"  ✅ 计算结果2: {storage.unwrap(result2)}")
            print(f"  ⏱️  首次调用耗时: {first_call_time:.3f}秒")
        
        # 2. 重复调用（从缓存加载）
        print("\n2️⃣ 重复调用（从缓存加载）:")
        with storage:
            start_time = time.time()
            result1_cached = expensive_computation(5, 0.2)
            result2_cached = data_processing([1, 2, 3, 4, 5], "sum")
            cached_call_time = time.time() - start_time
            
            print(f"  ✅ 缓存结果1: {storage.unwrap(result1_cached)}")
            print(f"  ✅ 缓存结果2: {storage.unwrap(result2_cached)}")
            print(f"  ⏱️  缓存调用耗时: {cached_call_time:.3f}秒")
            print(f"  🚀 加速比: {first_call_time/cached_call_time:.1f}x")
        
        # 3. 引用比较
        print("\n3️⃣ 引用比较:")
        print(f"  🔗 result1 == result1_cached: {result1.hid == result1_cached.hid}")
        print(f"  🔗 result2 == result2_cached: {result2.hid == result2_cached.hid}")
        print(f"  📋 result1 引用ID: {result1.hid[:16]}...")
        print(f"  📋 result2 引用ID: {result2.hid[:16]}...")
        
        # 4. 缓存状态检查
        print("\n4️⃣ 缓存状态检查:")
        self.print_storage_info(storage, "记忆化后的Storage状态")
        
        self.phase_results[2] = {
            'results': [result1, result2],
            'timing': {
                'first_call': first_call_time,
                'cached_call': cached_call_time,
                'speedup': first_call_time/cached_call_time
            }
        }
        
        return storage
    
    def demonstrate_version_management(self, storage: Storage):
        """阶段3: 演示版本管理和依赖跟踪"""
        self.print_phase_header(3, "版本管理与依赖跟踪", 
                               "展示Storage如何管理函数版本和跟踪依赖变化")
        
        print("🔧 版本管理演示:")
        
        # 1. 定义初始版本的函数
        print("\n1️⃣ 初始版本函数:")
        
        # 全局配置变量
        GLOBAL_MULTIPLIER = 2.0
        
        @op
        def versioned_function(x: int):
            """版本化函数 V1"""
            print(f"    🔄 执行版本化函数 V1: x={x}")
            return x * GLOBAL_MULTIPLIER
        
        with storage:
            v1_result = versioned_function(10)
            print(f"  ✅ V1结果: {storage.unwrap(v1_result)}")
        
        # 2. 检查版本信息
        print("\n2️⃣ 版本信息检查:")
        if storage.versioned:
            try:
                versions = storage.versions(versioned_function)
                print(f"  📦 函数版本数: {len(versions)}")
                for i, version in enumerate(versions):
                    print(f"    版本{i+1}: {version[:16]}...")
            except Exception as e:
                print(f"  ❌ 版本信息获取失败: {e}")
        
        # 3. 修改全局变量（模拟依赖变化）
        print("\n3️⃣ 修改依赖（全局变量）:")
        global GLOBAL_MULTIPLIER
        GLOBAL_MULTIPLIER = 3.0
        print(f"  🔄 修改全局变量: GLOBAL_MULTIPLIER = {GLOBAL_MULTIPLIER}")
        
        # 重新定义函数（模拟代码变化）
        @op
        def versioned_function(x: int):
            """版本化函数 V2"""
            print(f"    🔄 执行版本化函数 V2: x={x}")
            return x * GLOBAL_MULTIPLIER + 1  # 添加了+1
        
        with storage:
            v2_result = versioned_function(10)
            print(f"  ✅ V2结果: {storage.unwrap(v2_result)}")
        
        # 4. 版本对比
        print("\n4️⃣ 版本对比:")
        print(f"  📊 V1结果: {storage.unwrap(v1_result)}")
        print(f"  📊 V2结果: {storage.unwrap(v2_result)}")
        print(f"  🔄 结果变化: {storage.unwrap(v2_result) - storage.unwrap(v1_result)}")
        
        # 5. 更新后的版本信息
        print("\n5️⃣ 更新后的版本信息:")
        if storage.versioned:
            try:
                versions_after = storage.versions(versioned_function)
                print(f"  📦 更新后版本数: {len(versions_after)}")
                print(f"  🆕 新增版本: {'是' if len(versions_after) > len(versions) else '否'}")
            except Exception as e:
                print(f"  ❌ 版本信息获取失败: {e}")
        
        self.phase_results[3] = {
            'v1_result': v1_result,
            'v2_result': v2_result,
            'versions_before': versions if 'versions' in locals() else [],
            'versions_after': versions_after if 'versions_after' in locals() else []
        }
        
        return storage

    def demonstrate_reference_management(self, storage: Storage):
        """阶段4: 演示引用管理和对象存储"""
        self.print_phase_header(4, "引用管理与对象存储",
                               "展示Storage如何管理引用和存储不同类型的对象")

        print("🔧 引用管理演示:")

        # 1. 不同类型对象的存储
        print("\n1️⃣ 不同类型对象存储:")

        @op
        def create_various_objects():
            """创建各种类型的对象"""
            print("    🔄 创建各种类型对象")
            return {
                'number': 42,
                'string': "Hello, Storage!",
                'list': [1, 2, 3, 4, 5],
                'dict': {'a': 1, 'b': 2, 'c': 3},
                'nested': {'data': [1, 2, {'inner': 'value'}]}
            }

        @op
        def create_large_object(size: int = 1000):
            """创建大对象（可能触发溢出存储）"""
            print(f"    🔄 创建大对象，大小: {size}")
            return list(range(size))

        with storage:
            various_obj = create_various_objects()
            large_obj = create_large_object(10000)  # 大对象

            print(f"  ✅ 各种对象: {storage.unwrap(various_obj)}")
            print(f"  ✅ 大对象长度: {len(storage.unwrap(large_obj))}")

        # 2. 引用操作
        print("\n2️⃣ 引用操作:")
        print(f"  🔗 各种对象引用: {various_obj}")
        print(f"  🔗 大对象引用: {large_obj}")
        print(f"  📋 引用ID: {various_obj.hid[:16]}...")
        print(f"  📋 内容ID: {various_obj.cid[:16]}...")

        # 3. 引用解包和包装
        print("\n3️⃣ 引用解包和包装:")
        unwrapped = storage.unwrap(various_obj)
        print(f"  📦 解包对象类型: {type(unwrapped)}")
        print(f"  📦 解包对象内容: {unwrapped}")

        # 4. 引用创建者查询
        print("\n4️⃣ 引用创建者查询:")
        try:
            creator_call = storage.get_ref_creator(various_obj)
            print(f"  👤 创建者调用: {creator_call}")
            print(f"  ⚙️  创建者函数: {creator_call.op.name}")
            print(f"  📥 输入参数: {creator_call.inputs}")
            print(f"  📤 输出参数: {creator_call.outputs}")
        except Exception as e:
            print(f"  ❌ 创建者查询失败: {e}")

        # 5. 内存管理
        print("\n5️⃣ 内存管理:")
        try:
            # 加载到内存
            loaded_ref = storage.attach(various_obj, inplace=False)
            print(f"  💾 加载到内存: {loaded_ref}")

            # 检查是否在内存中
            in_memory = hasattr(loaded_ref, 'obj') and loaded_ref.obj is not None
            print(f"  🧠 是否在内存: {'是' if in_memory else '否'}")
        except Exception as e:
            print(f"  ❌ 内存管理操作失败: {e}")

        self.phase_results[4] = {
            'various_obj': various_obj,
            'large_obj': large_obj,
            'creator_call': creator_call if 'creator_call' in locals() else None
        }

        return storage

    def demonstrate_database_operations(self, storage: Storage):
        """阶段5: 演示数据库操作和事务管理"""
        self.print_phase_header(5, "数据库操作与事务管理",
                               "展示Storage的数据库操作和事务处理能力")

        print("🔧 数据库操作演示:")

        # 1. 数据库连接信息
        print("\n1️⃣ 数据库连接信息:")
        print(f"  🗄️  数据库路径: {storage.db.db_path}")
        print(f"  🔌 连接状态: {'活跃' if storage.db else '未连接'}")

        # 2. 表结构查询
        print("\n2️⃣ 数据库表结构:")
        try:
            with storage.conn() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"  📋 数据库表: {[table[0] for table in tables]}")

                # 查询调用表的记录数
                cursor.execute("SELECT COUNT(*) FROM calls;")
                call_count = cursor.fetchone()[0]
                print(f"  📊 调用记录数: {call_count}")

                # 查询原子表的记录数
                cursor.execute("SELECT COUNT(*) FROM atoms;")
                atom_count = cursor.fetchone()[0]
                print(f"  📊 原子记录数: {atom_count}")
        except Exception as e:
            print(f"  ❌ 数据库查询失败: {e}")

        # 3. 事务操作演示
        print("\n3️⃣ 事务操作演示:")

        @op
        def transactional_operation(data: list):
            """事务性操作"""
            print(f"    🔄 执行事务性操作: {len(data)} 个元素")
            # 模拟可能失败的操作
            if len(data) > 100:
                raise ValueError("数据太大，操作失败")
            return sum(data)

        # 成功的事务
        print("  ✅ 成功事务:")
        try:
            with storage:
                success_result = transactional_operation([1, 2, 3, 4, 5])
                print(f"    结果: {storage.unwrap(success_result)}")
        except Exception as e:
            print(f"    ❌ 事务失败: {e}")

        # 4. 数据持久化验证
        print("\n4️⃣ 数据持久化验证:")
        try:
            # 提交当前状态
            storage.commit()
            print("  💾 数据已提交到磁盘")

            # 检查文件大小
            if os.path.exists(storage.db.db_path):
                file_size = os.path.getsize(storage.db.db_path)
                print(f"  📏 数据库文件大小: {file_size} 字节")

        except Exception as e:
            print(f"  ❌ 持久化验证失败: {e}")

        # 5. 缓存统计
        print("\n5️⃣ 缓存统计:")
        self.print_storage_info(storage, "最终Storage状态")

        self.phase_results[5] = {
            'tables': tables if 'tables' in locals() else [],
            'call_count': call_count if 'call_count' in locals() else 0,
            'atom_count': atom_count if 'atom_count' in locals() else 0,
            'db_size': file_size if 'file_size' in locals() else 0
        }

        return storage

    def demonstrate_advanced_features(self, storage: Storage):
        """阶段6: 演示高级功能"""
        self.print_phase_header(6, "高级功能演示",
                               "展示Storage的高级功能和特性")

        print("🔧 高级功能演示:")

        # 1. 批量操作
        print("\n1️⃣ 批量操作:")

        @op
        def batch_processor(items: list, operation: str):
            """批量处理器"""
            print(f"    🔄 批量处理: {len(items)} 个项目, 操作={operation}")
            if operation == "square":
                return [x**2 for x in items]
            elif operation == "double":
                return [x*2 for x in items]
            return items

        with storage:
            batch_data = list(range(1, 11))
            squared = batch_processor(batch_data, "square")
            doubled = batch_processor(batch_data, "double")

            print(f"  ✅ 原始数据: {batch_data}")
            print(f"  ✅ 平方结果: {storage.unwrap(squared)}")
            print(f"  ✅ 双倍结果: {storage.unwrap(doubled)}")

        # 2. 条件执行
        print("\n2️⃣ 条件执行:")

        @op
        def conditional_compute(x: int, threshold: int = 5):
            """条件计算"""
            print(f"    🔄 条件计算: x={x}, threshold={threshold}")
            if x > threshold:
                return x ** 2
            else:
                return x * 2

        with storage:
            results = []
            for i in range(1, 8):
                result = conditional_compute(i)
                results.append((i, storage.unwrap(result)))

            print("  📊 条件执行结果:")
            for i, result in results:
                condition = "平方" if i > 5 else "双倍"
                print(f"    {i} -> {result} ({condition})")

        # 3. 函数组合
        print("\n3️⃣ 函数组合:")

        @op
        def step1(x: int):
            """步骤1"""
            print(f"    🔄 步骤1: x={x}")
            return x + 10

        @op
        def step2(x: int):
            """步骤2"""
            print(f"    🔄 步骤2: x={x}")
            return x * 3

        @op
        def step3(x: int):
            """步骤3"""
            print(f"    🔄 步骤3: x={x}")
            return x - 5

        with storage:
            # 函数组合流水线
            input_val = 5
            result1 = step1(input_val)
            result2 = step2(storage.unwrap(result1))
            final_result = step3(storage.unwrap(result2))

            print(f"  🔄 流水线: {input_val} -> {storage.unwrap(result1)} -> {storage.unwrap(result2)} -> {storage.unwrap(final_result)}")

        # 4. 性能分析
        print("\n4️⃣ 性能分析:")
        start_time = time.time()

        with storage:
            # 重复执行已缓存的操作
            for _ in range(100):
                _ = conditional_compute(3)
                _ = batch_processor([1, 2, 3], "square")

        cached_time = time.time() - start_time
        print(f"  ⏱️  100次缓存调用耗时: {cached_time:.4f}秒")
        print(f"  🚀 平均每次调用: {cached_time/100:.6f}秒")

        self.phase_results[6] = {
            'batch_results': [squared, doubled],
            'conditional_results': results,
            'pipeline_result': final_result,
            'performance': {
                'cached_time': cached_time,
                'avg_call_time': cached_time/100
            }
        }

        return storage


def main():
    """主函数：运行Storage深度理解演示"""
    print("💾 Storage 深度理解用例")
    print("=" * 80)
    print("📖 本演示将展示Storage的完整生命周期和核心功能")
    print("🎯 目标：深入理解Storage的版本管理、缓存机制、数据持久化等")
    print("=" * 80)
    
    demo = StorageDeepUnderstanding()
    
    try:
        # 阶段1: Storage创建
        storage = demo.demonstrate_storage_creation()
        
        # 阶段2: 记忆化
        demo.demonstrate_memoization(storage)
        
        # 阶段3: 版本管理
        demo.demonstrate_version_management(storage)

        # 阶段4: 引用管理
        demo.demonstrate_reference_management(storage)

        # 阶段5: 数据库操作
        demo.demonstrate_database_operations(storage)

        # 阶段6: 高级功能
        demo.demonstrate_advanced_features(storage)

        print("\n" + "=" * 80)
        print("🎉 Storage深度理解演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        for phase, results in demo.phase_results.items():
            print(f"  阶段{phase}: ✅ 完成")

        print("\n📈 完整生命周期覆盖:")
        print("  ✅ Storage创建和配置")
        print("  ✅ 函数记忆化和缓存")
        print("  ✅ 版本管理和依赖跟踪")
        print("  ✅ 引用管理和对象存储")
        print("  ✅ 数据库操作和事务管理")
        print("  ✅ 高级功能和性能优化")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if demo.temp_dir and os.path.exists(demo.temp_dir):
            import shutil
            try:
                shutil.rmtree(demo.temp_dir)
                print(f"\n🧹 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


if __name__ == "__main__":
    main()
