# storage_utils.py 文档

## 文件内容与作用总体说明

`storage_utils.py` 文件是 mandala 框架的存储工具模块，提供了存储系统的底层实现组件。该文件定义了数据库适配器、各种存储抽象类和具体实现类，包括字典存储、调用存储等。这些类构成了 Storage 类的基础设施，负责数据的持久化、缓存管理、事务处理等核心功能。

## 文件内的所有变量的作用与说明

### 全局变量
- `tqdm`: 进度条库，用于显示长时间操作的进度
- `uuid`: UUID 生成库，用于创建唯一标识符
- `joblib`: 序列化库，用于数据持久化
- `sqlite3`: SQLite 数据库库
- `ABC`, `abstractmethod`: 抽象基类相关

### 类实例变量
各个存储类都有自己的实例变量，在相应的类方法中详细说明。

## 文件内的所有函数的作用与说明

### DBAdapter 类（数据库适配器）

#### __init__(self, db_path: str = ":memory:")

**作用**: 初始化数据库适配器，设置数据库连接和配置

**输入参数**:
- `db_path`: str，数据库路径，默认为内存数据库

**内部调用的函数**：
- uuid.uuid4: 生成唯一标识符
- sqlite3.connect: 创建数据库连接
- os.path.exists: 检查文件是否存在
- conn.execute: 执行 SQL 命令

**输出参数**: 无返回值

**其他说明**：
- 对于内存数据库，维护单一连接避免冲突
- 对于磁盘数据库，设置 WAL 模式和增量清理

#### in_memory(self) -> bool

**作用**: 检查数据库是否为内存数据库

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: bool - 是否为内存数据库

#### conn(self) -> sqlite3.Connection

**作用**: 获取数据库连接对象

**输入参数**: 无

**内部调用的函数**：
- sqlite3.connect: 创建新连接（磁盘数据库）

**输出参数**: sqlite3.Connection - 数据库连接对象

**其他说明**：内存数据库返回缓存的连接，磁盘数据库创建新连接

### 工具函数

#### is_in_memory_db(conn)

**作用**: 检查数据库连接是否为内存数据库

**输入参数**:
- `conn`: sqlite3.Connection，数据库连接

**内部调用的函数**：
- conn.execute: 执行 PRAGMA 命令
- cursor.fetchall: 获取查询结果

**输出参数**: bool - 是否为内存数据库

#### transaction(method)

**作用**: 事务装饰器，为类方法提供事务支持

**输入参数**:
- `method`: 要装饰的方法

**内部调用的函数**：
- logging.debug: 记录调试信息
- conn.execute: 执行事务命令

**输出参数**: 装饰后的方法

**其他说明**：
- 支持嵌套事务
- 自动处理事务的开始、提交和回滚

### DictStorage 抽象基类

#### get(self, key: str) -> Any

**作用**: 抽象方法，获取指定键的值

**输入参数**:
- `key`: str，要获取的键

**内部调用的函数**：
- 子类实现

**输出参数**: Any - 键对应的值

#### set(self, key: str, value: Any) -> None

**作用**: 抽象方法，设置键值对

**输入参数**:
- `key`: str，要设置的键
- `value`: Any，要设置的值

**内部调用的函数**：
- 子类实现

**输出参数**: 无返回值

#### drop(self, key: str) -> None

**作用**: 抽象方法，删除指定键

**输入参数**:
- `key`: str，要删除的键

**内部调用的函数**：
- 子类实现

**输出参数**: 无返回值

#### load_all(self) -> Dict[str, Any]

**作用**: 抽象方法，加载所有键值对

**输入参数**: 无

**内部调用的函数**：
- 子类实现

**输出参数**: Dict[str, Any] - 所有键值对

#### exists(self, key: str) -> bool

**作用**: 抽象方法，检查键是否存在

**输入参数**:
- `key`: str，要检查的键

**内部调用的函数**：
- 子类实现

**输出参数**: bool - 键是否存在

#### __getitem__(self, key: str) -> Any

**作用**: 实现字典式访问，支持 storage[key] 语法

**输入参数**:
- `key`: str，要获取的键

**内部调用的函数**：
- self.get: 调用 get 方法

**输出参数**: Any - 键对应的值

#### __setitem__(self, key: str, value: Any) -> None

**作用**: 实现字典式设置，支持 storage[key] = value 语法

**输入参数**:
- `key`: str，要设置的键
- `value`: Any，要设置的值

**内部调用的函数**：
- self.set: 调用 set 方法

**输出参数**: 无返回值

#### __contains__(self, key: str) -> bool

**作用**: 实现 in 操作符，支持 key in storage 语法

**输入参数**:
- `key`: str，要检查的键

**内部调用的函数**：
- self.exists: 调用 exists 方法

**输出参数**: bool - 键是否存在

#### __len__(self) -> int

**作用**: 返回存储中键的数量

**输入参数**: 无

**内部调用的函数**：
- self.keys: 获取所有键
- len: 计算长度

**输出参数**: int - 键的数量

### JoblibDictStorage 类（Joblib 字典存储）

#### __init__(self, root: str)

**作用**: 初始化 Joblib 字典存储，使用文件系统存储数据

**输入参数**:
- `root`: str，存储根目录路径

**内部调用的函数**：
- os.makedirs: 创建目录

**输出参数**: 无返回值

**其他说明**：使用 joblib 将数据序列化到磁盘文件

#### get_path_for_key(self, key: str) -> str

**作用**: 获取指定键对应的文件路径

**输入参数**:
- `key`: str，键名

**内部调用的函数**：
- os.path.join: 拼接路径

**输出参数**: str - 文件路径

#### get(self, key: str) -> Any

**作用**: 从文件加载指定键的值

**输入参数**:
- `key`: str，要获取的键

**内部调用的函数**：
- joblib.load: 加载序列化数据
- self.get_path_for_key: 获取文件路径

**输出参数**: Any - 反序列化的值

#### exists(self, key: str) -> bool

**作用**: 检查键对应的文件是否存在

**输入参数**:
- `key`: str，要检查的键

**内部调用的函数**：
- os.path.exists: 检查文件存在性
- self.get_path_for_key: 获取文件路径

**输出参数**: bool - 文件是否存在

#### set(self, key: str, value: Any) -> None

**作用**: 将值序列化保存到文件，实现写一次存储

**输入参数**:
- `key`: str，要设置的键
- `value`: Any，要保存的值

**内部调用的函数**：
- self.exists: 检查是否已存在
- joblib.dump: 序列化保存数据

**输出参数**: 无返回值

**其他说明**：如果键已存在则不覆盖（写一次存储）

#### drop(self, key: str) -> None

**作用**: 删除键对应的文件

**输入参数**:
- `key`: str，要删除的键

**内部调用的函数**：
- os.remove: 删除文件
- self.get_path_for_key: 获取文件路径

**输出参数**: 无返回值

### SQLiteDictStorage 类（SQLite 字典存储）

#### __init__(self, db: DBAdapter, table: str, overflow_storage: Optional[JoblibDictStorage] = None, overflow_threshold_MB: Optional[Union[int, float]] = 50)

**作用**: 初始化 SQLite 字典存储，支持大对象溢出到文件系统

**输入参数**:
- `db`: DBAdapter，数据库适配器
- `table`: str，表名
- `overflow_storage`: Optional[JoblibDictStorage]，溢出存储
- `overflow_threshold_MB`: Optional[Union[int, float]]，溢出阈值

**内部调用的函数**：
- self.conn: 获取数据库连接
- conn.execute: 创建表

**输出参数**: 无返回值

**其他说明**：自动创建表结构，支持大对象溢出机制

#### conn(self) -> sqlite3.Connection

**作用**: 获取数据库连接

**输入参数**: 无

**内部调用的函数**：
- self.db.conn: 从适配器获取连接

**输出参数**: sqlite3.Connection - 数据库连接

#### set(self, key: str, value: Any, conn: Optional[sqlite3.Connection] = None) -> None

**作用**: 设置键值对，支持大对象溢出处理

**输入参数**:
- `key`: str，要设置的键
- `value`: Any，要设置的值
- `conn`: Optional[sqlite3.Connection]，可选的数据库连接

**内部调用的函数**：
- serialize: 序列化对象
- self.overflow_storage.set: 溢出存储设置
- conn.execute: 执行 SQL 插入

**输出参数**: 无返回值

**其他说明**：
- 使用 @transaction 装饰器确保事务性
- 根据大小决定存储位置（数据库或文件系统）

### CachedDictStorage 类（缓存字典存储）

#### __init__(self, persistent: DictStorage)

**作用**: 初始化缓存字典存储，提供内存缓存层

**输入参数**:
- `persistent`: DictStorage，持久化存储后端

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：维护内存缓存和脏键集合

#### get(self, key: str) -> Any

**作用**: 获取键值，优先从缓存获取，缓存未命中时从持久化存储加载

**输入参数**:
- `key`: str，要获取的键

**内部调用的函数**：
- self.persistent.get: 从持久化存储获取

**输出参数**: Any - 键对应的值

#### set(self, key: str, value: Any) -> None

**作用**: 设置键值对，先存入缓存并标记为脏数据

**输入参数**:
- `key`: str，要设置的键
- `value`: Any，要设置的值

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：数据先存入缓存，延迟写入持久化存储

### InMemCallStorage 类（内存调用存储）

#### __init__(self)

**作用**: 初始化内存调用存储，使用 pandas DataFrame 存储调用数据

**输入参数**: 无

**内部调用的函数**：
- pd.DataFrame: 创建数据框
- pd.MultiIndex.from_tuples: 创建多级索引

**输出参数**: 无返回值

**其他说明**：使用多级索引 (call_history_id, name) 组织数据

#### save(self, call: Call)

**作用**: 保存调用对象到内存存储

**输入参数**:
- `call`: Call，要保存的调用对象

**内部调用的函数**：
- time.time: 记录时间（性能监控）
- self.df.loc: 设置 DataFrame 行数据

**输出参数**: 无返回值

**其他说明**：
- 分别保存输入和输出引用
- 记录操作时间用于性能分析

#### exists(self, call_history_id: str) -> bool

**作用**: 检查指定历史ID的调用是否存在

**输入参数**:
- `call_history_id`: str，调用历史ID

**内部调用的函数**：
- 无直接函数调用

**输出参数**: bool - 调用是否存在

#### mget_data(self, call_hids: List[str]) -> List[Dict[str, Any]]

**作用**: 批量获取多个调用的数据

**输入参数**:
- `call_hids`: List[str]，调用历史ID列表

**内部调用的函数**：
- self.df.loc: 获取 DataFrame 数据
- groupby: 按调用ID分组

**输出参数**: List[Dict[str, Any]] - 调用数据列表

**其他说明**：返回包含输入输出ID和版本信息的字典

### SQLiteCallStorage 类（SQLite 调用存储）

#### __init__(self, db: DBAdapter, table_name: str)

**作用**: 初始化 SQLite 调用存储，创建调用数据表

**输入参数**:
- `db`: DBAdapter，数据库适配器
- `table_name`: str，表名

**内部调用的函数**：
- self.db.conn: 获取数据库连接
- conn.execute: 创建表

**输出参数**: 无返回值

**其他说明**：创建包含调用历史ID和参数名的复合主键表

#### save(self, call_data: Dict[str, Any], conn: Optional[sqlite3.Connection] = None)

**作用**: 保存调用数据到 SQLite 数据库

**输入参数**:
- `call_data`: Dict[str, Any]，调用数据字典
- `conn`: Optional[sqlite3.Connection]，可选的数据库连接

**内部调用的函数**：
- conn.execute: 执行 SQL 插入语句

**输出参数**: 无返回值

**其他说明**：
- 使用 @transaction 装饰器确保事务性
- 分别插入输入和输出参数记录

#### exists(self, call_history_id: str) -> bool

**作用**: 检查调用是否存在于数据库中

**输入参数**:
- `call_history_id`: str，调用历史ID

**内部调用的函数**：
- self.conn: 获取数据库连接
- conn.execute: 执行查询
- cursor.fetchone: 获取查询结果

**输出参数**: bool - 调用是否存在

#### get_data(self, call_history_id: str) -> Dict[str, Any]

**作用**: 获取指定调用的完整数据

**输入参数**:
- `call_history_id`: str，调用历史ID

**内部调用的函数**：
- self.mget_data: 批量获取数据

**输出参数**: Dict[str, Any] - 调用数据字典

### CachedCallStorage 类（缓存调用存储）

#### __init__(self, persistent: SQLiteCallStorage)

**作用**: 初始化缓存调用存储，结合内存缓存和持久化存储

**输入参数**:
- `persistent`: SQLiteCallStorage，持久化存储后端

**内部调用的函数**：
- InMemCallStorage: 创建内存缓存

**输出参数**: 无返回值

**其他说明**：维护脏数据集合用于延迟写入

#### save(self, call: Call)

**作用**: 保存调用到缓存，标记为脏数据

**输入参数**:
- `call`: Call，要保存的调用对象

**内部调用的函数**：
- self.cache.save: 保存到内存缓存

**输出参数**: 无返回值

#### exists(self, call_history_id: str) -> bool

**作用**: 检查调用是否存在，优先检查缓存

**输入参数**:
- `call_history_id`: str，调用历史ID

**内部调用的函数**：
- self.cache.exists: 检查缓存
- self.persistent.exists: 检查持久化存储

**输出参数**: bool - 调用是否存在

#### get_data(self, call_history_id: str) -> Dict[str, Any]

**作用**: 获取调用数据，优先从缓存获取

**输入参数**:
- `call_history_id`: str，调用历史ID

**内部调用的函数**：
- self.cache.get_data: 从缓存获取
- self.persistent.get_data: 从持久化存储获取

**输出参数**: Dict[str, Any] - 调用数据字典

**其他说明**：实现两级存储架构，提供高性能的数据访问
