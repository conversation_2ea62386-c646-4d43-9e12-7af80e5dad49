"""
栈重放（Stack Replay）完整演示程序
展示栈重放功能的使用方法和效果，包括ComputationFrame遍历和Storage执行顺序遍历
"""

import os
import sys
from typing import Dict, List, Any

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    # 如果mandala1不可用，添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame

# 导入我们实现的遍历器
from cf_traverser import CFTraverser
from storage_traverser import StorageTraverser


class StackReplayDemo:
    """栈重放演示类"""
    
    def __init__(self):
        self.cf_traverser = CFTraverser()
        self.storage_traverser = StorageTraverser()
        
    def run_complete_demo(self):
        """运行完整的栈重放演示"""
        print("🚀 栈重放（Stack Replay）功能演示")
        print("=" * 80)
        print("基于mandala1框架实现的栈重放功能，能够：")
        print("1. 遍历计算图结构，展示函数层级关系")
        print("2. 按执行顺序遍历函数调用，展示执行流程")
        print("=" * 80)
        
        # 运行不同复杂度的演示
        self.demo_simple_computation()
        self.demo_complex_computation()
        self.demo_conditional_computation()
        
        print("\n🎉 栈重放功能演示完成！")
        
    def demo_simple_computation(self):
        """演示简单计算的栈重放"""
        print("\n" + "=" * 60)
        print("📊 演示1：简单计算的栈重放")
        print("=" * 60)
        
        # 定义简单的计算函数
        @op
        def add(x, y):
            """加法函数"""
            return x + y
        
        @op
        def multiply(x, y):
            """乘法函数"""
            return x * y
        
        @op
        def square(x):
            """平方函数"""
            return x * x
        
        # 执行计算
        storage = Storage()

        with storage:
            print("执行计算: (1 + 2) * (3 + 4) 的平方")
            a = add(1, 2)      # 3
            b = add(3, 4)      # 7
            c = multiply(a, b) # 21
            result = square(c) # 441

            print(f"最终结果: {storage.unwrap(result)}")

        # 在Storage上下文外进行遍历
        # 创建计算框架
        cf = storage.cf(result).expand_back(recursive=True)

        # 1. ComputationFrame遍历
        print("\n🔍 ComputationFrame遍历（计算图结构）:")
        self.cf_traverser.print_computation_graph(cf, show_details=True)

        # 2. Storage执行顺序遍历
        print("\n⏰ Storage执行顺序遍历（时间顺序）:")
        self.storage_traverser.print_execution_order(storage, show_details=True)
    
    def demo_complex_computation(self):
        """演示复杂计算的栈重放"""
        print("\n" + "=" * 60)
        print("📊 演示2：复杂计算的栈重放")
        print("=" * 60)
        
        # 定义更复杂的计算函数
        @op
        def double(x):
            """双倍函数"""
            return x * 2

        @op
        def triple(x):
            """三倍函数"""
            return x * 3

        @op
        def combine_results(a, b):
            """组合结果"""
            return a + b

        @op
        def final_transform(x):
            """最终转换"""
            return x ** 2
        
        # 执行计算
        storage = Storage()

        with storage:
            print("执行计算: double(5) + triple(4) 的平方")
            double_result = double(5)    # 10
            triple_result = triple(4)    # 12
            combined = combine_results(double_result, triple_result)  # 22
            final_result = final_transform(combined)  # 484

            print(f"double(5) = {storage.unwrap(double_result)}")
            print(f"triple(4) = {storage.unwrap(triple_result)}")
            print(f"combined = {storage.unwrap(combined)}")
            print(f"最终结果: {storage.unwrap(final_result)}")

        # 在Storage上下文外进行遍历
        # 创建计算框架
        cf = storage.cf(final_result).expand_back(recursive=True)

        # 1. ComputationFrame遍历
        print("\n🔍 ComputationFrame遍历（计算图结构）:")
        self.cf_traverser.print_computation_graph(cf, show_details=True)

        # 2. Storage执行顺序遍历（限制显示数量）
        print("\n⏰ Storage执行顺序遍历（时间顺序）:")
        self.storage_traverser.print_execution_order(storage, show_details=True, max_calls=15)
    
    def demo_conditional_computation(self):
        """演示条件计算的栈重放"""
        print("\n" + "=" * 60)
        print("📊 演示3：条件计算的栈重放")
        print("=" * 60)
        
        # 定义条件计算函数
        @op
        def process_data(x):
            """数据处理函数"""
            return x * 2
        
        @op
        def validate_data(x):
            """数据验证函数"""
            return x > 0
        
        @op
        def transform_positive(x):
            """正数转换函数"""
            return x ** 2
        
        @op
        def transform_negative(x):
            """负数转换函数"""
            return abs(x) * 3
        
        @op
        def finalize_result(x, y):
            """最终化结果"""
            return x + y
        
        # 执行条件计算
        storage = Storage()

        with storage:
            print("执行条件计算: 处理多个数据并根据条件选择不同的转换")

            # 处理多个数据
            data1 = process_data(5)   # 10
            data2 = process_data(-3)  # -6
            data3 = process_data(2)   # 4

            # 验证和转换
            is_positive1 = validate_data(storage.unwrap(data1))
            is_positive2 = validate_data(storage.unwrap(data2))

            results = []
            if is_positive1:
                result1 = transform_positive(storage.unwrap(data1))  # 100
                results.append(result1)
            else:
                result1 = transform_negative(storage.unwrap(data1))
                results.append(result1)

            if is_positive2:
                result2 = transform_positive(storage.unwrap(data2))
                results.append(result2)
            else:
                result2 = transform_negative(storage.unwrap(data2))  # 18
                results.append(result2)

            # 最终结果
            if len(results) >= 2:
                final_result = finalize_result(storage.unwrap(results[0]), storage.unwrap(results[1]))
                print(f"最终结果: {storage.unwrap(final_result)}")

        # 在Storage上下文外进行遍历
        if len(results) >= 2:
            # 创建计算框架
            cf = storage.cf(final_result).expand_back(recursive=True)

            # 1. ComputationFrame遍历
            print("\n🔍 ComputationFrame遍历（计算图结构）:")
            self.cf_traverser.print_computation_graph(cf, show_details=True)

            # 2. Storage执行顺序遍历
            print("\n⏰ Storage执行顺序遍历（时间顺序）:")
            self.storage_traverser.print_execution_order(storage, show_details=True, max_calls=12)
    
    def demo_interactive_mode(self):
        """交互式演示模式"""
        print("\n" + "=" * 60)
        print("🎮 交互式栈重放演示")
        print("=" * 60)
        print("您可以输入自己的计算表达式来测试栈重放功能")
        print("支持的操作: add(x,y), multiply(x,y), power(x,n), square(x)")
        print("示例: add(multiply(2,3), power(2,3))")
        print("输入 'quit' 退出")
        
        # 定义交互式函数
        @op
        def add(x, y):
            return x + y
        
        @op
        def multiply(x, y):
            return x * y
        
        @op
        def power(x, n):
            return x ** n
        
        @op
        def square(x):
            return x * x
        
        with Storage() as storage:
            while True:
                try:
                    user_input = input("\n请输入计算表达式: ").strip()
                    if user_input.lower() == 'quit':
                        break
                    
                    # 简单的表达式评估（仅用于演示）
                    if user_input:
                        print(f"执行: {user_input}")
                        # 这里可以添加更复杂的表达式解析逻辑
                        print("（交互式模式需要更复杂的表达式解析，此处仅为演示框架）")
                        
                except KeyboardInterrupt:
                    print("\n退出交互式模式")
                    break
                except Exception as e:
                    print(f"执行出错: {e}")


def main():
    """主函数"""
    demo = StackReplayDemo()
    
    try:
        # 运行完整演示
        demo.run_complete_demo()
        
        # 可选：运行交互式演示
        # demo.demo_interactive_mode()
        
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
