{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Query the Storage with `ComputationFrame`s\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/topics/03_cf.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "## Why `Computation<PERSON><PERSON><PERSON>`s?\n", "The `ComputationFrame` data structure **formalizes the natural/intuitive way you\n", "think of the \"web\" of saved `@op` calls**. It gives you a \"grammar\" in which\n", "operations over persisted computation graphs that are easy to think of are also\n", "easy to implement.\n", "\n", "In computational projects, all queries boil down to how some variables depend on\n", "other variables: e.g., in ML you often care about what input parameters lead to\n", "final results with certain metrics. The `mandala` storage can automatically\n", "answer such questions when all operations along the way were `@op`s, because it\n", "represents the \"web\" of saved `@op` calls, linked by how the outputs of one\n", "`@op` are used as inputs to other `@op`s.\n", "\n", "The `ComputationFrame` (CF) is the data structure used to explore and query this\n", "web of calls. It's a high-level view of a collection of `@op` calls, so that\n", "calls that serve the same role are grouped together. **The groups of calls form a\n", "computational graph of variables and functions, which enables effective &\n", "natural high-level operations over storage**. \n", "\n", "This section covers basic tools to get up to speed with CFs. For more advanced\n", "usage, see [Advanced `ComputationFrame` tools](06_advanced_cf.md) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Typical workflow\n", "Using CFs typically goes through the following stages:\n", "\n", "- **[creation](#creating-computationframes)**: initialize a CF in various ways,\n", "e.g. from some `Ref`s, from all calls to an `@op`, .... This CF will have a\n", "limited view of storage because it will involve few (0 or 1) `@op`s\n", "- **[expansion](#exploring-storage-by-expanding-computationframes)**: add more\n", "context to the CF by adding new function nodes containing the calls that\n", "produced/used some variable(s). The goal of this stage is to incorporate in the\n", "CF all variables whose relationships you're interested in.\n", "- **combination & restriction**: merge multiple CFs, restrict to subgraphs or \n", "specific values of the variables along some predicates. This lets you focus on\n", "the computations you want before making expensive calls to the storage.\n", "- **[conversion to a `pandas.DataFrame`](#extracting-dataframes-from-computationframes)**: finally,\n", "extract a table representing the relationships between the variables in the CF\n", "for downstream analysis.\n", "\n", "### Toy ML pipeline\n", "In this section, we'll work with the following toy experiment on a small ML\n", "pipeline:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:44.982394Z", "iopub.status.busy": "2024-07-11T14:31:44.982032Z", "iopub.status.idle": "2024-07-11T14:31:45.000651Z", "shell.execute_reply": "2024-07-11T14:31:44.999572Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:45.008610Z", "iopub.status.busy": "2024-07-11T14:31:45.007935Z", "iopub.status.idle": "2024-07-11T14:31:48.484497Z", "shell.execute_reply": "2024-07-11T14:31:48.483753Z"}}, "outputs": [], "source": ["### define ops to train & eval a random forest model on digits dataset\n", "import numpy as np\n", "from sklearn.datasets import load_digits\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "from mandala.imports import *\n", "try:\n", "    import rich\n", "    from rich import print as pprint\n", "except ImportError:\n", "    print(\"rich not installed, using default print\")\n", "    pprint = print\n", "\n", "storage = Storage()\n", "\n", "np.random.seed(0)\n", "\n", "@op \n", "def generate_dataset(random_seed=42):\n", "    X, y = load_digits(return_X_y=True)\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=random_seed)\n", "    return X_train, X_test, y_train, y_test\n", "\n", "@op\n", "def train_model(X_train, y_train, n_estimators):\n", "    model = RandomForestClassifier(n_estimators=n_estimators, max_depth=2)\n", "    model.fit(X_train, y_train)\n", "    return model, round(model.score(X_train, y_train), 2)\n", "\n", "@op\n", "def eval_model(model, X_test, y_test):\n", "    return round(model.score(X_test, y_test), 2)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:48.488988Z", "iopub.status.busy": "2024-07-11T14:31:48.488282Z", "iopub.status.idle": "2024-07-11T14:31:49.350756Z", "shell.execute_reply": "2024-07-11T14:31:49.349992Z"}}, "outputs": [], "source": ["with storage: \n", "    X_train, X_test, y_train, y_test = generate_dataset()\n", "    for n_estimators in [10, 20, 40, 80]:\n", "        model, train_acc = train_model(X_train, y_train, n_estimators=n_estimators)\n", "        if storage.unwrap(train_acc) > 0.8: # conditional execution\n", "            test_acc = eval_model(model, X_test, y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating `ComputationFrame`s\n", "There are several ways to create a CF, all dispatched through the `storage.cf()`\n", "method.\n", "\n", "### From a single `Ref`\n", "The simplest example of a CF is to create one from a `Ref`:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:49.353532Z", "iopub.status.busy": "2024-07-11T14:31:49.353313Z", "iopub.status.idle": "2024-07-11T14:31:49.477339Z", "shell.execute_reply": "2024-07-11T14:31:49.476463Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ComputationFrame with:\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">variable</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> unique refs<span style=\"font-weight: bold\">)</span>\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">operation</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> unique calls<span style=\"font-weight: bold\">)</span>\n", "Computational graph:\n", "    v\n", "</pre>\n"], "text/plain": ["ComputationFrame with:\n", "    \u001b[1;36m1\u001b[0m \u001b[1;35mvariable\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m1\u001b[0m unique refs\u001b[1m)\u001b[0m\n", "    \u001b[1;36m0\u001b[0m \u001b[1;35moperation\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m0\u001b[0m unique calls\u001b[1m)\u001b[0m\n", "Computational graph:\n", "    v\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"148pt\" height=\"44pt\"\n", " viewBox=\"0.00 0.00 148.00 44.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 40)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-40 144,-40 144,4 -4,4\"/>\n", "<!-- v -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>v</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M128,-36C128,-36 12,-36 12,-36 6,-36 0,-30 0,-24 0,-24 0,-12 0,-12 0,-6 6,0 12,0 12,0 128,0 128,0 134,0 140,-6 140,-12 140,-12 140,-24 140,-24 140,-30 134,-36 128,-36\"/>\n", "<text text-anchor=\"start\" x=\"66.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources/1 sinks)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5e31480>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(test_acc)\n", "pprint(cf) # text description of the CF \n", "cf.draw(verbose=True) # pictorial representation of the CF"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As the description says, this is a CF with 1 variable (called `v`) and 0\n", "operations. The variable contains 1 `Ref`, i.e. 1 value. We can examine the refs\n", "in the variables and the calls in the operations:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:49.480880Z", "iopub.status.busy": "2024-07-11T14:31:49.480555Z", "iopub.status.idle": "2024-07-11T14:31:49.521741Z", "shell.execute_reply": "2024-07-11T14:31:49.520824Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Refs by variable:\n", "<span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'v'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">AtomRef</span><span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.82</span>, <span style=\"color: #808000; text-decoration-color: #808000\">hid</span>=<span style=\"color: #800080; text-decoration-color: #800080\">11a</span><span style=\"color: #808000; text-decoration-color: #808000\">...</span><span style=\"font-weight: bold\">)}}</span>\n", "</pre>\n"], "text/plain": ["Refs by variable:\n", "\u001b[1m{\u001b[0m\u001b[32m'v'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;35mAtomRef\u001b[0m\u001b[1m(\u001b[0m\u001b[1;36m0.82\u001b[0m, \u001b[33mhid\u001b[0m=\u001b[35m11a\u001b[0m\u001b[33m...\u001b[0m\u001b[1m)\u001b[0m\u001b[1m}\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Calls by operation:\n", "<span style=\"font-weight: bold\">{}</span>\n", "</pre>\n"], "text/plain": ["Calls by operation:\n", "\u001b[1m{\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pprint(f'Refs by variable:\\n{cf.refs_by_var()}')\n", "pprint(f'Calls by operation:\\n{cf.calls_by_func()}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To make this more interesting, we can call `expand_back()` (explained in more\n", "detail later) on the CF to add the full computational history of all values in\n", "all variables:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:49.525286Z", "iopub.status.busy": "2024-07-11T14:31:49.524867Z", "iopub.status.idle": "2024-07-11T14:31:50.027103Z", "shell.execute_reply": "2024-07-11T14:31:50.026347Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ComputationFrame with:\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">variable</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8</span> unique refs<span style=\"font-weight: bold\">)</span>\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">operation</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> unique calls<span style=\"font-weight: bold\">)</span>\n", "Computational graph:\n", "    X_train@output_0, X_test@output_1, y_train@output_2, y_test@output_3 = \n", "<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">generate_dataset</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">random_seed</span>=<span style=\"color: #800080; text-decoration-color: #800080\">random_seed</span><span style=\"font-weight: bold\">)</span>\n", "    model@output_0 = <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">train_model</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">X_train</span>=<span style=\"color: #800080; text-decoration-color: #800080\">X_train</span>, <span style=\"color: #808000; text-decoration-color: #808000\">n_estimators</span>=<span style=\"color: #800080; text-decoration-color: #800080\">n_estimators</span>, <span style=\"color: #808000; text-decoration-color: #808000\">y_train</span>=<span style=\"color: #800080; text-decoration-color: #800080\">y_train</span><span style=\"font-weight: bold\">)</span>\n", "    v@output_0 = <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">eval_model</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">X_test</span>=<span style=\"color: #800080; text-decoration-color: #800080\">X_test</span>, <span style=\"color: #808000; text-decoration-color: #808000\">model</span>=<span style=\"color: #800080; text-decoration-color: #800080\">model</span>, <span style=\"color: #808000; text-decoration-color: #808000\">y_test</span>=<span style=\"color: #800080; text-decoration-color: #800080\">y_test</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["ComputationFrame with:\n", "    \u001b[1;36m8\u001b[0m \u001b[1;35mvariable\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m8\u001b[0m unique refs\u001b[1m)\u001b[0m\n", "    \u001b[1;36m3\u001b[0m \u001b[1;35moperation\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m3\u001b[0m unique calls\u001b[1m)\u001b[0m\n", "Computational graph:\n", "    X_train@output_0, X_test@output_1, y_train@output_2, y_test@output_3 = \n", "\u001b[1;35mgenerate_dataset\u001b[0m\u001b[1m(\u001b[0m\u001b[33mrandom_seed\u001b[0m=\u001b[35mrandom_seed\u001b[0m\u001b[1m)\u001b[0m\n", "    model@output_0 = \u001b[1;35mtrain_model\u001b[0m\u001b[1m(\u001b[0m\u001b[33mX_train\u001b[0m=\u001b[35mX_train\u001b[0m, \u001b[33mn_estimators\u001b[0m=\u001b[35mn_estimators\u001b[0m, \u001b[33my_train\u001b[0m=\u001b[35my_train\u001b[0m\u001b[1m)\u001b[0m\n", "    v@output_0 = \u001b[1;35meval_model\u001b[0m\u001b[1m(\u001b[0m\u001b[33mX_test\u001b[0m=\u001b[35mX_test\u001b[0m, \u001b[33mmodel\u001b[0m=\u001b[35mmodel\u001b[0m, \u001b[33my_test\u001b[0m=\u001b[35my_test\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"365pt\" height=\"648pt\"\n", " viewBox=\"0.00 0.00 365.00 648.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 644)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-644 361,-644 361,4 -4,4\"/>\n", "<!-- v -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>v</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M207,-36C207,-36 137,-36 137,-36 131,-36 125,-30 125,-24 125,-24 125,-12 125,-12 125,-6 131,0 137,0 137,0 207,0 207,0 213,0 219,-6 219,-12 219,-12 219,-24 219,-24 219,-30 213,-36 207,-36\"/>\n", "<text text-anchor=\"start\" x=\"168.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"start\" x=\"133\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sinks)</text>\n", "</g>\n", "<!-- y_test -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M42,-394C42,-394 12,-394 12,-394 6,-394 0,-388 0,-382 0,-382 0,-370 0,-370 0,-364 6,-358 12,-358 12,-358 42,-358 42,-358 48,-358 54,-364 54,-370 54,-370 54,-382 54,-382 54,-388 48,-394 42,-394\"/>\n", "<text text-anchor=\"start\" x=\"9.5\" y=\"-378.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"8.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M205,-134C205,-134 139,-134 139,-134 133,-134 127,-128 127,-122 127,-122 127,-106 127,-106 127,-100 133,-94 139,-94 139,-94 205,-94 205,-94 211,-94 217,-100 217,-106 217,-106 217,-122 217,-122 217,-128 211,-134 205,-134\"/>\n", "<text text-anchor=\"start\" x=\"139\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"135\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"157.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M36.51,-357.94C60.94,-314.14 126.07,-197.36 156.25,-143.25\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"159.47,-144.65 161.29,-134.21 153.36,-141.24 159.47,-144.65\"/>\n", "<text text-anchor=\"middle\" x=\"119.5\" y=\"-267\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"119.5\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M212.5,-448C212.5,-448 131.5,-448 131.5,-448 125.5,-448 119.5,-442 119.5,-436 119.5,-436 119.5,-424 119.5,-424 119.5,-418 125.5,-412 131.5,-412 131.5,-412 212.5,-412 212.5,-412 218.5,-412 224.5,-418 224.5,-424 224.5,-424 224.5,-436 224.5,-436 224.5,-442 218.5,-448 212.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"134.5\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"127.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M205.5,-340C205.5,-340 138.5,-340 138.5,-340 132.5,-340 126.5,-334 126.5,-328 126.5,-328 126.5,-312 126.5,-312 126.5,-306 132.5,-300 138.5,-300 138.5,-300 205.5,-300 205.5,-300 211.5,-300 217.5,-306 217.5,-312 217.5,-312 217.5,-328 217.5,-328 217.5,-334 211.5,-340 205.5,-340\"/>\n", "<text text-anchor=\"start\" x=\"138\" y=\"-327.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"134.5\" y=\"-317\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"157.5\" y=\"-307\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge12\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M172,-411.65C172,-395.15 172,-370 172,-350.37\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"175.5,-350.13 172,-340.13 168.5,-350.13 175.5,-350.13\"/>\n", "<text text-anchor=\"middle\" x=\"200.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"200.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M287.5,-448C287.5,-448 254.5,-448 254.5,-448 248.5,-448 242.5,-442 242.5,-436 242.5,-436 242.5,-424 242.5,-424 242.5,-418 248.5,-412 254.5,-412 254.5,-412 287.5,-412 287.5,-412 293.5,-412 299.5,-418 299.5,-424 299.5,-424 299.5,-436 299.5,-436 299.5,-442 293.5,-448 287.5,-448\"/>\n", "<text text-anchor=\"start\" x=\"250.5\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"252.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M264.88,-411.91C258.69,-396.44 247.91,-373.73 233,-358 228.85,-353.62 224.08,-349.57 219.06,-345.88\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"220.69,-342.76 210.46,-340.02 216.75,-348.54 220.69,-342.76\"/>\n", "<text text-anchor=\"middle\" x=\"278.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"278.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- random_seed -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M232.5,-640C232.5,-640 151.5,-640 151.5,-640 145.5,-640 139.5,-634 139.5,-628 139.5,-628 139.5,-616 139.5,-616 139.5,-610 145.5,-604 151.5,-604 151.5,-604 232.5,-604 232.5,-604 238.5,-604 244.5,-610 244.5,-616 244.5,-616 244.5,-628 244.5,-628 244.5,-634 238.5,-640 232.5,-640\"/>\n", "<text text-anchor=\"start\" x=\"152.5\" y=\"-624.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"147.5\" y=\"-614\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M238,-546C238,-546 146,-546 146,-546 140,-546 134,-540 134,-534 134,-534 134,-518 134,-518 134,-512 140,-506 146,-506 146,-506 238,-506 238,-506 244,-506 250,-512 250,-518 250,-518 250,-534 250,-534 250,-540 244,-546 238,-546\"/>\n", "<text text-anchor=\"start\" x=\"142\" y=\"-533.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"142\" y=\"-523\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"177.5\" y=\"-513\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M192,-603.76C192,-590.5 192,-571.86 192,-556.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"195.5,-556.07 192,-546.07 188.5,-556.07 195.5,-556.07\"/>\n", "<text text-anchor=\"middle\" x=\"221.5\" y=\"-578\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"221.5\" y=\"-567\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M89,-448C89,-448 57,-448 57,-448 51,-448 45,-442 45,-436 45,-436 45,-424 45,-424 45,-418 51,-412 57,-412 57,-412 89,-412 89,-412 95,-412 101,-418 101,-424 101,-424 101,-436 101,-436 101,-442 95,-448 89,-448\"/>\n", "<text text-anchor=\"start\" x=\"53\" y=\"-432.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"54.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge13\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M83.59,-411.93C93.36,-396.89 108.76,-374.81 125,-358 128.79,-354.08 133,-350.21 137.3,-346.54\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"139.55,-349.21 145.07,-340.17 135.12,-343.8 139.55,-349.21\"/>\n", "<text text-anchor=\"middle\" x=\"146.5\" y=\"-379\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"146.5\" y=\"-368\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M187,-228C187,-228 157,-228 157,-228 151,-228 145,-222 145,-216 145,-216 145,-204 145,-204 145,-198 151,-192 157,-192 157,-192 187,-192 187,-192 193,-192 199,-198 199,-204 199,-204 199,-216 199,-216 199,-222 193,-228 187,-228\"/>\n", "<text text-anchor=\"start\" x=\"154\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"153.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M172,-191.76C172,-178.5 172,-159.86 172,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"175.5,-144.07 172,-134.07 168.5,-144.07 175.5,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"193.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"193.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M306,-282C306,-282 276,-282 276,-282 270,-282 264,-276 264,-270 264,-270 264,-258 264,-258 264,-252 270,-246 276,-246 276,-246 306,-246 306,-246 312,-246 318,-252 318,-258 318,-258 318,-270 318,-270 318,-276 312,-282 306,-282\"/>\n", "<text text-anchor=\"start\" x=\"272.5\" y=\"-266.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"272.5\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M282.07,-245.61C269.79,-222.66 246.09,-181.69 219,-152 215.5,-148.17 211.58,-144.42 207.53,-140.87\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"209.59,-138.03 199.67,-134.33 205.11,-143.41 209.59,-138.03\"/>\n", "<text text-anchor=\"middle\" x=\"294.5\" y=\"-213\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"294.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M172,-299.94C172,-282.85 172,-257.58 172,-238.34\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"175.5,-238.09 172,-228.09 168.5,-238.09 175.5,-238.09\"/>\n", "<text text-anchor=\"middle\" x=\"193.5\" y=\"-267\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"193.5\" y=\"-256\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;v -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;v</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M172,-93.98C172,-80.34 172,-61.75 172,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"175.5,-46.1 172,-36.1 168.5,-46.1 175.5,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"193.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"193.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;y_test -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;y_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M133.7,-513.77C113.15,-508.11 90.56,-499.86 72,-488 51.84,-475.12 45.96,-469.75 36,-448 29.8,-434.45 27.43,-418.01 26.67,-404.37\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"30.16,-404.02 26.36,-394.13 23.16,-404.23 30.16,-404.02\"/>\n", "<text text-anchor=\"middle\" x=\"93.5\" y=\"-480\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_3</text>\n", "<text text-anchor=\"middle\" x=\"93.5\" y=\"-469\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_train -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M207.99,-505.98C220.12,-491.54 236.9,-471.57 250.1,-455.87\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"252.88,-458 256.63,-448.1 247.52,-453.5 252.88,-458\"/>\n", "<text text-anchor=\"middle\" x=\"262.5\" y=\"-480\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"262.5\" y=\"-469\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;y_train -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M167.92,-505.98C149.06,-491.08 122.73,-470.28 102.58,-454.37\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"104.66,-451.55 94.64,-448.1 100.32,-457.04 104.66,-451.55\"/>\n", "<text text-anchor=\"middle\" x=\"165.5\" y=\"-480\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_2</text>\n", "<text text-anchor=\"middle\" x=\"165.5\" y=\"-469\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_test -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M250.1,-510.26C263.62,-504.86 277.2,-497.62 288,-488 302.99,-474.65 304.19,-467.5 309,-448 322.39,-393.69 309.02,-328 299.25,-291.95\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"302.57,-290.85 296.49,-282.18 295.84,-292.75 302.57,-290.85\"/>\n", "<text text-anchor=\"middle\" x=\"335.5\" y=\"-433\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"335.5\" y=\"-422\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d4a4f34430>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf.expand_back(inplace=True, recursive=True)\n", "pprint(cf)\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The variables added to the CF during expansion are given informative names based\n", "on input names of functions called on these variables. The result of expansion\n", "tells us the precise way `test_acc` was computed. We can get the values of each\n", "variable and the calls of each function:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.031212Z", "iopub.status.busy": "2024-07-11T14:31:50.030601Z", "iopub.status.idle": "2024-07-11T14:31:50.091817Z", "shell.execute_reply": "2024-07-11T14:31:50.091174Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span>\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'v'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.82</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'model'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">RandomForestClassifier</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">max_depth</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, <span style=\"color: #808000; text-decoration-color: #808000\">n_estimators</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">80</span><span style=\"font-weight: bold\">)}</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'random_seed'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">42</span><span style=\"font-weight: bold\">}</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'n_estimators'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">80</span><span style=\"font-weight: bold\">}</span>\n", "<span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\n", "    \u001b[32m'v'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;36m0.82\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[32m'model'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;35mRandomForestClassifier\u001b[0m\u001b[1m(\u001b[0m\u001b[33mmax_depth\u001b[0m=\u001b[1;36m2\u001b[0m, \u001b[33mn_estimators\u001b[0m=\u001b[1;36m80\u001b[0m\u001b[1m)\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[32m'random_seed'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;36m42\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[32m'n_estimators'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;36m80\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span>\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'eval_model'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Call</span><span style=\"font-weight: bold\">(</span>eval_model, <span style=\"color: #808000; text-decoration-color: #808000\">hid</span>=<span style=\"color: #800080; text-decoration-color: #800080\">d32</span><span style=\"color: #808000; text-decoration-color: #808000\">...</span><span style=\"font-weight: bold\">)}</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'generate_dataset'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Call</span><span style=\"font-weight: bold\">(</span>generate_dataset, <span style=\"color: #808000; text-decoration-color: #808000\">hid</span>=<span style=\"color: #800080; text-decoration-color: #800080\">c3f</span><span style=\"color: #808000; text-decoration-color: #808000\">...</span><span style=\"font-weight: bold\">)}</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'train_model'</span>: <span style=\"font-weight: bold\">{</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">Call</span><span style=\"font-weight: bold\">(</span>train_model, <span style=\"color: #808000; text-decoration-color: #808000\">hid</span>=<span style=\"color: #800080; text-decoration-color: #800080\">e60</span><span style=\"color: #808000; text-decoration-color: #808000\">...</span><span style=\"font-weight: bold\">)}</span>\n", "<span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\n", "    \u001b[32m'eval_model'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;35mCall\u001b[0m\u001b[1m(\u001b[0meval_model, \u001b[33mhid\u001b[0m=\u001b[35md32\u001b[0m\u001b[33m...\u001b[0m\u001b[1m)\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[32m'generate_dataset'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;35mCall\u001b[0m\u001b[1m(\u001b[0mgenerate_dataset, \u001b[33mhid\u001b[0m=\u001b[35mc3f\u001b[0m\u001b[33m...\u001b[0m\u001b[1m)\u001b[0m\u001b[1m}\u001b[0m,\n", "    \u001b[32m'train_model'\u001b[0m: \u001b[1m{\u001b[0m\u001b[1;35mCall\u001b[0m\u001b[1m(\u001b[0mtrain_model, \u001b[33mhid\u001b[0m=\u001b[35me60\u001b[0m\u001b[33m...\u001b[0m\u001b[1m)\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pprint({vname: storage.unwrap(refs) \n", " for vname, refs in cf.refs_by_var().items()\n", " if vname not in ['X_train', 'X_test', 'y_train', 'y_test'] # to save space\n", " })\n", "\n", "pprint(cf.calls_by_func())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### From all calls to an `@op`\n", "Another way to create a CF is to initialize is with all calls to a given `@op`:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.094894Z", "iopub.status.busy": "2024-07-11T14:31:50.094678Z", "iopub.status.idle": "2024-07-11T14:31:50.225715Z", "shell.execute_reply": "2024-07-11T14:31:50.225196Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ComputationFrame with:\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">variable</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span> unique refs<span style=\"font-weight: bold\">)</span>\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">operation</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> unique calls<span style=\"font-weight: bold\">)</span>\n", "Computational graph:\n", "    var_0@output_0, var_1@output_1 = <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">train_model</span><span style=\"font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">X_train</span>=<span style=\"color: #800080; text-decoration-color: #800080\">X_train</span>, <span style=\"color: #808000; text-decoration-color: #808000\">n_estimators</span>=<span style=\"color: #800080; text-decoration-color: #800080\">n_estimators</span>, <span style=\"color: #808000; text-decoration-color: #808000\">y_train</span>=<span style=\"color: #800080; text-decoration-color: #800080\">y_train</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["ComputationFrame with:\n", "    \u001b[1;36m5\u001b[0m \u001b[1;35mvariable\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m14\u001b[0m unique refs\u001b[1m)\u001b[0m\n", "    \u001b[1;36m1\u001b[0m \u001b[1;35moperation\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m4\u001b[0m unique calls\u001b[1m)\u001b[0m\n", "Computational graph:\n", "    var_0@output_0, var_1@output_1 = \u001b[1;35mtrain_model\u001b[0m\u001b[1m(\u001b[0m\u001b[33mX_train\u001b[0m=\u001b[35mX_train\u001b[0m, \u001b[33mn_estimators\u001b[0m=\u001b[35mn_estimators\u001b[0m, \u001b[33my_train\u001b[0m=\u001b[35my_train\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"359pt\" height=\"236pt\"\n", " viewBox=\"0.00 0.00 359.00 236.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 232)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-232 355,-232 355,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M154.5,-36C154.5,-36 84.5,-36 84.5,-36 78.5,-36 72.5,-30 72.5,-24 72.5,-24 72.5,-12 72.5,-12 72.5,-6 78.5,0 84.5,0 84.5,0 154.5,0 154.5,0 160.5,0 166.5,-6 166.5,-12 166.5,-12 166.5,-24 166.5,-24 166.5,-30 160.5,-36 154.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"103.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"80.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-228C93,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 93,-192 93,-192 99,-192 105,-198 105,-204 105,-204 105,-216 105,-216 105,-222 99,-228 93,-228\"/>\n", "<text text-anchor=\"start\" x=\"15\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M209,-134C209,-134 142,-134 142,-134 136,-134 130,-128 130,-122 130,-122 130,-106 130,-106 130,-100 136,-94 142,-94 142,-94 209,-94 209,-94 215,-94 221,-100 221,-106 221,-106 221,-122 221,-122 221,-128 215,-134 209,-134\"/>\n", "<text text-anchor=\"start\" x=\"141.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"138\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"161\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M69.33,-191.81C81.37,-179.96 98.25,-164.21 114.5,-152 120.29,-147.65 126.61,-143.36 132.92,-139.33\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"134.83,-142.26 141.48,-134.01 131.14,-136.32 134.83,-142.26\"/>\n", "<text text-anchor=\"middle\" x=\"143\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"143\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M216,-228C216,-228 135,-228 135,-228 129,-228 123,-222 123,-216 123,-216 123,-204 123,-204 123,-198 129,-192 135,-192 135,-192 216,-192 216,-192 222,-192 228,-198 228,-204 228,-204 228,-216 228,-216 228,-222 222,-228 216,-228\"/>\n", "<text text-anchor=\"start\" x=\"155\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-191.76C175.5,-178.5 175.5,-159.86 175.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179,-144.07 175.5,-134.07 172,-144.07 179,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M339,-228C339,-228 258,-228 258,-228 252,-228 246,-222 246,-216 246,-216 246,-204 246,-204 246,-198 252,-192 258,-192 258,-192 339,-192 339,-192 345,-192 351,-198 351,-204 351,-204 351,-216 351,-216 351,-222 345,-228 339,-228\"/>\n", "<text text-anchor=\"start\" x=\"278.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"254\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M276.23,-191.98C257.4,-177.59 230.16,-156.78 208.76,-140.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"210.64,-137.45 200.57,-134.16 206.39,-143.02 210.64,-137.45\"/>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- var_1 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>var_1</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M266.5,-36C266.5,-36 196.5,-36 196.5,-36 190.5,-36 184.5,-30 184.5,-24 184.5,-24 184.5,-12 184.5,-12 184.5,-6 190.5,0 196.5,0 196.5,0 266.5,0 266.5,0 272.5,0 278.5,-6 278.5,-12 278.5,-12 278.5,-24 278.5,-24 278.5,-30 272.5,-36 266.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"215.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_1</text>\n", "<text text-anchor=\"start\" x=\"192.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>train_model&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M164.17,-93.98C155.73,-79.81 144.1,-60.3 134.83,-44.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"137.81,-42.9 129.69,-36.1 131.8,-46.48 137.81,-42.9\"/>\n", "<text text-anchor=\"middle\" x=\"174\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"174\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;var_1 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>train_model&#45;&gt;var_1</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M188.19,-93.94C191.91,-88.22 195.94,-81.9 199.5,-76 205.55,-65.99 211.91,-54.8 217.36,-45\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"220.49,-46.57 222.26,-36.12 214.36,-43.19 220.49,-46.57\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5fd9000>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(train_model)\n", "pprint(cf)\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can extract a dataframe from any CF (explained more later); in particular,\n", "the dataframe for the CF of a single `@op` will be the memoization table for\n", "this `@op`:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.228776Z", "iopub.status.busy": "2024-07-11T14:31:50.228568Z", "iopub.status.idle": "2024-07-11T14:31:50.285995Z", "shell.execute_reply": "2024-07-11T14:31:50.283270Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    | n_estimators                         | y_train                              | X_train                              | train_model                   | var_1                                | var_0                                |\n", "|---:|:-------------------------------------|:-------------------------------------|:-------------------------------------|:------------------------------|:-------------------------------------|:-------------------------------------|\n", "|  0 | AtomRef(hid=98c..., in_memory=False) | AtomRef(hid=faf..., in_memory=False) | AtomRef(hid=efa..., in_memory=False) | Call(train_model, hid=5f7...) | AtomRef(hid=760..., in_memory=False) | AtomRef(hid=b25..., in_memory=False) |\n", "|  1 | AtomRef(hid=235..., in_memory=False) | AtomRef(hid=faf..., in_memory=False) | AtomRef(hid=efa..., in_memory=False) | Call(train_model, hid=c55...) | AtomRef(hid=5b7..., in_memory=False) | AtomRef(hid=208..., in_memory=False) |\n", "|  2 | AtomRef(hid=9fd..., in_memory=False) | AtomRef(hid=faf..., in_memory=False) | AtomRef(hid=efa..., in_memory=False) | Call(train_model, hid=514...) | AtomRef(hid=784..., in_memory=False) | AtomRef(hid=331..., in_memory=False) |\n", "|  3 | AtomRef(hid=120..., in_memory=False) | AtomRef(hid=faf..., in_memory=False) | AtomRef(hid=efa..., in_memory=False) | Call(train_model, hid=e60...) | AtomRef(hid=646..., in_memory=False) | AtomRef(hid=522..., in_memory=False) |\n"]}], "source": ["print(cf.df(values='refs').to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### From groups of `Ref`s to use as variables\n", "You can also manually initialize variables of the CF by passing a dict where\n", "values are `Ref` iterables:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.291104Z", "iopub.status.busy": "2024-07-11T14:31:50.290766Z", "iopub.status.idle": "2024-07-11T14:31:50.349674Z", "shell.execute_reply": "2024-07-11T14:31:50.348876Z"}}, "outputs": [], "source": ["with storage: \n", "    models, test_accs = [], []\n", "    X_train, X_test, y_train, y_test = generate_dataset()\n", "    for n_estimators in [10, 20, 40, 80]:\n", "        model, train_acc = train_model(X_train, y_train, n_estimators=n_estimators)\n", "        models.append(model)\n", "        if storage.unwrap(train_acc) > 0.8: # conditional execution\n", "            test_acc = eval_model(model, X_test, y_test)\n", "            test_accs.append(test_acc)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.353220Z", "iopub.status.busy": "2024-07-11T14:31:50.352760Z", "iopub.status.idle": "2024-07-11T14:31:50.468910Z", "shell.execute_reply": "2024-07-11T14:31:50.468156Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ComputationFrame with:\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">variable</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span> unique refs<span style=\"font-weight: bold\">)</span>\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">operation</span><span style=\"font-weight: bold\">(</span>s<span style=\"font-weight: bold\">)</span> <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> unique calls<span style=\"font-weight: bold\">)</span>\n", "Computational graph:\n", "    model, test_acc\n", "</pre>\n"], "text/plain": ["ComputationFrame with:\n", "    \u001b[1;36m2\u001b[0m \u001b[1;35mvariable\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m6\u001b[0m unique refs\u001b[1m)\u001b[0m\n", "    \u001b[1;36m0\u001b[0m \u001b[1;35moperation\u001b[0m\u001b[1m(\u001b[0ms\u001b[1m)\u001b[0m \u001b[1m(\u001b[0m\u001b[1;36m0\u001b[0m unique calls\u001b[1m)\u001b[0m\n", "Computational graph:\n", "    model, test_acc\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"306pt\" height=\"44pt\"\n", " viewBox=\"0.00 0.00 306.00 44.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 40)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-40 302,-40 302,4 -4,4\"/>\n", "<!-- test_acc -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>test_acc</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M128,-36C128,-36 12,-36 12,-36 6,-36 0,-30 0,-24 0,-24 0,-12 0,-12 0,-6 6,0 12,0 12,0 128,0 128,0 134,0 140,-6 140,-12 140,-12 140,-24 140,-24 140,-30 134,-36 128,-36\"/>\n", "<text text-anchor=\"start\" x=\"45.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">test_acc</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources/2 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M286,-36C286,-36 170,-36 170,-36 164,-36 158,-30 158,-24 158,-24 158,-12 158,-12 158,-6 164,0 170,0 170,0 286,0 286,0 292,0 298,-6 298,-12 298,-12 298,-24 298,-24 298,-30 292,-36 286,-36\"/>\n", "<text text-anchor=\"start\" x=\"210\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"166\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sources/4 sinks)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5f55930>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf({'model': models, 'test_acc': test_accs})\n", "pprint(cf)\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Again, this is not interesting unless you `expand` back and/or forward. We can \n", "illustrate by expanding only the `model` variable forward:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.472539Z", "iopub.status.busy": "2024-07-11T14:31:50.471959Z", "iopub.status.idle": "2024-07-11T14:31:50.607274Z", "shell.execute_reply": "2024-07-11T14:31:50.606483Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"148pt\" height=\"236pt\"\n", " viewBox=\"0.00 0.00 148.00 236.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 232)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-232 144,-232 144,4 -4,4\"/>\n", "<!-- test_acc -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>test_acc</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M105,-36C105,-36 35,-36 35,-36 29,-36 23,-30 23,-24 23,-24 23,-12 23,-12 23,-6 29,0 35,0 35,0 105,0 105,0 111,0 117,-6 117,-12 117,-12 117,-24 117,-24 117,-30 111,-36 105,-36\"/>\n", "<text text-anchor=\"start\" x=\"45.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">test_acc</text>\n", "<text text-anchor=\"start\" x=\"31\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M128,-228C128,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 128,-192 128,-192 134,-192 140,-198 140,-204 140,-204 140,-216 140,-216 140,-222 134,-228 128,-228\"/>\n", "<text text-anchor=\"start\" x=\"52\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sources/2 sinks)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M103,-134C103,-134 37,-134 37,-134 31,-134 25,-128 25,-122 25,-122 25,-106 25,-106 25,-100 31,-94 37,-94 37,-94 103,-94 103,-94 109,-94 115,-100 115,-106 115,-106 115,-122 115,-122 115,-128 109,-134 103,-134\"/>\n", "<text text-anchor=\"start\" x=\"37\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"33\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"55.5\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M70,-191.76C70,-178.5 70,-159.86 70,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"73.5,-144.07 70,-134.07 66.5,-144.07 73.5,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"91.5\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"91.5\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;test_acc -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>eval_model&#45;&gt;test_acc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M70,-93.98C70,-80.34 70,-61.75 70,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"73.5,-46.1 70,-36.1 66.5,-46.1 73.5,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"91.5\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"91.5\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5e05960>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf.expand_forward(varnames=['model'], inplace=True)\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The expansion algorithm figures out that the calls to `eval_model` we add\n", "should connect to the `test_acc` variable."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### From any collection of calls\n", "TODO"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring storage by expanding `ComputationFrame`s \n", "Once a CF is created, you can add computational context to it by calling one of\n", "a few methods:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.610888Z", "iopub.status.busy": "2024-07-11T14:31:50.610437Z", "iopub.status.idle": "2024-07-11T14:31:50.658974Z", "shell.execute_reply": "2024-07-11T14:31:50.658195Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Docstring for `expand_back`:\n", "</pre>\n"], "text/plain": ["Docstring for `expand_back`:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "        Join to the CF the calls that created all refs in the given variables\n", "        that currently do not have a connected creator call in the CF.\n", "\n", "        If such refs are found, this will result to the addition of \n", "        - new function nodes for the calls that created these refs;\n", "        - new variable nodes for the *inputs* of these calls.\n", "\n", "        The number of these nodes and how they connect to the CF will depend on\n", "        the structure of the calls that created the refs. \n", "\n", "        Arguments:\n", "        - `varnames`: the names of the variables to expand; if <span style=\"color: #800080; text-decoration-color: #800080; font-style: italic\">None</span>, expand all\n", "        the `Ref`s that don't have a creator call in any function node of the CF\n", "        that is connected to the `Ref`'s variable node as an output.\n", "        - `recursive`: if <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-style: italic\">True</span>, keep expanding until a fixed point is reached\n", "        \n", "</pre>\n"], "text/plain": ["\n", "        Join to the CF the calls that created all refs in the given variables\n", "        that currently do not have a connected creator call in the CF.\n", "\n", "        If such refs are found, this will result to the addition of \n", "        - new function nodes for the calls that created these refs;\n", "        - new variable nodes for the *inputs* of these calls.\n", "\n", "        The number of these nodes and how they connect to the CF will depend on\n", "        the structure of the calls that created the refs. \n", "\n", "        Arguments:\n", "        - `varnames`: the names of the variables to expand; if \u001b[3;35mNone\u001b[0m, expand all\n", "        the `Ref`s that don't have a creator call in any function node of the CF\n", "        that is connected to the `Ref`'s variable node as an output.\n", "        - `recursive`: if \u001b[3;92mTrue\u001b[0m, keep expanding until a fixed point is reached\n", "        \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Docstring for `expand_forward`:\n", "</pre>\n"], "text/plain": ["Docstring for `expand_forward`:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "        Join the calls that consume the given variables; see `expand_back` <span style=\"font-weight: bold\">(</span>the \n", "        dual operation<span style=\"font-weight: bold\">)</span> for more details.\n", "        \n", "</pre>\n"], "text/plain": ["\n", "        Join the calls that consume the given variables; see `expand_back` \u001b[1m(\u001b[0mthe \n", "        dual operation\u001b[1m)\u001b[0m for more details.\n", "        \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Docstring for `expand_all`:\n", "</pre>\n"], "text/plain": ["Docstring for `expand_all`:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "        Expand the computation frame by repeatedly applying `expand_back` and\n", "        `expand_forward` until a fixed point is reached.\n", "        \n", "</pre>\n"], "text/plain": ["\n", "        Expand the computation frame by repeatedly applying `expand_back` and\n", "        `expand_forward` until a fixed point is reached.\n", "        \n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pprint('Docstring for `expand_back`:')\n", "pprint(cf.expand_back.__doc__)\n", "pprint('Docstring for `expand_forward`:')\n", "pprint(cf.expand_forward.__doc__)\n", "pprint('Docstring for `expand_all`:')\n", "pprint(cf.expand_all.__doc__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Selective expansion with `expand_back`, `expand_forward`\n", "You can be very precise about which calls to add to the CF. For example, you can\n", "expand variables one by one. **When this results in convergent histories, the CF\n", "will detect this and reuse the calls**:  "]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.661950Z", "iopub.status.busy": "2024-07-11T14:31:50.661502Z", "iopub.status.idle": "2024-07-11T14:31:50.791104Z", "shell.execute_reply": "2024-07-11T14:31:50.790153Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"359pt\" height=\"236pt\"\n", " viewBox=\"0.00 0.00 359.00 236.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 232)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-232 355,-232 355,4 -4,4\"/>\n", "<!-- y_test -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-228C93,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 93,-192 93,-192 99,-192 105,-198 105,-204 105,-204 105,-216 105,-216 105,-222 99,-228 93,-228\"/>\n", "<text text-anchor=\"start\" x=\"35\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M208.5,-134C208.5,-134 142.5,-134 142.5,-134 136.5,-134 130.5,-128 130.5,-122 130.5,-122 130.5,-106 130.5,-106 130.5,-100 136.5,-94 142.5,-94 142.5,-94 208.5,-94 208.5,-94 214.5,-94 220.5,-100 220.5,-106 220.5,-106 220.5,-122 220.5,-122 220.5,-128 214.5,-134 208.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"142.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"138.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"161\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M74.77,-191.98C93.6,-177.59 120.84,-156.78 142.24,-140.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"144.61,-143.02 150.43,-134.16 140.36,-137.45 144.61,-143.02\"/>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- v -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>v</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M210.5,-36C210.5,-36 140.5,-36 140.5,-36 134.5,-36 128.5,-30 128.5,-24 128.5,-24 128.5,-12 128.5,-12 128.5,-6 134.5,0 140.5,0 140.5,0 210.5,0 210.5,0 216.5,0 222.5,-6 222.5,-12 222.5,-12 222.5,-24 222.5,-24 222.5,-30 216.5,-36 210.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"172\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"start\" x=\"136.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sinks)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M216,-228C216,-228 135,-228 135,-228 129,-228 123,-222 123,-216 123,-216 123,-204 123,-204 123,-198 129,-192 135,-192 135,-192 216,-192 216,-192 222,-192 228,-198 228,-204 228,-204 228,-216 228,-216 228,-222 222,-228 216,-228\"/>\n", "<text text-anchor=\"start\" x=\"157.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-191.76C175.5,-178.5 175.5,-159.86 175.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179,-144.07 175.5,-134.07 172,-144.07 179,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M339,-228C339,-228 258,-228 258,-228 252,-228 246,-222 246,-216 246,-216 246,-204 246,-204 246,-198 252,-192 258,-192 258,-192 339,-192 339,-192 345,-192 351,-198 351,-204 351,-204 351,-216 351,-216 351,-222 345,-228 339,-228\"/>\n", "<text text-anchor=\"start\" x=\"280\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"254\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M276.23,-191.98C257.4,-177.59 230.16,-156.78 208.76,-140.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"210.64,-137.45 200.57,-134.16 206.39,-143.02 210.64,-137.45\"/>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;v -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;v</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-93.98C175.5,-80.34 175.5,-61.75 175.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179,-46.1 175.5,-36.1 172,-46.1 179,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5e05ae0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(test_acc)\n", "cf = cf.expand_back('v')\n", "cf.draw(verbose=True)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.794534Z", "iopub.status.busy": "2024-07-11T14:31:50.794056Z", "iopub.status.idle": "2024-07-11T14:31:50.912096Z", "shell.execute_reply": "2024-07-11T14:31:50.911253Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"341pt\" height=\"428pt\"\n", " viewBox=\"0.00 0.00 340.50 428.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 424)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-424 336.5,-424 336.5,4 -4,4\"/>\n", "<!-- v -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>v</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M210.5,-36C210.5,-36 140.5,-36 140.5,-36 134.5,-36 128.5,-30 128.5,-24 128.5,-24 128.5,-12 128.5,-12 128.5,-6 134.5,0 140.5,0 140.5,0 210.5,0 210.5,0 216.5,0 222.5,-6 222.5,-12 222.5,-12 222.5,-24 222.5,-24 222.5,-30 216.5,-36 210.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"172\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"start\" x=\"136.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sinks)</text>\n", "</g>\n", "<!-- y_test -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-228C93,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 93,-192 93,-192 99,-192 105,-198 105,-204 105,-204 105,-216 105,-216 105,-222 99,-228 93,-228\"/>\n", "<text text-anchor=\"start\" x=\"35\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M208.5,-134C208.5,-134 142.5,-134 142.5,-134 136.5,-134 130.5,-128 130.5,-122 130.5,-122 130.5,-106 130.5,-106 130.5,-100 136.5,-94 142.5,-94 142.5,-94 208.5,-94 208.5,-94 214.5,-94 220.5,-100 220.5,-106 220.5,-106 220.5,-122 220.5,-122 220.5,-128 214.5,-134 208.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"142.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"138.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"161\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M74.77,-191.98C93.6,-177.59 120.84,-156.78 142.24,-140.42\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"144.61,-143.02 150.43,-134.16 140.36,-137.45 144.61,-143.02\"/>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"147\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- random_seed -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M314,-420C314,-420 233,-420 233,-420 227,-420 221,-414 221,-408 221,-408 221,-396 221,-396 221,-390 227,-384 233,-384 233,-384 314,-384 314,-384 320,-384 326,-390 326,-396 326,-396 326,-408 326,-408 326,-414 320,-420 314,-420\"/>\n", "<text text-anchor=\"start\" x=\"234\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"229\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M319.5,-326C319.5,-326 227.5,-326 227.5,-326 221.5,-326 215.5,-320 215.5,-314 215.5,-314 215.5,-298 215.5,-298 215.5,-292 221.5,-286 227.5,-286 227.5,-286 319.5,-286 319.5,-286 325.5,-286 331.5,-292 331.5,-298 331.5,-298 331.5,-314 331.5,-314 331.5,-320 325.5,-326 319.5,-326\"/>\n", "<text text-anchor=\"start\" x=\"223.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"223.5\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"259\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M273.5,-383.76C273.5,-370.5 273.5,-351.86 273.5,-336.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"277,-336.07 273.5,-326.07 270,-336.07 277,-336.07\"/>\n", "<text text-anchor=\"middle\" x=\"303\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"303\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M216,-228C216,-228 135,-228 135,-228 129,-228 123,-222 123,-216 123,-216 123,-204 123,-204 123,-198 129,-192 135,-192 135,-192 216,-192 216,-192 222,-192 228,-198 228,-204 228,-204 228,-216 228,-216 228,-222 222,-228 216,-228\"/>\n", "<text text-anchor=\"start\" x=\"157.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-191.76C175.5,-178.5 175.5,-159.86 175.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179,-144.07 175.5,-134.07 172,-144.07 179,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M288.5,-228C288.5,-228 258.5,-228 258.5,-228 252.5,-228 246.5,-222 246.5,-216 246.5,-216 246.5,-204 246.5,-204 246.5,-198 252.5,-192 258.5,-192 258.5,-192 288.5,-192 288.5,-192 294.5,-192 300.5,-198 300.5,-204 300.5,-204 300.5,-216 300.5,-216 300.5,-222 294.5,-228 288.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"255\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"255\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M259.5,-191.83C249.64,-180.13 235.86,-164.54 222.5,-152 218.43,-148.18 214,-144.33 209.53,-140.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"211.52,-137.74 201.54,-134.19 207.13,-143.19 211.52,-137.74\"/>\n", "<text text-anchor=\"middle\" x=\"264\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"264\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;v -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;v</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-93.98C175.5,-80.34 175.5,-61.75 175.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179,-46.1 175.5,-36.1 172,-46.1 179,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"197\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_test -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M273.5,-285.98C273.5,-272.34 273.5,-253.75 273.5,-238.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"277,-238.1 273.5,-228.1 270,-238.1 277,-238.1\"/>\n", "<text text-anchor=\"middle\" x=\"295\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"295\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5fdab60>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = cf.expand_back('X_test')\n", "cf.draw(verbose=True)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:50.915142Z", "iopub.status.busy": "2024-07-11T14:31:50.914833Z", "iopub.status.idle": "2024-07-11T14:31:51.034000Z", "shell.execute_reply": "2024-07-11T14:31:51.033381Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"482pt\" height=\"428pt\"\n", " viewBox=\"0.00 0.00 482.00 428.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 424)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-424 478,-424 478,4 -4,4\"/>\n", "<!-- v -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>v</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M294.5,-36C294.5,-36 224.5,-36 224.5,-36 218.5,-36 212.5,-30 212.5,-24 212.5,-24 212.5,-12 212.5,-12 212.5,-6 218.5,0 224.5,0 224.5,0 294.5,0 294.5,0 300.5,0 306.5,-6 306.5,-12 306.5,-12 306.5,-24 306.5,-24 306.5,-30 300.5,-36 294.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"256\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"start\" x=\"220.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sinks)</text>\n", "</g>\n", "<!-- y_test -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M202,-228C202,-228 121,-228 121,-228 115,-228 109,-222 109,-216 109,-216 109,-204 109,-204 109,-198 115,-192 121,-192 121,-192 202,-192 202,-192 208,-192 214,-198 214,-204 214,-204 214,-216 214,-216 214,-222 208,-228 202,-228\"/>\n", "<text text-anchor=\"start\" x=\"144\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"117\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M292.5,-134C292.5,-134 226.5,-134 226.5,-134 220.5,-134 214.5,-128 214.5,-122 214.5,-122 214.5,-106 214.5,-106 214.5,-100 220.5,-94 226.5,-94 226.5,-94 292.5,-94 292.5,-94 298.5,-94 304.5,-100 304.5,-106 304.5,-106 304.5,-122 304.5,-122 304.5,-128 298.5,-134 292.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"226.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"222.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"245\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M175.5,-191.83C185.36,-180.13 199.14,-164.54 212.5,-152 216.57,-148.18 221,-144.33 225.47,-140.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"227.87,-143.19 233.46,-134.19 223.48,-137.74 227.87,-143.19\"/>\n", "<text text-anchor=\"middle\" x=\"234\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"234\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420\"/>\n", "<text text-anchor=\"start\" x=\"15\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M270,-326C270,-326 203,-326 203,-326 197,-326 191,-320 191,-314 191,-314 191,-298 191,-298 191,-292 197,-286 203,-286 203,-286 270,-286 270,-286 276,-286 282,-292 282,-298 282,-298 282,-314 282,-314 282,-320 276,-326 270,-326\"/>\n", "<text text-anchor=\"start\" x=\"202.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"199\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"222\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M75.36,-383.79C92.21,-371.63 116.02,-355.51 138.5,-344 151.92,-337.13 166.92,-330.83 181.07,-325.45\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"182.54,-328.64 190.7,-321.88 180.11,-322.08 182.54,-328.64\"/>\n", "<text text-anchor=\"middle\" x=\"167\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"167\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M216,-420C216,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 216,-384 216,-384 222,-384 228,-390 228,-396 228,-396 228,-408 228,-408 228,-414 222,-420 216,-420\"/>\n", "<text text-anchor=\"start\" x=\"155\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M186.69,-383.76C195.63,-369.99 208.32,-350.42 218.66,-334.49\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"221.62,-336.37 224.12,-326.07 215.74,-332.56 221.62,-336.37\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- random_seed -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M462,-420C462,-420 381,-420 381,-420 375,-420 369,-414 369,-408 369,-408 369,-396 369,-396 369,-390 375,-384 381,-384 381,-384 462,-384 462,-384 468,-384 474,-390 474,-396 474,-396 474,-408 474,-408 474,-414 468,-420 462,-420\"/>\n", "<text text-anchor=\"start\" x=\"382\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"377\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M435.5,-326C435.5,-326 343.5,-326 343.5,-326 337.5,-326 331.5,-320 331.5,-314 331.5,-314 331.5,-298 331.5,-298 331.5,-292 337.5,-286 343.5,-286 343.5,-286 435.5,-286 435.5,-286 441.5,-286 447.5,-292 447.5,-298 447.5,-298 447.5,-314 447.5,-314 447.5,-320 441.5,-326 435.5,-326\"/>\n", "<text text-anchor=\"start\" x=\"339.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"339.5\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"375\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M415.63,-383.76C411.07,-370.37 404.65,-351.5 399.31,-335.82\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"402.53,-334.41 395.99,-326.07 395.9,-336.67 402.53,-334.41\"/>\n", "<text text-anchor=\"middle\" x=\"438\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"438\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M339,-420C339,-420 258,-420 258,-420 252,-420 246,-414 246,-408 246,-408 246,-396 246,-396 246,-390 252,-384 258,-384 258,-384 339,-384 339,-384 345,-384 351,-390 351,-396 351,-396 351,-408 351,-408 351,-414 345,-420 339,-420\"/>\n", "<text text-anchor=\"start\" x=\"278.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"254\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M287.13,-383.76C278.04,-369.99 265.14,-350.42 254.63,-334.49\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"257.51,-332.49 249.08,-326.07 251.66,-336.35 257.51,-332.49\"/>\n", "<text text-anchor=\"middle\" x=\"297\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"297\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M274.5,-228C274.5,-228 244.5,-228 244.5,-228 238.5,-228 232.5,-222 232.5,-216 232.5,-216 232.5,-204 232.5,-204 232.5,-198 238.5,-192 244.5,-192 244.5,-192 274.5,-192 274.5,-192 280.5,-192 286.5,-198 286.5,-204 286.5,-204 286.5,-216 286.5,-216 286.5,-222 280.5,-228 274.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"241.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"241\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M259.5,-191.76C259.5,-178.5 259.5,-159.86 259.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"263,-144.07 259.5,-134.07 256,-144.07 263,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"281\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"281\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M371.5,-228C371.5,-228 341.5,-228 341.5,-228 335.5,-228 329.5,-222 329.5,-216 329.5,-216 329.5,-204 329.5,-204 329.5,-198 335.5,-192 341.5,-192 341.5,-192 371.5,-192 371.5,-192 377.5,-192 383.5,-198 383.5,-204 383.5,-204 383.5,-216 383.5,-216 383.5,-222 377.5,-228 371.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"338\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"338\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M342.83,-191.8C333.18,-180.09 319.68,-164.5 306.5,-152 302.45,-148.16 298.03,-144.3 293.57,-140.6\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"295.56,-137.71 285.58,-134.15 291.16,-143.15 295.56,-137.71\"/>\n", "<text text-anchor=\"middle\" x=\"349\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"349\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M241.15,-285.98C244.52,-272.2 249.13,-253.39 252.88,-238.05\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"256.34,-238.64 255.32,-228.1 249.54,-236.98 256.34,-238.64\"/>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"273\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;v -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;v</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M259.5,-93.98C259.5,-80.34 259.5,-61.75 259.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"263,-46.1 259.5,-36.1 256,-46.1 263,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"281\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"281\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_test -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M382.82,-285.98C377.94,-272.07 371.25,-253.03 365.84,-237.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"369.12,-236.37 362.5,-228.1 362.51,-238.69 369.12,-236.37\"/>\n", "<text text-anchor=\"middle\" x=\"397\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"397\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5fd9a20>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = cf.expand_back('model')\n", "cf.draw(verbose=True)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:51.036791Z", "iopub.status.busy": "2024-07-11T14:31:51.036531Z", "iopub.status.idle": "2024-07-11T14:31:51.153862Z", "shell.execute_reply": "2024-07-11T14:31:51.153235Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"371pt\" height=\"634pt\"\n", " viewBox=\"0.00 0.00 370.50 634.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 630)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-630 366.5,-630 366.5,4 -4,4\"/>\n", "<!-- v -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>v</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M266.5,-36C266.5,-36 196.5,-36 196.5,-36 190.5,-36 184.5,-30 184.5,-24 184.5,-24 184.5,-12 184.5,-12 184.5,-6 190.5,0 196.5,0 196.5,0 266.5,0 266.5,0 272.5,0 278.5,-6 278.5,-12 278.5,-12 278.5,-24 278.5,-24 278.5,-30 272.5,-36 266.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"228\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">v</text>\n", "<text text-anchor=\"start\" x=\"192.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sinks)</text>\n", "</g>\n", "<!-- y_test -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>y_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M174,-228C174,-228 93,-228 93,-228 87,-228 81,-222 81,-216 81,-216 81,-204 81,-204 81,-198 87,-192 93,-192 93,-192 174,-192 174,-192 180,-192 186,-198 186,-204 186,-204 186,-216 186,-216 186,-222 180,-228 174,-228\"/>\n", "<text text-anchor=\"start\" x=\"116\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"start\" x=\"89\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M264.5,-134C264.5,-134 198.5,-134 198.5,-134 192.5,-134 186.5,-128 186.5,-122 186.5,-122 186.5,-106 186.5,-106 186.5,-100 192.5,-94 198.5,-94 198.5,-94 264.5,-94 264.5,-94 270.5,-94 276.5,-100 276.5,-106 276.5,-106 276.5,-122 276.5,-122 276.5,-128 270.5,-134 264.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"198.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"194.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"217\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- y_test&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>y_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M147.5,-191.83C157.36,-180.13 171.14,-164.54 184.5,-152 188.57,-148.18 193,-144.33 197.47,-140.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"199.87,-143.19 205.46,-134.19 195.48,-137.74 199.87,-143.19\"/>\n", "<text text-anchor=\"middle\" x=\"206\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_test</text>\n", "<text text-anchor=\"middle\" x=\"206\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M168,-434C168,-434 87,-434 87,-434 81,-434 75,-428 75,-422 75,-422 75,-410 75,-410 75,-404 81,-398 87,-398 87,-398 168,-398 168,-398 174,-398 180,-404 180,-410 180,-410 180,-422 180,-422 180,-428 174,-434 168,-434\"/>\n", "<text text-anchor=\"start\" x=\"90\" y=\"-418.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"83\" y=\"-408\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M222,-326C222,-326 155,-326 155,-326 149,-326 143,-320 143,-314 143,-314 143,-298 143,-298 143,-292 149,-286 155,-286 155,-286 222,-286 222,-286 228,-286 234,-292 234,-298 234,-298 234,-314 234,-314 234,-320 228,-326 222,-326\"/>\n", "<text text-anchor=\"start\" x=\"154.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"151\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"174\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M137.3,-397.65C146.84,-380.76 161.48,-354.83 172.67,-335.02\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"175.82,-336.56 177.7,-326.13 169.73,-333.12 175.82,-336.56\"/>\n", "<text text-anchor=\"middle\" x=\"195\" y=\"-365\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"195\" y=\"-354\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M45,-434C45,-434 12,-434 12,-434 6,-434 0,-428 0,-422 0,-422 0,-410 0,-410 0,-404 6,-398 12,-398 12,-398 45,-398 45,-398 51,-398 57,-404 57,-410 57,-410 57,-422 57,-422 57,-428 51,-434 45,-434\"/>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-418.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"10\" y=\"-408\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M42.7,-397.95C56.45,-382.3 78.53,-359.27 101.5,-344 111.23,-337.53 122.27,-331.8 133.14,-326.9\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"134.82,-329.98 142.61,-322.8 132.05,-323.56 134.82,-329.98\"/>\n", "<text text-anchor=\"middle\" x=\"123\" y=\"-365\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"123\" y=\"-354\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- random_seed -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M221,-626C221,-626 140,-626 140,-626 134,-626 128,-620 128,-614 128,-614 128,-602 128,-602 128,-596 134,-590 140,-590 140,-590 221,-590 221,-590 227,-590 233,-596 233,-602 233,-602 233,-614 233,-614 233,-620 227,-626 221,-626\"/>\n", "<text text-anchor=\"start\" x=\"141\" y=\"-610.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"136\" y=\"-600\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node11\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M226.5,-532C226.5,-532 134.5,-532 134.5,-532 128.5,-532 122.5,-526 122.5,-520 122.5,-520 122.5,-504 122.5,-504 122.5,-498 128.5,-492 134.5,-492 134.5,-492 226.5,-492 226.5,-492 232.5,-492 238.5,-498 238.5,-504 238.5,-504 238.5,-520 238.5,-520 238.5,-526 232.5,-532 226.5,-532\"/>\n", "<text text-anchor=\"start\" x=\"130.5\" y=\"-519.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"130.5\" y=\"-509\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"166\" y=\"-499\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M180.5,-589.76C180.5,-576.5 180.5,-557.86 180.5,-542.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"184,-542.07 180.5,-532.07 177,-542.07 184,-542.07\"/>\n", "<text text-anchor=\"middle\" x=\"210\" y=\"-564\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"210\" y=\"-553\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M291,-434C291,-434 210,-434 210,-434 204,-434 198,-428 198,-422 198,-422 198,-410 198,-410 198,-404 204,-398 210,-398 210,-398 291,-398 291,-398 297,-398 303,-404 303,-410 303,-410 303,-422 303,-422 303,-428 297,-434 291,-434\"/>\n", "<text text-anchor=\"start\" x=\"230.5\" y=\"-418.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"206\" y=\"-408\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M247.46,-397.91C244.22,-382.86 238.07,-360.77 227.5,-344 225.2,-340.36 222.47,-336.83 219.53,-333.49\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"221.86,-330.86 212.4,-326.07 216.81,-335.71 221.86,-330.86\"/>\n", "<text text-anchor=\"middle\" x=\"264\" y=\"-365\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"264\" y=\"-354\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M246.5,-228C246.5,-228 216.5,-228 216.5,-228 210.5,-228 204.5,-222 204.5,-216 204.5,-216 204.5,-204 204.5,-204 204.5,-198 210.5,-192 216.5,-192 216.5,-192 246.5,-192 246.5,-192 252.5,-192 258.5,-198 258.5,-204 258.5,-204 258.5,-216 258.5,-216 258.5,-222 252.5,-228 246.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"213.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"213\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M231.5,-191.76C231.5,-178.5 231.5,-159.86 231.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"235,-144.07 231.5,-134.07 228,-144.07 235,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"253\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"253\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- X_test -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>X_test</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M343.5,-380C343.5,-380 313.5,-380 313.5,-380 307.5,-380 301.5,-374 301.5,-368 301.5,-368 301.5,-356 301.5,-356 301.5,-350 307.5,-344 313.5,-344 313.5,-344 343.5,-344 343.5,-344 349.5,-344 355.5,-350 355.5,-356 355.5,-356 355.5,-368 355.5,-368 355.5,-374 349.5,-380 343.5,-380\"/>\n", "<text text-anchor=\"start\" x=\"310\" y=\"-364.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"start\" x=\"310\" y=\"-354\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_test&#45;&gt;eval_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X_test&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M328.12,-343.83C326.54,-306.14 318.52,-214.53 278.5,-152 276.04,-148.16 273.04,-144.54 269.76,-141.19\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"271.96,-138.47 262.22,-134.29 267.23,-143.63 271.96,-138.47\"/>\n", "<text text-anchor=\"middle\" x=\"341\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_test</text>\n", "<text text-anchor=\"middle\" x=\"341\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M197.2,-285.98C203.56,-272.07 212.27,-253.03 219.33,-237.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"222.7,-238.65 223.68,-228.1 216.34,-235.74 222.7,-238.65\"/>\n", "<text text-anchor=\"middle\" x=\"236\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"236\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;v -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;v</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M231.5,-93.98C231.5,-80.34 231.5,-61.75 231.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"235,-46.1 231.5,-36.1 228,-46.1 235,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"253\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"253\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_train -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M149.74,-491.98C125.22,-476.81 90.82,-455.54 64.92,-439.52\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"66.49,-436.38 56.15,-434.1 62.81,-442.33 66.49,-436.38\"/>\n", "<text text-anchor=\"middle\" x=\"139\" y=\"-466\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"139\" y=\"-455\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_test -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_test</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M234.09,-491.86C261.48,-479.44 293.06,-460.54 312.5,-434 321.72,-421.41 325.77,-404.44 327.49,-390.25\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"331,-390.33 328.4,-380.06 324.02,-389.71 331,-390.33\"/>\n", "<text text-anchor=\"middle\" x=\"316\" y=\"-466\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"316\" y=\"-455\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5da1480>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = cf.expand_back('X_train')\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When we expand `X_train` at the end, the expansion algorithm detects that the\n", "`generate_dataset` node should be reused instead of creating a new node."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Full expansion with `expand_all`\n", "The easiest way to add all the calls that can be reached from a CF by calling\n", "`expand_back` or `expand_forward` is using `expand_all`"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:51.156955Z", "iopub.status.busy": "2024-07-11T14:31:51.156609Z", "iopub.status.idle": "2024-07-11T14:31:51.318771Z", "shell.execute_reply": "2024-07-11T14:31:51.318000Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"263pt\" height=\"620pt\"\n", " viewBox=\"0.00 0.00 262.50 620.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 616)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-616 258.5,-616 258.5,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M130.5,-228C130.5,-228 60.5,-228 60.5,-228 54.5,-228 48.5,-222 48.5,-216 48.5,-216 48.5,-204 48.5,-204 48.5,-198 54.5,-192 60.5,-192 60.5,-192 130.5,-192 130.5,-192 136.5,-192 142.5,-198 142.5,-204 142.5,-204 142.5,-216 142.5,-216 142.5,-222 136.5,-228 130.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"79.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"56.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (2 sinks)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M128.5,-134C128.5,-134 62.5,-134 62.5,-134 56.5,-134 50.5,-128 50.5,-122 50.5,-122 50.5,-106 50.5,-106 50.5,-100 56.5,-94 62.5,-94 62.5,-94 128.5,-94 128.5,-94 134.5,-94 140.5,-100 140.5,-106 140.5,-106 140.5,-122 140.5,-122 140.5,-128 134.5,-134 128.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"62.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"58.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"81\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- var_0&#45;&gt;eval_model -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>var_0&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M95.5,-191.76C95.5,-178.5 95.5,-159.86 95.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"99,-144.07 95.5,-134.07 92,-144.07 99,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420\"/>\n", "<text text-anchor=\"start\" x=\"15\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M185,-326C185,-326 118,-326 118,-326 112,-326 106,-320 106,-314 106,-314 106,-298 106,-298 106,-292 112,-286 118,-286 118,-286 185,-286 185,-286 191,-286 197,-292 197,-298 197,-298 197,-314 197,-314 197,-320 191,-326 185,-326\"/>\n", "<text text-anchor=\"start\" x=\"117.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"114\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"137\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M61.28,-383.85C68.09,-371.71 78.39,-355.6 90.5,-344 95.08,-339.61 100.26,-335.5 105.63,-331.73\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"107.75,-334.53 114.16,-326.09 103.88,-328.69 107.75,-334.53\"/>\n", "<text text-anchor=\"middle\" x=\"119\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"119\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M168,-420C168,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 168,-384 168,-384 174,-384 180,-390 180,-396 180,-396 180,-408 180,-408 180,-414 174,-420 168,-420\"/>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"133\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M151.5,-383.76C151.5,-370.5 151.5,-351.86 151.5,-336.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"155,-336.07 151.5,-326.07 148,-336.07 155,-336.07\"/>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- var_2 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>var_2</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M130.5,-36C130.5,-36 60.5,-36 60.5,-36 54.5,-36 48.5,-30 48.5,-24 48.5,-24 48.5,-12 48.5,-12 48.5,-6 54.5,0 60.5,0 60.5,0 130.5,0 130.5,0 136.5,0 142.5,-6 142.5,-12 142.5,-12 142.5,-24 142.5,-24 142.5,-30 136.5,-36 130.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"79.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_2</text>\n", "<text text-anchor=\"start\" x=\"56.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- random_seed -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M229,-612C229,-612 148,-612 148,-612 142,-612 136,-606 136,-600 136,-600 136,-588 136,-588 136,-582 142,-576 148,-576 148,-576 229,-576 229,-576 235,-576 241,-582 241,-588 241,-588 241,-600 241,-600 241,-606 235,-612 229,-612\"/>\n", "<text text-anchor=\"start\" x=\"149\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"144\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M234.5,-518C234.5,-518 142.5,-518 142.5,-518 136.5,-518 130.5,-512 130.5,-506 130.5,-506 130.5,-490 130.5,-490 130.5,-484 136.5,-478 142.5,-478 142.5,-478 234.5,-478 234.5,-478 240.5,-478 246.5,-484 246.5,-490 246.5,-490 246.5,-506 246.5,-506 246.5,-512 240.5,-518 234.5,-518\"/>\n", "<text text-anchor=\"start\" x=\"138.5\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"138.5\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"174\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M188.5,-575.76C188.5,-562.5 188.5,-543.86 188.5,-528.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"192,-528.07 188.5,-518.07 185,-528.07 192,-528.07\"/>\n", "<text text-anchor=\"middle\" x=\"218\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"218\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M242.5,-420C242.5,-420 210.5,-420 210.5,-420 204.5,-420 198.5,-414 198.5,-408 198.5,-408 198.5,-396 198.5,-396 198.5,-390 204.5,-384 210.5,-384 210.5,-384 242.5,-384 242.5,-384 248.5,-384 254.5,-390 254.5,-396 254.5,-396 254.5,-408 254.5,-408 254.5,-414 248.5,-420 242.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"206.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"208\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M220.44,-383.89C215.67,-371.92 208.21,-356 198.5,-344 195.4,-340.17 191.83,-336.48 188.07,-333.02\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"190.06,-330.11 180.19,-326.26 185.5,-335.43 190.06,-330.11\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- var_1 -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>var_1</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M242.5,-228C242.5,-228 172.5,-228 172.5,-228 166.5,-228 160.5,-222 160.5,-216 160.5,-216 160.5,-204 160.5,-204 160.5,-198 166.5,-192 172.5,-192 172.5,-192 242.5,-192 242.5,-192 248.5,-192 254.5,-198 254.5,-204 254.5,-204 254.5,-216 254.5,-216 254.5,-222 248.5,-228 242.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"191.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_1</text>\n", "<text text-anchor=\"start\" x=\"168.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>train_model&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M140.17,-285.98C131.73,-271.81 120.1,-252.3 110.83,-236.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"113.81,-234.9 105.69,-228.1 107.8,-238.48 113.81,-234.9\"/>\n", "<text text-anchor=\"middle\" x=\"150\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"150\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;var_1 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>train_model&#45;&gt;var_1</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M164.19,-285.94C167.91,-280.22 171.94,-273.9 175.5,-268 181.55,-257.99 187.91,-246.8 193.36,-237\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"196.49,-238.57 198.26,-228.12 190.36,-235.19 196.49,-238.57\"/>\n", "<text text-anchor=\"middle\" x=\"209\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"209\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;var_2 -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>eval_model&#45;&gt;var_2</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M95.5,-93.98C95.5,-80.34 95.5,-61.75 95.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"99,-46.1 95.5,-36.1 92,-46.1 99,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_train -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M169.75,-478C165.38,-472.57 161.22,-466.39 158.5,-460 154.55,-450.7 152.63,-439.83 151.75,-430.09\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"155.24,-429.87 151.16,-420.1 148.26,-430.28 155.24,-429.87\"/>\n", "<text text-anchor=\"middle\" x=\"180\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"180\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;y_train -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M196.19,-477.98C201.81,-464.07 209.51,-445.03 215.74,-429.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"219.09,-430.68 219.59,-420.1 212.6,-428.06 219.09,-430.68\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_2</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5e323b0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(train_model).expand_all()\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This adds all the calls in the storage, because they're all reachable from some\n", "call to `train_model` by following inputs/outputs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extracting `DataFrame`s from `ComputationFrame`s\n", "\n", "### `ComputationFrame`s as generalized dataframes\n", "You can think of `ComputationFrame`s as a generalization the familiar\n", "`pandas.DataFrame` class:\n", "\n", "- **instead of columns, you have a computational graph**: functions whose\n", "input/output edges connect to variables.\n", "- **instead of rows, you have computation traces**: variable values and function\n", "calls that (possibly partially) follow this graph\n", "\n", "Conversely, **a dataframe can be extracted from any computation frame** for\n", "easier later analysis:\n", "\n", "- the columns are the nodes in the graph (functions and variables)\n", "- each row is a computation trace, possibly padded with `NaN`s where no\n", "value/call is present."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The `.df()` method\n", "All ways to turn a CF into a DF are dispatched through a CF's `.df()` method. We\n", "can apply this to the full storage CF we computed last:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:51.324968Z", "iopub.status.busy": "2024-07-11T14:31:51.324455Z", "iopub.status.idle": "2024-07-11T14:31:51.487076Z", "shell.execute_reply": "2024-07-11T14:31:51.486481Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    |   random_seed | generate_dataset                   |   n_estimators | train_model                   |   var_1 | var_0                                                | eval_model                   |   var_2 |\n", "|---:|--------------:|:-----------------------------------|---------------:|:------------------------------|--------:|:-----------------------------------------------------|:-----------------------------|--------:|\n", "|  0 |            42 | Call(generate_dataset, hid=c3f...) |             10 | Call(train_model, hid=5f7...) |    0.74 | RandomForestClassifier(max_depth=2, n_estimators=10) |                              |  nan    |\n", "|  1 |            42 | Call(generate_dataset, hid=c3f...) |             20 | Call(train_model, hid=c55...) |    0.8  | RandomForestClassifier(max_depth=2, n_estimators=20) |                              |  nan    |\n", "|  2 |            42 | Call(generate_dataset, hid=c3f...) |             40 | Call(train_model, hid=514...) |    0.82 | RandomForestClassifier(max_depth=2, n_estimators=40) | Call(eval_model, hid=5d3...) |    0.81 |\n", "|  3 |            42 | Call(generate_dataset, hid=c3f...) |             80 | Call(train_model, hid=e60...) |    0.83 | RandomForestClassifier(max_depth=2, n_estimators=80) | Call(eval_model, hid=d32...) |    0.82 |\n"]}], "source": ["cf = storage.cf(train_model).expand_all()\n", "print(cf.df().drop(columns=['X_train', 'y_train']).to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Importantly, we see that some computations only partially follow the full\n", "computation graph, because we didn't call `eval_model` on all the `train_model`\n", "outputs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What does `.df()` actually compute?\n", "The `.df` method does roughly speaking the following:\n", "\n", "- finds all the sink, i.e. \"final\", `<PERSON><PERSON>`s in the CF, i.e. the ones that are not\n", "used as inputs to any call in a connected function node.\n", "- for each variable's sink `Ref`s, it computes a table where \n", "    - columns are variables and functions that these `Ref`s depend on\n", "    - rows contain the set of `Ref`/`Call` dependencies from each such node\n", "- joins these tables over all the variables containing sink `Ref`s along their\n", "shared columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing and summarizing `ComputationFrame`s\n", "CFs are complex data structures, and benefit from several kinds of high-level\n", "summaries."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The `.draw()` method\n", "We've used the `.draw()` method a bunch of times so far. Let's revisit the most\n", "recent CF:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:51.490079Z", "iopub.status.busy": "2024-07-11T14:31:51.489876Z", "iopub.status.idle": "2024-07-11T14:31:51.586985Z", "shell.execute_reply": "2024-07-11T14:31:51.586355Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"263pt\" height=\"620pt\"\n", " viewBox=\"0.00 0.00 262.50 620.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 616)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-616 258.5,-616 258.5,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M130.5,-228C130.5,-228 60.5,-228 60.5,-228 54.5,-228 48.5,-222 48.5,-216 48.5,-216 48.5,-204 48.5,-204 48.5,-198 54.5,-192 60.5,-192 60.5,-192 130.5,-192 130.5,-192 136.5,-192 142.5,-198 142.5,-204 142.5,-204 142.5,-216 142.5,-216 142.5,-222 136.5,-228 130.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"79.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"56.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (2 sinks)</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M128.5,-134C128.5,-134 62.5,-134 62.5,-134 56.5,-134 50.5,-128 50.5,-122 50.5,-122 50.5,-106 50.5,-106 50.5,-100 56.5,-94 62.5,-94 62.5,-94 128.5,-94 128.5,-94 134.5,-94 140.5,-100 140.5,-106 140.5,-106 140.5,-122 140.5,-122 140.5,-128 134.5,-134 128.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"62.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"58.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model</text>\n", "<text text-anchor=\"start\" x=\"81\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- var_0&#45;&gt;eval_model -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>var_0&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M95.5,-191.76C95.5,-178.5 95.5,-159.86 95.5,-144.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"99,-144.07 95.5,-134.07 92,-144.07 99,-144.07\"/>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- n_estimators -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>n_estimators</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420\"/>\n", "<text text-anchor=\"start\" x=\"15\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sources)</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M185,-326C185,-326 118,-326 118,-326 112,-326 106,-320 106,-314 106,-314 106,-298 106,-298 106,-292 112,-286 118,-286 118,-286 185,-286 185,-286 191,-286 197,-292 197,-298 197,-298 197,-314 197,-314 197,-320 191,-326 185,-326\"/>\n", "<text text-anchor=\"start\" x=\"117.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"114\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model</text>\n", "<text text-anchor=\"start\" x=\"137\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- n_estimators&#45;&gt;train_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>n_estimators&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M61.28,-383.85C68.09,-371.71 78.39,-355.6 90.5,-344 95.08,-339.61 100.26,-335.5 105.63,-331.73\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"107.75,-334.53 114.16,-326.09 103.88,-328.69 107.75,-334.53\"/>\n", "<text text-anchor=\"middle\" x=\"119\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">n_estimators</text>\n", "<text text-anchor=\"middle\" x=\"119\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- X_train -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>X_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M168,-420C168,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 168,-384 168,-384 174,-384 180,-390 180,-396 180,-396 180,-408 180,-408 180,-414 174,-420 168,-420\"/>\n", "<text text-anchor=\"start\" x=\"131\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"start\" x=\"133\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- X_train&#45;&gt;train_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M151.5,-383.76C151.5,-370.5 151.5,-351.86 151.5,-336.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"155,-336.07 151.5,-326.07 148,-336.07 155,-336.07\"/>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X_train</text>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- var_2 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>var_2</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M130.5,-36C130.5,-36 60.5,-36 60.5,-36 54.5,-36 48.5,-30 48.5,-24 48.5,-24 48.5,-12 48.5,-12 48.5,-6 54.5,0 60.5,0 60.5,0 130.5,0 130.5,0 136.5,0 142.5,-6 142.5,-12 142.5,-12 142.5,-24 142.5,-24 142.5,-30 136.5,-36 130.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"79.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_2</text>\n", "<text text-anchor=\"start\" x=\"56.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sinks)</text>\n", "</g>\n", "<!-- random_seed -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>random_seed</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M229,-612C229,-612 148,-612 148,-612 142,-612 136,-606 136,-600 136,-600 136,-588 136,-588 136,-582 142,-576 148,-576 148,-576 229,-576 229,-576 235,-576 241,-582 241,-588 241,-588 241,-600 241,-600 241,-606 235,-612 229,-612\"/>\n", "<text text-anchor=\"start\" x=\"149\" y=\"-596.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"start\" x=\"144\" y=\"-586\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sources)</text>\n", "</g>\n", "<!-- generate_dataset -->\n", "<g id=\"node10\" class=\"node\">\n", "<title>generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M234.5,-518C234.5,-518 142.5,-518 142.5,-518 136.5,-518 130.5,-512 130.5,-506 130.5,-506 130.5,-490 130.5,-490 130.5,-484 136.5,-478 142.5,-478 142.5,-478 234.5,-478 234.5,-478 240.5,-478 246.5,-484 246.5,-490 246.5,-490 246.5,-506 246.5,-506 246.5,-512 240.5,-518 234.5,-518\"/>\n", "<text text-anchor=\"start\" x=\"138.5\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"138.5\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:generate_dataset</text>\n", "<text text-anchor=\"start\" x=\"174\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- random_seed&#45;&gt;generate_dataset -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>random_seed&#45;&gt;generate_dataset</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M188.5,-575.76C188.5,-562.5 188.5,-543.86 188.5,-528.27\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"192,-528.07 188.5,-518.07 185,-528.07 192,-528.07\"/>\n", "<text text-anchor=\"middle\" x=\"218\" y=\"-550\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">random_seed</text>\n", "<text text-anchor=\"middle\" x=\"218\" y=\"-539\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- y_train -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>y_train</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M242.5,-420C242.5,-420 210.5,-420 210.5,-420 204.5,-420 198.5,-414 198.5,-408 198.5,-408 198.5,-396 198.5,-396 198.5,-390 204.5,-384 210.5,-384 210.5,-384 242.5,-384 242.5,-384 248.5,-384 254.5,-390 254.5,-396 254.5,-396 254.5,-408 254.5,-408 254.5,-414 248.5,-420 242.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"206.5\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"start\" x=\"208\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- y_train&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>y_train&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M220.44,-383.89C215.67,-371.92 208.21,-356 198.5,-344 195.4,-340.17 191.83,-336.48 188.07,-333.02\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"190.06,-330.11 180.19,-326.26 185.5,-335.43 190.06,-330.11\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y_train</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- var_1 -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>var_1</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M242.5,-228C242.5,-228 172.5,-228 172.5,-228 166.5,-228 160.5,-222 160.5,-216 160.5,-216 160.5,-204 160.5,-204 160.5,-198 166.5,-192 172.5,-192 172.5,-192 242.5,-192 242.5,-192 248.5,-192 254.5,-198 254.5,-204 254.5,-204 254.5,-216 254.5,-216 254.5,-222 248.5,-228 242.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"191.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_1</text>\n", "<text text-anchor=\"start\" x=\"168.5\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>train_model&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M140.17,-285.98C131.73,-271.81 120.1,-252.3 110.83,-236.74\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"113.81,-234.9 105.69,-228.1 107.8,-238.48 113.81,-234.9\"/>\n", "<text text-anchor=\"middle\" x=\"150\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"150\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;var_1 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>train_model&#45;&gt;var_1</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M164.19,-285.94C167.91,-280.22 171.94,-273.9 175.5,-268 181.55,-257.99 187.91,-246.8 193.36,-237\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"196.49,-238.57 198.26,-228.12 190.36,-235.19 196.49,-238.57\"/>\n", "<text text-anchor=\"middle\" x=\"209\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"209\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;var_2 -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>eval_model&#45;&gt;var_2</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M95.5,-93.98C95.5,-80.34 95.5,-61.75 95.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"99,-46.1 95.5,-36.1 92,-46.1 99,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"117\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;X_train -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;X_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M169.75,-478C165.38,-472.57 161.22,-466.39 158.5,-460 154.55,-450.7 152.63,-439.83 151.75,-430.09\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"155.24,-429.87 151.16,-420.1 148.26,-430.28 155.24,-429.87\"/>\n", "<text text-anchor=\"middle\" x=\"180\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"180\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- generate_dataset&#45;&gt;y_train -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>generate_dataset&#45;&gt;y_train</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M196.19,-477.98C201.81,-464.07 209.51,-445.03 215.74,-429.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"219.09,-430.68 219.59,-420.1 212.6,-428.06 219.09,-430.68\"/>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_2</text>\n", "<text text-anchor=\"middle\" x=\"233\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x78d3c5dba1a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `.draw()` method shows the computational graph of a CF and how many\n", "calls/refs there are in each function/variable. When called with `verbose=True`,\n", "this method shows a summary of how the variable values and function calls\n", "connect across the CF:\n", "\n", "- **source (i.e. initial) `Ref`s** in each variable: the `Ref`s in this variable\n", "that aren't outputs of any call in a function node using the variable as output.\n", "- **sink (i.e. final) `Ref`s** in each variable: dual to source refs, these are\n", "the `Ref`s that are not inputs to calls in any function node using the variable\n", "as input.\n", "- **edge `Ref`s** for each edge: for edges that are inputs to functions, this is\n", "the number of refs from the input variable that are used in calls to the\n", "function. For edges that are outputs of functions, this is the number of refs in\n", "the output variable created by calls in the function node."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Printing the computation graph of a `ComputationFrame`\n", "The computational graph of a CF is part of its `repr()`, but you can also print\n", "just the graph:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:51.590425Z", "iopub.status.busy": "2024-07-11T14:31:51.590075Z", "iopub.status.idle": "2024-07-11T14:31:51.621683Z", "shell.execute_reply": "2024-07-11T14:31:51.620782Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X_train@output_0, y_train@output_2 = generate_dataset(random_seed=random_seed)\n", "var_0@output_0, var_1@output_1 = train_model(X_train=X_train, n_estimators=n_estimators, y_train=y_train)\n", "var_2@output_0 = eval_model(model=var_0)\n"]}], "source": ["cf.print_graph()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that outputs are appended with `@output_0`, .... This is to indicate the\n", "name each output is bound to, in case some outputs are missing from the graph\n", "(which is possible, because CFs can represent partial computations)."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}