# mandala1框架概念对比指南

## 概述

本指南通过对比分析的方式，深入解释mandala1框架中核心概念的区别、联系和应用场景。

## 1. 函数执行模式对比

### 传统Python函数 vs @op装饰函数

| 特性 | 传统函数 | @op函数(无Context) | @op函数(有Context) |
|------|----------|-------------------|-------------------|
| **执行方式** | 直接执行 | 直接执行 | Storage管理执行 |
| **返回类型** | 原始对象 | 原始对象 | Ref对象 |
| **缓存机制** | 无 | 无 | 智能记忆化 |
| **版本管理** | 无 | 有版本但不使用 | 完整版本控制 |
| **依赖追踪** | 无 | 无 | 完整追踪 |
| **性能特点** | 每次重新计算 | 每次重新计算 | 缓存加速 |

### 代码示例对比

```python
# 1. 传统Python函数
def traditional_compute(x, y):
    time.sleep(1)  # 模拟耗时计算
    return x + y

result1 = traditional_compute(1, 2)  # 耗时1秒
result2 = traditional_compute(1, 2)  # 又耗时1秒
# 特点：每次都重新计算，无优化

# 2. @op函数（无Context）
@op
def op_compute(x, y):
    time.sleep(1)  # 模拟耗时计算
    return x + y

result1 = op_compute(1, 2)  # 耗时1秒，返回原始值
result2 = op_compute(1, 2)  # 又耗时1秒，返回原始值
# 特点：有Op包装但表现如传统函数

# 3. @op函数（有Context）
@op
def op_compute(x, y):
    time.sleep(1)  # 模拟耗时计算
    return x + y

with Storage():
    result1 = op_compute(1, 2)  # 耗时1秒，返回Ref
    result2 = op_compute(1, 2)  # 几乎瞬间，从缓存加载
# 特点：智能缓存，显著性能提升
```

## 2. 数据对象演化对比

### 原始对象 → Ref → 图节点

```python
# 阶段1：原始Python对象
raw_data = [1, 2, 3, 4, 5]
print(type(raw_data))  # <class 'list'>
# 特点：
# - 普通Python对象
# - 无唯一标识
# - 无来源信息
# - 无特殊功能

# 阶段2：Ref包装对象
@op
def create_data():
    return [1, 2, 3, 4, 5]

with Storage() as storage:
    data_ref = create_data()
    print(type(data_ref))  # <class 'mandala1.model.AtomRef'>
    print(f"cid: {data_ref.cid[:8]}...")  # 内容标识
    print(f"hid: {data_ref.hid[:8]}...")  # 历史标识
# 特点：
# - 有唯一标识（cid, hid）
# - 可追踪来源
# - 支持延迟加载
# - 版本管理

# 阶段3：图节点
cf = storage.cf(data_ref)
print(f"图中节点: {list(cf.nodes)}")  # ['v']
# 特点：
# - 成为计算图的节点
# - 可分析依赖关系
# - 支持图操作
# - 拓扑分析
```

## 3. 存储层次对比

### 内存 → 缓存 → 数据库

| 层次 | 存储位置 | 访问速度 | 持久性 | 容量限制 |
|------|----------|----------|--------|----------|
| **内存** | RAM | 最快 | 程序运行期 | 受内存限制 |
| **缓存** | 内存+磁盘 | 快 | 会话期间 | 可配置 |
| **数据库** | 磁盘文件 | 较慢 | 永久 | 磁盘空间 |

```python
# 内存层：Ref.obj
ref.in_memory == True
data = ref.obj  # 直接访问，最快

# 缓存层：Storage缓存
storage.call_cache  # 调用缓存
storage.atoms_cache  # 原子缓存

# 数据库层：SQLite文件
storage.db  # 持久化存储
# 表结构：calls, atoms, refs
```

## 4. 图扩展策略对比

### expand_back vs expand_forward vs expand_all

| 策略 | 方向 | 用途 | 性能开销 | 适用场景 |
|------|------|------|----------|----------|
| **expand_back** | 向上游 | 追踪数据来源 | 中等 | 调试、溯源 |
| **expand_forward** | 向下游 | 评估影响范围 | 中等 | 变更分析 |
| **expand_all** | 双向 | 完整上下文 | 最高 | 系统理解 |

### 应用场景对比

```python
# 场景1：调试问题（expand_back）
error_result = problematic_function(data)
debug_cf = storage.cf(error_result).expand_back(recursive=True)
# 分析：input → process1 → process2 → error
# 目标：找到问题的根源

# 场景2：影响评估（expand_forward）
critical_data = important_computation(input)
impact_cf = storage.cf(critical_data).expand_forward(recursive=True)
# 分析：critical_data → usage1 → usage2 → final_results
# 目标：评估修改的影响范围

# 场景3：系统理解（expand_all）
any_node = some_computation(data)
full_cf = storage.cf(any_node).expand_all()
# 分析：完整的计算生态系统
# 目标：理解整体架构
```

## 5. 版本管理维度对比

### 函数版本 vs 语义版本 vs 内容版本

| 版本类型 | 计算基础 | 变更触发 | 用途 |
|----------|----------|----------|------|
| **函数版本** | 函数代码内容 | 代码修改 | 基础版本标识 |
| **语义版本** | 函数签名+依赖 | 接口变更 | 缓存兼容性 |
| **内容版本** | 实际执行内容 | 执行逻辑变更 | 精确版本控制 |

```python
# 函数版本示例
@op
def compute(x):
    return x * 2  # 版本1

@op
def compute(x):
    return x * 3  # 版本2，Op.version自动更新

# 语义版本示例
# 基于函数签名和依赖关系
# 用于判断是否可以复用缓存

# 内容版本示例
# 基于实际执行的内容
# 最精确的版本控制
```

## 6. 内存管理策略对比

### 立即加载 vs 延迟加载 vs 混合策略

```python
# 立即加载策略
ref = storage.wrap(large_object)
ref.in_memory == True  # 对象在内存中
# 优点：访问速度快
# 缺点：内存占用大

# 延迟加载策略
detached_ref = ref.detached()
detached_ref.in_memory == False  # 对象不在内存
# 优点：内存占用小
# 缺点：访问时需要加载

# 混合策略
if object_size > threshold:
    use_detached_strategy()  # 大对象延迟加载
else:
    keep_in_memory()  # 小对象立即加载
```

## 7. 计算图分析层次对比

### 节点级 vs 子图级 vs 全图级

| 分析层次 | 范围 | 复杂度 | 信息量 | 适用场景 |
|----------|------|--------|--------|----------|
| **节点级** | 单个节点 | 低 | 局部 | 快速检查 |
| **子图级** | 相关节点 | 中 | 中等 | 局部分析 |
| **全图级** | 所有节点 | 高 | 完整 | 全局理解 |

```python
# 节点级分析
node_info = cf.get_node_info(node_name)
neighbors = cf.in_neighbors(node_name)

# 子图级分析
upstream_cf = cf.upstream(node_name)
downstream_cf = cf.downstream(node_name)

# 全图级分析
full_cf = cf.expand_all()
topo_order = full_cf.topsort_modulo_sccs()
```

## 8. 性能优化层次对比

### Op级 vs Storage级 vs 系统级

```python
# Op级优化
@op(ignore_args=['debug'])  # 减少缓存键冲突
@op(__structural__=True)    # 标记结构化操作
def optimized_op(data, debug=False):
    pass

# Storage级优化
storage = Storage(
    overflow_threshold_MB=100,  # 控制内存使用
    deps_path='__main__'       # 限制版本跟踪范围
)

# 系统级优化
# 1. 按需构建ComputationFrame
cf = storage.cf(result)  # 最小图
cf = cf.expand_back()    # 只扩展需要的部分

# 2. 并行执行规划
topo_order = cf.topsort_modulo_sccs()
parallel_groups = group_by_level(topo_order)
```

## 9. 使用模式对比

### 交互式 vs 批处理 vs 生产环境

| 使用模式 | Storage配置 | 图策略 | 优化重点 |
|----------|-------------|--------|----------|
| **交互式** | 内存存储 | 按需扩展 | 响应速度 |
| **批处理** | 持久化存储 | 完整构建 | 吞吐量 |
| **生产环境** | 优化配置 | 混合策略 | 稳定性 |

```python
# 交互式模式
with Storage():  # 内存存储，快速响应
    result = quick_analysis(data)
    cf = storage.cf(result)  # 最小图

# 批处理模式
with Storage("batch.db"):  # 持久化存储
    results = []
    for data_batch in large_dataset:
        result = process_batch(data_batch)
        results.append(result)
    
    # 完整分析
    cf = storage.cf(results[-1]).expand_all()

# 生产环境模式
with Storage(
    db_path="production.db",
    overflow_threshold_MB=500,
    deps_path='__main__'
):
    result = production_pipeline(data)
    # 监控和日志记录
```

## 总结

mandala1框架通过这些多层次、多维度的设计，实现了：

1. **灵活性**：支持从简单脚本到复杂系统的各种场景
2. **性能**：智能缓存和版本管理带来显著性能提升
3. **可观测性**：完整的计算历史和图分析能力
4. **可维护性**：清晰的组件分离和丰富的调试工具

通过理解这些概念的对比和联系，开发者可以更好地利用mandala1框架的强大功能。
