---
description: 
globs: 
alwaysApply: true
---
## 4.1 关键原则

在实现功能前，必须实现所有的开发文档
每个领域对象都会对应有一个开发文档
以下为开发文档示例内容，请生成时按照此格式。
并后续开发以此文档为基准开发。
## 4.2 开发文档示例
```markdown
# 开发文档示例（以 `[模块/实体名称]` 为例）

```markdown
# [模块/实体名称] 开发文档

## 功能概述
- [简要描述该模块/实体的核心功能和职责]。

## API说明
| 方法/属性         | 参数类型                     | 返回类型       | 描述                                                                 |
|-------------------|------------------------------|----------------|----------------------------------------------------------------------|
| `__init__(self, param1: type, param2: type)` | `param1`: [描述], `param2`: [描述] | `None`       | 初始化 `[模块/实体名称]` 对象。                                      |
| `[核心业务方法](mdc:self, ...)`  | `...`                        | `[返回类型]`   | [描述该方法的核心业务逻辑和作用]。                   |

## 使用示例
```python
# 示例代码展示如何创建和使用该模块/实体
instance = [模块/实体名称](mdc:param1_value, param2_value)
result = instance.[核心业务方法](mdc:...)
# assert [预期结果]


## 测试用例
- **测试文件路径**：`/tdd_files/[layer]/[module_type]/test_[module_name].py`
- **测试覆盖要求**：
  - `[核心业务方法]()` 方法需覆盖典型场景和边界条件。
  - `__init__` 参数类型和有效性检查。

## 用例文件
- **用例文件路径**：`/cdd_files/[layer]/[module_type]/example_[module_name].py`
- **有例要求**：
  - 清晰展示 `[模块/实体名称]` 的创建和核心方法调用流程。
  - 输出结果需易于理解。

## 依赖接口
- [列出该模块依赖的应用层接口或领域层其他组件]。

## 异常处理
- `[自定义异常名1]`：当 [触发条件1] 时抛出。
- `[自定义异常名2]`：当 [触发条件2] 时抛出。

## 完成进度
- **状态**：[未开始/进行中/已完成]
- **备注**：[任何需要特别注意的事项]。

## 相关文件
- **领域对象**：`/core/[layer]/[module_type]/[module_name].py`
- **测试文件**：`/tdd_files/[layer]/[module_type]/test_[module_name].py`
- **用例文件**：`/cdd_files/[layer]/[module_type]/example_[module_name].py`
```
```

---



```markdown
## 代码审查检查表（针对 `[模块/实体名称]`）

- [ ] 测试文件是否覆盖 `[核心方法]` 的边界条件？
- [ ] 有例文件能否直接运行并清晰展示 `[模块/实体名称]` 的核心用法？
- [ ] 领域层代码是否无具体技术实现依赖 (如 UI 库, DB 驱动)?
- [ ] 接口契约 (如 `[接口名]`) 是否被严格遵循？
- [ ] 依赖方向是否符合架构分层约束？

---

## 依赖关系验证（Mermaid图示）

```mermaid
graph LR
    Domain[领域层] --> Application[应用层]
    Application --> Infrastructure[基础设施层]
    %% 根据实际情况，基础设施层可能被适配器层使用，或通过接口层间接关联
    
```markdown
**验证规则**：
- `/core/domain` 层不可直接 `import` `/core/infrastructure` 或 `/core/interface_adapters` 中的具体实现类。
- `[核心接口名]` 接口需在 `[适配器实现文件名]` 实现前通过模拟（Mock）测试。