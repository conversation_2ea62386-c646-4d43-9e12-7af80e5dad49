<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="359pt" height="428pt"
 viewBox="0.00 0.00 359.00 428.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 424)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-424 355,-424 355,4 -4,4"/>
<!-- var_1 -->
<g id="node1" class="node">
<title>var_1</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M210.5,-36C210.5,-36 140.5,-36 140.5,-36 134.5,-36 128.5,-30 128.5,-24 128.5,-24 128.5,-12 128.5,-12 128.5,-6 134.5,0 140.5,0 140.5,0 210.5,0 210.5,0 216.5,0 222.5,-6 222.5,-12 222.5,-12 222.5,-24 222.5,-24 222.5,-30 216.5,-36 210.5,-36"/>
<text text-anchor="start" x="159.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_1</text>
<text text-anchor="start" x="136.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sinks)</text>
</g>
<!-- version -->
<g id="node2" class="node">
<title>version</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420"/>
<text text-anchor="start" x="31" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">version</text>
<text text-anchor="start" x="8" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- compute_v2 -->
<g id="node7" class="node">
<title>compute_v2</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M210.5,-326C210.5,-326 140.5,-326 140.5,-326 134.5,-326 128.5,-320 128.5,-314 128.5,-314 128.5,-298 128.5,-298 128.5,-292 134.5,-286 140.5,-286 140.5,-286 210.5,-286 210.5,-286 216.5,-286 222.5,-292 222.5,-298 222.5,-298 222.5,-314 222.5,-314 222.5,-320 216.5,-326 210.5,-326"/>
<text text-anchor="start" x="140" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">compute_v2</text>
<text text-anchor="start" x="136.5" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:compute_v2</text>
<text text-anchor="start" x="161" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- version&#45;&gt;compute_v2 -->
<g id="edge4" class="edge">
<title>version&#45;&gt;compute_v2</title>
<path fill="none" stroke="#002b36" d="M74.77,-383.98C93.6,-369.59 120.84,-348.78 142.24,-332.42"/>
<polygon fill="#002b36" stroke="#002b36" points="144.61,-335.02 150.43,-326.16 140.36,-329.45 144.61,-335.02"/>
<text text-anchor="middle" x="147" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">version</text>
<text text-anchor="middle" x="147" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- data -->
<g id="node3" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M216,-420C216,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 216,-384 216,-384 222,-384 228,-390 228,-396 228,-396 228,-408 228,-408 228,-414 222,-420 216,-420"/>
<text text-anchor="start" x="163" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="131" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- data&#45;&gt;compute_v2 -->
<g id="edge2" class="edge">
<title>data&#45;&gt;compute_v2</title>
<path fill="none" stroke="#002b36" d="M175.5,-383.76C175.5,-370.5 175.5,-351.86 175.5,-336.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-336.07 175.5,-326.07 172,-336.07 179,-336.07"/>
<text text-anchor="middle" x="197" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="197" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- mode -->
<g id="node4" class="node">
<title>mode</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M339,-420C339,-420 258,-420 258,-420 252,-420 246,-414 246,-408 246,-408 246,-396 246,-396 246,-390 252,-384 258,-384 258,-384 339,-384 339,-384 345,-384 351,-390 351,-396 351,-396 351,-408 351,-408 351,-414 345,-420 339,-420"/>
<text text-anchor="start" x="282" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">mode</text>
<text text-anchor="start" x="254" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- mode&#45;&gt;compute_v2 -->
<g id="edge3" class="edge">
<title>mode&#45;&gt;compute_v2</title>
<path fill="none" stroke="#002b36" d="M276.23,-383.98C257.4,-369.59 230.16,-348.78 208.76,-332.42"/>
<polygon fill="#002b36" stroke="#002b36" points="210.64,-329.45 200.57,-326.16 206.39,-335.02 210.64,-329.45"/>
<text text-anchor="middle" x="273" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">mode</text>
<text text-anchor="middle" x="273" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- var_0 -->
<g id="node5" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M190.5,-228C190.5,-228 160.5,-228 160.5,-228 154.5,-228 148.5,-222 148.5,-216 148.5,-216 148.5,-204 148.5,-204 148.5,-198 154.5,-192 160.5,-192 160.5,-192 190.5,-192 190.5,-192 196.5,-192 202.5,-198 202.5,-204 202.5,-204 202.5,-216 202.5,-216 202.5,-222 196.5,-228 190.5,-228"/>
<text text-anchor="start" x="159.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="157" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values</text>
</g>
<!-- summarize -->
<g id="node6" class="node">
<title>summarize</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M208,-134C208,-134 143,-134 143,-134 137,-134 131,-128 131,-122 131,-122 131,-106 131,-106 131,-100 137,-94 143,-94 143,-94 208,-94 208,-94 214,-94 220,-100 220,-106 220,-106 220,-122 220,-122 220,-128 214,-134 208,-134"/>
<text text-anchor="start" x="144" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">summarize</text>
<text text-anchor="start" x="139" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:summarize</text>
<text text-anchor="start" x="161" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- var_0&#45;&gt;summarize -->
<g id="edge5" class="edge">
<title>var_0&#45;&gt;summarize</title>
<path fill="none" stroke="#002b36" d="M175.5,-191.76C175.5,-178.5 175.5,-159.86 175.5,-144.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-144.07 175.5,-134.07 172,-144.07 179,-144.07"/>
<text text-anchor="middle" x="197" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="197" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- summarize&#45;&gt;var_1 -->
<g id="edge6" class="edge">
<title>summarize&#45;&gt;var_1</title>
<path fill="none" stroke="#002b36" d="M175.5,-93.98C175.5,-80.34 175.5,-61.75 175.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-46.1 175.5,-36.1 172,-46.1 179,-46.1"/>
<text text-anchor="middle" x="197" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- compute_v2&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>compute_v2&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M175.5,-285.98C175.5,-272.34 175.5,-253.75 175.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-238.1 175.5,-228.1 172,-238.1 179,-238.1"/>
<text text-anchor="middle" x="197" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
</g>
</svg>
