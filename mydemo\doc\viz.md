# viz.py 文档

## 文件内容与作用总体说明

`viz.py` 文件是 mandala 框架的可视化模块，提供了计算图和依赖图的可视化功能。该文件定义了用于生成 Graphviz DOT 格式图形的类和函数，包括节点、边、分组等图形元素的表示，以及将图形输出为各种格式（SVG、PNG等）的功能。主要用于 ComputationFrame 的图形化展示和依赖关系的可视化分析。

## 文件内的所有变量的作用与说明

### 颜色配置
- `SOLARIZED_LIGHT`: Dict，Solarized Light 配色方案，包含各种颜色的十六进制值
- `GRAPH_CONFIG`: Dict，图形的全局配置参数
- `NODE_CONFIG`: Dict，节点的默认配置参数
- `EDGE_CONFIG`: Dict，边的默认配置参数

### 图形元素类
- `Node`: 节点类，表示图中的节点
- `Edge`: 边类，表示图中的边
- `Group`: 分组类，用于节点的分组显示

## 文件内的所有函数的作用与说明

### Node 类（图形节点）

#### __init__(self, internal_name: str, label: str, color: str, shape: str = "box", style: str = "filled")

**作用**: 初始化图形节点对象

**输入参数**:
- `internal_name`: str，内部名称，用于 DOT 格式中的节点标识
- `label`: str，显示标签，用户可见的节点名称
- `color`: str，节点颜色，十六进制颜色值
- `shape`: str，节点形状，默认为 "box"
- `style`: str，节点样式，默认为 "filled"

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### to_dot_string(self) -> str

**作用**: 将节点转换为 DOT 格式字符串

**输入参数**: 无

**内部调用的函数**：
- dict_to_dot_string: 将属性字典转换为 DOT 格式

**输出参数**: str - DOT 格式的节点定义

### Edge 类（图形边）

#### __init__(self, source_node: Node, target_node: Node, label: str = "", color: str = "black", style: str = "solid")

**作用**: 初始化图形边对象

**输入参数**:
- `source_node`: Node，源节点
- `target_node`: Node，目标节点
- `label`: str，边的标签，默认为空
- `color`: str，边的颜色，默认为黑色
- `style`: str，边的样式，默认为实线

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### to_dot_string(self) -> str

**作用**: 将边转换为 DOT 格式字符串

**输入参数**: 无

**内部调用的函数**：
- dict_to_dot_string: 将属性字典转换为 DOT 格式

**输出参数**: str - DOT 格式的边定义

### Group 类（节点分组）

#### __init__(self, label: str, nodes: List[Node], parent: Optional["Group"] = None)

**作用**: 初始化节点分组对象

**输入参数**:
- `label`: str，分组标签
- `nodes`: List[Node]，分组中的节点列表
- `parent`: Optional[Group]，父分组，用于嵌套分组

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

### 工具函数

#### dict_to_dot_string(d: Dict[str, Any]) -> str

**作用**: 将字典转换为 DOT 格式的属性字符串

**输入参数**:
- `d`: Dict[str, Any]，要转换的属性字典

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - DOT 格式的属性字符串

**其他说明**：处理字符串值的引号转义

#### get_group_string(group: Group, groups_forest: Dict[Group, List[Group]]) -> str

**作用**: 生成分组的 DOT 格式字符串，支持嵌套分组

**输入参数**:
- `group`: Group，要处理的分组
- `groups_forest`: Dict[Group, List[Group]]，分组的层次结构

**内部调用的函数**：
- get_group_string: 递归调用处理子分组

**输出参数**: str - DOT 格式的分组定义

#### to_dot_string(nodes: List[Node], edges: List[Edge], groups: List[Group], rankdir: Literal["TB", "BT", "LR", "RL"] = "TB") -> str

**作用**: 将图形元素转换为完整的 DOT 格式字符串

**输入参数**:
- `nodes`: List[Node]，节点列表
- `edges`: List[Edge]，边列表
- `groups`: List[Group]，分组列表
- `rankdir`: Literal["TB", "BT", "LR", "RL"]，图的方向，默认为从上到下

**内部调用的函数**：
- dict_to_dot_string: 转换配置字典
- node.to_dot_string: 转换节点
- edge.to_dot_string: 转换边
- get_group_string: 转换分组

**输出参数**: str - 完整的 DOT 格式图形定义

**其他说明**：生成包含所有图形元素的完整 DOT 文件内容

#### write_output(dot_string: str, output_path: Optional[Path] = None, output_ext: str = "svg", show_how: str = "none")

**作用**: 将 DOT 字符串输出为图形文件或显示

**输入参数**:
- `dot_string`: str，DOT 格式的图形定义
- `output_path`: Optional[Path]，输出文件路径
- `output_ext`: str，输出文件扩展名，默认为 "svg"
- `show_how`: str，显示方式，可选 "browser"、"inline"、"none"

**内部调用的函数**：
- tempfile.NamedTemporaryFile: 创建临时文件
- subprocess.call: 调用 graphviz 命令
- webbrowser.open: 在浏览器中打开文件
- Source: 创建 graphviz Source 对象
- display.display: 在 Jupyter 中显示

**输出参数**: 无返回值

**其他说明**：
- 支持多种输出格式（PNG、JPG、SVG等）
- 支持在浏览器中查看或在 Jupyter 中内联显示
- 使用临时文件处理 DOT 到图形的转换

#### _get_colorized_diff(old_str: str, new_str: str) -> str

**作用**: 生成两个字符串之间的彩色差异显示

**输入参数**:
- `old_str`: str，旧字符串
- `new_str`: str，新字符串

**内部调用的函数**：
- difflib.unified_diff: 生成统一差异格式
- rich 相关函数: 彩色输出（如果可用）

**输出参数**: str - 彩色差异字符串

**其他说明**：用于显示代码或配置的变更，支持 rich 库的彩色输出

### 颜色配置说明

#### SOLARIZED_LIGHT 配色方案

包含以下颜色定义：
- `base03`: 深色背景色
- `base02`: 次深背景色
- `base01`: 强调内容色
- `base00`: 主要内容色
- `base0`: 次要内容色
- `base1`: 可选内容色
- `base2`: 背景高亮色
- `base3`: 背景基色
- `yellow`: 黄色强调
- `orange`: 橙色强调
- `red`: 红色强调
- `magenta`: 品红色强调
- `violet`: 紫色强调
- `blue`: 蓝色强调
- `cyan`: 青色强调
- `green`: 绿色强调

### 图形配置说明

#### GRAPH_CONFIG（图形全局配置）
- 设置图形的整体布局和样式参数
- 包括字体、间距、方向等设置

#### NODE_CONFIG（节点默认配置）
- 定义节点的默认外观
- 包括形状、样式、字体等属性

#### EDGE_CONFIG（边默认配置）
- 定义边的默认外观
- 包括箭头样式、线条样式等属性

### 使用场景

1. **计算图可视化**：将 ComputationFrame 转换为可视化图形
2. **依赖关系展示**：显示函数和变量之间的依赖关系
3. **调试和分析**：通过图形化方式理解复杂的计算流程
4. **文档生成**：为项目文档生成图形化的计算流程说明

### 输出格式支持

- **SVG**：矢量图形，适合网页显示和文档嵌入
- **PNG**：位图格式，适合一般用途
- **JPG**：压缩位图，适合文件大小敏感的场景
- **PDF**：适合打印和文档分发

### 显示方式

- **browser**：在默认浏览器中打开图形文件
- **inline**：在 Jupyter Notebook 中内联显示
- **none**：仅生成文件，不自动显示
