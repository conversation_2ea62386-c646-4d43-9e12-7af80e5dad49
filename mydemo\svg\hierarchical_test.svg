<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="249pt" height="812pt"
 viewBox="0.00 0.00 248.50 812.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 808)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-808 244.5,-808 244.5,4 -4,4"/>
<!-- metadata -->
<g id="node1" class="node">
<title>metadata</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-228C93,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 93,-192 93,-192 99,-192 105,-198 105,-204 105,-204 105,-216 105,-216 105,-222 99,-228 93,-228"/>
<text text-anchor="start" x="26" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">metadata</text>
<text text-anchor="start" x="8" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- step4_finalize -->
<g id="node9" class="node">
<title>step4_finalize</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M124,-134C124,-134 47,-134 47,-134 41,-134 35,-128 35,-122 35,-122 35,-106 35,-106 35,-100 41,-94 47,-94 47,-94 124,-94 124,-94 130,-94 136,-100 136,-106 136,-106 136,-122 136,-122 136,-128 130,-134 124,-134"/>
<text text-anchor="start" x="46" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">step4_finalize</text>
<text text-anchor="start" x="43" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:step4_finalize</text>
<text text-anchor="start" x="71" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- metadata&#45;&gt;step4_finalize -->
<g id="edge3" class="edge">
<title>metadata&#45;&gt;step4_finalize</title>
<path fill="none" stroke="#002b36" d="M58.55,-191.76C63.25,-178.37 69.88,-159.5 75.38,-143.82"/>
<polygon fill="#002b36" stroke="#002b36" points="78.79,-144.67 78.8,-134.07 72.19,-142.35 78.79,-144.67"/>
<text text-anchor="middle" x="94" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">metadata</text>
<text text-anchor="middle" x="94" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- transformed_data -->
<g id="node2" class="node">
<title>transformed_data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M211.5,-420C211.5,-420 117.5,-420 117.5,-420 111.5,-420 105.5,-414 105.5,-408 105.5,-408 105.5,-396 105.5,-396 105.5,-390 111.5,-384 117.5,-384 117.5,-384 211.5,-384 211.5,-384 217.5,-384 223.5,-390 223.5,-396 223.5,-396 223.5,-408 223.5,-408 223.5,-414 217.5,-420 211.5,-420"/>
<text text-anchor="start" x="113.5" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">transformed_data</text>
<text text-anchor="start" x="146" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values</text>
</g>
<!-- step3_aggregate -->
<g id="node8" class="node">
<title>step3_aggregate</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M209,-326C209,-326 120,-326 120,-326 114,-326 108,-320 108,-314 108,-314 108,-298 108,-298 108,-292 114,-286 120,-286 120,-286 209,-286 209,-286 215,-286 221,-292 221,-298 221,-298 221,-314 221,-314 221,-320 215,-326 209,-326"/>
<text text-anchor="start" x="116" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">step3_aggregate</text>
<text text-anchor="start" x="116" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:step3_aggregate</text>
<text text-anchor="start" x="150" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- transformed_data&#45;&gt;step3_aggregate -->
<g id="edge5" class="edge">
<title>transformed_data&#45;&gt;step3_aggregate</title>
<path fill="none" stroke="#002b36" d="M164.5,-383.76C164.5,-370.5 164.5,-351.86 164.5,-336.27"/>
<polygon fill="#002b36" stroke="#002b36" points="168,-336.07 164.5,-326.07 161,-336.07 168,-336.07"/>
<text text-anchor="middle" x="202.5" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">transformed_data</text>
<text text-anchor="middle" x="202.5" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- data -->
<g id="node3" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M205,-804C205,-804 124,-804 124,-804 118,-804 112,-798 112,-792 112,-792 112,-780 112,-780 112,-774 118,-768 124,-768 124,-768 205,-768 205,-768 211,-768 217,-774 217,-780 217,-780 217,-792 217,-792 217,-798 211,-804 205,-804"/>
<text text-anchor="start" x="152" y="-788.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="120" y="-778" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- step1_process -->
<g id="node10" class="node">
<title>step1_process</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M204.5,-710C204.5,-710 124.5,-710 124.5,-710 118.5,-710 112.5,-704 112.5,-698 112.5,-698 112.5,-682 112.5,-682 112.5,-676 118.5,-670 124.5,-670 124.5,-670 204.5,-670 204.5,-670 210.5,-670 216.5,-676 216.5,-682 216.5,-682 216.5,-698 216.5,-698 216.5,-704 210.5,-710 204.5,-710"/>
<text text-anchor="start" x="122" y="-697.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">step1_process</text>
<text text-anchor="start" x="120.5" y="-687" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:step1_process</text>
<text text-anchor="start" x="150" y="-677" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- data&#45;&gt;step1_process -->
<g id="edge9" class="edge">
<title>data&#45;&gt;step1_process</title>
<path fill="none" stroke="#002b36" d="M164.5,-767.76C164.5,-754.5 164.5,-735.86 164.5,-720.27"/>
<polygon fill="#002b36" stroke="#002b36" points="168,-720.07 164.5,-710.07 161,-720.07 168,-720.07"/>
<text text-anchor="middle" x="186" y="-742" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="186" y="-731" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- processed_data -->
<g id="node4" class="node">
<title>processed_data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M207,-612C207,-612 122,-612 122,-612 116,-612 110,-606 110,-600 110,-600 110,-588 110,-588 110,-582 116,-576 122,-576 122,-576 207,-576 207,-576 213,-576 219,-582 219,-588 219,-588 219,-600 219,-600 219,-606 213,-612 207,-612"/>
<text text-anchor="start" x="118" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">processed_data</text>
<text text-anchor="start" x="146" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values</text>
</g>
<!-- step2_transform -->
<g id="node7" class="node">
<title>step2_transform</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M208,-518C208,-518 121,-518 121,-518 115,-518 109,-512 109,-506 109,-506 109,-490 109,-490 109,-484 115,-478 121,-478 121,-478 208,-478 208,-478 214,-478 220,-484 220,-490 220,-490 220,-506 220,-506 220,-512 214,-518 208,-518"/>
<text text-anchor="start" x="117.5" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">step2_transform</text>
<text text-anchor="start" x="117" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:step2_transform</text>
<text text-anchor="start" x="150" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- processed_data&#45;&gt;step2_transform -->
<g id="edge7" class="edge">
<title>processed_data&#45;&gt;step2_transform</title>
<path fill="none" stroke="#002b36" d="M164.5,-575.76C164.5,-562.5 164.5,-543.86 164.5,-528.27"/>
<polygon fill="#002b36" stroke="#002b36" points="168,-528.07 164.5,-518.07 161,-528.07 168,-528.07"/>
<text text-anchor="middle" x="199.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">processed_data</text>
<text text-anchor="middle" x="199.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- aggregated -->
<g id="node5" class="node">
<title>aggregated</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M193.5,-228C193.5,-228 135.5,-228 135.5,-228 129.5,-228 123.5,-222 123.5,-216 123.5,-216 123.5,-204 123.5,-204 123.5,-198 129.5,-192 135.5,-192 135.5,-192 193.5,-192 193.5,-192 199.5,-192 205.5,-198 205.5,-204 205.5,-204 205.5,-216 205.5,-216 205.5,-222 199.5,-228 193.5,-228"/>
<text text-anchor="start" x="131.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">aggregated</text>
<text text-anchor="start" x="146" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values</text>
</g>
<!-- aggregated&#45;&gt;step4_finalize -->
<g id="edge2" class="edge">
<title>aggregated&#45;&gt;step4_finalize</title>
<path fill="none" stroke="#002b36" d="M151.44,-191.77C142.63,-180.32 130.62,-165.05 119.5,-152 116.7,-148.71 113.71,-145.31 110.71,-141.97"/>
<polygon fill="#002b36" stroke="#002b36" points="113.09,-139.39 103.78,-134.34 107.91,-144.09 113.09,-139.39"/>
<text text-anchor="middle" x="161" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">aggregated</text>
<text text-anchor="middle" x="161" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- var_0 -->
<g id="node6" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M120.5,-36C120.5,-36 50.5,-36 50.5,-36 44.5,-36 38.5,-30 38.5,-24 38.5,-24 38.5,-12 38.5,-12 38.5,-6 44.5,0 50.5,0 50.5,0 120.5,0 120.5,0 126.5,0 132.5,-6 132.5,-12 132.5,-12 132.5,-24 132.5,-24 132.5,-30 126.5,-36 120.5,-36"/>
<text text-anchor="start" x="69.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="46.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sinks)</text>
</g>
<!-- step2_transform&#45;&gt;transformed_data -->
<g id="edge6" class="edge">
<title>step2_transform&#45;&gt;transformed_data</title>
<path fill="none" stroke="#002b36" d="M164.5,-477.98C164.5,-464.34 164.5,-445.75 164.5,-430.5"/>
<polygon fill="#002b36" stroke="#002b36" points="168,-430.1 164.5,-420.1 161,-430.1 168,-430.1"/>
<text text-anchor="middle" x="186" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="186" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- step3_aggregate&#45;&gt;aggregated -->
<g id="edge4" class="edge">
<title>step3_aggregate&#45;&gt;aggregated</title>
<path fill="none" stroke="#002b36" d="M164.5,-285.98C164.5,-272.34 164.5,-253.75 164.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="168,-238.1 164.5,-228.1 161,-238.1 168,-238.1"/>
<text text-anchor="middle" x="186" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="186" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- step4_finalize&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>step4_finalize&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M85.5,-93.98C85.5,-80.34 85.5,-61.75 85.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="89,-46.1 85.5,-36.1 82,-46.1 89,-46.1"/>
<text text-anchor="middle" x="107" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="107" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- step1_process&#45;&gt;processed_data -->
<g id="edge8" class="edge">
<title>step1_process&#45;&gt;processed_data</title>
<path fill="none" stroke="#002b36" d="M164.5,-669.98C164.5,-656.34 164.5,-637.75 164.5,-622.5"/>
<polygon fill="#002b36" stroke="#002b36" points="168,-622.1 164.5,-612.1 161,-622.1 168,-622.1"/>
<text text-anchor="middle" x="186" y="-644" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="186" y="-633" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
</g>
</svg>
