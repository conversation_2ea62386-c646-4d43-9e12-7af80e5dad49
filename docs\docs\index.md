# Introduction
This is the documentation for [mandala](https://github.com/amakelov/mandala), a
simple & elegant experiment tracking framework for Python.

Most methods in `mandala` are provided by the `Storage` and `ComputationFrame`
classes. In general, you'll probably find yourself only interacting with 5-10
methods on a regular basis, and their docstrings provide detailed explanations. 

To complement this, this documentation contains a few short walkthroughs
illustrating the use of these methods.