# crawler.py 文档

## 文件内容与作用总体说明

`crawler.py` 文件是 mandala 框架依赖跟踪模块的爬虫组件，负责静态分析和动态发现 Python 代码中的可调用对象（函数、方法等）。该文件提供了两个核心功能：对象级别的爬取（`crawl_obj`）和静态文件级别的爬取（`crawl_static`）。这些功能用于构建依赖图，支持版本控制和代码变更跟踪。

## 文件内的所有变量的作用与说明

### 导入的模块和类型
- `types`: Python 内置类型模块，用于类型检查
- `importlib`: 动态导入模块
- `DepKey`: 依赖键类型，来自 model 模块
- `CallableNode`: 可调用节点类，来自 model 模块
- `is_callable_obj`: 判断对象是否可调用的工具函数
- `extract_func_obj`: 提取函数对象的工具函数
- `unknown_function`: 未知函数的占位符

### 函数内部变量
在各个函数中定义的局部变量，具体说明见函数部分。

## 文件内的所有函数的作用与说明

### crawl_obj(obj: Any, module_name: str, include_methods: bool, result: Dict[DepKey, CallableNode], strict: bool, objs_result: Dict[DepKey, Callable])

**作用**: 爬取单个对象，查找该对象模块中的本地函数和可选的方法

**输入参数**:
- `obj`: Any，要爬取的对象
- `module_name`: str，模块名称
- `include_methods`: bool，是否包含方法
- `result`: Dict[DepKey, CallableNode]，结果字典，存储依赖键到可调用节点的映射
- `strict`: bool，是否严格模式
- `objs_result`: Dict[DepKey, Callable]，对象结果字典，存储依赖键到可调用对象的映射

**内部调用的函数**：
- `is_callable_obj`: 检查对象是否可调用
- `unwrap_decorators`: 解包装饰器
- `extract_func_obj`: 提取函数对象
- `CallableNode.from_obj`: 从对象创建可调用节点
- `crawl_obj`: 递归调用自身处理类的方法

**输出参数**: 无返回值（通过修改 result 和 objs_result 参数返回结果）

**其他说明**：
- 排除内置函数类型
- 只处理本地模块的函数，排除非本地函数
- 如果对象是类且 include_methods 为 True，则递归处理类的所有方法

### crawl_static(root: Optional[Path], strict: bool, package_name: Optional[str] = None, include_methods: bool = False) -> Tuple[Dict[DepKey, CallableNode], Dict[DepKey, Callable]]

**作用**: 静态爬取根目录下的所有 Python 文件，使用 importlib 导入并查找可调用对象，创建可调用节点

**输入参数**:
- `root`: Optional[Path]，根目录路径，如果为 None 则只处理 __main__ 模块
- `strict`: bool，是否严格模式，影响导入失败时的处理方式
- `package_name`: Optional[str]，包名称，用于构建完整的模块名
- `include_methods`: bool，是否包含方法

**内部调用的函数**：
- `Path.rglob`: 递归查找 Python 文件
- `importlib.import_module`: 动态导入模块
- `crawl_obj`: 爬取模块中的每个对象
- `logger.warning`: 记录警告信息

**输出参数**: Tuple[Dict[DepKey, CallableNode], Dict[DepKey, Callable]] - 返回两个字典的元组
- 第一个字典：依赖键到可调用节点的映射
- 第二个字典：依赖键到可调用对象的映射

**其他说明**：
- 自动跳过 setup.py 和 console.py 文件
- 支持单文件和目录两种模式
- 总是包含 __main__ 模块
- 在严格模式下，导入失败会抛出异常；非严格模式下只记录警告
- 构建模块名时会考虑包名称和相对路径
- 遍历模块的 __dict__ 来发现所有对象

### 设计模式和架构

#### 爬虫策略
1. **静态分析**：通过文件系统遍历发现 Python 文件
2. **动态导入**：使用 importlib 实际导入模块
3. **对象检查**：检查模块中的每个对象是否为可调用对象
4. **节点创建**：为发现的可调用对象创建依赖节点

#### 错误处理
- **严格模式**：导入失败时抛出异常，确保完整性
- **宽松模式**：导入失败时记录警告并继续，提高容错性

#### 过滤机制
- **本地性检查**：只处理属于指定模块的函数
- **类型过滤**：排除内置函数类型
- **文件过滤**：跳过特定的系统文件

#### 递归处理
- **类方法处理**：当 include_methods 为 True 时，递归处理类的所有方法
- **嵌套对象**：支持处理嵌套在类中的可调用对象

### 使用场景

1. **依赖分析**：分析代码库中的函数依赖关系
2. **版本跟踪**：跟踪函数和方法的变更
3. **代码发现**：自动发现项目中的所有可调用对象
4. **静态分析**：为静态分析工具提供基础数据

### 与其他模块的关系

- **model.py**：使用 DepKey 和 CallableNode 类型
- **utils.py**：使用工具函数进行对象检查和提取
- **versioner.py**：为版本管理提供代码发现功能
- **tracers**：与跟踪器模块配合进行动态分析
