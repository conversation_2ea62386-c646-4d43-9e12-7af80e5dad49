# deps/tracers/__init__.py 文档

## 文件内容与作用总体说明

`deps/tracers/__init__.py` 文件是 mandala 框架依赖跟踪器模块的包初始化文件，负责导出跟踪器相关的核心类。该文件提供了跟踪器的统一接口，包括抽象基类 `TracerABC` 和两个具体实现：装饰器跟踪器 `DecTracer` 和系统跟踪器 `SysTracer`。这些跟踪器用于在函数执行过程中动态捕获依赖关系，是实现 mandala 自动依赖发现的关键组件。

## 文件内的所有变量的作用与说明

### 导入的类
- `TracerABC`: 跟踪器抽象基类，定义了所有跟踪器的通用接口
- `DecTracer`: 装饰器跟踪器，基于装饰器模式实现依赖跟踪
- `SysTracer`: 系统跟踪器，基于 Python 系统跟踪机制实现依赖跟踪

## 文件内的所有函数的作用与说明

### 包级别导出

该文件没有定义函数，只是导出了三个核心类：

#### TracerABC
**作用**: 跟踪器抽象基类，定义了所有跟踪器必须实现的接口

**主要功能**:
- 定义跟踪器的基本结构和行为
- 提供上下文管理器接口
- 定义依赖图构建的抽象方法

#### DecTracer
**作用**: 装饰器跟踪器，通过装饰器模式实现函数调用跟踪

**主要功能**:
- 使用装饰器包装函数来捕获调用关系
- 适用于显式标记需要跟踪的函数
- 提供精确的依赖关系控制

#### SysTracer
**作用**: 系统跟踪器，使用 Python 的 sys.settrace 机制实现全局跟踪

**主要功能**:
- 自动跟踪所有函数调用，无需显式装饰
- 提供全面的依赖关系发现
- 适用于分析现有代码的依赖关系

### 跟踪器选择指南

#### 使用 DecTracer 的场景
- 需要精确控制哪些函数被跟踪
- 性能敏感的应用，只跟踪关键函数
- 已有代码结构清晰，容易添加装饰器
- 需要避免跟踪系统库和第三方库

#### 使用 SysTracer 的场景
- 需要全面分析代码的依赖关系
- 分析遗留代码或第三方库的依赖
- 调试复杂的依赖问题
- 自动发现隐藏的依赖关系

### 架构设计

#### 抽象基类模式
- `TracerABC` 定义了统一的接口
- 不同实现可以根据需要选择不同的跟踪策略
- 便于扩展新的跟踪器实现

#### 策略模式
- 不同的跟踪器实现不同的跟踪策略
- 可以根据应用场景选择合适的跟踪器
- 支持运行时切换跟踪策略

#### 上下文管理
- 所有跟踪器都支持上下文管理器协议
- 确保跟踪状态的正确管理
- 提供异常安全的跟踪控制

### 与其他模块的关系

- **tracer_base.py**: 提供抽象基类定义
- **dec_impl.py**: 实现装饰器跟踪器
- **sys_impl.py**: 实现系统跟踪器
- **../model.py**: 使用依赖图和节点类型
- **../versioner.py**: 被版本管理器使用进行依赖跟踪

### 使用示例

#### 基本用法
```python
from mandala1.deps.tracers import DecTracer, SysTracer

# 使用装饰器跟踪器
with DecTracer(paths=[Path(".")]) as tracer:
    # 执行需要跟踪的代码
    result = some_function()
    # 获取依赖图
    dependency_graph = tracer.graph

# 使用系统跟踪器
with SysTracer(paths=[Path(".")]) as tracer:
    # 执行需要跟踪的代码
    result = some_function()
    # 获取依赖图
    dependency_graph = tracer.graph
```

#### 配置选项
```python
tracer = DecTracer(
    paths=[Path("src")],           # 跟踪路径
    strict=True,                   # 严格模式
    allow_methods=True,            # 允许跟踪方法
    track_globals=True,            # 跟踪全局变量
    skip_unhashable_globals=True,  # 跳过不可哈希的全局变量
    skip_globals_silently=False    # 不静默跳过全局变量
)
```

### 性能考虑

#### DecTracer 性能特点
- **低开销**: 只跟踪装饰的函数
- **精确控制**: 可以选择性跟踪
- **适合生产**: 性能影响最小

#### SysTracer 性能特点
- **高开销**: 跟踪所有函数调用
- **全面覆盖**: 捕获所有依赖关系
- **适合分析**: 主要用于开发和调试阶段

### 扩展性

#### 自定义跟踪器
- 继承 `TracerABC` 实现自定义跟踪逻辑
- 可以结合多种跟踪策略
- 支持特定领域的依赖跟踪需求

#### 插件机制
- 可以通过配置选择不同的跟踪器
- 支持运行时动态切换
- 便于集成到不同的应用框架中
