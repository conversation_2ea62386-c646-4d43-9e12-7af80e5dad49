# storage.py 文档

## 文件内容与作用总体说明

`storage.py` 文件是 mandala 框架的核心存储模块，实现了 `Storage` 类，负责管理所有的计算历史、数据存储、版本控制和依赖跟踪。Storage 类是整个框架的中央协调器，提供了数据持久化、记忆化、计算图构建、以及与 @op 装饰器的集成等功能。它支持内存和磁盘存储，自动版本管理，以及复杂的数据类型处理。

## 文件内的所有变量的作用与说明

### Storage 类的实例变量
- `db`: DBAdapter 对象，数据库适配器，管理 SQLite 数据库连接
- `call_storage`: SQLiteCallStorage 对象，持久化调用存储
- `calls`: CachedCallStorage 对象，缓存的调用存储，包装持久化存储
- `call_cache`: 调用缓存，快速访问内存中的调用对象
- `overflow_dir`: Optional[str]，溢出目录路径，用于存储大型对象
- `overflow_threshold_MB`: Optional[Union[int, float]]，溢出阈值（MB），超过此大小的对象存储到溢出目录
- `overflow_storage`: Optional[JoblibDictStorage]，溢出存储对象
- `atoms`: CachedDictStorage 对象，原子值存储，{cid -> 序列化对象}
- `shapes`: CachedDictStorage 对象，形状信息存储
- `ops`: CachedDictStorage 对象，操作定义存储
- `sources`: CachedDictStorage 对象，源代码存储
- `versioner`: Versioner 对象，版本管理器，处理依赖跟踪和代码版本控制
- `_deps_path`: Optional[Union[str, Path]]，依赖路径配置
- `_tracer_impl`: Optional[type]，跟踪器实现类型
- `_strict_tracing`: bool，是否严格跟踪
- `_skip_unhashable_globals`: bool，是否跳过不可哈希的全局变量
- `_skip_globals_silently`: bool，是否静默跳过全局变量
- `_skip_missing_deps`: bool，是否跳过缺失的依赖
- `_skip_missing_silently`: bool，是否静默跳过缺失的依赖
- `_deps_package`: Optional[str]，依赖包名称
- `_track_globals`: bool，是否跟踪全局变量

## 文件内的所有函数的作用与说明

### Storage 类的方法

#### __init__(self, db_path: str = ":memory:", overflow_dir: Optional[str] = None, overflow_threshold_MB: Optional[Union[int, float]] = 50.0, deps_path: Optional[Union[str, Path]] = None, tracer_impl: Optional[type] = None, strict_tracing: bool = False, skip_unhashable_globals: bool = True, skip_globals_silently: bool = False, skip_missing_deps: bool = True, skip_missing_silently: bool = False, deps_package: Optional[str] = None, track_globals: bool = True)

**作用**: 初始化 Storage 对象，设置数据库、存储配置和版本控制参数

**输入参数**:
- `db_path`: str，数据库路径，默认为内存数据库
- `overflow_dir`: Optional[str]，溢出目录路径
- `overflow_threshold_MB`: Optional[Union[int, float]]，溢出阈值
- `deps_path`: Optional[Union[str, Path]]，依赖路径
- `tracer_impl`: Optional[type]，跟踪器实现类
- `strict_tracing`: bool，是否严格跟踪
- `skip_unhashable_globals`: bool，是否跳过不可哈希的全局变量
- `skip_globals_silently`: bool，是否静默跳过全局变量
- `skip_missing_deps`: bool，是否跳过缺失的依赖
- `skip_missing_silently`: bool，是否静默跳过缺失的依赖
- `deps_package`: Optional[str]，依赖包名称
- `track_globals`: bool，是否跟踪全局变量

**内部调用的函数**：
- DBAdapter: 创建数据库适配器
- SQLiteCallStorage: 创建调用存储
- CachedCallStorage: 创建缓存调用存储
- CachedDictStorage: 创建缓存字典存储
- SQLiteDictStorage: 创建 SQLite 字典存储
- JoblibDictStorage: 创建 joblib 字典存储
- Versioner: 创建版本管理器

**输出参数**: 无返回值

**其他说明**：这是 Storage 的核心初始化方法，设置所有存储组件和配置

#### dump_config(self) -> dict[str, Any]

**作用**: 导出当前存储配置为字典

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: dict[str, Any] - 包含所有配置参数的字典

**其他说明**：用于配置的序列化和调试

#### conn(self) -> sqlite3.Connection

**作用**: 获取数据库连接对象

**输入参数**: 无

**内部调用的函数**：
- self.db.conn: 获取数据库连接

**输出参数**: sqlite3.Connection - 数据库连接对象

#### vacuum(self)

**作用**: 执行数据库清理操作，回收空间

**输入参数**: 无

**内部调用的函数**：
- self.conn: 获取数据库连接
- conn.execute: 执行 VACUUM 命令

**输出参数**: 无返回值

#### __enter__(self) -> "Storage"

**作用**: 上下文管理器入口，设置当前存储为活动状态

**输入参数**: 无

**内部调用的函数**：
- Context.set_current_context: 设置当前上下文

**输出参数**: Storage - 返回自身

**其他说明**：支持 with 语句使用

#### __exit__(self, exc_type, exc_val, exc_tb)

**作用**: 上下文管理器出口，清理当前上下文

**输入参数**:
- `exc_type`: 异常类型
- `exc_val`: 异常值
- `exc_tb`: 异常回溯

**内部调用的函数**：
- Context.clear_current_context: 清理当前上下文

**输出参数**: 无返回值

#### save_call(self, call: Call)

**作用**: 保存新的调用到存储中，如果操作未见过则同时保存操作定义

**输入参数**:
- `call`: Call，要保存的调用对象

**内部调用的函数**：
- self.calls.exists: 检查调用是否存在
- self.ops.exists: 检查操作是否存在
- self.save_ref: 保存引用对象
- self.calls.save: 保存调用

**输出参数**: 无返回值

**其他说明**：确保所有输入输出引用都已保存

#### save_ref(self, ref: Ref)

**作用**: 保存引用对象到存储中

**输入参数**:
- `ref`: Ref，要保存的引用对象

**内部调用的函数**：
- self.atoms.exists: 检查原子是否存在
- serialize: 序列化对象
- self.atoms.__setitem__: 保存原子值
- self.shapes.__setitem__: 保存形状信息

**输出参数**: 无返回值

**其他说明**：处理对象的序列化和存储

#### load_ref(self, hid: str, in_memory: bool = False) -> Ref

**作用**: 根据历史ID加载引用对象

**输入参数**:
- `hid`: str，历史ID
- `in_memory`: bool，是否加载到内存

**内部调用的函数**：
- self.call_storage.get_ref_by_hid: 获取引用信息
- self.atoms.__getitem__: 获取原子值
- deserialize: 反序列化对象

**输出参数**: Ref - 加载的引用对象

#### unwrap(self, obj: Union[Ref, Any]) -> Any

**作用**: 解包引用对象，获取实际值

**输入参数**:
- `obj`: Union[Ref, Any]，要解包的对象

**内部调用的函数**：
- self.atoms.__getitem__: 获取原子值
- deserialize: 反序列化对象

**输出参数**: Any - 解包后的实际值

**其他说明**：如果输入不是引用对象，直接返回

#### attach(self, obj: Union[Ref, Any], inplace: bool = True) -> Ref

**作用**: 将对象附加到内存中，如果需要则从存储加载

**输入参数**:
- `obj`: Union[Ref, Any]，要附加的对象
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.unwrap: 解包对象
- type(obj): 创建新的引用对象

**输出参数**: Ref - 附加后的引用对象

#### mget_call(self, hids: List[str], in_memory: bool = False) -> List[Call]

**作用**: 批量获取调用对象

**输入参数**:
- `hids`: List[str]，历史ID列表
- `in_memory`: bool，是否加载到内存

**内部调用的函数**：
- self.calls.mget: 批量获取调用

**输出参数**: List[Call] - 调用对象列表

#### get_call(self, call_hid: Optional[str] = None, call_cid: Optional[str] = None, op: Optional[Op] = None) -> Optional[Call]

**作用**: 根据历史ID、内容ID或操作获取调用对象

**输入参数**:
- `call_hid`: Optional[str]，调用历史ID
- `call_cid`: Optional[str]，调用内容ID
- `op`: Optional[Op]，操作对象

**内部调用的函数**：
- self.calls.get: 获取调用
- self.call_storage.get_call_by_cid: 根据内容ID获取调用

**输出参数**: Optional[Call] - 调用对象或 None

#### construct(self, tp: Type, val: Any) -> Tuple[Ref, List[Call]]

**作用**: 根据目标类型和值构造引用对象及相关的结构化调用

**输入参数**:
- `tp`: Type，目标类型
- `val`: Any，要构造的值

**内部调用的函数**：
- wrap_atom: 包装原子值
- self.get_struct_builder: 获取结构构建器
- self.get_struct_inputs: 获取结构输入
- self.get_struct_tps: 获取结构类型
- self.call_internal: 内部调用

**输出参数**: Tuple[Ref, List[Call]] - 引用对象和相关调用列表

**其他说明**：处理复杂数据结构的构造

#### call(self, op: Op, args: Tuple[Any, ...], kwargs: Dict[str, Any], config: Optional[Dict[str, Any]] = None) -> Union[Tuple[Ref, ...], Ref]

**作用**: 执行操作调用，这是 @op 装饰器的核心实现

**输入参数**:
- `op`: Op，要执行的操作
- `args`: Tuple[Any, ...]，位置参数
- `kwargs`: Dict[str, Any]，关键字参数
- `config`: Optional[Dict[str, Any]]，配置选项

**内部调用的函数**：
- self.parse_args: 解析参数
- self.get_call: 查找现有调用
- self.versioner.get_semantic_version: 获取语义版本
- op.f: 执行函数
- parse_returns: 解析返回值
- self.construct: 构造返回值
- self.save_call: 保存调用

**输出参数**: Union[Tuple[Ref, ...], Ref] - 执行结果的引用

**其他说明**：实现记忆化逻辑，如果调用已存在则直接返回缓存结果

#### cf(self, *refs_or_calls: Union[Ref, Call, Op], expand: bool = False) -> "ComputationFrame"

**作用**: 从引用、调用或操作创建计算框架

**输入参数**:
- `refs_or_calls`: Union[Ref, Call, Op]，引用、调用或操作对象
- `expand`: bool，是否自动扩展

**内部调用的函数**：
- ComputationFrame.from_op: 从操作创建计算框架
- ComputationFrame: 创建计算框架
- cf.expand_back: 向后扩展

**输出参数**: ComputationFrame - 创建的计算框架

**其他说明**：这是创建计算框架的主要入口点

#### drop_calls(self, calls_or_hids: Union[Iterable[str], Iterable[Call]], delete_dependents: bool = False, conn: Optional[sqlite3.Connection] = None)

**作用**: 删除指定的调用及其可选的依赖调用

**输入参数**:
- `calls_or_hids`: Union[Iterable[str], Iterable[Call]]，要删除的调用或历史ID
- `delete_dependents`: bool，是否删除依赖调用
- `conn`: Optional[sqlite3.Connection]，可选的数据库连接

**内部调用的函数**：
- self.calls.exists: 检查调用是否存在
- self.call_storage.get_dependents: 获取依赖调用
- self.calls.drop: 删除调用

**输出参数**: 无返回值

**其他说明**：支持级联删除依赖的调用

#### drop_refs(self, refs_or_hids: Union[Iterable[str], Iterable[Ref]], delete_dependents: bool = False, conn: Optional[sqlite3.Connection] = None)

**作用**: 删除指定的引用及其可选的依赖引用

**输入参数**:
- `refs_or_hids`: Union[Iterable[str], Iterable[Ref]]，要删除的引用或历史ID
- `delete_dependents`: bool，是否删除依赖引用
- `conn`: Optional[sqlite3.Connection]，可选的数据库连接

**内部调用的函数**：
- self.atoms.exists: 检查原子是否存在
- self.call_storage.get_ref_dependents: 获取依赖引用
- self.atoms.drop: 删除原子

**输出参数**: 无返回值

#### get_defaults(self, f: Callable) -> Dict[str, Any]

**作用**: 获取函数的默认参数值

**输入参数**:
- `f`: Callable，目标函数

**内部调用的函数**：
- inspect.signature: 获取函数签名

**输出参数**: Dict[str, Any] - 默认参数字典

#### parse_args(self, sig: inspect.Signature, args, kwargs, apply_defaults: bool, ignore_args: Optional[Tuple[str,...]] = None) -> Tuple[inspect.BoundArguments, Dict[str, Any], Dict[str, Any]]

**作用**: 解析传递给 @op 调用的输入参数，确定存储函数的输入、类型注解和函数调用的输入

**输入参数**:
- `sig`: inspect.Signature，函数签名
- `args`: 位置参数
- `kwargs`: 关键字参数
- `apply_defaults`: bool，是否应用默认值
- `ignore_args`: Optional[Tuple[str,...]]，要忽略的参数名

**内部调用的函数**：
- sig.bind: 绑定参数
- self.get_defaults: 获取默认值
- Type.from_annotation: 从注解获取类型

**输出参数**: Tuple[inspect.BoundArguments, Dict[str, Any], Dict[str, Any]] - 绑定参数、存储输入和类型注解

**其他说明**：处理参数绑定、默认值应用和类型推断

#### get_struct_builder(self, tp: Type) -> Op

**作用**: 获取指定类型的结构构建器操作

**输入参数**:
- `tp`: Type，目标类型

**内部调用的函数**：
- 根据类型返回相应的构建器操作（如 __make_list__, __make_dict__）

**输出参数**: Op - 结构构建器操作

#### get_struct_inputs(self, tp: Type, val: Any) -> Dict[str, Ref]

**作用**: 获取构造指定类型所需的结构输入

**输入参数**:
- `tp`: Type，目标类型
- `val`: Any，要构造的值

**内部调用的函数**：
- wrap_atom: 包装原子值
- self.construct: 递归构造子元素

**输出参数**: Dict[str, Ref] - 结构输入字典

#### get_struct_tps(self, tp: Type, struct_inputs: Dict[str, Ref]) -> Dict[str, Type]

**作用**: 获取结构输入的类型映射

**输入参数**:
- `tp`: Type，目标类型
- `struct_inputs`: Dict[str, Ref]，结构输入

**内部调用的函数**：
- 根据类型和输入确定类型映射

**输出参数**: Dict[str, Type] - 类型映射字典

#### call_internal(self, op: Op, storage_inputs: Dict[str, Ref], storage_tps: Dict[str, Type]) -> Tuple[Any, Call, List[Call]]

**作用**: 内部调用方法，执行操作并创建调用对象

**输入参数**:
- `op`: Op，要执行的操作
- `storage_inputs`: Dict[str, Ref]，存储输入
- `storage_tps`: Dict[str, Type]，存储类型

**内部调用的函数**：
- op.get_call_content_id: 获取调用内容ID
- op.get_call_history_id: 获取调用历史ID
- op.f: 执行函数
- parse_returns: 解析返回值
- self.construct: 构造返回值

**输出参数**: Tuple[Any, Call, List[Call]] - 返回值、主调用和相关调用列表

#### get_ref_creator(self, ref: Ref) -> Optional[Call]

**作用**: 获取创建指定引用的调用

**输入参数**:
- `ref`: Ref，目标引用

**内部调用的函数**：
- self.call_storage.get_ref_creator: 从存储获取创建者

**输出参数**: Optional[Call] - 创建者调用或 None

#### get_ref_consumers(self, ref: Ref) -> List[Call]

**作用**: 获取消费指定引用的所有调用

**输入参数**:
- `ref`: Ref，目标引用

**内部调用的函数**：
- self.call_storage.get_ref_consumers: 从存储获取消费者

**输出参数**: List[Call] - 消费者调用列表

### 全局函数

#### noop(*args, **kwargs) -> Any

**作用**: 空操作函数，直接返回输入

**输入参数**:
- `args`: 任意位置参数
- `kwargs`: 任意关键字参数

**内部调用的函数**：
- 无函数调用

**输出参数**: Any - 直接返回输入

**其他说明**：用于测试和调试目的

#### mode(self) -> str

**作用**: 获取当前存储模式

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 当前模式（'run' 或 'noop'）

#### allow_new_calls(self, allow: bool)

**作用**: 设置是否允许新的调用

**输入参数**:
- `allow`: bool，是否允许新调用

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### clear_cache(self, allow_uncommitted: bool = False)

**作用**: 清理所有缓存

**输入参数**:
- `allow_uncommitted`: bool，是否允许清理未提交的数据

**内部调用的函数**：
- self.atoms.clear: 清理原子缓存
- self.shapes.clear: 清理形状缓存
- self.ops.clear: 清理操作缓存
- self.calls.clear: 清理调用缓存

**输出参数**: 无返回值

#### cache_info(self) -> str

**作用**: 返回缓存信息的字符串表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 缓存信息

#### preload_calls(self)

**作用**: 预加载所有调用到缓存

**输入参数**: 无

**内部调用的函数**：
- self.call_storage.get_df: 获取调用数据框

**输出参数**: 无返回值

#### preload_shapes(self)

**作用**: 预加载所有形状信息到缓存

**输入参数**: 无

**内部调用的函数**：
- self.shapes.persistent.load_all: 加载所有形状

**输出参数**: 无返回值

#### preload_ops(self)

**作用**: 预加载所有操作到缓存

**输入参数**: 无

**内部调用的函数**：
- self.ops.persistent.load_all: 加载所有操作

**输出参数**: 无返回值

#### preload_atoms(self)

**作用**: 预加载所有原子到缓存

**输入参数**: 无

**内部调用的函数**：
- self.atoms.persistent.load_all: 加载所有原子

**输出参数**: 无返回值

#### preload(self, lazy: bool = True)

**作用**: 预加载所有数据到缓存

**输入参数**:
- `lazy`: bool，是否延迟加载

**内部调用的函数**：
- self.preload_calls: 预加载调用
- self.preload_shapes: 预加载形状
- self.preload_ops: 预加载操作
- self.preload_atoms: 预加载原子（如果不是延迟加载）

**输出参数**: 无返回值

#### commit(self)

**作用**: 提交所有缓存的更改到持久化存储

**输入参数**: 无

**内部调用的函数**：
- self.atoms.commit: 提交原子
- self.shapes.commit: 提交形状
- self.ops.commit: 提交操作
- self.calls.commit: 提交调用

**输出参数**: 无返回值

#### in_context(self) -> bool

**作用**: 检查是否在存储上下文中

**输入参数**: 无

**内部调用的函数**：
- Context.current_context: 检查当前上下文

**输出参数**: bool - 是否在上下文中

#### exists_call(self, hid: str) -> bool

**作用**: 检查指定历史ID的调用是否存在

**输入参数**:
- `hid`: str，调用历史ID

**内部调用的函数**：
- self.calls.exists: 检查调用存在性

**输出参数**: bool - 调用是否存在

#### get_call(self, hid: str, lazy: bool) -> Call

**作用**: 获取指定历史ID的调用对象

**输入参数**:
- `hid`: str，调用历史ID
- `lazy`: bool，是否延迟加载

**内部调用的函数**：
- self.mget_call: 批量获取调用

**输出参数**: Call - 调用对象

#### get_creators(self, ref_hids: Iterable[str]) -> List[Call]

**作用**: 获取创建指定引用的调用列表

**输入参数**:
- `ref_hids`: Iterable[str]，引用历史ID列表

**内部调用的函数**：
- self.call_storage.get_creators: 从存储获取创建者

**输出参数**: List[Call] - 创建者调用列表

#### get_consumers(self, ref_hids: Iterable[str]) -> List[Call]

**作用**: 获取消费指定引用的调用列表

**输入参数**:
- `ref_hids`: Iterable[str]，引用历史ID列表

**内部调用的函数**：
- self.call_storage.get_consumers: 从存储获取消费者

**输出参数**: List[Call] - 消费者调用列表

#### get_orphans(self) -> Set[str]

**作用**: 获取孤立的引用（没有创建者的引用）

**输入参数**: 无

**内部调用的函数**：
- self.call_storage.get_orphans: 从存储获取孤立引用

**输出参数**: Set[str] - 孤立引用的历史ID集合

#### get_unreferenced_cids(self) -> Set[str]

**作用**: 获取未被引用的内容ID

**输入参数**: 无

**内部调用的函数**：
- self.call_storage.get_unreferenced_cids: 从存储获取

**输出参数**: Set[str] - 未被引用的内容ID集合

#### destruct(self, ref: Ref, tp: Type) -> Tuple[Ref, List[Call]]

**作用**: 解构引用对象，将其分解为组成部分

**输入参数**:
- `ref`: Ref，要解构的引用
- `tp`: Type，目标类型

**内部调用的函数**：
- self.get_struct_builder: 获取结构构建器
- self.call_internal: 内部调用

**输出参数**: Tuple[Ref, List[Call]] - 解构后的引用和相关调用

#### lookup_call(self, op: Op, storage_inputs: Dict[str, Ref], storage_tps: Dict[str, Type], semantic_version: Optional[str] = None) -> Optional[Call]

**作用**: 查找匹配的已存在调用

**输入参数**:
- `op`: Op，操作对象
- `storage_inputs`: Dict[str, Ref]，存储输入
- `storage_tps`: Dict[str, Type]，存储类型
- `semantic_version`: Optional[str]，语义版本

**内部调用的函数**：
- op.get_call_content_id: 获取调用内容ID
- self.calls.get: 获取调用

**输出参数**: Optional[Call] - 匹配的调用或 None

#### get_versioner(self, conn: Optional[sqlite3.Connection] = None) -> Versioner

**作用**: 获取版本管理器

**输入参数**:
- `conn`: Optional[sqlite3.Connection]，可选的数据库连接

**内部调用的函数**：
- self.sources.__getitem__: 获取版本管理器

**输出参数**: Versioner - 版本管理器对象

#### save_versioner(self, versioner: Versioner, conn: Optional[sqlite3.Connection] = None)

**作用**: 保存版本管理器

**输入参数**:
- `versioner`: Versioner，版本管理器对象
- `conn`: Optional[sqlite3.Connection]，可选的数据库连接

**内部调用的函数**：
- self.sources.__setitem__: 保存版本管理器

**输出参数**: 无返回值

#### versioned(self) -> bool

**作用**: 检查存储是否启用了版本控制

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: bool - 是否启用版本控制

#### guess_code_state(self, versioner: Optional[Versioner] = None, conn: Optional[sqlite3.Connection] = None) -> str

**作用**: 猜测当前代码状态

**输入参数**:
- `versioner`: Optional[Versioner]，版本管理器
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.guess_code_state: 猜测代码状态

**输出参数**: str - 代码状态

#### sync_code(self, conn: Optional[sqlite3.Connection] = None) -> str

**作用**: 同步代码状态

**输入参数**:
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.sync_code: 同步代码

**输出参数**: str - 同步后的版本ID

#### sync_component(self, component: str, conn: Optional[sqlite3.Connection] = None) -> str

**作用**: 同步指定组件

**输入参数**:
- `component`: str，组件名称
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.sync_component: 同步组件

**输出参数**: str - 同步后的版本ID

#### versions(self, component: Optional[str] = None, conn: Optional[sqlite3.Connection] = None) -> pd.DataFrame

**作用**: 获取版本历史信息

**输入参数**:
- `component`: Optional[str]，组件名称
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.versions: 获取版本信息

**输出参数**: pd.DataFrame - 版本历史数据框

#### source_history(self, component: Optional[str] = None, conn: Optional[sqlite3.Connection] = None) -> pd.DataFrame

**作用**: 获取源代码历史

**输入参数**:
- `component`: Optional[str]，组件名称
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.source_history: 获取源代码历史

**输出参数**: pd.DataFrame - 源代码历史数据框

#### code(self, version_id: str, meta: bool = False, conn: Optional[sqlite3.Connection] = None) -> str

**作用**: 获取指定版本的代码

**输入参数**:
- `version_id`: str，版本ID
- `meta`: bool，是否包含元数据
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.get_code: 获取代码

**输出参数**: str - 代码内容

#### get_code(self, component: str, version_id: Optional[str] = None, conn: Optional[sqlite3.Connection] = None) -> str

**作用**: 获取组件的代码

**输入参数**:
- `component`: str，组件名称
- `version_id`: Optional[str]，版本ID
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.get_code: 获取代码

**输出参数**: str - 代码内容

#### diff(self, version_id_1: str, version_id_2: str, component: Optional[str] = None, conn: Optional[sqlite3.Connection] = None) -> str

**作用**: 比较两个版本之间的差异

**输入参数**:
- `version_id_1`: str，第一个版本ID
- `version_id_2`: str，第二个版本ID
- `component`: Optional[str]，组件名称
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.diff: 比较差异

**输出参数**: str - 差异信息

#### drop_version(self, semantic_version: str, conn: Optional[sqlite3.Connection] = None)

**作用**: 删除指定版本

**输入参数**:
- `semantic_version`: str，语义版本
- `conn`: Optional[sqlite3.Connection]，数据库连接

**内部调用的函数**：
- self.get_versioner: 获取版本管理器
- versioner.drop_version: 删除版本

**输出参数**: 无返回值

#### __call__(self, mode: Literal['run', 'noop'] = 'run') -> "Storage"

**作用**: 设置存储的下一个模式

**输入参数**:
- `mode`: Literal['run', 'noop']，运行模式

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Storage - 返回自身
