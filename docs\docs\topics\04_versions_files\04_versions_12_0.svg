<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="326pt" height="526pt"
 viewBox="0.00 0.00 325.50 526.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 522)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-522 321.5,-522 321.5,4 -4,4"/>
<!-- model -->
<g id="node1" class="node">
<title>model</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M182.5,-228C182.5,-228 152.5,-228 152.5,-228 146.5,-228 140.5,-222 140.5,-216 140.5,-216 140.5,-204 140.5,-204 140.5,-198 146.5,-192 152.5,-192 152.5,-192 182.5,-192 182.5,-192 188.5,-192 194.5,-198 194.5,-204 194.5,-204 194.5,-216 194.5,-216 194.5,-222 188.5,-228 182.5,-228"/>
<text text-anchor="start" x="149.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">model</text>
<text text-anchor="start" x="149" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values</text>
</g>
<!-- eval_model -->
<g id="node7" class="node">
<title>eval_model</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M183.5,-134C183.5,-134 63.5,-134 63.5,-134 57.5,-134 51.5,-128 51.5,-122 51.5,-122 51.5,-106 51.5,-106 51.5,-100 57.5,-94 63.5,-94 63.5,-94 183.5,-94 183.5,-94 189.5,-94 195.5,-100 195.5,-106 195.5,-106 195.5,-122 195.5,-122 195.5,-128 189.5,-134 183.5,-134"/>
<text text-anchor="start" x="90.5" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">eval_model</text>
<text text-anchor="start" x="59.5" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:eval_model (3 versions)</text>
<text text-anchor="start" x="109" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">4 calls</text>
</g>
<!-- model&#45;&gt;eval_model -->
<g id="edge4" class="edge">
<title>model&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M159.43,-191.76C153.1,-178.24 144.16,-159.14 136.78,-143.38"/>
<polygon fill="#002b36" stroke="#002b36" points="139.84,-141.64 132.43,-134.07 133.5,-144.61 139.84,-141.64"/>
<text text-anchor="middle" x="173" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">model</text>
<text text-anchor="middle" x="173" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- y -->
<g id="node2" class="node">
<title>y</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M47.5,-420C47.5,-420 17.5,-420 17.5,-420 11.5,-420 5.5,-414 5.5,-408 5.5,-408 5.5,-396 5.5,-396 5.5,-390 11.5,-384 17.5,-384 17.5,-384 47.5,-384 47.5,-384 53.5,-384 59.5,-390 59.5,-396 59.5,-396 59.5,-408 59.5,-408 59.5,-414 53.5,-420 47.5,-420"/>
<text text-anchor="start" x="29" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y</text>
<text text-anchor="start" x="14" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- train_model -->
<g id="node6" class="node">
<title>train_model</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M259,-326C259,-326 138,-326 138,-326 132,-326 126,-320 126,-314 126,-314 126,-298 126,-298 126,-292 132,-286 138,-286 138,-286 259,-286 259,-286 265,-286 271,-292 271,-298 271,-298 271,-314 271,-314 271,-320 265,-326 259,-326"/>
<text text-anchor="start" x="164.5" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">train_model</text>
<text text-anchor="start" x="134" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:train_model (3 versions)</text>
<text text-anchor="start" x="184" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">4 calls</text>
</g>
<!-- y&#45;&gt;train_model -->
<g id="edge7" class="edge">
<title>y&#45;&gt;train_model</title>
<path fill="none" stroke="#002b36" d="M47.3,-383.77C58.82,-371.27 75.78,-354.75 93.5,-344 102.27,-338.68 111.91,-333.98 121.69,-329.87"/>
<polygon fill="#002b36" stroke="#002b36" points="123.1,-333.07 131.08,-326.1 120.49,-326.57 123.1,-333.07"/>
<text text-anchor="middle" x="115" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y</text>
<text text-anchor="middle" x="115" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- y&#45;&gt;eval_model -->
<g id="edge8" class="edge">
<title>y&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M35.12,-383.97C39.8,-355.13 50.63,-295.01 66.5,-246 78.29,-209.59 96.57,-169.49 109.24,-143.36"/>
<polygon fill="#002b36" stroke="#002b36" points="112.53,-144.59 113.79,-134.07 106.25,-141.51 112.53,-144.59"/>
<text text-anchor="middle" x="88" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y</text>
<text text-anchor="middle" x="88" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- var_0 -->
<g id="node3" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M158.5,-36C158.5,-36 88.5,-36 88.5,-36 82.5,-36 76.5,-30 76.5,-24 76.5,-24 76.5,-12 76.5,-12 76.5,-6 82.5,0 88.5,0 88.5,0 158.5,0 158.5,0 164.5,0 170.5,-6 170.5,-12 170.5,-12 170.5,-24 170.5,-24 170.5,-30 164.5,-36 158.5,-36"/>
<text text-anchor="start" x="107.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="84.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values (4 sinks)</text>
</g>
<!-- X -->
<g id="node4" class="node">
<title>X</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M119.5,-420C119.5,-420 89.5,-420 89.5,-420 83.5,-420 77.5,-414 77.5,-408 77.5,-408 77.5,-396 77.5,-396 77.5,-390 83.5,-384 89.5,-384 89.5,-384 119.5,-384 119.5,-384 125.5,-384 131.5,-390 131.5,-396 131.5,-396 131.5,-408 131.5,-408 131.5,-414 125.5,-420 119.5,-420"/>
<text text-anchor="start" x="100" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X</text>
<text text-anchor="start" x="86" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- X&#45;&gt;train_model -->
<g id="edge2" class="edge">
<title>X&#45;&gt;train_model</title>
<path fill="none" stroke="#002b36" d="M121.74,-383.76C135.9,-369.6 156.17,-349.33 172.33,-333.17"/>
<polygon fill="#002b36" stroke="#002b36" points="174.83,-335.62 179.43,-326.07 169.88,-330.67 174.83,-335.62"/>
<text text-anchor="middle" x="182" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="182" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- X&#45;&gt;eval_model -->
<g id="edge3" class="edge">
<title>X&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M105.63,-383.97C108.77,-336.76 117.6,-203.82 121.55,-144.37"/>
<polygon fill="#002b36" stroke="#002b36" points="125.06,-144.37 122.23,-134.16 118.07,-143.91 125.06,-144.37"/>
<text text-anchor="middle" x="137" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="137" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- scale -->
<g id="node5" class="node">
<title>scale</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M300,-420C300,-420 219,-420 219,-420 213,-420 207,-414 207,-408 207,-408 207,-396 207,-396 207,-390 213,-384 219,-384 219,-384 300,-384 300,-384 306,-384 312,-390 312,-396 312,-396 312,-408 312,-408 312,-414 306,-420 300,-420"/>
<text text-anchor="start" x="244" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">scale</text>
<text text-anchor="start" x="215" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- scale&#45;&gt;train_model -->
<g id="edge5" class="edge">
<title>scale&#45;&gt;train_model</title>
<path fill="none" stroke="#002b36" d="M248.31,-383.76C239.37,-369.99 226.68,-350.42 216.34,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="219.26,-332.56 210.88,-326.07 213.38,-336.37 219.26,-332.56"/>
<text text-anchor="middle" x="258" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">scale</text>
<text text-anchor="middle" x="258" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- scale&#45;&gt;eval_model -->
<g id="edge6" class="edge">
<title>scale&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M274.21,-383.93C277.99,-378.54 281.54,-372.33 283.5,-366 287.79,-352.17 287.31,-307.98 280.5,-286 259.84,-219.31 250.06,-199.08 198.5,-152 193.39,-147.33 187.57,-143.12 181.5,-139.35"/>
<polygon fill="#002b36" stroke="#002b36" points="182.88,-136.11 172.47,-134.15 179.39,-142.17 182.88,-136.11"/>
<text text-anchor="middle" x="296" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">scale</text>
<text text-anchor="middle" x="296" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- train_model&#45;&gt;model -->
<g id="edge11" class="edge">
<title>train_model&#45;&gt;model</title>
<path fill="none" stroke="#002b36" d="M192.23,-285.98C187.64,-272.07 181.36,-253.03 176.28,-237.61"/>
<polygon fill="#002b36" stroke="#002b36" points="179.59,-236.5 173.14,-228.1 172.95,-238.69 179.59,-236.5"/>
<text text-anchor="middle" x="208" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="208" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- eval_model&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>eval_model&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M123.5,-93.98C123.5,-80.34 123.5,-61.75 123.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="127,-46.1 123.5,-36.1 120,-46.1 127,-46.1"/>
<text text-anchor="middle" x="145" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="145" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- load_data -->
<g id="node8" class="node">
<title>load_data</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M125,-518C125,-518 12,-518 12,-518 6,-518 0,-512 0,-506 0,-506 0,-490 0,-490 0,-484 6,-478 12,-478 12,-478 125,-478 125,-478 131,-478 137,-484 137,-490 137,-490 137,-506 137,-506 137,-512 131,-518 125,-518"/>
<text text-anchor="start" x="40" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">load_data</text>
<text text-anchor="start" x="8" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:load_data (2 versions)</text>
<text text-anchor="start" x="54" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- load_data&#45;&gt;y -->
<g id="edge10" class="edge">
<title>load_data&#45;&gt;y</title>
<path fill="none" stroke="#002b36" d="M47.93,-477.88C43.4,-472.55 39.16,-466.44 36.5,-460 32.7,-450.81 31.27,-440.07 30.93,-430.41"/>
<polygon fill="#002b36" stroke="#002b36" points="34.43,-430.11 30.94,-420.1 27.43,-430.1 34.43,-430.11"/>
<text text-anchor="middle" x="58" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_1</text>
<text text-anchor="middle" x="58" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- load_data&#45;&gt;X -->
<g id="edge9" class="edge">
<title>load_data&#45;&gt;X</title>
<path fill="none" stroke="#002b36" d="M75.79,-477.98C81.11,-464.07 88.4,-445.03 94.31,-429.61"/>
<polygon fill="#002b36" stroke="#002b36" points="97.64,-430.69 97.95,-420.1 91.11,-428.18 97.64,-430.69"/>
<text text-anchor="middle" x="113" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="113" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
</g>
</svg>
