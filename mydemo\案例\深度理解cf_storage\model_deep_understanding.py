"""
mandala1.model 深度理解用例
展示 model.py 中核心类和概念的完整生命周期

功能覆盖：
1. Ref引用系统的创建和管理
2. Op操作的定义和执行机制
3. Call调用的生命周期和元数据
4. @op装饰器的工作原理
5. 不同类型Ref的包装和解包
6. Context上下文管理机制
7. 集合类型的引用处理

基于mandala1框架，深度展示model.py的核心概念
"""

import os
import sys
import time
import tempfile
from typing import Dict, List, Any, Optional

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.model import (
        Ref, AtomRef, ListRef, DictRef, SetRef,
        Op, Call, Context,
        wrap_atom,
        ValuePointer, _Ignore, _NewArgDefault,
        RefCollection, ValueCollection, CallCollection
    )
    from mandala1.utils import get_content_hash
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.model import (
        Ref, AtomRef, ListRef, DictRef, SetRef,
        Op, Call, Context,
        wrap_atom,
        ValuePointer, _Ignore, _NewArgDefault,
        RefCollection, ValueCollection, CallCollection
    )
    from mandala1.utils import get_content_hash


class ModelDeepUnderstanding:
    """mandala1.model深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.current_phase = 0
        self.phase_results = {}
        
    def print_phase_header(self, phase_num: int, title: str, description: str):
        """打印阶段标题"""
        self.current_phase = phase_num
        print("\n" + "=" * 80)
        print(f"🔧 阶段 {phase_num}: {title}")
        print("=" * 80)
        print(f"📝 描述: {description}")
        print("-" * 80)

    def print_ref_concepts_explanation(self, ref: Ref):
        """打印Ref核心概念的详细说明"""
        print(f"\n💡 Ref引用系统核心概念详解:")

        # Ref基本概念
        print(f"  📋 Ref基本概念:")
        print(f"    • Ref是mandala1中所有数据的包装器")
        print(f"    • 为每个数据对象提供唯一标识和来源追踪")
        print(f"    • 支持延迟加载和内存管理优化")
        print(f"    💡 设计目的: 统一数据表示，支持计算图构建")

        # 双ID系统详解
        print(f"  🆔 双ID系统详解:")
        print(f"    • 内容ID(cid): {ref.cid[:16]}... - 基于对象内容的哈希")
        print(f"    • 历史ID(hid): {ref.hid[:16]}... - 基于计算历史的哈希")
        print(f"    💡 cid用途: 存储去重、内容比较、缓存优化")
        print(f"    💡 hid用途: 计算溯源、依赖追踪、版本管理")

        # Ref类型层次
        print(f"  📊 Ref类型层次:")
        print(f"    • 当前类型: {type(ref).__name__}")
        print(f"    • AtomRef: 原子类型(int, str, float, bool)")
        print(f"    • ListRef: 列表类型")
        print(f"    • DictRef: 字典类型")
        print(f"    • SetRef: 集合类型")
        print(f"    • TupleRef: 元组类型")
        print(f"    💡 类型选择: 根据包装对象的类型自动确定")

        # 内存管理
        print(f"  💾 内存管理机制:")
        print(f"    • 在内存状态: {ref.in_memory}")
        if ref.in_memory:
            print(f"    • 对象类型: {type(ref.obj).__name__}")
            if hasattr(ref.obj, '__len__'):
                print(f"    • 对象大小: {len(ref.obj)}")
            print(f"    💡 内存模式: 直接访问，性能最优")
        else:
            print(f"    💡 分离模式: 节省内存，按需加载")

        # 状态转换
        print(f"  🔄 状态转换机制:")
        print(f"    • detached(): 创建分离状态副本，释放内存")
        print(f"    • attached(obj): 重新附加对象到内存")
        print(f"    • with_hid(hid): 创建新历史ID的副本")
        print(f"    💡 应用场景: 内存优化、序列化传输、版本管理")

    def print_op_concepts_explanation(self, op: Op):
        """打印Op核心概念的详细说明"""
        print(f"\n💡 Op操作系统核心概念详解:")

        # Op基本概念
        print(f"  📋 Op基本概念:")
        print(f"    • Op是@op装饰器创建的函数包装器")
        print(f"    • 将普通函数转换为可记忆化的操作")
        print(f"    • 提供版本管理、参数配置、调用拦截功能")
        print(f"    💡 核心价值: 透明的记忆化，无需修改函数逻辑")

        # Op属性详解
        print(f"  ⚙️  Op属性详解:")
        print(f"    • 名称: {op.name}")
        print(f"    • 版本: {op.version}")
        print(f"    • 输出数量: {op.nout}")
        print(f"    • 输出名称: {op.output_names}")
        print(f"    • 忽略参数: {op.ignore_args}")
        print(f"    💡 版本计算: 基于函数代码内容自动生成")

        # 特殊标志
        print(f"  🏷️  特殊标志:")
        structural = getattr(op, '__structural__', False)
        side_effects = getattr(op, '__allow_side_effects__', False)
        print(f"    • 结构化操作: {structural}")
        print(f"    • 允许副作用: {side_effects}")
        print(f"    💡 结构化: 影响图构建和分析")
        print(f"    💡 副作用: 影响缓存和重复执行策略")

        # 调用机制
        print(f"  📞 调用机制:")
        print(f"    • 无Context: 直接调用原函数，返回原始值")
        print(f"    • 有Context: Storage拦截调用，返回Ref对象")
        print(f"    • 记忆化: 相同参数的重复调用从缓存加载")
        print(f"    💡 透明性: 用户无需关心内部机制")

    def print_call_concepts_explanation(self, call: Call):
        """打印Call核心概念的详细说明"""
        print(f"\n💡 Call调用系统核心概念详解:")

        # Call基本概念
        print(f"  📋 Call基本概念:")
        print(f"    • Call记录了一次完整的函数调用")
        print(f"    • 包含输入参数、输出结果、版本信息等元数据")
        print(f"    • 是Storage记忆化和ComputationFrame构建的基础")
        print(f"    💡 核心价值: 完整的调用历史和可重现性")

        # Call元数据
        print(f"  📊 Call元数据:")
        print(f"    • 操作名称: {call.op.name}")
        print(f"    • 内容ID: {call.cid[:16]}...")
        print(f"    • 历史ID: {call.hid[:16]}...")
        print(f"    • 语义版本: {call.semantic_version}")
        print(f"    • 内容版本: {call.content_version}")
        print(f"    💡 版本用途: 缓存有效性判断和依赖追踪")

        # 输入输出分析
        print(f"  📥📤 输入输出分析:")
        print(f"    • 输入参数数量: {len(call.inputs)}")
        print(f"    • 输出结果数量: {len(call.outputs)}")
        for name, ref in call.inputs.items():
            print(f"      输入 {name}: {type(ref).__name__}")
        for name, ref in call.outputs.items():
            print(f"      输出 {name}: {type(ref).__name__}")
        print(f"    💡 完整记录: 支持精确的重现和分析")

        # Call状态
        print(f"  🔄 Call状态管理:")
        print(f"    • detached(): 创建不包含内存对象的副本")
        print(f"    • 用途: 序列化、传输、长期存储")
        print(f"    💡 设计优势: 灵活的内存管理和数据传输")

    def print_context_concepts_explanation(self):
        """打印Context核心概念的详细说明"""
        print(f"\n💡 Context上下文系统核心概念详解:")

        # Context基本概念
        print(f"  📋 Context基本概念:")
        print(f"    • Context管理Storage的执行环境")
        print(f"    • 通过with语句自动设置和清理上下文")
        print(f"    • 支持嵌套上下文和多Storage管理")
        print(f"    💡 设计优势: 透明的环境管理，无需手动设置")

        # 当前上下文状态
        current = Context.current_context
        print(f"  🔄 当前上下文状态:")
        if current:
            print(f"    • 上下文存在: 是")
            print(f"    • Storage类型: {type(current.storage).__name__}")
            print(f"    • 上下文ID: {id(current)}")
            print(f"    💡 活跃状态: @op函数调用将被记忆化")
        else:
            print(f"    • 上下文存在: 否")
            print(f"    💡 非活跃状态: @op函数直接执行，无记忆化")

        # 嵌套机制
        print(f"  🔗 嵌套机制:")
        print(f"    • 支持多层嵌套: with storage1: with storage2:")
        print(f"    • 内层优先: 内层Storage拦截函数调用")
        print(f"    • 自动恢复: 退出内层后恢复外层上下文")
        print(f"    💡 应用场景: A/B测试、实验隔离、多环境支持")

        # 生命周期
        print(f"  ⏰ 生命周期管理:")
        print(f"    • 进入: __enter__()设置当前上下文")
        print(f"    • 执行: 函数调用被Storage拦截处理")
        print(f"    • 退出: __exit__()清理并恢复上下文")
        print(f"    💡 异常安全: 即使出现异常也能正确清理")
    
    def print_ref_info(self, ref: Ref, title: str = "引用信息"):
        """打印Ref的详细信息"""
        print(f"\n📊 {title}:")
        print(f"  🏷️  类型: {type(ref).__name__}")
        print(f"  🔑 内容ID: {ref.cid[:16]}...")
        print(f"  📋 历史ID: {ref.hid[:16]}...")
        print(f"  🧠 在内存: {'是' if ref.in_memory else '否'}")
        if ref.in_memory and ref.obj is not None:
            obj_str = str(ref.obj)
            if len(obj_str) > 50:
                obj_str = obj_str[:47] + "..."
            print(f"  📦 对象值: {obj_str}")
            print(f"  📏 对象类型: {type(ref.obj).__name__}")
        print(f"  🔗 引用表示: {repr(ref)}")
    
    def print_op_info(self, op: Op, title: str = "操作信息"):
        """打印Op的详细信息"""
        print(f"\n⚙️  {title}:")
        print(f"  📛 名称: {op.name}")
        print(f"  🔢 版本: {op.version}")
        print(f"  📤 输出数量: {op.nout}")
        print(f"  📋 输出名称: {op.output_names}")
        print(f"  🏗️  结构化: {'是' if op.__structural__ else '否'}")
        print(f"  ⚠️  允许副作用: {'是' if op.__allow_side_effects__ else '否'}")
        print(f"  🚫 忽略参数: {op.ignore_args}")
        print(f"  🔧 函数对象: {'存在' if op.f is not None else '不存在'}")
    
    def print_call_info(self, call: Call, title: str = "调用信息"):
        """打印Call的详细信息"""
        print(f"\n📞 {title}:")
        print(f"  ⚙️  操作: {call.op.name}")
        print(f"  🔑 内容ID: {call.cid[:16]}...")
        print(f"  📋 历史ID: {call.hid[:16]}...")
        print(f"  📥 输入数量: {len(call.inputs)}")
        print(f"  📤 输出数量: {len(call.outputs)}")
        print(f"  🏷️  语义版本: {call.semantic_version}")
        print(f"  📦 内容版本: {call.content_version}")
        
        if call.inputs:
            print("  📥 输入详情:")
            for name, ref in list(call.inputs.items())[:3]:
                print(f"    {name}: {type(ref).__name__}({ref.hid[:8]}...)")
        
        if call.outputs:
            print("  📤 输出详情:")
            for name, ref in list(call.outputs.items())[:3]:
                print(f"    {name}: {type(ref).__name__}({ref.hid[:8]}...)")
    
    def demonstrate_ref_system(self):
        """阶段1: 演示Ref引用系统"""
        self.print_phase_header(1, "Ref引用系统深度理解", 
                               "展示Ref的创建、类型、状态管理和操作")
        
        print("📚 Ref引用系统概念详解:")
        print("=" * 60)
        print("🔍 核心概念：")
        print("  • Ref是mandala1中所有数据的包装器")
        print("  • 每个Ref有两个重要ID：内容ID(cid)和历史ID(hid)")
        print("  • cid基于对象内容计算，相同内容有相同cid")
        print("  • hid基于计算历史，记录数据的来源和依赖")
        print("  • in_memory标志控制对象是否在内存中")
        print("=" * 60)
        
        print("\n🔧 Ref创建和操作演示:")
        
        # 1. AtomRef - 原子引用
        print("\n1️⃣ AtomRef（原子引用）:")
        print("💡 概念：包装基本数据类型（数字、字符串等）")
        
        # 创建不同类型的原子引用
        atom_int = wrap_atom(42)
        atom_str = wrap_atom("Hello, mandala!")
        atom_float = wrap_atom(3.14159)
        atom_bool = wrap_atom(True)
        
        self.print_ref_info(atom_int, "整数原子引用")
        self.print_ref_concepts_explanation(atom_int)
        self.print_ref_info(atom_str, "字符串原子引用")
        
        # 2. 相同内容的引用
        print("\n2️⃣ 相同内容的引用对比:")
        print("💡 概念：相同内容的对象应该有相同的cid")
        
        atom_int2 = wrap_atom(42)
        print(f"  🔑 atom_int.cid:  {atom_int.cid}")
        print(f"  🔑 atom_int2.cid: {atom_int2.cid}")
        print(f"  ✅ cid相同: {atom_int.cid == atom_int2.cid}")
        print(f"  📋 hid相同: {atom_int.hid == atom_int2.hid}")
        
        # 3. 复杂类型的Ref演示
        print("\n3️⃣ 复杂类型的Ref演示:")
        print("💡 概念：通过@op函数返回复杂类型来创建复杂Ref")

        # 创建临时Storage来演示复杂类型
        temp_storage = Storage()

        @op
        def create_list_data() -> list:
            """创建列表数据"""
            return [1, 2, 3, "hello", True]

        @op
        def create_dict_data() -> dict:
            """创建字典数据"""
            return {"name": "mandala", "version": 1.0, "active": True}

        @op
        def create_set_data() -> set:
            """创建集合数据"""
            return {1, 2, 3, "unique"}

        with temp_storage:
            list_ref = create_list_data()
            dict_ref = create_dict_data()
            set_ref = create_set_data()

        self.print_ref_info(list_ref, "列表引用（通过@op创建）")
        print(f"  📦 解包内容: {temp_storage.unwrap(list_ref)}")

        self.print_ref_info(dict_ref, "字典引用（通过@op创建）")
        print(f"  📦 解包内容: {temp_storage.unwrap(dict_ref)}")

        self.print_ref_info(set_ref, "集合引用（通过@op创建）")
        print(f"  📦 解包内容: {temp_storage.unwrap(set_ref)}")

        # 4. 手动创建复杂Ref类型
        print("\n4️⃣ 手动创建复杂Ref类型:")
        print("💡 概念：直接创建ListRef、DictRef等类型")

        # 创建包含AtomRef的ListRef
        atom_refs = [wrap_atom(1), wrap_atom(2), wrap_atom(3)]
        manual_list_ref = ListRef(
            cid=get_content_hash([ref.cid for ref in atom_refs]),
            hid=get_content_hash([ref.hid for ref in atom_refs]),
            in_memory=True,
            obj=atom_refs
        )

        self.print_ref_info(manual_list_ref, "手动创建的ListRef")
        print("  📋 列表元素:")
        for i, elem_ref in enumerate(manual_list_ref.obj):
            print(f"    [{i}]: {type(elem_ref).__name__}({elem_ref.obj})")

        # 创建包含AtomRef的DictRef
        dict_atom_refs = {
            "a": wrap_atom(1),
            "b": wrap_atom("hello"),
            "c": wrap_atom(True)
        }
        manual_dict_ref = DictRef(
            cid=get_content_hash(sorted([(k, v.cid) for k, v in dict_atom_refs.items()])),
            hid=get_content_hash(sorted([(k, v.hid) for k, v in dict_atom_refs.items()])),
            in_memory=True,
            obj=dict_atom_refs
        )

        self.print_ref_info(manual_dict_ref, "手动创建的DictRef")
        print("  📋 字典元素:")
        for key, value_ref in manual_dict_ref.obj.items():
            print(f"    {key}: {type(value_ref).__name__}({value_ref.obj})")
        
        # 6. Ref状态操作
        print("\n6️⃣ Ref状态操作:")
        print("💡 概念：Ref可以在内存和非内存状态间转换")
        
        # detached - 分离（移出内存）
        detached_ref = atom_int.detached()
        print(f"  🔓 原始引用在内存: {atom_int.in_memory}")
        print(f"  🔓 分离后在内存: {detached_ref.in_memory}")
        print(f"  🔑 ID保持不变: {atom_int.hid == detached_ref.hid}")
        
        # attached - 附加（加载到内存）
        attached_ref = detached_ref.attached(42)
        print(f"  🔒 重新附加在内存: {attached_ref.in_memory}")
        print(f"  📦 对象值恢复: {attached_ref.obj}")
        
        # with_hid - 创建新历史ID的副本
        new_hid = get_content_hash("new_history")
        ref_with_new_hid = atom_int.with_hid(new_hid)
        print(f"  🆕 新历史ID: {ref_with_new_hid.hid[:16]}...")
        print(f"  🔑 内容ID不变: {atom_int.cid == ref_with_new_hid.cid}")
        
        self.phase_results[1] = {
            'atom_refs': [atom_int, atom_str, atom_float, atom_bool],
            'complex_refs': [list_ref, dict_ref, set_ref],
            'manual_refs': [manual_list_ref, manual_dict_ref],
            'state_operations': [detached_ref, attached_ref, ref_with_new_hid]
        }

        return atom_int, list_ref, dict_ref

    def demonstrate_op_system(self):
        """阶段2: 演示Op操作系统"""
        self.print_phase_header(2, "Op操作系统深度理解",
                               "展示Op的创建、配置和@op装饰器的工作原理")

        print("📚 Op操作系统概念详解:")
        print("=" * 60)
        print("🔍 核心概念：")
        print("  • Op是mandala1中函数的包装器")
        print("  • @op装饰器将普通函数转换为可记忆化的操作")
        print("  • Op包含函数元数据：名称、版本、输出配置等")
        print("  • Op可以控制输出数量、忽略参数、结构化标志等")
        print("  • Op的调用会自动触发Storage的记忆化机制")
        print("=" * 60)

        print("\n🔧 Op创建和配置演示:")

        # 1. 基本Op创建
        print("\n1️⃣ 基本@op装饰器:")
        print("💡 概念：最简单的函数装饰")

        @op
        def simple_add(a: int, b: int) -> int:
            """简单的加法函数"""
            print(f"    🔄 执行加法: {a} + {b}")
            return a + b

        self.print_op_info(simple_add, "简单加法操作")
        self.print_op_detailed_analysis(simple_add)

        # 2. 指定输出名称的Op
        print("\n2️⃣ 指定输出名称的Op:")
        print("💡 概念：为输出指定有意义的名称")

        @op(output_names=["result"])
        def multiply_operation(a: int, b: int) -> int:
            """乘法操作"""
            print(f"    🔄 执行乘法操作: {a} × {b}")
            return a * b

        self.print_op_info(multiply_operation, "指定输出名称的操作")

        # 3. 忽略参数的Op
        print("\n3️⃣ 忽略参数的Op:")
        print("💡 概念：某些参数不影响结果，可以忽略")

        @op(ignore_args=("debug", "verbose"))
        def compute_with_debug(data: list, multiplier: float = 2.0,
                             debug: bool = False, verbose: bool = False) -> list:
            """带调试参数的计算函数"""
            if debug:
                print(f"    🐛 调试模式: 处理{len(data)}个元素")
            if verbose:
                print(f"    📢 详细模式: 乘数={multiplier}")
            print(f"    🔄 执行计算: {len(data)}个元素 × {multiplier}")
            return [x * multiplier for x in data]

        self.print_op_info(compute_with_debug, "带忽略参数的操作")

        # 4. 结构化Op
        print("\n4️⃣ 结构化Op:")
        print("💡 概念：标记为结构化的操作，通常用于内部操作")

        @op(__structural__=True, output_names=["processed_list"])
        def structural_operation(items: list) -> list:
            """结构化操作示例"""
            print(f"    🏗️  执行结构化操作: {len(items)}个项目")
            return [f"processed_{item}" for item in items]

        self.print_op_info(structural_operation, "结构化操作")

        # 5. 允许副作用的Op
        print("\n5️⃣ 允许副作用的Op:")
        print("💡 概念：某些操作可能有副作用（如文件IO）")

        @op(__allow_side_effects__=True, output_names=["log_message"])
        def logging_operation(message: str, level: str = "INFO") -> str:
            """带副作用的日志操作"""
            log_msg = f"[{level}] {message}"
            print(f"    📝 记录日志: {log_msg}")
            # 这里可能写入文件等副作用操作
            return log_msg

        self.print_op_info(logging_operation, "副作用操作")

        # 6. 手动创建Op对象
        print("\n6️⃣ 手动创建Op对象:")
        print("💡 概念：直接创建Op实例，不使用装饰器")

        def manual_function(x: float, y: float) -> float:
            """手动函数"""
            print(f"    🔄 手动函数执行: {x} ** {y}")
            return x ** y

        manual_op = Op(
            name="power_operation",
            f=manual_function,
            output_names=["result"],
            nout=1,
            __structural__=False,
            __allow_side_effects__=False
        )

        self.print_op_info(manual_op, "手动创建的操作")

        # 7. Op的detached操作
        print("\n7️⃣ Op的detached操作:")
        print("💡 概念：创建不包含函数对象的Op副本")

        detached_op = simple_add.detached()
        print(f"  🔧 原始Op有函数: {'是' if simple_add.f is not None else '否'}")
        print(f"  🔧 分离Op有函数: {'是' if detached_op.f is not None else '否'}")
        print(f"  📛 名称保持不变: {simple_add.name == detached_op.name}")
        print(f"  🔢 版本保持不变: {simple_add.version == detached_op.version}")

        self.phase_results[2] = {
            'simple_add': simple_add,
            'multiply_operation': multiply_operation,
            'compute_with_debug': compute_with_debug,
            'structural_operation': structural_operation,
            'logging_operation': logging_operation,
            'manual_op': manual_op,
            'detached_op': detached_op
        }

        return simple_add, multiply_operation, compute_with_debug

    def demonstrate_call_system(self, ops: tuple):
        """阶段3: 演示Call调用系统"""
        self.print_phase_header(3, "Call调用系统深度理解",
                               "展示Call的生命周期、元数据和Storage集成")

        print("📚 Call调用系统概念详解:")
        print("=" * 60)
        print("🔍 核心概念：")
        print("  • Call记录函数调用的完整信息")
        print("  • 包含输入、输出、操作、版本等元数据")
        print("  • Call是Storage记忆化的基本单位")
        print("  • 每个Call有唯一的内容ID和历史ID")
        print("  • Call可以detached，用于序列化和传输")
        print("=" * 60)

        # 创建Storage进行实际调用
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "model_demo.db")
        self.storage = Storage(db_path=db_path)

        print("\n🔧 Call生成和分析演示:")

        simple_add, multiply_operation, compute_with_debug = ops

        # 1. 简单函数调用
        print("\n1️⃣ 简单函数调用:")
        print("💡 概念：基本的Op调用生成Call")

        with self.storage:
            result1 = simple_add(10, 20)
            print(f"  ✅ 调用结果: {self.storage.unwrap(result1)}")

        # 获取调用信息
        call1 = self.storage.get_ref_creator(result1)
        if call1:
            self.print_call_info(call1, "简单加法调用")

        # 2. 指定输出名称的函数调用
        print("\n2️⃣ 指定输出名称的函数调用:")
        print("💡 概念：使用指定输出名称的Op调用")

        with self.storage:
            multiply_result = multiply_operation(5, 6)
            print(f"  ✅ 乘积: {self.storage.unwrap(multiply_result)}")

        # 获取调用信息
        call2 = self.storage.get_ref_creator(multiply_result)
        if call2:
            self.print_call_info(call2, "乘法操作调用")
            self.print_call_detailed_analysis(call2)

        # 3. 带忽略参数的调用
        print("\n3️⃣ 带忽略参数的调用:")
        print("💡 概念：忽略的参数不影响Call的ID")

        with self.storage:
            # 相同的核心参数，不同的忽略参数
            result3a = compute_with_debug([1, 2, 3], 2.0, debug=False, verbose=False)
            result3b = compute_with_debug([1, 2, 3], 2.0, debug=True, verbose=True)

            print(f"  ✅ 结果A: {self.storage.unwrap(result3a)}")
            print(f"  ✅ 结果B: {self.storage.unwrap(result3b)}")

        # 检查是否使用了缓存
        call3a = self.storage.get_ref_creator(result3a)
        call3b = self.storage.get_ref_creator(result3b)

        if call3a and call3b:
            print(f"  🔑 调用A的ID: {call3a.hid[:16]}...")
            print(f"  🔑 调用B的ID: {call3b.hid[:16]}...")
            print(f"  ✅ 使用缓存: {call3a.hid == call3b.hid}")

        # 4. Call的detached操作
        print("\n4️⃣ Call的detached操作:")
        print("💡 概念：创建不包含内存对象的Call副本")

        if call1:
            detached_call = call1.detached()
            print(f"  🔧 原始Call输入在内存: {all(ref.in_memory for ref in call1.inputs.values())}")
            print(f"  🔧 分离Call输入在内存: {all(ref.in_memory for ref in detached_call.inputs.values())}")
            print(f"  📋 ID保持不变: {call1.hid == detached_call.hid}")

        # 5. Call集合操作
        print("\n5️⃣ Call集合操作:")
        print("💡 概念：CallCollection用于管理多个Call")

        calls = [call1, call2, call3a] if all([call1, call2, call3a]) else []
        if calls:
            call_collection = CallCollection(calls)
            print(f"  📦 Call集合: {len(call_collection.calls)} 个调用")
            print(f"  🔄 排序方式: 按历史ID排序")
            for i, call in enumerate(call_collection.calls):
                print(f"    {i+1}. {call.op.name} ({call.hid[:8]}...)")

        self.phase_results[3] = {
            'calls': calls,
            'results': [result1, multiply_result, result3a, result3b] if 'multiply_result' in locals() else [result1, result3a, result3b] if 'result3a' in locals() else [result1],
            'call_collection': call_collection if 'call_collection' in locals() else None
        }

        return calls

    def demonstrate_context_system(self):
        """阶段4: 演示Context上下文系统"""
        self.print_phase_header(4, "Context上下文系统深度理解",
                               "展示Context的工作原理和Storage集成机制")

        print("📚 Context上下文系统概念详解:")
        print("=" * 60)
        print("🔍 核心概念：")
        print("  • Context管理Storage的生命周期")
        print("  • 使用with语句自动设置和清理上下文")
        print("  • Context.current_context是全局状态")
        print("  • Op调用时自动检查当前上下文")
        print("  • 无上下文时Op表现为普通函数")
        print("=" * 60)

        print("\n🔧 Context使用演示:")

        # 1. 无上下文的Op调用
        print("\n1️⃣ 无上下文的Op调用:")
        print("💡 概念：没有Storage上下文时，Op表现为普通函数")

        @op
        def test_function(x: int) -> int:
            print(f"    🔄 测试函数执行: {x}")
            return x * 2

        print(f"  🔍 当前上下文: {Context.current_context}")
        result_no_context = test_function(5)
        print(f"  ✅ 无上下文结果: {result_no_context} (类型: {type(result_no_context)})")
        print("  💡 注意：返回的是原始值，不是Ref")

        # 2. 有上下文的Op调用
        print("\n2️⃣ 有上下文的Op调用:")
        print("💡 概念：有Storage上下文时，Op返回Ref并记忆化")

        with self.storage:
            print(f"  🔍 当前上下文: {Context.current_context}")
            print(f"  💾 上下文Storage: {Context.current_context.storage}")
            result_with_context = test_function(5)
            print(f"  ✅ 有上下文结果: {result_with_context} (类型: {type(result_with_context)})")
            print(f"  📦 解包结果: {self.storage.unwrap(result_with_context)}")

        print(f"  🔍 退出后上下文: {Context.current_context}")

        # 3. 嵌套上下文
        print("\n3️⃣ 嵌套上下文测试:")
        print("💡 概念：Context支持嵌套使用")

        # 创建另一个Storage
        temp_storage = Storage()

        with self.storage:
            print(f"  🔍 外层上下文Storage: {id(Context.current_context.storage)}")
            outer_result = test_function(10)

            with temp_storage:
                print(f"  🔍 内层上下文Storage: {id(Context.current_context.storage)}")
                inner_result = test_function(10)
                print(f"  ✅ 内层结果: {temp_storage.unwrap(inner_result)}")

            if Context.current_context:
                print(f"  🔍 回到外层Storage: {id(Context.current_context.storage)}")
                print(f"  ✅ 外层结果: {self.storage.unwrap(outer_result)}")
            else:
                print(f"  🔍 回到外层：上下文已退出")
                print(f"  ✅ 外层结果: {self.storage.unwrap(outer_result)}")

        # 4. Context性能统计
        print("\n4️⃣ Context性能统计:")
        print("💡 概念：Context可以收集性能统计信息")

        print(f"  📊 性能统计: {Context._profiling_stats}")
        Context.reset_profiling_stats()
        print(f"  🔄 重置后统计: {Context._profiling_stats}")

        self.phase_results[4] = {
            'no_context_result': result_no_context,
            'with_context_result': result_with_context,
            'nested_results': [outer_result, inner_result] if 'outer_result' in locals() else []
        }

        return test_function

    def demonstrate_special_wrappers(self):
        """阶段5: 演示特殊包装器和工具类"""
        self.print_phase_header(5, "特殊包装器与工具类",
                               "展示ValuePointer、_Ignore、集合类等特殊功能")

        print("📚 特殊包装器概念详解:")
        print("=" * 60)
        print("🔍 核心概念：")
        print("  • ValuePointer：用人类可读名称替代对象")
        print("  • _Ignore：标记应被Storage忽略的值")
        print("  • _NewArgDefault：向后兼容的新参数默认值")
        print("  • RefCollection：管理多个Ref的集合")
        print("  • ValueCollection：管理多个值的集合")
        print("=" * 60)

        print("\n🔧 特殊包装器演示:")

        # 1. ValuePointer使用
        print("\n1️⃣ ValuePointer使用:")
        print("💡 概念：用名称标识对象，不保存实际内容")

        class DatabaseConnection:
            def __init__(self, host: str):
                self.host = host
            def __repr__(self):
                return f"DB({self.host})"

        db_conn = DatabaseConnection("localhost")
        db_pointer = ValuePointer("database_connection", db_conn)

        print(f"  🏷️  ValuePointer ID: {db_pointer.id}")
        print(f"  📦 包装的对象: {db_pointer.obj}")
        print(f"  🔗 表示: {repr(db_pointer)}")

        @op
        def query_database(conn: DatabaseConnection, query: str) -> str:
            print(f"    🔄 查询数据库 {conn}: {query}")
            return f"Result from {conn.host}: {query}"

        with self.storage:
            # 使用ValuePointer包装的连接
            result = query_database(db_pointer, "SELECT * FROM users")
            print(f"  ✅ 查询结果: {self.storage.unwrap(result)}")

        # 2. _Ignore使用
        print("\n2️⃣ _Ignore使用:")
        print("💡 概念：标记Storage应该忽略的参数")

        @op
        def process_with_config(data: list, multiplier: float,
                              config: dict = {"debug": True}) -> list:
            print(f"    🔄 处理数据，配置: {config}")
            return [x * multiplier for x in data]

        with self.storage:
            # config参数不同，但演示概念
            result1 = process_with_config([1, 2, 3], 2.0, {"debug": True})
            result2 = process_with_config([1, 2, 3], 2.0, {"debug": False})

            print(f"  ✅ 结果1: {self.storage.unwrap(result1)}")
            print(f"  ✅ 结果2: {self.storage.unwrap(result2)}")

        # 检查是否使用了缓存（退出上下文后）
        try:
            call1 = self.storage.get_ref_creator(result1)
            call2 = self.storage.get_ref_creator(result2)
            if call1 and call2:
                print(f"  🔄 使用缓存: {call1.hid == call2.hid}")
        except Exception as e:
            print(f"  ❌ 缓存检查失败: {e}")

        # 3. 参数默认值演示
        print("\n3️⃣ 参数默认值演示:")
        print("💡 概念：函数参数的默认值处理")

        @op
        def backward_compatible_func(data: list, prefix: str = "default") -> list:
            print(f"    🔄 向后兼容函数，前缀: {prefix}")
            return [f"{prefix}_{x}" for x in data]

        with self.storage:
            # 使用默认值
            result_default = backward_compatible_func(["a", "b"])
            # 使用自定义值
            result_custom = backward_compatible_func(["a", "b"], "custom")

            print(f"  ✅ 默认结果: {self.storage.unwrap(result_default)}")
            print(f"  ✅ 自定义结果: {self.storage.unwrap(result_custom)}")

        # 4. RefCollection和ValueCollection
        print("\n4️⃣ 集合类使用:")
        print("💡 概念：管理多个Ref或值的集合")

        # 创建一些Ref
        refs = []
        with self.storage:
            for i in range(3):
                ref = query_database(db_pointer, f"Query {i}")
                refs.append(ref)

        # RefCollection
        ref_collection = RefCollection(refs)
        print(f"  📦 RefCollection: {len(ref_collection.refs)} 个引用")
        print(f"  🔄 排序: 按hid排序")
        for i, ref in enumerate(ref_collection.refs):
            print(f"    {i+1}. {ref.hid[:8]}...")

        # ValueCollection
        values = [self.storage.unwrap(ref) for ref in refs]
        value_collection = ValueCollection(values)
        print(f"  📦 ValueCollection: {len(value_collection.values)} 个值")
        for i, value in enumerate(value_collection.values):
            print(f"    {i+1}. {value}")

        self.phase_results[5] = {
            'value_pointer': db_pointer,
            'ignore_results': [result1, result2] if 'result1' in locals() else [],
            'new_arg_results': [result_default, result_custom] if 'result_default' in locals() else [],
            'ref_collection': ref_collection,
            'value_collection': value_collection
        }

        return ref_collection, value_collection

    def print_op_detailed_analysis(self, op):
        """打印Op类的详细分析"""
        print(f"\n📚 Op类详细分析:")
        print(f"=" * 80)

        print(f"\n🏗️  Op类成员变量详解:")
        print(f"  📛 name: str")
        print(f"    💡 含义: 操作的名称标识")
        print(f"    🎯 作用: 用于缓存键生成和调试显示")
        print(f"    📝 实际值: {op.name}")
        print(f"    🔍 重要性: 唯一标识不同的操作")

        print(f"  🔢 nout: Union[Literal['var', 'auto'], int]")
        print(f"    💡 含义: 输出数量规格")
        print(f"    🎯 作用: 控制函数返回值的处理方式")
        print(f"    📝 实际值: {op.nout}")
        print(f"    🔍 可能值: 'auto'(自动检测), 'var'(可变), 具体数字")

        print(f"  🏷️  output_names: Optional[List[str]]")
        print(f"    💡 含义: 输出参数的名称列表")
        print(f"    🎯 作用: 为多输出函数提供语义化的名称")
        print(f"    📝 实际值: {op.output_names}")
        print(f"    🔍 重要性: 提高代码可读性和调试便利性")

        print(f"  🔖 version: Optional[int]")
        print(f"    💡 含义: 操作的版本号")
        print(f"    🎯 作用: 手动控制函数版本，影响缓存有效性")
        print(f"    📝 实际值: {op.version}")
        print(f"    🔍 重要性: 支持手动版本管理")

        print(f"  🚫 ignore_args: Optional[Tuple[str,...]]")
        print(f"    💡 含义: 计算哈希时忽略的参数名")
        print(f"    🎯 作用: 排除不影响结果的参数(如随机种子)")
        print(f"    📝 实际值: {op.ignore_args}")
        print(f"    🔍 重要性: 提高缓存命中率")

        print(f"  🏗️  __structural__: bool")
        print(f"    💡 含义: 是否为结构化操作")
        print(f"    🎯 作用: 影响图构建和分析行为")
        print(f"    📝 实际值: {op.__structural__}")
        print(f"    🔍 重要性: 区分数据操作和控制操作")

        print(f"  ⚠️  __allow_side_effects__: bool")
        print(f"    💡 含义: 是否允许副作用")
        print(f"    🎯 作用: 控制缓存和重复执行策略")
        print(f"    📝 实际值: {op.__allow_side_effects__}")
        print(f"    🔍 重要性: 确保副作用函数的正确处理")

        print(f"  🔧 f: Callable")
        print(f"    💡 含义: 被包装的原始函数")
        print(f"    🎯 作用: 实际执行计算的函数对象")
        print(f"    📝 实际值: {op.f}")
        print(f"    🔍 重要性: Op的核心执行逻辑")

        print(f"\n💡 Op类设计理念:")
        print(f"  🎯 装饰器模式: 透明地包装普通函数")
        print(f"  🔄 版本管理: 自动和手动版本控制结合")
        print(f"  ⚡ 性能优化: 智能缓存和参数过滤")
        print(f"  🔧 灵活配置: 丰富的配置选项适应不同需求")
        print(f"=" * 80)

    def print_call_detailed_analysis(self, call):
        """打印Call类的详细分析"""
        print(f"\n📚 Call类详细分析:")
        print(f"=" * 80)

        print(f"\n🏗️  Call类成员变量详解:")
        print(f"  ⚙️  op: Op")
        print(f"    💡 含义: 执行的操作对象")
        print(f"    🎯 作用: 记录调用的函数信息")
        print(f"    📝 实际值: {call.op.name}")
        print(f"    🔍 重要性: 连接调用记录和函数定义")

        print(f"  🆔 cid: str")
        print(f"    💡 含义: 内容ID，基于输入参数计算")
        print(f"    🎯 作用: 用于缓存查找和去重")
        print(f"    📝 实际值: {call.cid[:16]}...")
        print(f"    🔍 重要性: 实现记忆化的关键")

        print(f"  🆔 hid: str")
        print(f"    💡 含义: 历史ID，包含版本和上下文信息")
        print(f"    🎯 作用: 唯一标识每次调用实例")
        print(f"    📝 实际值: {call.hid[:16]}...")
        print(f"    🔍 重要性: 支持完整的调用历史追踪")

        print(f"  📥 inputs: Dict[str, Ref]")
        print(f"    💡 含义: 输入参数名到Ref对象的映射")
        print(f"    🎯 作用: 记录函数调用的所有输入")
        print(f"    📝 参数数量: {len(call.inputs)}")
        print(f"    📝 参数名称: {list(call.inputs.keys())}")
        print(f"    🔍 重要性: 支持参数级别的依赖追踪")

        print(f"  📤 outputs: Dict[str, Ref]")
        print(f"    💡 含义: 输出参数名到Ref对象的映射")
        print(f"    🎯 作用: 记录函数调用的所有输出")
        print(f"    📝 输出数量: {len(call.outputs)}")
        print(f"    📝 输出名称: {list(call.outputs.keys())}")
        print(f"    🔍 重要性: 支持多输出函数的完整记录")

        print(f"  🏷️  semantic_version: Optional[str]")
        print(f"    💡 含义: 语义版本号")
        print(f"    🎯 作用: 记录函数的语义版本信息")
        print(f"    📝 实际值: {call.semantic_version}")
        print(f"    🔍 重要性: 支持版本兼容性检查")

        print(f"  🏷️  content_version: Optional[str]")
        print(f"    💡 含义: 内容版本号")
        print(f"    🎯 作用: 记录函数内容的版本信息")
        print(f"    📝 实际值: {call.content_version}")
        print(f"    🔍 重要性: 支持精确的版本控制")

        print(f"\n💡 Call类设计理念:")
        print(f"  📝 完整记录: 记录函数调用的所有相关信息")
        print(f"  🔗 关系追踪: 通过Ref对象建立数据依赖关系")
        print(f"  🔄 版本管理: 多层次的版本信息记录")
        print(f"  ⚡ 缓存支持: 提供缓存查找所需的所有信息")
        print(f"=" * 80)

    def print_node_callablenode_analysis(self):
        """打印Node和CallableNode的详细分析"""
        print(f"\n📚 Node和CallableNode类详细分析:")
        print(f"=" * 80)

        print(f"\n🏗️  Node类 (deps/model.py):")
        print(f"  📋 基本概念:")
        print(f"    💡 Node是依赖追踪系统中的抽象基类")
        print(f"    🎯 作用: 表示代码依赖图中的节点")
        print(f"    🔍 位置: mandala1/deps/model.py")

        print(f"  🏗️  成员变量:")
        print(f"    📛 module_name: str")
        print(f"      💡 含义: 模块名称")
        print(f"      🎯 作用: 标识节点所属的Python模块")

        print(f"    📛 obj_name: str")
        print(f"      💡 含义: 对象名称")
        print(f"      🎯 作用: 标识模块中的具体对象")

        print(f"    📦 representation: Any")
        print(f"      💡 含义: 对象的表示形式")
        print(f"      🎯 作用: 存储对象的序列化表示")

        print(f"  🔑 关键属性:")
        print(f"    🆔 key: DepKey")
        print(f"      💡 含义: (module_name, obj_name)元组")
        print(f"      🎯 作用: 唯一标识依赖节点")

        print(f"    #️⃣ content_hash: str")
        print(f"      💡 含义: 内容哈希值")
        print(f"      🎯 作用: 检测对象内容变化")

        print(f"\n🏗️  CallableNode类 (deps/model.py):")
        print(f"  📋 基本概念:")
        print(f"    💡 CallableNode是Node的子类，专门处理可调用对象")
        print(f"    🎯 作用: 表示函数、方法等可调用对象的依赖")
        print(f"    🔍 继承: 继承自Node类")

        print(f"  🏗️  额外成员变量:")
        print(f"    📝 runtime_description: str")
        print(f"      💡 含义: 运行时描述信息")
        print(f"      🎯 作用: 记录函数的运行时特征")

        print(f"    📦 _representation: Optional[str]")
        print(f"      💡 含义: 私有的源代码表示")
        print(f"      🎯 作用: 存储函数的源代码")

        print(f"    #️⃣ _content_hash: Optional[str]")
        print(f"      💡 含义: 私有的内容哈希")
        print(f"      🎯 作用: 缓存计算的哈希值")

        print(f"  🏭 工厂方法:")
        print(f"    🔧 from_obj(obj, dep_key)")
        print(f"      💡 含义: 从对象创建CallableNode")
        print(f"      🎯 作用: 静态分析时使用")

        print(f"    🔧 from_runtime(module_name, obj_name, code_obj)")
        print(f"      💡 含义: 从运行时信息创建CallableNode")
        print(f"      🎯 作用: 动态分析时使用")

        print(f"\n🎨 可视化Node类 (viz.py):")
        print(f"  📋 基本概念:")
        print(f"    💡 用于ComputationFrame图的可视化")
        print(f"    🎯 作用: 定义图节点的显示样式")
        print(f"    🔍 位置: mandala1/viz.py")

        print(f"  🎨 样式属性:")
        print(f"    🏷️  label: str - 节点显示标签")
        print(f"    🎨 color: str - 节点颜色")
        print(f"    📐 shape: str - 节点形状(rect/record/Mrecord)")
        print(f"    📏 label_size: int - 标签字体大小")
        print(f"    📝 additional_lines: List[str] - 额外显示行")

        print(f"\n💡 三种Node类的关系:")
        print(f"  🔗 deps.Node: 依赖追踪的抽象基类")
        print(f"  🔗 deps.CallableNode: 可调用对象的具体实现")
        print(f"  🔗 viz.Node: 可视化显示的样式定义")
        print(f"  🎯 协同工作: 依赖追踪 → 图构建 → 可视化显示")
        print(f"=" * 80)


def main():
    """主函数：运行model深度理解演示"""
    print("🔧 mandala1.model 深度理解用例")
    print("=" * 80)
    print("📖 本演示将展示model.py中核心类和概念的完整生命周期")
    print("🎯 目标：深入理解Ref、Op、Call等核心组件的工作原理")
    print("=" * 80)
    
    demo = ModelDeepUnderstanding()

    # 首先介绍Node和CallableNode的概念
    demo.print_node_callablenode_analysis()

    try:
        # 阶段1: Ref引用系统
        atom_ref, list_ref, dict_ref = demo.demonstrate_ref_system()

        # 阶段2: Op操作系统
        ops = demo.demonstrate_op_system()

        # 阶段3: Call调用系统
        calls = demo.demonstrate_call_system(ops)

        # 阶段4: Context上下文系统
        test_func = demo.demonstrate_context_system()

        # 阶段5: 特殊包装器
        ref_collection, value_collection = demo.demonstrate_special_wrappers()

        print("\n" + "=" * 80)
        print("🎉 model深度理解演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        for phase, results in demo.phase_results.items():
            print(f"  阶段{phase}: ✅ 完成")

        print("\n📈 完整功能覆盖:")
        print("  ✅ Ref引用系统：AtomRef、ListRef、DictRef、SetRef")
        print("  ✅ Op操作系统：@op装饰器、配置选项、手动创建")
        print("  ✅ Call调用系统：调用记录、元数据、detached操作")
        print("  ✅ Context上下文：生命周期管理、嵌套上下文")
        print("  ✅ 特殊包装器：ValuePointer、_Ignore、集合类")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if demo.temp_dir and os.path.exists(demo.temp_dir):
            import shutil
            try:
                shutil.rmtree(demo.temp_dir)
                print(f"\n🧹 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


def main():
    """主函数"""
    demo = ModelDeepUnderstanding()

    # 首先介绍Node和CallableNode的概念
    demo.print_node_callablenode_analysis()

    try:
        # 1. Ref引用系统演示
        demo.demonstrate_ref_system()

        # 2. Op操作系统演示
        ops = demo.demonstrate_op_system()

        # 3. Call调用系统演示
        demo.demonstrate_call_system(ops)

        # 4. Context上下文系统演示
        demo.demonstrate_context_system()

        # 5. 特殊包装器演示
        demo.demonstrate_special_wrappers()

        print("\n🎉 演示完成!")
        print("=" * 80)
        print("通过以上演示，我们深入了解了mandala1的核心模型组件：")
        print("• Ref引用系统：统一的数据包装和标识")
        print("• Op操作系统：函数的装饰和版本管理")
        print("• Call调用系统：完整的调用记录和追踪")
        print("• Context上下文系统：透明的执行环境管理")
        print("• 这些组件协同工作，构成了mandala1的核心架构")
        print("=" * 80)

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if hasattr(demo, 'temp_dir') and demo.temp_dir and os.path.exists(demo.temp_dir):
            import shutil
            try:
                shutil.rmtree(demo.temp_dir)
                print(f"\n🧹 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


if __name__ == "__main__":
    main()
