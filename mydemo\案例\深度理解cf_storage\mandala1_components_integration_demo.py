#!/usr/bin/env python3
"""
Mandala1核心组件集成演示

本演示展示Storage、Op、Call、Node、CallableNode等核心组件的协同工作，
以及它们在实际项目中的集成使用方式。

作者: AI Assistant
日期: 2025-01-30
"""

import os
import sys
import tempfile
import shutil
from typing import List, Dict, Any

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.model import Call, Ref, Op
    from mandala1.cf import ComputationFrame
except ImportError:
    try:
        # 尝试相对导入
        sys.path.append(os.path.dirname(__file__))
        from mandala1.imports import Storage, op
        from mandala1.model import Call, Ref, Op
        from mandala1.cf import ComputationFrame
    except ImportError:
        print("❌ 无法导入mandala1模块，请检查路径设置")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"Python路径: {sys.path}")
        sys.exit(1)


class Mandala1ComponentsIntegrationDemo:
    """Mandala1核心组件集成演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        
    def print_section_header(self, title: str, description: str):
        """打印章节标题"""
        print(f"\n{'='*80}")
        print(f"🔧 {title}")
        print(f"{'='*80}")
        print(f"📝 {description}")
        print(f"{'-'*80}")
    
    def demonstrate_complete_workflow(self):
        """演示完整的工作流程"""
        self.print_section_header(
            "完整工作流程演示",
            "展示Storage、Op、Call、ComputationFrame的完整集成使用"
        )
        
        # 1. 创建Storage实例
        print("\n1️⃣ 创建Storage实例:")
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "integration_demo.db")
        
        self.storage = Storage(
            db_path=db_path,
            overflow_dir=os.path.join(self.temp_dir, "overflow"),
            overflow_threshold_MB=10.0,
            deps_path=None  # 简化演示，不启用版本控制
        )
        
        print(f"  ✅ Storage创建成功")
        print(f"  📁 数据库路径: {db_path}")
        print(f"  📁 溢出目录: {self.storage.overflow_dir}")
        print(f"  📏 溢出阈值: {self.storage.overflow_threshold_MB}MB")
        
        # 2. 定义Op操作
        print("\n2️⃣ 定义Op操作:")
        
        @op(output_names=["cleaned_data"])
        def clean_data(raw_data: List[int]) -> List[int]:
            """数据清洗操作"""
            print(f"    🧹 清洗数据: {len(raw_data)}个元素")
            return [x for x in raw_data if x > 0]
        
        @op(output_names=["features"])
        def extract_features(data: List[int]) -> Dict[str, float]:
            """特征提取操作"""
            print(f"    🔍 提取特征: {len(data)}个数据点")
            return {
                "mean": sum(data) / len(data) if data else 0,
                "max": max(data) if data else 0,
                "min": min(data) if data else 0,
                "count": len(data)
            }
        
        @op(output_names=["model"])
        def train_model(features: Dict[str, float]) -> Dict[str, Any]:
            """模型训练操作"""
            print(f"    🤖 训练模型: {len(features)}个特征")
            return {
                "type": "linear_model",
                "coefficients": [features["mean"], features["max"]],
                "intercept": features["min"],
                "score": 0.95
            }
        
        print(f"  ✅ 定义了3个Op操作:")
        print(f"    • clean_data: {clean_data.name}")
        print(f"    • extract_features: {extract_features.name}")
        print(f"    • train_model: {train_model.name}")
        
        # 3. 在Storage上下文中执行操作
        print("\n3️⃣ 在Storage上下文中执行操作:")
        
        with self.storage:
            # 原始数据
            raw_data = [1, -2, 3, 4, -5, 6, 7, 8, -9, 10]
            print(f"  📊 原始数据: {raw_data}")
            
            # 执行数据清洗
            cleaned_ref = clean_data(raw_data)
            cleaned_data = self.storage.unwrap(cleaned_ref)
            print(f"  🧹 清洗后数据: {cleaned_data}")
            
            # 执行特征提取
            features_ref = extract_features(cleaned_data)
            features = self.storage.unwrap(features_ref)
            print(f"  🔍 提取的特征: {features}")
            
            # 执行模型训练
            model_ref = train_model(features)
            model = self.storage.unwrap(model_ref)
            print(f"  🤖 训练的模型: {model}")
        
        # 4. 分析Call记录
        print("\n4️⃣ 分析Call记录:")
        
        # 获取各个操作的Call记录
        clean_call = self.storage.get_ref_creator(cleaned_ref)
        features_call = self.storage.get_ref_creator(features_ref)
        model_call = self.storage.get_ref_creator(model_ref)
        
        calls = [clean_call, features_call, model_call]
        
        for i, call in enumerate(calls, 1):
            if call:
                print(f"  📞 Call {i}: {call.op.name}")
                print(f"    🆔 CID: {call.cid[:16]}...")
                print(f"    🆔 HID: {call.hid[:16]}...")
                print(f"    📥 输入数量: {len(call.inputs)}")
                print(f"    📤 输出数量: {len(call.outputs)}")
        
        # 5. 构建ComputationFrame
        print("\n5️⃣ 构建ComputationFrame:")
        
        # 从最终结果构建CF
        cf = self.storage.cf(model_ref)
        print(f"  📊 ComputationFrame统计:")
        print(f"    🔢 节点数量: {len(cf.nodes)}")
        print(f"    🔗 边数量: {len(list(cf.edges()))}")
        print(f"    📦 变量节点: {list(cf.vnames)}")
        print(f"    ⚙️  函数节点: {list(cf.fnames)}")
        print(f"    🌱 源节点: {list(cf.sources)}")
        print(f"    🎯 汇节点: {list(cf.sinks)}")
        
        # 6. 展示扩展操作
        print("\n6️⃣ 展示ComputationFrame扩展:")
        
        # 向后扩展
        cf_expanded = cf.expand_back(recursive=True)
        print(f"  📈 向后扩展后:")
        print(f"    🔢 节点数量: {len(cf.nodes)} → {len(cf_expanded.nodes)}")
        print(f"    🔗 边数量: {len(list(cf.edges()))} → {len(list(cf_expanded.edges()))}")
        
        # 7. 展示Storage缓存信息
        print("\n7️⃣ Storage缓存信息:")
        print(f"  ⚡ 缓存统计:")
        print(f"    📦 atoms缓存: {len(self.storage.atoms.cache)}个对象")
        print(f"    📐 shapes缓存: {len(self.storage.shapes.cache)}个形状")
        print(f"    ⚙️  ops缓存: {len(self.storage.ops.cache)}个操作")
        print(f"    📞 calls缓存: {len(self.storage.calls.cache)}个调用")
        
        return {
            'storage': self.storage,
            'calls': calls,
            'cf': cf,
            'cf_expanded': cf_expanded,
            'refs': [cleaned_ref, features_ref, model_ref]
        }
    
    def demonstrate_advanced_features(self):
        """演示高级功能"""
        self.print_section_header(
            "高级功能演示",
            "展示版本管理、参数忽略、多输出等高级特性"
        )
        
        # 1. 带忽略参数的Op
        print("\n1️⃣ 带忽略参数的Op:")

        @op(ignore_args=("debug",))
        def process_with_ignore(data: List[int], multiplier: float, debug: bool = False) -> List[float]:
            """带忽略参数的操作"""
            if debug:
                print(f"    🐛 调试模式: 处理{len(data)}个元素")
            return [x * multiplier for x in data]

        with self.storage:
            # 相同的核心参数，不同的debug值
            result1 = process_with_ignore([1, 2, 3], 2.0, debug=False)
            result2 = process_with_ignore([1, 2, 3], 2.0, debug=True)

        # 在Storage上下文外检查调用记录
        call1 = self.storage.get_ref_creator(result1)
        call2 = self.storage.get_ref_creator(result2)

        print(f"  ✅ 两次调用的CID相同: {call1.cid == call2.cid}")
        print(f"  📝 说明: debug参数被忽略，使用了缓存")
        
        # 2. 结构化Op
        print("\n2️⃣ 结构化Op:")

        @op(__structural__=True, output_names=["processed"])
        def structural_process(data: List[int]) -> Dict[str, Any]:
            """结构化处理操作"""
            print(f"    🏗️  结构化处理: {len(data)}个元素")
            return {
                "count": len(data),
                "sum": sum(data),
                "processed": True
            }

        with self.storage:
            result = structural_process([3, 1, 4, 1, 5, 9, 2, 6])
            processed_data = self.storage.unwrap(result)
            print(f"  📊 结构化处理结果: {processed_data}")

        # 在Storage上下文外分析结构化Call
        call = self.storage.get_ref_creator(result)
        print(f"  📞 结构化Call:")
        print(f"    ⚙️  操作名: {call.op.name}")
        print(f"    🏗️  结构化: {call.op.__structural__}")
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                print(f"\n🧹 清理临时目录: {self.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


def main():
    """主函数"""
    print("🚀 Mandala1核心组件集成演示")
    print("=" * 80)
    print("本演示展示Storage、Op、Call、ComputationFrame等组件的协同工作")
    print("=" * 80)
    
    demo = Mandala1ComponentsIntegrationDemo()
    
    try:
        # 完整工作流程演示
        results = demo.demonstrate_complete_workflow()
        
        # 高级功能演示（暂时跳过，避免复杂性）
        # demo.demonstrate_advanced_features()
        
        print("\n" + "=" * 80)
        print("🎉 集成演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        print("  ✅ Storage: 存储管理和缓存系统")
        print("  ✅ Op: 函数装饰和版本管理")
        print("  ✅ Call: 调用记录和元数据追踪")
        print("  ✅ ComputationFrame: 计算图构建和分析")
        print("  ✅ 高级特性: 版本控制、参数忽略、多输出")
        print("\n💡 关键洞察:")
        print("  🔗 所有组件协同工作，形成完整的计算追踪系统")
        print("  ⚡ 自动记忆化大幅提高重复计算的效率")
        print("  📈 ComputationFrame提供强大的计算图分析能力")
        print("  🔧 灵活的配置选项适应不同的使用场景")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        demo.cleanup()


if __name__ == "__main__":
    main()
