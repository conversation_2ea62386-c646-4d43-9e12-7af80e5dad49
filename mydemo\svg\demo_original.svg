<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="236pt" height="428pt"
 viewBox="0.00 0.00 236.00 428.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 424)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-424 232,-424 232,4 -4,4"/>
<!-- mode -->
<g id="node1" class="node">
<title>mode</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-420C93,-420 12,-420 12,-420 6,-420 0,-414 0,-408 0,-408 0,-396 0,-396 0,-390 6,-384 12,-384 12,-384 93,-384 93,-384 99,-384 105,-390 105,-396 105,-396 105,-408 105,-408 105,-414 99,-420 93,-420"/>
<text text-anchor="start" x="36" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">mode</text>
<text text-anchor="start" x="8" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- compute -->
<g id="node6" class="node">
<title>compute</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M140.5,-326C140.5,-326 86.5,-326 86.5,-326 80.5,-326 74.5,-320 74.5,-314 74.5,-314 74.5,-298 74.5,-298 74.5,-292 80.5,-286 86.5,-286 86.5,-286 140.5,-286 140.5,-286 146.5,-286 152.5,-292 152.5,-298 152.5,-298 152.5,-314 152.5,-314 152.5,-320 146.5,-326 140.5,-326"/>
<text text-anchor="start" x="88" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">compute</text>
<text text-anchor="start" x="82.5" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:compute</text>
<text text-anchor="start" x="99" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- mode&#45;&gt;compute -->
<g id="edge5" class="edge">
<title>mode&#45;&gt;compute</title>
<path fill="none" stroke="#002b36" d="M63.69,-383.76C72.63,-369.99 85.32,-350.42 95.66,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="98.62,-336.37 101.12,-326.07 92.74,-332.56 98.62,-336.37"/>
<text text-anchor="middle" x="110" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">mode</text>
<text text-anchor="middle" x="110" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- var_0 -->
<g id="node2" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M148.5,-36C148.5,-36 78.5,-36 78.5,-36 72.5,-36 66.5,-30 66.5,-24 66.5,-24 66.5,-12 66.5,-12 66.5,-6 72.5,0 78.5,0 78.5,0 148.5,0 148.5,0 154.5,0 160.5,-6 160.5,-12 160.5,-12 160.5,-24 160.5,-24 160.5,-30 154.5,-36 148.5,-36"/>
<text text-anchor="start" x="97.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="74.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sinks)</text>
</g>
<!-- data_0 -->
<g id="node3" class="node">
<title>data_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M216,-420C216,-420 135,-420 135,-420 129,-420 123,-414 123,-408 123,-408 123,-396 123,-396 123,-390 129,-384 135,-384 135,-384 216,-384 216,-384 222,-384 228,-390 228,-396 228,-396 228,-408 228,-408 228,-414 222,-420 216,-420"/>
<text text-anchor="start" x="156" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data_0</text>
<text text-anchor="start" x="131" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- data_0&#45;&gt;compute -->
<g id="edge4" class="edge">
<title>data_0&#45;&gt;compute</title>
<path fill="none" stroke="#002b36" d="M164.13,-383.76C155.04,-369.99 142.14,-350.42 131.63,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="134.51,-332.49 126.08,-326.07 128.66,-336.35 134.51,-332.49"/>
<text text-anchor="middle" x="173" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="173" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- data -->
<g id="node4" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M128.5,-228C128.5,-228 98.5,-228 98.5,-228 92.5,-228 86.5,-222 86.5,-216 86.5,-216 86.5,-204 86.5,-204 86.5,-198 92.5,-192 98.5,-192 98.5,-192 128.5,-192 128.5,-192 134.5,-192 140.5,-198 140.5,-204 140.5,-204 140.5,-216 140.5,-216 140.5,-222 134.5,-228 128.5,-228"/>
<text text-anchor="start" x="101" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="95" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values</text>
</g>
<!-- summarize -->
<g id="node5" class="node">
<title>summarize</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M146,-134C146,-134 81,-134 81,-134 75,-134 69,-128 69,-122 69,-122 69,-106 69,-106 69,-100 75,-94 81,-94 81,-94 146,-94 146,-94 152,-94 158,-100 158,-106 158,-106 158,-122 158,-122 158,-128 152,-134 146,-134"/>
<text text-anchor="start" x="82" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">summarize</text>
<text text-anchor="start" x="77" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:summarize</text>
<text text-anchor="start" x="99" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- data&#45;&gt;summarize -->
<g id="edge2" class="edge">
<title>data&#45;&gt;summarize</title>
<path fill="none" stroke="#002b36" d="M113.5,-191.76C113.5,-178.5 113.5,-159.86 113.5,-144.27"/>
<polygon fill="#002b36" stroke="#002b36" points="117,-144.07 113.5,-134.07 110,-144.07 117,-144.07"/>
<text text-anchor="middle" x="135" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="135" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- summarize&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>summarize&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M113.5,-93.98C113.5,-80.34 113.5,-61.75 113.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="117,-46.1 113.5,-36.1 110,-46.1 117,-46.1"/>
<text text-anchor="middle" x="135" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="135" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- compute&#45;&gt;data -->
<g id="edge3" class="edge">
<title>compute&#45;&gt;data</title>
<path fill="none" stroke="#002b36" d="M113.5,-285.98C113.5,-272.34 113.5,-253.75 113.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="117,-238.1 113.5,-228.1 110,-238.1 117,-238.1"/>
<text text-anchor="middle" x="135" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="135" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
</g>
</svg>
