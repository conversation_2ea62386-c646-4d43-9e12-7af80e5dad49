{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Practical dependency tracking for Python function calls\n", "\n", "## tl;dr\n", "Tracking the code and data accessed by a (Python) function call is a broadly\n", "useful primitive, from drawing dependency graphs, to debugging and profiling, to\n", "cache invalidation. This post is a journey through the landscape\n", "of possible implementations, with a focus on solutions that are transparent,\n", "robust and applicable to practical production scenarios. A minimal\n", "viable implementation in <100 lines of code is included ([gist](https://gist.github.com/amakelov/c48d1bfb2eec75385dd5df2d81dcd759)); a practical\n", "implementation is part of\n", "[mandala](https://github.com/amakelov/mandala), a library for incremental\n", "computing and experiment management.\n", "\n", "![Dependency graph](dependency_graph.png)\n", "\n", "<em>Figure. Dependencies extracted from a call to the function <code>train_model</code>\n", "in module <code>__main__</code>: functions (blue), methods (purple) and globals (red) </em>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outline\n", "- [motivation](#motivation-efficient-and-reproducible-computational-experiments):\n", "  the use case I ran into, and [technical requirements](#technical-requirements) that came out of it\n", "- [proposed solution](#proposed-solution): a prototype in <100 lines of code you\n", "  can customize to your own use cases\n", "- [what doesn't work and why](#what-doesnt-work-and-why): alternative designs and why\n", "  I decided against them"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Motivation: efficient and reproducible computational experiments\n", "Function dependency information is useful for all sorts of stuff, from [drawing\n", "pretty call graphs](https://github.com/gak/pycallgraph) to\n", "[debugging](https://docs.python.org/3/library/pdb.html) and\n", "[profiling](https://github.com/joerick/pyinstrument) to [measuring test\n", "coverage](https://github.com/nedbat/coveragepy). Personally, I wanted to cache\n", "function calls and detect when a cached call is no longer valid because the code\n", "and/or data it depends on have changed. This means that, for each call, you must\n", "know the *exact* functions/methods it called and globals it accessed. \n", "\n", "Concretely, such a [memoization tool](https://en.wikipedia.org/wiki/Memoization)\n", "can save a lot of computer/programmer time in computational fields like [machine\n", "learning](https://en.wikipedia.org/wiki/Machine_learning) and [data\n", "science](https://en.wikipedia.org/wiki/Data_science). Projects there typically\n", "have many moving pieces, and each piece can change at any time. It's common for\n", "a change in one piece to affect only some steps of a project, and re-running\n", "everything from scratch takes too long: you want to do the \"new\" computations\n", "only.\n", "\n", "Manually keeping track of this is error-prone and distracts you from your actual\n", "project! There exist tools like [dvc](https://dvc.org/) that can sort of\n", "automate this, but they are generally more rigid - e.g., require you to break\n", "your code up into scripts instead of functions. Instead, I wanted something\n", "simpler to understand and add to existing code in e.g. your [<PERSON><PERSON><PERSON>\n", "notebook](https://jupyter.org/), so you can do your work in the most\n", "straightforward way with minimal boilerplate."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Technical requirements\n", "Deploying dependency tracking in a production ML/DS system poses more challenges\n", "than using it for e.g. debugging/profiling, because it's now part of all the\n", "computations you do! Unfortunately, I'm not aware of a tool that meets all the\n", "requirements of this use case:\n", "\n", "- **track the dependencies *actually* accessed by each call** (including global\n", "  variable accesses) as opposed to an over- or under-estimate\n", "- easily **limit the tracked dependencies** to user code\n", "  (library functions typically don't change, even over relatively long projects)\n", "- **report/abort when a dependency cannot be tracked**, e.g. when a function\n", "accesses a [closure](https://en.wikipedia.org/wiki/Closure_(computer_programming)) or a\n", "global variable that can't be hashed\n", "- be **robust** and **non-invasive** to the main computational process so that\n", "  your code behaves as it would without tracking\n", "- introduce **low performance overhead**, which is particularly important in\n", "  fast-feedback interactive settings, like exploratory computations in Jupyter notebooks.\n", "- ...and more (e.g. deal with concurrency/parallelism, which we won't get to here)\n", "\n", "As we'll see [later](#what-doesnt-work-and-why), take together, these\n", "requirements rule out several standard approaches: [static\n", "analyzers](#static-analysis) (which can over- and under-estimate dependencies),\n", "[Python's `sys.settrace`](#syssettrace) (which is too invasive and inefficient),\n", "and [profilers](#cprofile) (which are designed to provide aggregate statistics\n", "post-execution)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Proposed solution\n", "After tinkering with various\n", "[magical](https://en.wikipedia.org/wiki/Magic_(programming)) ways to gather this\n", "data using Python internals, I found out that none of them really fit all the\n", "requirements of my use case. What ended up working was something simple but\n", "ultimately more reliable and efficient:\n", "\n", "- decorate all the functions whose code you want to track. The decorator implements its own [call\n", "stack](https://en.wikipedia.org/wiki/Call_stack), separate from Python's, that\n", "tracks just these functions' calls.\n", "- the decorator also hooks into the `__globals__` of the function object (the\n", "dictionary of globals available to the function), and tracks every access to it.\n", "I learned this from [this blog post](https://www.benkuhn.net/deps/).\n", "\n", "The only downside is that you have to explicitly decorate the functions/classes\n", "you want to track (you could do this automatically with an [import\n", "hook](https://docs.python.org/3/reference/import.html#import-hooks), but that's\n", "perhaps too much magic). The full code + an example is in [this\n", "gist](https://gist.github.com/amakelov/c48d1bfb2eec75385dd5df2d81dcd759)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The decorator\n", "The `@track` decorator simply modifies a function `f` to emit an event to the\n", "global `Tracer` object (defined [below](#the-tracer)) right before and after it\n", "is called:\n", "```python\n", "from types import FunctionType\n", "from functools import wraps\n", "from typing import Optional\n", "\n", "class TracerState:\n", "    current: Optional['Tracer'] = None\n", "\n", "def track(f: FunctionType):\n", "\n", "    @wraps(f) # to make the wrapped function look like `f`\n", "    def wrapper(*args, **kwargs):\n", "        tracer = TracerState.current\n", "        if tracer is not None:\n", "            tracer.register_call(func=f) # put call to `f` on stack\n", "            result = f(*args, **kwargs)\n", "            tracer.register_return() # pop call to `f` from stack\n", "            return result\n", "        else:\n", "            return f(*args, **kwargs)\n", "\n", "    return wrapper\n", "```\n", "### The tracer\n", "Most importantly, the tracer keeps track of calls to decorated functions by\n", "[putting a call on the stack](https://en.wikipedia.org/wiki/Call_stack) right\n", "before a decorated function is called, and popping the top call when a decorated\n", "function returns. Using the call stack, you can derive all sorts of other useful\n", "information. For example, the implementation below uses the stack to build a [dynamic call\n", "graph](https://en.wikipedia.org/wiki/Call_graph) (represented as a list of edges\n", "for simplicity). It's implemented as a [context\n", "manager](https://docs.python.org/3/library/stdtypes.html#typecontextmanager)\n", "that only tracks calls that happen inside a `with` block:\n", "```python\n", "from typing import Callable\n", "\n", "class Tracer:\n", "    def __init__(self):\n", "        # call stack of (module name, qualified function/method name) tuples\n", "        self.stack = [] \n", "        # list of (caller module, caller qualname, callee module, callee\n", "        # qualname) tuples\n", "        self.graph = [] \n", "    \n", "    def register_call(self, func: Callable): \n", "        # Add a call to the stack and the graph\n", "        module_name, qual_name = func.__module__, func.__qualname__\n", "        self.stack.append((module_name, qual_name))\n", "        if len(self.stack) > 1:\n", "            caller_module, caller_qual_name = self.stack[-2]\n", "            self.graph.append((caller_module, caller_qual_name,\n", "                               module_name, qual_name))\n", "    \n", "    def register_return(self):\n", "        self.stack.pop()\n", "    \n", "    def __enter__(self):\n", "        TracerState.current = self\n", "        return self\n", "    \n", "    def __exit__(self, exc_type, exc_value, traceback):\n", "        TracerState.current = None\n", "```\n", "Note that we use the [qualified name](https://peps.python.org/pep-3155/) of a\n", "function, which contains all the nested class names in the case of methods.\n", "\n", "### A minimal example\n", "You can already use this as follows:\n", "```python\n", "In [1]: @track\n", "   ...: def f(x):\n", "   ...:     return x + 1\n", "   ...: \n", "\n", "In [2]: @track\n", "   ...: def g(x):\n", "   ...:     return f(x) + 1\n", "   ...: \n", "\n", "In [3]: with <PERSON><PERSON>() as t:\n", "   ...:     g(23)\n", "   ...: \n", "\n", "In [4]: t.graph\n", "Out[4]: [('__main__', 'g', '__main__', 'f')]\n", "```\n", "\n", "### Adding globals tracking\n", "When a function `f` is called, how does Python know how names in the code of `f`\n", "correspond to values in the program? You can read about this at length in\n", "[Python's documentation](https://docs.python.org/3/tutorial/classes.html#python-scopes-and-namespaces),\n", "but the gist of it is that the relevant scopes are looked up in the following\n", "order:\n", "\n", "- **local**: `func`'s own scope,\n", "- **enclosing**: any scopes of functions inside which `func` is defined,\n", "accessible through `func.__closure__`\n", "- **global**: the the namespace of the function's module, accessible through `func.__globals__`.\n", "- **builtin**: Python's imported-by-default objects\n", "\n", "It even has a \"catchy\" acronym: the LEGB rule. For now, we'll assume there's\n", "no enclosing scope. In this case, we are really only interested in accesses to\n", "`__globals__`. As it turns out, we can substitute a function's `__globals__` -\n", "which is a dictionary - with a modified object that behaves exactly the same but\n", "also tracks accesses. For this, we add a `register_global_access` method to\n", "`Tracer` (which adds globals to the graph as key-value pairs to disambiguate\n", "them from function calls), and define a simple subclass of `dict`:\n", "```python\n", "class Tracer:\n", "    ...\n", "\n", "    def register_global_access(self, key: str, value): # <- ADD THIS METHOD\n", "        assert len(self.stack) > 0\n", "        caller_module, caller_qual_name = self.stack[-1]\n", "        self.graph.append((caller_module, caller_qual_name, {key: value}))\n", "\n", "    ...\n", "\n", "from typing import Any\n", "\n", "class TrackedDict(dict):\n", "    def __init__(self, original: dict):\n", "        self.__original__ = original\n", "\n", "    def __getitem__(self, __key: str) -> Any:\n", "        value = self.__original__.__getitem__(__key)\n", "        if TracerState.current is not None:\n", "            tracer = TracerState.current\n", "            tracer.register_global_access(key=__key, value=value)\n", "        return value\n", "```\n", "Implementing the strategy is somewhat complicated by the fact that `__globals__`\n", "is a read-only attribute and can't be updated in-place. The below helper\n", "copies a function, keeping everything the same except for using a `TrackedDict`\n", "for the globals:\n", "```python\n", "import copy\n", "from functools import update_wrapper\n", "\n", "def make_tracked_copy(f: FunctionType) -> FunctionType:\n", "    result = FunctionType(\n", "        code=f.__code__,\n", "        globals=TrackedDict(f.__globals__),\n", "        name=f.__name__,\n", "        argdefs=f.__defaults__,\n", "        closure=f.__closure__,\n", "    )\n", "    result = update_wrapper(result, f)\n", "    result.__module__ = f.__module__\n", "    result.__kwdefaults__ = copy.deepcopy(f.__kwdefaults__)\n", "    result.__annotations__ = copy.deepcopy(f.__annotations__)\n", "    return result\n", "```\n", "Note that, even though we use\n", "[`update_wrapper`](https://docs.python.org/3/library/functools.html#functools.update_wrapper),\n", "some properties of `f` must be carried over manually to `f<PERSON>'s copy; maybe there\n", "are some others you need to copy as well depending on your use case. You can now\n", "modify the `track` decorator as\n", "```python\n", "def track(f: FunctionType):\n", "    f = make_tracked_copy(f) # add this line\n", "\n", "    @wraps(f)\n", "    ...\n", "```\n", "\n", "### A more interesting example\n", "Here's a more interesting example of all the stuff we covered so far in action:\n", "tracking global variables, functions, and even nested class methods:\n", "```python\n", "A = 23\n", "B = 42\n", "\n", "@track\n", "def f(x):\n", "    return x + A\n", "\n", "class C:\n", "    @track\n", "    def __init__(self, x):\n", "        self.x = x + B\n", "\n", "    @track\n", "    def m(self, y):\n", "        return self.x + y\n", "\n", "    class D:\n", "        @track\n", "        def __init__(self, x):\n", "            self.x = x + f(x)\n", "\n", "        @track\n", "        def m(self, y):\n", "            return y + A\n", "\n", "@track\n", "def g(x):\n", "    if x % 2 == 0:\n", "        return C(x).m(x)\n", "    else:\n", "        return C.D(x).m(x)\n", "```\n", "As expected, you get different results for the two branches of `g`:\n", "```python\n", "In [1]: with <PERSON><PERSON>() as t:\n", "   ...:     g(23)\n", "   ...: \n", "\n", "In [2]: t.graph\n", "Out[2]: \n", "[('__main__', 'g', {'C': __main__.C}),\n", " ('__main__', 'g', '__main__', 'C.D.__init__'),\n", " ('__main__', 'C.D.__init__', {'f': <function __main__.f(x)>}),\n", " ('__main__', 'C.D.__init__', '__main__', 'f'),\n", " ('__main__', 'f', {'A': 23}),\n", " ('__main__', 'g', '__main__', 'C.D.m'),\n", " ('__main__', 'C.D.m', {'A': 23})]\n", "\n", "In [3]: with <PERSON><PERSON>() as t:\n", "    ...:     g(42)\n", "    ...: \n", "\n", "In [4]: t.graph\n", "Out[4]: \n", "[('__main__', 'g', {'C': __main__.C}),\n", " ('__main__', 'g', '__main__', 'C.__init__'),\n", " ('__main__', 'C.__init__', {'B': 42}),\n", " ('__main__', 'g', '__main__', 'C.m')]\n", "```\n", "\n", "### Beyond the prototype\n", "The code so far already has all the key components of a\n", "solution. Even better, it's easily customizable: it's up to you to decide\n", "whether some calls or globals should be excluded, how to respond to changes in\n", "dependencies, etc. To make this scaffolding more robust and practical, you might\n", "want to add a few minor improvements. I found the following helpful:\n", "\n", "- **replace global variable values with content hashes**, because otherwise you\n", "  might end up tracking a lot of state that is not garbage-collected\n", "- **apply the decorator to entire classes** by decorating each of their methods\n", "  automatically. This saves you at least some of the manual work!\n", "- **filter out function/method/class accesses** when tracking globals accesses.\n", "  As you can see above, `<PERSON><PERSON><PERSON>.__init__` accesses the global variable `f`, but you\n", "  probably don't care about this most of the time.\n", "- **check for\n", "[closures](https://en.wikipedia.org/wiki/Closure_(computer_programming))** using\n", "the `__closure__` attribute of the function being called. Closures are more\n", "complex to track than code available at import time. To make life simpler, you\n", "may choose to detect closures at runtime and raise an error to disable them.\n", "- **make the decorator work well with other decorators**: Python decorators are\n", "great, but also a total anarchy. Anybody can use something like `lambda x: None`\n", "as a decorator! If it's in your power, you should put `@track` on the bottom of\n", "decorator stacks (i.e. directly over the function definition). Otherwise, cross\n", "your fingers that whoever implemented the decorators in your code was nice and\n", "exposed a `__wrapped__` attribute. Take a look at [the Python\n", "docs](https://docs.python.org/3/library/functools.html#functools.update_wrapper). \n", "- **use [import\n", "hooks](https://docs.python.org/3/reference/import.html#import-hooks)** to\n", "automatically decorate your code at import time, if you dare.\n", "\n", "## What doesn't work, and why\n", "What follows is a tour through some Python tools/internals that can address\n", "parts of the problem, but ultimately fail to satisfy all requirements:\n", "\n", "- [`sys.settrace`](#syssettrace) is a solid alternative, but\n", "introduces [too much unavoidable overhead](#syssettraces-unavoidable-overhead)\n", "in practical interactive scenarios, and can't track dynamic accesses to the globals.\n", "- [profilers](#cprofile) like [cProfile](https://docs.python.org/3.5/library/profile.html#module-cProfile) introduce less overhead than `sys.settrace`. However,\n", "  they don't track per-call dependencies, don't give you runtime control over what\n", "  the program does (so you can't e.g. react to a dependency that you fundamentally\n", "  can't track), and make it harder to extract full dependency information.\n", "- [static analysis](#static-analysis) can discover more/fewer dependencies than\n", "  the ground truth, and is altogether messier to implement.\n", "\n", "\n", "### `sys.settrace`\n", "Python is a famously (notoriously?) hackable language: it lets you hook into a\n", "lot of the internal machinery of the interpreter itself. One such piece of magic\n", "is [`sys.settrace`](https://docs.python.org/3/library/sys.html#sys.settrace),\n", "which allows you to install a hook that gets called for each of the main events\n", "of the interpreter: function calls/returns, and even executing a single line of\n", "code in a function (for example, this is how\n", "[coverage](https://github.com/nedbat/coveragepy) can be so fine-grained).\n", "\n", "Using `sys.settrace`, we can obtain something very similar to the [solution\n", "developed above](#proposed-solution), but without the need to explicitly\n", "decorate your code. Here is a minimal example of a stateful context manager\n", "using `settrace` to maintain a call stack of the functions that get called and\n", "the modules they originate from:\n", "```python\n", "import sys, types \n", "\n", "class Tracer:\n", "    def __init__(self):\n", "        # stack of (module name, function name) tuples\n", "        self.call_stack = []\n", "    \n", "    def __enter__(self):\n", "        def tracer(frame: types.FrameType, event: str, arg):\n", "            # the name of the function being executed\n", "            func_name = frame.f_code.co_name\n", "            # the name of the module in which the function is defined\n", "            module_name = frame.f_globals.get(\"__name__\")\n", "            if event == 'call': # function call\n", "                self.call_stack.append((module_name, func_name))\n", "                print(f\"Calling {module_name}.{func_name}\")\n", "            elif event == 'return': # function return\n", "                ret_module, ret_func = self.call_stack.pop()\n", "                print(f\"Returning from {ret_module}.{ret_func}\")\n", "            else:\n", "                pass\n", "            return tracer\n", "        \n", "        sys.settrace(tracer) # enable tracing\n", "        return self\n", "    \n", "    def __exit__(self, exc_type, exc_value, traceback):\n", "        sys.settrace(None) # disable tracing\n", "```\n", "The `frame` object is what Python puts on its [call\n", "stack](https://en.wikipedia.org/wiki/Call_stack), and contains data about the\n", "function being called, its\n", "[bytecode](https://docs.python.org/3/glossary.html#term-bytecode), who called\n", "it, etc. You can use this context manager as follows:\n", "```python\n", "### in funcs.py\n", "def f(x):\n", "    return x + 1\n", "\n", "### in IPython session\n", "In [1]: from funcs import *\n", "\n", "In [2]: def g(x):\n", "   ...:     return f(x) + 1\n", "   ...: \n", "\n", "In [3]: with <PERSON><PERSON>():\n", "   ...:     g(23)\n", "   ...: \n", "Calling __main__.g\n", "Calling funcs.f\n", "Returning from funcs.f\n", "Returning from __main__.g\n", "Calling funcs.__exit__ # you'd have to manually remove this one\n", "```\n", "This can be extended with more features [much like the decorator-based\n", "tracer](#beyond-the-prototype). There are some `settrace`-specific problems\n", "you have to deal with though:\n", "\n", "- **limit the dependencies to user code** by looking at the module in which the\n", "function is defined, getting its path, and deciding if it's a user's file or\n", "not.\n", "- **get the qualified name**: this is frustratingly not readily available as\n", "  part of the `frame` object. You need some hacks to extract it:\n", "```python\n", "def get_qualname_from_frame(frame: types.FrameType) -> str:\n", "    arg_names = frame.f_code.co_varnames[: frame.f_code.co_argcount]\n", "    if len(arg_names) > 0 and arg_names[0] == 'self':\n", "        cls_candidate = frame.f_locals['self'].__class__\n", "        method_candidate = cls_candidate.__dict__.get(frame.f_code.co_name)\n", "        if method_candidate is not None and method_candidate.__code__ is frame.f_code:\n", "            return method_candidate.__qualname__\n", "    return frame.f_code.co_name\n", "```\n", "- **skip over non-function frames**: the interpreter assigns comprehensions,\n", "  generators and `lambda`-calls their own frames. You have to check for this\n", "  using `frame.f_code.co_name`, and assign their dependencies to the closest \"actual\"\n", "  function call on the stack.\n", "\n", "\n", "#### `sys.settrace`'s unavoidable overhead\n", "A good reason to avoid `settrace` in production code is that it's [too magical\n", "for its own good](https://stackoverflow.com/a/1693108/6538618). However, the\n", "real deal-breaker for my use case was the impossible-to-avoid factor by which it\n", "slows down some kinds of code.\n", "\n", "The crux is that the trace function is inherently called for each `call` event,\n", "including calls to library functions that you don't care about tracking, because\n", "they typically don't change over the course of a months-long project. For\n", "relatively fast function calls (on the order of seconds), you may get an\n", "**order-of-magnitude slowdown** if the call involves many sub-calls. **This is\n", "unacceptable for interactive workflows**!\n", "\n", "You might think you could fix that with a bit of manual work by excluding such\n", "library code from the tracing. Indeed, you can define a simple context manager\n", "that temporarily suspends the current trace:\n", "```python\n", "class Suspend:\n", "    def __init__(self):\n", "        self.suspended_trace = None\n", "\n", "    def __enter__(self) -> \"Suspend\":\n", "        if sys.gettrace() is not None:\n", "            self.suspended_trace = sys.gettrace()\n", "            sys.settrace(None)\n", "        return self\n", "\n", "    def __exit__(self, *exc_info):\n", "        if self.suspended_trace is not None:\n", "            sys.settrace(self.suspended_trace)\n", "            self.suspended_trace = None\n", "```\n", "Then you can use it like this:\n", "```python\n", "def my_tracked_func(...):\n", "    ...\n", "    a = another_tracked_func()\n", "    with Suspend():\n", "        b = some_library_calls_you_dont_want_to_track(a)\n", "        ...\n", "    ...\n", "```\n", "However, there are cases when you simply can't do that!  To give a concrete\n", "example, I was going though the code for\n", "[this](https://google-research.github.io/self-organising-systems/2022/diff-fsm/)\n", "blog post, and I ran into an interesting scenario. A user-defined function `f`\n", "was passed into\n", "[`jax.lax.scan`](https://jax.readthedocs.io/en/latest/_autosummary/jax.lax.scan.html),\n", "as a way to speed up certain applications of `f`:\n", "```python\n", "def run_fsm(fsm: FSM, inputs):\n", "  def f(s, x):\n", "    y  = jp.einsum('x,s,xsy->y', x, s, fsm.R)\n", "    s1 = jp.einsum('x,s,xst->t', x, s, fsm.T)\n", "    return s1, (y, s1)\n", "  _, (outputs, states) = jax.lax.scan(f, fsm.s0, inputs) # THIS IS BAD\n", "  return outputs, jp.vstack([fsm.s0, states]\n", "```\n", "Because you're passing your function to the library and it can call it however\n", "it likes, you **lose the ability to separate the executions of your code from\n", "those of library code**. The `Suspend` trick can't work: you're forced to trace\n", "all the internal calls the library makes alongside the calls to your code.\n", "\n", "\n", "### `cProfile`\n", "A [profiler](https://en.wikipedia.org/wiki/Profiling_(computer_programming)) is\n", "a dynamic program analysis tool typically used to pinpoint performance\n", "bottlenecks in code. There are two main kinds: \n", "\n", "- [statistical\n", "profilers](https://en.wikipedia.org/wiki/Profiling_(computer_programming)#Statistical_profilers)\n", "sample a program's state (e.g. call stack, memory allocation) at regular\n", "intervals. This reduces overhead, while still detecting functions where the\n", "program spends a lot of time.\n", "- [deterministic\n", "  profilers](https://docs.python.org/3.5/library/profile.html#what-is-deterministic-profiling)\n", "  by contrast record *every* function call that happens in the program, and\n", "  accordingly suffer higher overhead.\n", "\n", "In dependency tracking, failing to notice even a single dependency that is fast\n", "and gets called rarely can have disastrous results, so statistical profilers are\n", "not really an option: you need a deterministic one. Since Python's interpreter\n", "adds so much overhead anyway, Python's built-in (deterministic) profilers\n", "don't introduce *that* much over-overhead. Of the two, `cProfile` is faster. Here's the minimal\n", "implementation of a tracer based on profiling:\n", "```python\n", "import cProfile\n", "import pstats\n", "\n", "class ProfilingTracer:\n", "    def __init__(self):\n", "        self._profiler = cProfile.Profile()\n", "\n", "    def __enter__(self):\n", "        self._profiler.enable()\n", "        return self\n", "\n", "    def __exit__(self, exc_type, exc_value, traceback):\n", "        self._profiler.disable()\n", "        stats = pstats.Stats(self._profiler)\n", "        stats.print_callees()\n", "```\n", "Conveniently, the output of the profiler has a method `print_callees` that\n", "prints all the functions that were called by each given function in the profiled\n", "block of code. We can run it on the [code from\n", "before](#a-more-interesting-example) to get this (simplified for readability) output:\n", "```python\n", "Function                                  called...\n", "                ncalls  tottime  cumtime\n", "(__init__)  ->       1    0.000    0.000  (f)\n", "(m)         -> \n", "(g)         ->       1    0.000    0.000  (__init__)\n", "                     1    0.000    0.000  (m)\n", "                     1    0.000    0.000  (__init__)\n", "                     1    0.000    0.000  (m)\n", "(m)         -> \n", "(f)         -> \n", "(__init__)  -> \n", "```\n", "The downsides of this approach become clear:\n", "\n", "- **data is aggregated across all calls to a given function**: in the tracked\n", "  code, we call `g` twice, and the two calls have different dependencies. But in\n", "  the final report of the profiler, the two calls are grouped together. So **we\n", "  have no way of tracking per-call dependencies based on this data**!\n", "- **no qualified names and globals**: you would have to do some extra work (e.g. looking at\n", "  line numbers, which *do* appear in the profiler report) to disambiguate the\n", "  classes methods come from. And as with `settrace`, you have no way to detect\n", "  globals accesses.\n", "- **no runtime control over dependencies**: the profiler report is an\n", "  after-the-fact summary of what happened; you don't have the option to abort if\n", "  you detect a bad dependency.\n", "\n", "Of course, it's not surprising that profilers have a hard time tracking\n", "fine-grained dependencies: they weren't designed for that!\n", "\n", "\n", "### Static analysis\n", "Finally, [static analysis](https://en.wikipedia.org/wiki/Static_program_analysis) is a\n", "collection of methods for deducing program properties from source code alone,\n", "i.e. *without running the program*. For example,\n", "[code2flow](https://github.com/scottrogowski/code2flow) is a static call graph\n", "builder for Python. In Python, static analyses typically proceed from the [abstract syntax\n", "tree](https://docs.python.org/3/library/ast.html) and/or the\n", "[bytecode](https://docs.python.org/3/library/dis.html).\n", "\n", "This approach doesn't interact with your running program at\n", "all, which is great for performance and generally letting your code work the way\n", "it's supposed to. Unfortunately, it's fundamentally flawed for many other reasons:\n", "\n", "- **false positives**: suppose `f`'s source code contains calls to `g` and `h`,\n", "but some calls to `f` use only `g`, and others only `h`.  A static analysis\n", "would miss that and declare `g` and `h` as dependencies of all calls.\n", "- **false negatives**: your function can call another function in all sorts of\n", "  weird ways that cannot be inferred from the syntax tree and/or bytecode alone. For an extreme\n", "  example, consider something like `a = eval('my_' + 'function(42)')`. Because\n", "  of variations of the [halting problem](https://en.wikipedia.org/wiki/Hal<PERSON>_problem), it's [both difficult\n", "  and impossible](https://youtu.be/Z3515_x8R9c?t=136) for a static analysis to\n", "  determine what a function will do at runtime.\n", "- **high implementation complexity**: even if you adopt a best-effort approach,\n", "  you have to do a lot of work to figure out the precise function called by\n", "  an expression like `a().b().c()`.\n", "\n", "Overall, using static analysis is not worth the hassle given the ultimately\n", "coarse-grained information it can provide."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}