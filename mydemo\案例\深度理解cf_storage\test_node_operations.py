"""
测试节点操作修复的简化版本
"""

import os
import sys
import tempfile

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from mandala1.imports import Storage, op


def test_node_operations():
    """测试节点操作的修复"""
    print("🧪 测试节点操作修复")
    print("=" * 50)
    
    # 创建临时存储
    temp_dir = tempfile.mkdtemp()
    storage = Storage(db_path=os.path.join(temp_dir, "test.db"))
    
    # 定义基础计算函数
    @op
    def load_data(size: int = 10):
        """加载数据"""
        print(f"  📂 加载数据: size={size}")
        return list(range(size))
    
    @op
    def preprocess(data: list):
        """预处理数据"""
        print(f"  🔄 预处理数据: {len(data)} 个元素")
        return [x * 2 for x in data]
    
    @op
    def compute_stats(data: list):
        """计算统计"""
        print(f"  📊 计算统计: {len(data)} 个元素")
        return {
            'count': len(data),
            'sum': sum(data),
            'avg': sum(data) / len(data) if data else 0
        }
    
    # 执行基础计算
    print("\n1️⃣ 执行基础计算:")
    with storage:
        raw_data = load_data(10)
        processed_data = preprocess(storage.unwrap(raw_data))
        stats = compute_stats(storage.unwrap(processed_data))
    
    # 创建原始CF
    original_cf = storage.cf(stats).expand_back(recursive=True)
    print(f"  📊 原始CF节点数: {len(original_cf.nodes)}")
    print(f"  📦 变量节点: {list(original_cf.vnames)}")
    print(f"  ⚙️  函数节点: {list(original_cf.fnames)}")
    
    # 定义新的计算函数
    @op
    def new_computation(data: list, multiplier: float = 1.5):
        """新增的计算函数"""
        print(f"  🆕 执行新计算: 数据长度={len(data)}, 乘数={multiplier}")
        return [x * multiplier for x in data]
    
    @op  
    def derived_analysis(original_stats: dict, new_data: list):
        """基于新数据的派生分析"""
        print(f"  🔄 执行派生分析")
        return {
            'original_count': original_stats.get('count', 0),
            'new_count': len(new_data),
            'ratio': len(new_data) / max(original_stats.get('count', 1), 1)
        }
    
    # 测试增加节点操作
    print("\n2️⃣ 测试增加节点操作:")
    with storage:
        # 执行新计算
        new_result = new_computation(storage.unwrap(processed_data), 1.5)
        derived_result = derived_analysis(storage.unwrap(stats), 
                                        storage.unwrap(new_result))
    
    # 正确的增加节点方法：合并原始CF和新计算的CF
    new_computation_cf = storage.cf(derived_result).expand_back(recursive=True)
    cf_with_new_nodes = original_cf | new_computation_cf  # 使用并集操作合并
    
    print(f"  📊 原始CF节点数: {len(original_cf.nodes)}")
    print(f"  📊 新计算CF节点数: {len(new_computation_cf.nodes)}")
    print(f"  📊 合并后CF节点数: {len(cf_with_new_nodes.nodes)}")
    print(f"  ➕ 实际新增节点数: {len(cf_with_new_nodes.nodes) - len(original_cf.nodes)}")
    
    # 显示新增的节点
    original_nodes = set(original_cf.nodes)
    new_nodes = set(cf_with_new_nodes.nodes) - original_nodes
    if new_nodes:
        print(f"  🆕 新增的节点: {list(new_nodes)}")
    else:
        print("  ℹ️  没有新增节点（可能存在重叠）")
    
    # 测试修改节点操作
    print("\n3️⃣ 测试修改节点操作:")
    if original_cf.vnames:
        target_node = list(original_cf.vnames)[0]
        print(f"  🎯 目标节点: {target_node}")
        
        # 步骤1：删除节点
        cf_without_target = original_cf.drop_node(target_node, inplace=False)
        print(f"    🗑️  删除后节点数: {len(cf_without_target.nodes)}")
        
        # 步骤2：正确的修改方法 - 保持原有节点顺序并添加新节点
        cf_modified = cf_without_target | new_computation_cf
        print(f"    ➕ 合并后节点数: {len(cf_modified.nodes)}")
        
        # 验证节点保留情况
        original_nodes_except_target = set(original_cf.nodes) - {target_node}
        modified_nodes = set(cf_modified.nodes)
        preserved_nodes = original_nodes_except_target & modified_nodes
        new_added_nodes = modified_nodes - original_nodes_except_target
        
        print(f"    ✅ 保留原节点数: {len(preserved_nodes)}")
        print(f"    🆕 新增节点数: {len(new_added_nodes)}")
        if new_added_nodes:
            print(f"    🆕 新增节点: {list(new_added_nodes)}")
    
    # 验证结果
    print("\n4️⃣ 验证结果:")
    print(f"  ✅ 增加节点操作: {'成功' if len(cf_with_new_nodes.nodes) >= len(original_cf.nodes) else '失败'}")
    print(f"  ✅ 修改节点操作: {'成功' if 'cf_modified' in locals() and len(cf_modified.nodes) > 0 else '失败'}")
    
    # 清理
    import shutil
    try:
        shutil.rmtree(temp_dir)
        print(f"  🧹 清理临时目录: {temp_dir}")
    except Exception as e:
        print(f"  ⚠️  清理失败: {e}")
    
    print("\n🎉 节点操作测试完成！")


if __name__ == "__main__":
    test_node_operations()
