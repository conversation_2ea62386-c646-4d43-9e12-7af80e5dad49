<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   width="210mm"
   height="192mm"
   viewBox="0 0 210 192"
   version="1.1"
   id="svg1"
   xml:space="preserve"
   inkscape:version="1.3.1 (9b9bdc1480, 2023-11-25, custom)"
   sodipodi:docname="output.svg"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"><sodipodi:namedview
     id="namedview1"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:document-units="mm"
     inkscape:zoom="1.0200267"
     inkscape:cx="358.81415"
     inkscape:cy="433.32201"
     inkscape:window-width="2490"
     inkscape:window-height="1369"
     inkscape:window-x="1990"
     inkscape:window-y="34"
     inkscape:window-maximized="1"
     inkscape:current-layer="layer1"
     showguides="true" /><defs
     id="defs1"><rect
       x="491.37043"
       y="653.11679"
       width="283.37093"
       height="69.058996"
       id="rect87" /><rect
       x="128.53692"
       y="555.41444"
       width="352.74574"
       height="71.102723"
       id="rect86" /><rect
       x="53.037209"
       y="255.07188"
       width="322.81968"
       height="42.465774"
       id="rect85" /><g
       class="clips"
       id="g1" /><g
       class="gradients"
       id="g2" /><g
       class="patterns"
       id="g3" /><g
       class="clips"
       id="g78" /><clipPath
       class="scroll-area-clip"
       id="clip1c505e_scrollAreaBottomClip_165f8ftrue"><rect
         class="scroll-area-clip-rect"
         x="-45"
         y="-5"
         fill="none"
         width="630"
         height="325"
         id="rect75" /></clipPath><clipPath
       class="column-boundary-clippath"
       id="clip1c505e_columnBoundaryClippath_165f8ftrue_0"><rect
         class="column-boundary-rect"
         fill="none"
         width="182"
         height="327"
         x="-1"
         y="-1"
         id="rect34" /></clipPath><clipPath
       class="column-boundary-clippath"
       id="clip1c505e_columnBoundaryClippath_165f8ftrue_1"><rect
         class="column-boundary-rect"
         fill="none"
         width="182"
         height="327"
         x="-1"
         y="-1"
         id="rect54" /></clipPath><clipPath
       class="column-boundary-clippath"
       id="clip1c505e_columnBoundaryClippath_165f8ftrue_2"><rect
         class="column-boundary-rect"
         fill="none"
         width="182"
         height="327"
         x="-1"
         y="-1"
         id="rect74" /></clipPath><g
       class="clips"
       id="g1-9" /><g
       class="gradients"
       id="g2-3" /><g
       class="patterns"
       id="g3-6" /><g
       class="clips"
       id="g78-2" /><g
       class="clips"
       id="g1-92" /><g
       class="gradients"
       id="g2-2" /><g
       class="patterns"
       id="g3-8" /><g
       class="clips"
       id="g78-1" /><g
       class="clips"
       id="g1-94" /><g
       class="gradients"
       id="g2-7" /><g
       class="patterns"
       id="g3-84" /><g
       class="clips"
       id="g78-20" /><g
       class="clips"
       id="g1-2" /><g
       class="gradients"
       id="g2-72" /><g
       class="patterns"
       id="g3-2" /><g
       class="clips"
       id="g78-4" /><rect
       x="53.037209"
       y="255.07188"
       width="277.13666"
       height="36.0536"
       id="rect85-3" /></defs><g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"><g
       id="g84"
       transform="matrix(0.32044686,0,0,0.32044686,10.354535,101.16822)"><rect
         x="0"
         y="0"
         width="400"
         height="132"
         style="fill:#ffffff;fill-opacity:1"
         id="rect1" /><g
         class="bglayer"
         id="g4" /><g
         class="layer-below"
         id="g7"><g
           class="imagelayer"
           id="g5" /><g
           class="shapelayer"
           id="g6" /></g><g
         class="cartesianlayer"
         id="g8" /><g
         class="polarlayer"
         id="g9" /><g
         class="smithlayer"
         id="g10" /><g
         class="ternarylayer"
         id="g11" /><g
         class="geolayer"
         id="g12" /><g
         class="funnelarealayer"
         id="g13" /><g
         class="pielayer"
         id="g14" /><g
         class="iciclelayer"
         id="g15" /><g
         class="treemaplayer"
         id="g16" /><g
         class="sunburstlayer"
         id="g17" /><g
         class="glimages"
         id="g18" /><g
         class="table"
         overflow="visible"
         width="400"
         height="132"
         style="overflow:visible"
         id="g77"><g
           class="table-control-view"
           clip-path="url(#clip4bba59_scrollAreaBottomClip_f1a6e0true)"
           id="g76"><rect
             class="scroll-background"
             fill="none"
             width="400"
             height="132"
             id="rect18"
             x="0"
             y="0" /><g
             class="y-column"
             clip-path="url(#clip4bba59_columnBoundaryClippath_f1a6e0true_0)"
             id="g35"><g
               class="column-block"
               id="cells1"><g
                 class="column-cells"
                 id="g29"><g
                   class="column-cell"
                   transform="translate(0,28)"
                   id="g20"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect19"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g19"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text19-6">0</text></g></g><g
                   class="column-cell"
                   transform="translate(0,48)"
                   id="g22"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect20"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g21"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text20-1">1</text></g></g><g
                   class="column-cell"
                   transform="translate(0,68)"
                   id="g24"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect22"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g23"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text22-0">2</text></g></g><g
                   class="column-cell"
                   transform="translate(0,88)"
                   id="g26"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect24"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g25"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text24">3</text></g></g><g
                   class="column-cell"
                   transform="translate(0,108)"
                   id="g28"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect26"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g27"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text26">4</text></g></g></g></g><g
               class="column-block"
               id="cells2"><g
                 class="column-cells"
                 id="g30" /></g><g
               class="column-block"
               id="header"><g
                 class="column-cells"
                 id="g33"><g
                   class="column-cell"
                   id="g32"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="28"
                     style="fill:#add8e6;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect30"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g31"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:14px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text30">x</text></g></g></g></g><g
               class="column-boundary"
               id="g34" /><clipPath
               class="column-boundary-clippath"
               id="clip4bba59_columnBoundaryClippath_f1a6e0true_0"><rect
                 class="column-boundary-rect"
                 fill="none"
                 width="125.07692"
                 height="139"
                 x="-1"
                 y="-1"
                 id="rect34-6" /></clipPath></g><g
             class="y-column"
             transform="translate(123.07692)"
             clip-path="url(#clip4bba59_columnBoundaryClippath_f1a6e0true_1)"
             id="g55"><g
               class="column-block"
               id="g47"><g
                 class="column-cells"
                 id="g46"><g
                   class="column-cell"
                   transform="translate(0,28)"
                   id="g37"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect35"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g36"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text35">1</text></g></g><g
                   class="column-cell"
                   transform="translate(0,48)"
                   id="g39"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect37"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g38"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text37">2</text></g></g><g
                   class="column-cell"
                   transform="translate(0,68)"
                   id="g41"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect39"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g40"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text39">3</text></g></g><g
                   class="column-cell"
                   transform="translate(0,88)"
                   id="g43"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect41"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g42"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text41">4</text></g></g><g
                   class="column-cell"
                   transform="translate(0,108)"
                   id="g45"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect43"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g44"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text43">5</text></g></g></g></g><g
               class="column-block"
               id="g49"><g
                 class="column-cells"
                 id="g48" /></g><g
               class="column-block"
               id="g53"><g
                 class="column-cells"
                 id="g52"><g
                   class="column-cell"
                   id="g51"><rect
                     class="cell-rect"
                     width="123.07692"
                     stroke-width="1"
                     height="28"
                     style="fill:#add8e6;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect49"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(61.538462,8)"
                     text-anchor="middle"
                     id="g50"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:14px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text49">y</text></g></g></g></g><g
               class="column-boundary"
               id="g54" /><clipPath
               class="column-boundary-clippath"
               id="clip4bba59_columnBoundaryClippath_f1a6e0true_1"><rect
                 class="column-boundary-rect"
                 fill="none"
                 width="125.07692"
                 height="139"
                 x="-1"
                 y="-1"
                 id="rect54-1" /></clipPath></g><g
             class="y-column"
             transform="translate(246.15385)"
             clip-path="url(#clip4bba59_columnBoundaryClippath_f1a6e0true_2)"
             id="g75"><g
               class="column-block"
               id="g67"><g
                 class="column-cells"
                 id="g66"><g
                   class="column-cell"
                   transform="translate(0,28)"
                   id="g57"><rect
                     class="cell-rect"
                     width="153.84616"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect55"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(76.923077,8)"
                     text-anchor="middle"
                     id="g56"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text55">1</text></g></g><g
                   class="column-cell"
                   transform="translate(0,48)"
                   id="g59"><rect
                     class="cell-rect"
                     width="153.84616"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect57"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(76.923077,8)"
                     text-anchor="middle"
                     id="g58"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text57">null</text></g></g><g
                   class="column-cell"
                   transform="translate(0,68)"
                   id="g61"><rect
                     class="cell-rect"
                     width="153.84616"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect59"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(76.923077,8)"
                     text-anchor="middle"
                     id="g60"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text59">5</text></g></g><g
                   class="column-cell"
                   transform="translate(0,88)"
                   id="g63"><rect
                     class="cell-rect"
                     width="153.84616"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect61"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(76.923077,8)"
                     text-anchor="middle"
                     id="g62"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text61">null</text></g></g><g
                   class="column-cell"
                   transform="translate(0,108)"
                   id="g65"><rect
                     class="cell-rect"
                     width="153.84616"
                     stroke-width="1"
                     height="20"
                     style="fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect63"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(76.923077,8)"
                     text-anchor="middle"
                     id="g64"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:12px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text63">9</text></g></g></g></g><g
               class="column-block"
               id="g69"><g
                 class="column-cells"
                 id="g68" /></g><g
               class="column-block"
               id="g73"><g
                 class="column-cells"
                 id="g72"><g
                   class="column-cell"
                   id="g71"><rect
                     class="cell-rect"
                     width="153.84616"
                     stroke-width="1"
                     height="28"
                     style="fill:#add8e6;fill-opacity:1;stroke:#ffffff;stroke-opacity:1"
                     id="rect69"
                     x="0"
                     y="0" /><g
                     class="cell-text-holder"
                     transform="translate(76.923077,8)"
                     text-anchor="middle"
                     id="g70"><text
                       class="cell-text"
                       dy="0.75em"
                       style="font-size:14px;font-family:'Open Sans', verdana, arial, sans-serif;fill:#2a3f5f;fill-opacity:1"
                       id="text69">z</text></g></g></g></g><g
               class="column-boundary"
               id="g74" /><clipPath
               class="column-boundary-clippath"
               id="clip4bba59_columnBoundaryClippath_f1a6e0true_2"><rect
                 class="column-boundary-rect"
                 fill="none"
                 width="155.84616"
                 height="139"
                 x="-1"
                 y="-1"
                 id="rect74-5" /></clipPath></g><clipPath
             class="scroll-area-clip"
             id="clip4bba59_scrollAreaBottomClip_f1a6e0true"><rect
               class="scroll-area-clip-rect"
               x="-45"
               y="-5"
               fill="none"
               width="490"
               height="137"
               id="rect75-9" /></clipPath></g></g><g
         class="layer-above"
         id="g81"><g
           class="imagelayer"
           id="g79" /><g
           class="shapelayer"
           id="g80" /></g><g
         class="infolayer"
         id="g83"><g
           class="g-gtitle"
           id="g82" /></g></g><text
       xml:space="preserve"
       style="font-size:3.175px;fill:#800000;stroke-width:0.264583"
       x="19.44199"
       y="71.113205"
       id="text84"><tspan
         sodipodi:role="line"
         id="tspan84"
         style="stroke-width:0.264583"
         x="19.44199"
         y="71.113205"></tspan></text><text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,16.537355,-1.3843543)"
       id="text85"
       style="white-space:pre;shape-inside:url(#rect85);display:inline;fill:#1a1a1a"><tspan
         x="53.037109"
         y="276.30664"
         id="tspan55"><tspan
           style="font-size:24px;font-family:Sans;-inkscape-font-specification:'Sans, Normal';fill:#666666"
           id="tspan54">(a) Computational code</tspan></tspan></text><text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,-3.038504,8.7690364)"
       id="text86"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:24px;font-family:Sans;-inkscape-font-specification:'Sans, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;white-space:pre;shape-inside:url(#rect86);display:inline;fill:#666666"><tspan
         x="128.53711"
         y="576.64844"
         id="tspan56">(c) Dataframe extracted </tspan><tspan
         x="128.53711"
         y="606.64844"
         id="tspan57">from the computation frame</tspan></text><text
       xml:space="preserve"
       transform="matrix(0.26458333,0,0,0.26458333,9.1584584,-1.1265295)"
       id="text87"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:24px;font-family:Sans;-inkscape-font-specification:'Sans, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;white-space:pre;shape-inside:url(#rect87);display:inline;fill:#666666"><tspan
         x="491.37109"
         y="674.35156"
         id="tspan58">(b) Depiction of </tspan><tspan
         x="491.37109"
         y="704.35156"
         id="tspan59">computation frame</tspan></text><text
       xml:space="preserve"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:6.35px;font-family:Sans;-inkscape-font-specification:'Sans, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;fill:#666666;stroke-width:0.264583"
       x="77.71888"
       y="159.59608"
       id="text88"><tspan
         sodipodi:role="line"
         id="tspan88"
         style="stroke-width:0.264583"
         x="77.71888"
         y="159.59608"> </tspan></text></g><g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Layer 2"><g
       id="graph0"
       class="graph"
       transform="matrix(0.38587126,0,0,0.38587126,139.5357,167.78159)"><title
         id="title1">G</title><polygon
         fill="#ffffff"
         stroke="transparent"
         points="-4,-424 162.5,-424 162.5,4 -4,4 "
         id="polygon1" /><!-- z --><g
         id="node1"
         class="node"><title
           id="title2">z</title><path
           fill="none"
           stroke="#268bd2"
           d="m 87.5,-36 c 0,0 -70,0 -70,0 -6,0 -12,6 -12,12 0,0 0,12 0,12 0,6 6,12 12,12 0,0 70,0 70,0 6,0 12,-6 12,-12 0,0 0,-12 0,-12 0,-6 -6,-12 -12,-12"
           id="path2" /><text
           text-anchor="start"
           x="49"
           y="-20.4"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-weight="bold"
           font-size="12px"
           fill="#002b36"
           id="text2">z</text><text
           text-anchor="start"
           x="13.5"
           y="-10"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#268bd2"
           id="text3">3 values (3 sinks)</text></g><!-- x --><g
         id="node2"
         class="node"><title
           id="title3">x</title><path
           fill="none"
           stroke="#268bd2"
           d="m 93,-420 c 0,0 -81,0 -81,0 -6,0 -12,6 -12,12 0,0 0,12 0,12 0,6 6,12 12,12 0,0 81,0 81,0 6,0 12,-6 12,-12 0,0 0,-12 0,-12 0,-6 -6,-12 -12,-12"
           id="path3" /><text
           text-anchor="start"
           x="49"
           y="-404.39999"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-weight="bold"
           font-size="12px"
           fill="#002b36"
           id="text4">x</text><text
           text-anchor="start"
           x="8"
           y="-394"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#268bd2"
           id="text5">5 values (5 sources)</text></g><!-- add --><g
         id="node4"
         class="node"><title
           id="title5">add</title><path
           fill="none"
           stroke="#dc322f"
           d="m 68.5,-134 c 0,0 -32,0 -32,0 -6,0 -12,6 -12,12 0,0 0,16 0,16 0,6 6,12 12,12 0,0 32,0 32,0 6,0 12,-6 12,-12 0,0 0,-16 0,-16 0,-6 -6,-12 -12,-12"
           id="path5" /><text
           text-anchor="start"
           x="41.5"
           y="-121.4"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-weight="bold"
           font-size="12px"
           fill="#002b36"
           id="text6">add</text><text
           text-anchor="start"
           x="32.5"
           y="-111"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text7">@op:add</text><text
           text-anchor="start"
           x="38"
           y="-101"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#dc322f"
           id="text8">3 calls</text></g><!-- x&#45;&gt;add --><g
         id="edge2"
         class="edge"><title
           id="title8">x-&gt;add</title><path
           fill="none"
           stroke="#002b36"
           d="m 52.5,-383.97 c 0,47.21 0,180.15 0,239.6"
           id="path8" /><polygon
           fill="#002b36"
           stroke="#002b36"
           points="49,-144.16 56,-144.16 52.5,-134.16 "
           id="polygon8" /><text
           text-anchor="middle"
           x="74"
           y="-260"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text9">x</text><text
           text-anchor="middle"
           x="74"
           y="-249"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text10">(3 values)</text></g><!-- increment --><g
         id="node5"
         class="node"><title
           id="title10">increment</title><path
           fill="none"
           stroke="#dc322f"
           d="m 138,-326 c 0,0 -59,0 -59,0 -6,0 -12,6 -12,12 0,0 0,16 0,16 0,6 6,12 12,12 0,0 59,0 59,0 6,0 12,-6 12,-12 0,0 0,-16 0,-16 0,-6 -6,-12 -12,-12"
           id="path10" /><text
           text-anchor="start"
           x="79.5"
           y="-313.39999"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-weight="bold"
           font-size="12px"
           fill="#002b36"
           id="text11">increment</text><text
           text-anchor="start"
           x="75"
           y="-303"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text12">@op:increment</text><text
           text-anchor="start"
           x="94"
           y="-293"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#dc322f"
           id="text13">5 calls</text></g><!-- x&#45;&gt;increment --><g
         id="edge3"
         class="edge"><title
           id="title13">x-&gt;increment</title><path
           fill="none"
           stroke="#002b36"
           d="m 62.77,-383.76 c 8.13,13.65 19.65,32.98 29.09,48.83"
           id="path13" /><polygon
           fill="#002b36"
           stroke="#002b36"
           points="89.01,-332.87 95.03,-336.45 97.14,-326.07 "
           id="polygon13" /><text
           text-anchor="middle"
           x="107"
           y="-358"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text14">x</text><text
           text-anchor="middle"
           x="107"
           y="-347"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text15">(5 values)</text></g><!-- y --><g
         id="node3"
         class="node"><title
           id="title15">y</title><path
           fill="none"
           stroke="#268bd2"
           d="m 146.5,-228 c 0,0 -70,0 -70,0 -6,0 -12,6 -12,12 0,0 0,12 0,12 0,6 6,12 12,12 0,0 70,0 70,0 6,0 12,-6 12,-12 0,0 0,-12 0,-12 0,-6 -6,-12 -12,-12"
           id="path15" /><text
           text-anchor="start"
           x="108"
           y="-212.39999"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-weight="bold"
           font-size="12px"
           fill="#002b36"
           id="text16">y</text><text
           text-anchor="start"
           x="72.5"
           y="-202"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#268bd2"
           id="text17">5 values (2 sinks)</text></g><!-- y&#45;&gt;add --><g
         id="edge5"
         class="edge"><title
           id="title17">y-&gt;add</title><path
           fill="none"
           stroke="#002b36"
           d="m 100.68,-191.76 c -8.57,13.65 -20.7,32.98 -30.65,48.83"
           id="path17" /><polygon
           fill="#002b36"
           stroke="#002b36"
           points="66.82,-144.4 72.75,-140.68 64.47,-134.07 "
           id="polygon17" /><text
           text-anchor="middle"
           x="110"
           y="-166"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text18">y</text><text
           text-anchor="middle"
           x="110"
           y="-155"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text19">(3 values)</text></g><!-- add&#45;&gt;z --><g
         id="edge4"
         class="edge"><title
           id="title19">add-&gt;z</title><path
           fill="none"
           stroke="#002b36"
           d="m 52.5,-93.98 c 0,13.64 0,32.23 0,47.48"
           id="path19" /><polygon
           fill="#002b36"
           stroke="#002b36"
           points="49,-46.1 56,-46.1 52.5,-36.1 "
           id="polygon19" /><text
           text-anchor="middle"
           x="74"
           y="-68"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text20">output_0</text><text
           text-anchor="middle"
           x="74"
           y="-57"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text21">(3 values)</text></g><!-- increment&#45;&gt;y --><g
         id="edge1"
         class="edge"><title
           id="title21">increment-&gt;y</title><path
           fill="none"
           stroke="#002b36"
           d="m 109.11,-285.98 c 0.43,13.64 1.03,32.23 1.51,47.48"
           id="path21" /><polygon
           fill="#002b36"
           stroke="#002b36"
           points="107.14,-237.98 114.13,-238.2 110.95,-228.1 "
           id="polygon21" /><text
           text-anchor="middle"
           x="131"
           y="-260"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text22">output_0</text><text
           text-anchor="middle"
           x="131"
           y="-249"
           font-family="'Liberation Sans', Helvetica, Arial, sans-serif"
           font-size="10px"
           fill="#002b36"
           id="text23">(5 values)</text></g></g><g
       font-family="monospace"
       font-size="14px"
       id="g53-7"
       transform="matrix(0.53468435,0,0,0.53468435,23.468576,16.081706)"><text
         x="0"
         y="14"
         xml:space="preserve"
         id="text12-1"><tspan
           fill="#859900"
           id="tspan1">for</tspan><tspan
           fill="#657b83"
           id="tspan2"> </tspan><tspan
           fill="#657b83"
           id="tspan3">x</tspan><tspan
           fill="#657b83"
           id="tspan4"> </tspan><tspan
           fill="#859900"
           id="tspan5">in</tspan><tspan
           fill="#657b83"
           id="tspan6"> </tspan><tspan
           fill="#268bd2"
           id="tspan7">range</tspan><tspan
           fill="#657b83"
           id="tspan8">(</tspan><tspan
           fill="#2aa198"
           id="tspan9">5</tspan><tspan
           fill="#657b83"
           id="tspan10">)</tspan><tspan
           fill="#657b83"
           id="tspan11">:</tspan><tspan
           fill="#657b83"
           id="tspan12" /></text><text
         x="0"
         y="33"
         xml:space="preserve"
         id="text23-7"><tspan
           fill="#657b83"
           id="tspan13" /><tspan
           fill="#657b83"
           id="tspan14">    </tspan><tspan
           fill="#657b83"
           id="tspan15">y</tspan><tspan
           fill="#657b83"
           id="tspan16"> </tspan><tspan
           fill="#93a1a1"
           id="tspan17">=</tspan><tspan
           fill="#657b83"
           id="tspan18"> </tspan><tspan
           fill="#657b83"
           id="tspan19">increment</tspan><tspan
           fill="#657b83"
           id="tspan20">(</tspan><tspan
           fill="#657b83"
           id="tspan21">x</tspan><tspan
           fill="#657b83"
           id="tspan22">)</tspan><tspan
           fill="#657b83"
           id="tspan23" /></text><text
         x="0"
         y="52"
         xml:space="preserve"
         id="text38"><tspan
           fill="#657b83"
           id="tspan24" /><tspan
           fill="#657b83"
           id="tspan25">    </tspan><tspan
           fill="#859900"
           id="tspan26">if</tspan><tspan
           fill="#657b83"
           id="tspan27"> </tspan><tspan
           fill="#657b83"
           id="tspan28">x</tspan><tspan
           fill="#657b83"
           id="tspan29"> </tspan><tspan
           fill="#93a1a1"
           id="tspan30">%</tspan><tspan
           fill="#657b83"
           id="tspan31"> </tspan><tspan
           fill="#2aa198"
           id="tspan32">2</tspan><tspan
           fill="#657b83"
           id="tspan33"> </tspan><tspan
           fill="#93a1a1"
           id="tspan34">==</tspan><tspan
           fill="#657b83"
           id="tspan35"> </tspan><tspan
           fill="#2aa198"
           id="tspan36">0</tspan><tspan
           fill="#657b83"
           id="tspan37">:</tspan><tspan
           fill="#657b83"
           id="tspan38" /></text><text
         x="0"
         y="71"
         xml:space="preserve"
         id="text52"><tspan
           fill="#657b83"
           id="tspan39" /><tspan
           fill="#657b83"
           id="tspan40">        </tspan><tspan
           fill="#657b83"
           id="tspan41">z</tspan><tspan
           fill="#657b83"
           id="tspan42"> </tspan><tspan
           fill="#93a1a1"
           id="tspan43">=</tspan><tspan
           fill="#657b83"
           id="tspan44"> </tspan><tspan
           fill="#657b83"
           id="tspan45">add</tspan><tspan
           fill="#657b83"
           id="tspan46">(</tspan><tspan
           fill="#657b83"
           id="tspan47">x</tspan><tspan
           fill="#657b83"
           id="tspan48">,</tspan><tspan
           fill="#657b83"
           id="tspan49"> </tspan><tspan
           fill="#657b83"
           id="tspan50">y</tspan><tspan
           fill="#657b83"
           id="tspan51">)</tspan><tspan
           fill="#657b83"
           id="tspan52" /></text><text
         x="0"
         y="90"
         xml:space="preserve"
         id="text53"><tspan
           fill="#657b83"
           id="tspan53" /></text></g></g><g
     inkscape:groupmode="layer"
     id="layer3"
     inkscape:label="Layer 3" /></svg>
