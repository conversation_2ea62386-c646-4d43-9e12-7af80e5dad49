"""
mandala1框架完整工作流程演示
展示@op、Ref、Storage、ComputationFrame四大组件的协作关系

本演示通过一个完整的数据科学流水线，详细展示：
1. @op装饰器如何转换函数
2. Storage如何管理计算和记忆化
3. Ref如何包装和标识数据
4. ComputationFrame如何构建和分析计算图
5. 组件间的详细交互过程
"""

import os
import sys
import time
import tempfile
from typing import List, Dict, Tuple

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from mandala1.imports import Storage, op
from mandala1.model import Context, Ref, Call, Op


class ComponentInteractionDemo:
    """组件交互演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.interaction_log = []
        
    def log_interaction(self, component: str, action: str, details: str):
        """记录组件交互"""
        timestamp = time.time()
        self.interaction_log.append({
            'timestamp': timestamp,
            'component': component,
            'action': action,
            'details': details
        })
        print(f"[{component}] {action}: {details}")
    
    def print_section(self, title: str, emoji: str = "🔍"):
        """打印章节标题"""
        print("\n" + "=" * 80)
        print(f"{emoji} {title}")
        print("=" * 80)
    
    def demonstrate_op_transformation(self):
        """演示@op装饰器的函数转换过程"""
        self.print_section("@op装饰器：函数转换过程", "🎭")
        
        print("📝 定义原始函数:")
        def original_load_data(file_path: str, encoding: str = 'utf-8') -> List[str]:
            """原始的数据加载函数"""
            print(f"    📂 加载文件: {file_path}")
            # 模拟文件加载
            return [f"line_{i}" for i in range(100)]
        
        print(f"  原始函数: {original_load_data}")
        print(f"  函数类型: {type(original_load_data)}")
        
        print("\n🎭 应用@op装饰器:")
        @op(output_names=['data'], ignore_args=['encoding'])
        def load_data(file_path: str, encoding: str = 'utf-8') -> List[str]:
            """@op装饰的数据加载函数"""
            print(f"    📂 加载文件: {file_path}")
            return [f"line_{i}" for i in range(100)]
        
        print(f"  装饰后对象: {load_data}")
        print(f"  对象类型: {type(load_data)}")
        print(f"  Op名称: {load_data.name}")
        print(f"  Op版本: {load_data.version}")
        print(f"  输出名称: {load_data.output_names}")
        print(f"  忽略参数: {load_data.ignore_args}")
        
        self.log_interaction("@op装饰器", "函数转换", 
                           f"将{original_load_data.__name__}转换为Op对象")
        
        return load_data
    
    def demonstrate_storage_lifecycle(self, load_data_op):
        """演示Storage的生命周期管理"""
        self.print_section("Storage：生命周期管理", "💾")
        
        # 创建Storage
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "workflow_demo.db")
        
        print("🏗️  创建Storage:")
        self.storage = Storage(
            db_path=db_path,
            overflow_dir=os.path.join(self.temp_dir, "overflow")
        )
        print(f"  数据库路径: {db_path}")
        print(f"  版本控制: {'启用' if self.storage.versioned else '禁用'}")
        
        self.log_interaction("Storage", "创建", f"数据库路径: {db_path}")
        
        # 演示Context管理
        print("\n🔄 Context上下文管理:")
        print(f"  进入前Context: {Context.current_context}")
        
        with self.storage:
            print(f"  进入后Context: {Context.current_context}")
            print(f"  Context中的Storage: {Context.current_context.storage}")
            
            self.log_interaction("Context", "进入", "设置Storage上下文")
            
            # 在上下文中调用函数
            print("\n📞 在Storage上下文中调用@op函数:")
            result = load_data_op("data.txt", encoding="utf-8")
            
            print(f"  调用结果类型: {type(result)}")
            print(f"  结果是Ref: {isinstance(result, Ref)}")
            
            self.log_interaction("Storage", "函数调用", 
                               f"执行{load_data_op.name}，返回{type(result).__name__}")
            
            return result
    
    def demonstrate_ref_system(self, data_ref):
        """演示Ref引用系统"""
        self.print_section("Ref：引用系统详解", "🔗")
        
        print("📋 Ref对象详细信息:")
        print(f"  Ref类型: {type(data_ref).__name__}")
        print(f"  内容ID: {data_ref.cid[:16]}...")
        print(f"  历史ID: {data_ref.hid[:16]}...")
        print(f"  在内存: {data_ref.in_memory}")
        
        if data_ref.in_memory:
            print(f"  对象类型: {type(data_ref.obj).__name__}")
            print(f"  对象长度: {len(data_ref.obj) if hasattr(data_ref.obj, '__len__') else 'N/A'}")
        
        self.log_interaction("Ref", "创建", 
                           f"包装数据对象，cid={data_ref.cid[:8]}...")
        
        # 演示Ref状态转换
        print("\n🔄 Ref状态转换:")
        
        # detached操作
        detached_ref = data_ref.detached()
        print(f"  原始Ref在内存: {data_ref.in_memory}")
        print(f"  分离后在内存: {detached_ref.in_memory}")
        print(f"  ID保持不变: {data_ref.hid == detached_ref.hid}")
        
        self.log_interaction("Ref", "detached", "创建分离状态的Ref副本")
        
        # 解包操作
        unwrapped_data = self.storage.unwrap(data_ref)
        print(f"  解包后类型: {type(unwrapped_data)}")
        print(f"  解包后长度: {len(unwrapped_data)}")
        
        self.log_interaction("Ref", "unwrap", "解包获取原始数据")
        
        return data_ref
    
    def demonstrate_call_system(self, data_ref):
        """演示Call调用系统"""
        self.print_section("Call：调用记录系统", "📞")
        
        # 获取创建Ref的Call
        creator_call = self.storage.get_ref_creator(data_ref)
        
        print("📋 Call对象详细信息:")
        print(f"  Call对象: {creator_call}")
        print(f"  操作名称: {creator_call.op.name}")
        print(f"  内容ID: {creator_call.cid[:16]}...")
        print(f"  历史ID: {creator_call.hid[:16]}...")
        print(f"  语义版本: {creator_call.semantic_version}")
        print(f"  内容版本: {creator_call.content_version}")
        
        print("\n📥 输入参数:")
        for name, ref in creator_call.inputs.items():
            print(f"  {name}: {type(ref).__name__}({self.storage.unwrap(ref)})")
        
        print("\n📤 输出结果:")
        for name, ref in creator_call.outputs.items():
            print(f"  {name}: {type(ref).__name__}(长度={len(self.storage.unwrap(ref))})")
        
        self.log_interaction("Call", "记录", 
                           f"保存{creator_call.op.name}的调用历史")
        
        return creator_call
    
    def demonstrate_computation_frame(self, data_ref):
        """演示ComputationFrame计算图"""
        self.print_section("ComputationFrame：计算图构建", "🕸️")
        
        # 定义更多操作来构建复杂图
        @op(output_names=['processed_data'])
        def process_data(raw_data: List[str]) -> List[str]:
            """数据处理"""
            print(f"    🔄 处理数据: {len(raw_data)} 行")
            return [line.upper() for line in raw_data]
        
        @op(output_names=['stats'])
        def analyze_data(processed_data: List[str]) -> Dict[str, int]:
            """数据分析"""
            print(f"    📊 分析数据: {len(processed_data)} 行")
            return {
                'total_lines': len(processed_data),
                'avg_length': sum(len(line) for line in processed_data) // len(processed_data)
            }
        
        # 执行完整流水线
        with self.storage:
            processed = process_data(self.storage.unwrap(data_ref))
            stats = analyze_data(self.storage.unwrap(processed))
        
        self.log_interaction("Pipeline", "执行", "完成数据处理流水线")
        
        # 创建ComputationFrame
        print("\n🏗️  创建ComputationFrame:")
        cf_minimal = self.storage.cf(stats)
        print(f"  最小CF节点数: {len(cf_minimal.nodes)}")
        print(f"  变量节点: {list(cf_minimal.vnames)}")
        print(f"  函数节点: {list(cf_minimal.fnames)}")
        
        # 扩展ComputationFrame
        print("\n📈 扩展ComputationFrame:")
        cf_expanded = cf_minimal.expand_back(recursive=True)
        print(f"  扩展后节点数: {len(cf_expanded.nodes)}")
        print(f"  变量节点: {len(cf_expanded.vnames)} 个")
        print(f"  函数节点: {len(cf_expanded.fnames)} 个")
        print(f"  边数量: {len(list(cf_expanded.edges()))}")
        
        self.log_interaction("ComputationFrame", "构建", 
                           f"从{len(cf_minimal.nodes)}节点扩展到{len(cf_expanded.nodes)}节点")
        
        # 拓扑分析
        print("\n🔍 拓扑分析:")
        topo_order = cf_expanded.topsort_modulo_sccs()
        print(f"  拓扑排序: {topo_order}")
        
        func_order = [node for node in topo_order if node in cf_expanded.fnames]
        print(f"  函数执行顺序: {func_order}")
        
        self.log_interaction("ComputationFrame", "分析", 
                           f"拓扑排序完成，执行顺序: {func_order}")
        
        return cf_expanded
    
    def demonstrate_complete_interaction(self):
        """演示完整的组件交互过程"""
        self.print_section("完整交互流程演示", "🔄")
        
        print("🎯 完整工作流程:")
        print("1. @op装饰器转换函数")
        print("2. Storage管理执行上下文")
        print("3. 函数调用创建Call和Ref")
        print("4. ComputationFrame构建计算图")
        print("5. 图分析和可视化")
        
        # 执行完整流程
        load_data_op = self.demonstrate_op_transformation()
        data_ref = self.demonstrate_storage_lifecycle(load_data_op)
        self.demonstrate_ref_system(data_ref)
        self.demonstrate_call_system(data_ref)
        cf = self.demonstrate_computation_frame(data_ref)
        
        # 总结交互日志
        self.print_section("组件交互总结", "📊")
        
        component_counts = {}
        for log_entry in self.interaction_log:
            component = log_entry['component']
            component_counts[component] = component_counts.get(component, 0) + 1
        
        print("📈 组件交互统计:")
        for component, count in component_counts.items():
            print(f"  {component}: {count} 次交互")
        
        print("\n🔗 关键交互链:")
        print("  @op → Op对象 → Storage.call() → Call+Ref → ComputationFrame")
        
        return cf


def main():
    """主函数：运行完整的组件交互演示"""
    print("🚀 mandala1框架完整工作流程演示")
    print("=" * 80)
    print("📖 本演示展示@op、Ref、Storage、ComputationFrame的协作关系")
    print("🎯 通过完整的数据处理流水线观察组件交互")
    print("=" * 80)
    
    demo = ComponentInteractionDemo()
    
    try:
        cf = demo.demonstrate_complete_interaction()
        
        print("\n" + "=" * 80)
        print("🎉 完整工作流程演示完成！")
        print("=" * 80)
        print("✅ 成功展示了所有核心组件的协作关系")
        print("✅ 详细记录了组件间的交互过程")
        print("✅ 构建了完整的计算图并进行了分析")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if demo.temp_dir and os.path.exists(demo.temp_dir):
            import shutil
            try:
                shutil.rmtree(demo.temp_dir)
                print(f"\n🧹 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


if __name__ == "__main__":
    main()
