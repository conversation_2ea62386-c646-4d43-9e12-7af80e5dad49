<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="826pt" height="910pt"
 viewBox="0.00 0.00 825.50 910.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 906)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-906 821.5,-906 821.5,4 -4,4"/>
<!-- y_test -->
<g id="node1" class="node">
<title>y_test</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M98,-612C98,-612 68,-612 68,-612 62,-612 56,-606 56,-600 56,-600 56,-588 56,-588 56,-582 62,-576 68,-576 68,-576 98,-576 98,-576 104,-576 110,-582 110,-588 110,-588 110,-600 110,-600 110,-606 104,-612 98,-612"/>
<text text-anchor="start" x="65.5" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y_test</text>
<text text-anchor="start" x="64.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- eval_model -->
<g id="node17" class="node">
<title>eval_model</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M192,-326C192,-326 126,-326 126,-326 120,-326 114,-320 114,-314 114,-314 114,-298 114,-298 114,-292 120,-286 126,-286 126,-286 192,-286 192,-286 198,-286 204,-292 204,-298 204,-298 204,-314 204,-314 204,-320 198,-326 192,-326"/>
<text text-anchor="start" x="126" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">eval_model</text>
<text text-anchor="start" x="122" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:eval_model</text>
<text text-anchor="start" x="142" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">12 calls</text>
</g>
<!-- y_test&#45;&gt;eval_model -->
<g id="edge2" class="edge">
<title>y_test&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M81.08,-575.6C78.34,-546.71 74.61,-487.16 84,-438 88.83,-412.71 92.85,-406.71 105,-384 114.25,-366.71 126.7,-348.7 137.32,-334.45"/>
<polygon fill="#002b36" stroke="#002b36" points="140.39,-336.18 143.65,-326.1 134.82,-331.96 140.39,-336.18"/>
<text text-anchor="middle" x="105.5" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_test</text>
<text text-anchor="middle" x="105.5" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- eval_ensemble -->
<g id="node19" class="node">
<title>eval_ensemble</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M117,-134C117,-134 35,-134 35,-134 29,-134 23,-128 23,-122 23,-122 23,-106 23,-106 23,-100 29,-94 35,-94 35,-94 117,-94 117,-94 123,-94 129,-100 129,-106 129,-106 129,-122 129,-122 129,-128 123,-134 117,-134"/>
<text text-anchor="start" x="33" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">eval_ensemble</text>
<text text-anchor="start" x="31" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:eval_ensemble</text>
<text text-anchor="start" x="61.5" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- y_test&#45;&gt;eval_ensemble -->
<g id="edge1" class="edge">
<title>y_test&#45;&gt;eval_ensemble</title>
<path fill="none" stroke="#002b36" d="M55.82,-577.34C31.62,-561.14 0,-533.4 0,-499 0,-499 0,-499 0,-209 0,-181.52 19.89,-157.55 39.32,-140.7"/>
<polygon fill="#002b36" stroke="#002b36" points="41.78,-143.21 47.27,-134.15 37.32,-137.81 41.78,-143.21"/>
<text text-anchor="middle" x="21.5" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_test</text>
<text text-anchor="middle" x="21.5" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- max_depth -->
<g id="node2" class="node">
<title>max_depth</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M805.5,-612C805.5,-612 724.5,-612 724.5,-612 718.5,-612 712.5,-606 712.5,-600 712.5,-600 712.5,-588 712.5,-588 712.5,-582 718.5,-576 724.5,-576 724.5,-576 805.5,-576 805.5,-576 811.5,-576 817.5,-582 817.5,-588 817.5,-588 817.5,-600 817.5,-600 817.5,-606 811.5,-612 805.5,-612"/>
<text text-anchor="start" x="733" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">max_depth</text>
<text text-anchor="start" x="720.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- train_random_forest -->
<g id="node15" class="node">
<title>train_random_forest</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M630.5,-518C630.5,-518 521.5,-518 521.5,-518 515.5,-518 509.5,-512 509.5,-506 509.5,-506 509.5,-490 509.5,-490 509.5,-484 515.5,-478 521.5,-478 521.5,-478 630.5,-478 630.5,-478 636.5,-478 642.5,-484 642.5,-490 642.5,-490 642.5,-506 642.5,-506 642.5,-512 636.5,-518 630.5,-518"/>
<text text-anchor="start" x="517.5" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">train_random_forest</text>
<text text-anchor="start" x="520.5" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:train_random_forest</text>
<text text-anchor="start" x="561.5" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">6 calls</text>
</g>
<!-- max_depth&#45;&gt;train_random_forest -->
<g id="edge21" class="edge">
<title>max_depth&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M744.69,-575.98C729.13,-563.6 706.71,-547.11 685,-536 674.58,-530.66 663.22,-525.83 651.89,-521.55"/>
<polygon fill="#002b36" stroke="#002b36" points="653,-518.23 642.41,-518.09 650.6,-524.81 653,-518.23"/>
<text text-anchor="middle" x="743.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">max_depth</text>
<text text-anchor="middle" x="743.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- y_train -->
<g id="node3" class="node">
<title>y_train</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M535,-612C535,-612 503,-612 503,-612 497,-612 491,-606 491,-600 491,-600 491,-588 491,-588 491,-582 497,-576 503,-576 503,-576 535,-576 535,-576 541,-576 547,-582 547,-588 547,-588 547,-600 547,-600 547,-606 541,-612 535,-612"/>
<text text-anchor="start" x="499" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y_train</text>
<text text-anchor="start" x="500.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- y_train&#45;&gt;train_random_forest -->
<g id="edge24" class="edge">
<title>y_train&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M539.15,-575.78C544.5,-570.49 549.91,-564.36 554,-558 559.94,-548.76 564.61,-537.68 568.08,-527.63"/>
<polygon fill="#002b36" stroke="#002b36" points="571.44,-528.62 571.17,-518.03 564.78,-526.48 571.44,-528.62"/>
<text text-anchor="middle" x="585.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_train</text>
<text text-anchor="middle" x="585.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- train_svc -->
<g id="node20" class="node">
<title>train_svc</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M340,-518C340,-518 284,-518 284,-518 278,-518 272,-512 272,-506 272,-506 272,-490 272,-490 272,-484 278,-478 284,-478 284,-478 340,-478 340,-478 346,-478 352,-484 352,-490 352,-490 352,-506 352,-506 352,-512 346,-518 340,-518"/>
<text text-anchor="start" x="285.5" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">train_svc</text>
<text text-anchor="start" x="280" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:train_svc</text>
<text text-anchor="start" x="297.5" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">6 calls</text>
</g>
<!-- y_train&#45;&gt;train_svc -->
<g id="edge23" class="edge">
<title>y_train&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M514.06,-575.91C509.42,-562.98 501.19,-545.82 488,-536 468.42,-521.43 407.13,-510.85 362.37,-504.83"/>
<polygon fill="#002b36" stroke="#002b36" points="362.6,-501.33 352.23,-503.51 361.69,-508.27 362.6,-501.33"/>
<text text-anchor="middle" x="526.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_train</text>
<text text-anchor="middle" x="526.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- X_test -->
<g id="node4" class="node">
<title>X_test</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M156,-420C156,-420 126,-420 126,-420 120,-420 114,-414 114,-408 114,-408 114,-396 114,-396 114,-390 120,-384 126,-384 126,-384 156,-384 156,-384 162,-384 168,-390 168,-396 168,-396 168,-408 168,-408 168,-414 162,-420 156,-420"/>
<text text-anchor="start" x="122.5" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X_test</text>
<text text-anchor="start" x="122.5" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- X_test&#45;&gt;eval_model -->
<g id="edge5" class="edge">
<title>X_test&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M144.3,-383.76C146.84,-370.5 150.41,-351.86 153.39,-336.27"/>
<polygon fill="#002b36" stroke="#002b36" points="156.9,-336.55 155.35,-326.07 150.03,-335.24 156.9,-336.55"/>
<text text-anchor="middle" x="173.5" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_test</text>
<text text-anchor="middle" x="173.5" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- X_test&#45;&gt;eval_ensemble -->
<g id="edge4" class="edge">
<title>X_test&#45;&gt;eval_ensemble</title>
<path fill="none" stroke="#002b36" d="M131.71,-383.79C123.88,-368.82 112.74,-346.39 105,-326 88.92,-283.65 83.48,-272.83 77,-228 72.93,-199.84 73.15,-167.32 74.11,-144.42"/>
<polygon fill="#002b36" stroke="#002b36" points="77.61,-144.47 74.6,-134.31 70.62,-144.13 77.61,-144.47"/>
<text text-anchor="middle" x="106.5" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_test</text>
<text text-anchor="middle" x="106.5" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- y -->
<g id="node5" class="node">
<title>y</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M235,-804C235,-804 205,-804 205,-804 199,-804 193,-798 193,-792 193,-792 193,-780 193,-780 193,-774 199,-768 205,-768 205,-768 235,-768 235,-768 241,-768 247,-774 247,-780 247,-780 247,-792 247,-792 247,-798 241,-804 235,-804"/>
<text text-anchor="start" x="216.5" y="-788.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y</text>
<text text-anchor="start" x="201.5" y="-778" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values</text>
</g>
<!-- get_train_test_split -->
<g id="node18" class="node">
<title>get_train_test_split</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M275.5,-710C275.5,-710 174.5,-710 174.5,-710 168.5,-710 162.5,-704 162.5,-698 162.5,-698 162.5,-682 162.5,-682 162.5,-676 168.5,-670 174.5,-670 174.5,-670 275.5,-670 275.5,-670 281.5,-670 287.5,-676 287.5,-682 287.5,-682 287.5,-698 287.5,-698 287.5,-704 281.5,-710 275.5,-710"/>
<text text-anchor="start" x="170.5" y="-697.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">get_train_test_split</text>
<text text-anchor="start" x="171.5" y="-687" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:get_train_test_split</text>
<text text-anchor="start" x="210.5" y="-677" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- y&#45;&gt;get_train_test_split -->
<g id="edge17" class="edge">
<title>y&#45;&gt;get_train_test_split</title>
<path fill="none" stroke="#002b36" d="M201.66,-767.64C197.33,-762.43 193.3,-756.38 191,-750 187.68,-740.8 187.45,-737.11 191,-728 192.35,-724.55 194.18,-721.22 196.31,-718.07"/>
<polygon fill="#002b36" stroke="#002b36" points="199.07,-720.23 202.48,-710.2 193.56,-715.91 199.07,-720.23"/>
<text text-anchor="middle" x="212.5" y="-742" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y</text>
<text text-anchor="middle" x="212.5" y="-731" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- X -->
<g id="node6" class="node">
<title>X</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M322,-804C322,-804 292,-804 292,-804 286,-804 280,-798 280,-792 280,-792 280,-780 280,-780 280,-774 286,-768 292,-768 292,-768 322,-768 322,-768 328,-768 334,-774 334,-780 334,-780 334,-792 334,-792 334,-798 328,-804 322,-804"/>
<text text-anchor="start" x="302.5" y="-788.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X</text>
<text text-anchor="start" x="288.5" y="-778" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- scale_data -->
<g id="node14" class="node">
<title>scale_data</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M385,-710C385,-710 321,-710 321,-710 315,-710 309,-704 309,-698 309,-698 309,-682 309,-682 309,-676 315,-670 321,-670 321,-670 385,-670 385,-670 391,-670 397,-676 397,-682 397,-682 397,-698 397,-698 397,-704 391,-710 385,-710"/>
<text text-anchor="start" x="322" y="-697.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">scale_data</text>
<text text-anchor="start" x="317" y="-687" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:scale_data</text>
<text text-anchor="start" x="338.5" y="-677" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- X&#45;&gt;scale_data -->
<g id="edge16" class="edge">
<title>X&#45;&gt;scale_data</title>
<path fill="none" stroke="#002b36" d="M309.91,-767.86C312.34,-756.18 316.48,-740.6 323,-728 324.7,-724.72 326.71,-721.46 328.89,-718.32"/>
<polygon fill="#002b36" stroke="#002b36" points="331.69,-720.41 334.89,-710.31 326.09,-716.21 331.69,-720.41"/>
<text text-anchor="middle" x="344.5" y="-742" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="344.5" y="-731" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- X&#45;&gt;get_train_test_split -->
<g id="edge15" class="edge">
<title>X&#45;&gt;get_train_test_split</title>
<path fill="none" stroke="#002b36" d="M286.4,-767.96C280.22,-762.48 273.61,-756.21 268,-750 259.17,-740.23 250.43,-728.67 243.15,-718.38"/>
<polygon fill="#002b36" stroke="#002b36" points="245.99,-716.33 237.42,-710.11 240.24,-720.32 245.99,-716.33"/>
<text text-anchor="middle" x="289.5" y="-742" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="289.5" y="-731" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- C -->
<g id="node7" class="node">
<title>C</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M262.5,-612C262.5,-612 181.5,-612 181.5,-612 175.5,-612 169.5,-606 169.5,-600 169.5,-600 169.5,-588 169.5,-588 169.5,-582 175.5,-576 181.5,-576 181.5,-576 262.5,-576 262.5,-576 268.5,-576 274.5,-582 274.5,-588 274.5,-588 274.5,-600 274.5,-600 274.5,-606 268.5,-612 262.5,-612"/>
<text text-anchor="start" x="217" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">C</text>
<text text-anchor="start" x="177.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- C&#45;&gt;train_svc -->
<g id="edge26" class="edge">
<title>C&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M231.92,-575.96C239.33,-564.03 250.18,-548.12 262,-536 265.91,-531.99 270.29,-528.08 274.79,-524.41"/>
<polygon fill="#002b36" stroke="#002b36" points="277.19,-526.97 282.93,-518.06 272.89,-521.45 277.19,-526.97"/>
<text text-anchor="middle" x="283.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">C</text>
<text text-anchor="middle" x="283.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- n_estimators -->
<g id="node8" class="node">
<title>n_estimators</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M682.5,-612C682.5,-612 601.5,-612 601.5,-612 595.5,-612 589.5,-606 589.5,-600 589.5,-600 589.5,-588 589.5,-588 589.5,-582 595.5,-576 601.5,-576 601.5,-576 682.5,-576 682.5,-576 688.5,-576 694.5,-582 694.5,-588 694.5,-588 694.5,-600 694.5,-600 694.5,-606 688.5,-612 682.5,-612"/>
<text text-anchor="start" x="604.5" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">n_estimators</text>
<text text-anchor="start" x="597.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- n_estimators&#45;&gt;train_random_forest -->
<g id="edge22" class="edge">
<title>n_estimators&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M634.13,-575.85C628.43,-564.16 620.13,-548.58 611,-536 608.49,-532.54 605.67,-529.06 602.76,-525.7"/>
<polygon fill="#002b36" stroke="#002b36" points="605.21,-523.19 595.89,-518.14 600.02,-527.89 605.21,-523.19"/>
<text text-anchor="middle" x="652.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">n_estimators</text>
<text text-anchor="middle" x="652.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- kernel -->
<g id="node9" class="node">
<title>kernel</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M385.5,-612C385.5,-612 304.5,-612 304.5,-612 298.5,-612 292.5,-606 292.5,-600 292.5,-600 292.5,-588 292.5,-588 292.5,-582 298.5,-576 304.5,-576 304.5,-576 385.5,-576 385.5,-576 391.5,-576 397.5,-582 397.5,-588 397.5,-588 397.5,-600 397.5,-600 397.5,-606 391.5,-612 385.5,-612"/>
<text text-anchor="start" x="327" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">kernel</text>
<text text-anchor="start" x="300.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- kernel&#45;&gt;train_svc -->
<g id="edge27" class="edge">
<title>kernel&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M324.87,-575.9C320.09,-570.69 315.62,-564.58 313,-558 309.3,-548.7 308.28,-537.91 308.43,-528.12"/>
<polygon fill="#002b36" stroke="#002b36" points="311.93,-528.18 308.98,-518.01 304.94,-527.8 311.93,-528.18"/>
<text text-anchor="middle" x="334.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">kernel</text>
<text text-anchor="middle" x="334.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- X_train -->
<g id="node10" class="node">
<title>X_train</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M460.5,-612C460.5,-612 427.5,-612 427.5,-612 421.5,-612 415.5,-606 415.5,-600 415.5,-600 415.5,-588 415.5,-588 415.5,-582 421.5,-576 427.5,-576 427.5,-576 460.5,-576 460.5,-576 466.5,-576 472.5,-582 472.5,-588 472.5,-588 472.5,-600 472.5,-600 472.5,-606 466.5,-612 460.5,-612"/>
<text text-anchor="start" x="423.5" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X_train</text>
<text text-anchor="start" x="425.5" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- X_train&#45;&gt;train_random_forest -->
<g id="edge20" class="edge">
<title>X_train&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M439.31,-575.78C437.05,-563.45 436.42,-547.13 445,-536 452.42,-526.37 475.02,-518.51 499.41,-512.55"/>
<polygon fill="#002b36" stroke="#002b36" points="500.23,-515.95 509.18,-510.27 498.65,-509.13 500.23,-515.95"/>
<text text-anchor="middle" x="466.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_train</text>
<text text-anchor="middle" x="466.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- X_train&#45;&gt;train_svc -->
<g id="edge19" class="edge">
<title>X_train&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M415.49,-579.76C403.76,-573.78 390.3,-566.21 379,-558 367.56,-549.68 366.53,-545.45 356,-536 351.91,-532.33 347.55,-528.54 343.22,-524.83"/>
<polygon fill="#002b36" stroke="#002b36" points="345.42,-522.11 335.53,-518.31 340.9,-527.45 345.42,-522.11"/>
<text text-anchor="middle" x="400.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_train</text>
<text text-anchor="middle" x="400.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- accuracy -->
<g id="node11" class="node">
<title>accuracy</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M174,-36C174,-36 94,-36 94,-36 88,-36 82,-30 82,-24 82,-24 82,-12 82,-12 82,-6 88,0 94,0 94,0 174,0 174,0 180,0 186,-6 186,-12 186,-12 186,-24 186,-24 186,-30 180,-36 174,-36"/>
<text text-anchor="start" x="107.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">accuracy</text>
<text text-anchor="start" x="90" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">14 values (14 sinks)</text>
</g>
<!-- model -->
<g id="node12" class="node">
<title>model</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M326.5,-420C326.5,-420 291.5,-420 291.5,-420 285.5,-420 279.5,-414 279.5,-408 279.5,-408 279.5,-396 279.5,-396 279.5,-390 285.5,-384 291.5,-384 291.5,-384 326.5,-384 326.5,-384 332.5,-384 338.5,-390 338.5,-396 338.5,-396 338.5,-408 338.5,-408 338.5,-414 332.5,-420 326.5,-420"/>
<text text-anchor="start" x="291" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">model</text>
<text text-anchor="start" x="287.5" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">12 values</text>
</g>
<!-- __make_list__ -->
<g id="node16" class="node">
<title>__make_list__</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M325,-326C325,-326 245,-326 245,-326 239,-326 233,-320 233,-314 233,-314 233,-298 233,-298 233,-292 239,-286 245,-286 245,-286 325,-286 325,-286 331,-286 337,-292 337,-298 337,-298 337,-314 337,-314 337,-320 331,-326 325,-326"/>
<text text-anchor="start" x="244" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">__make_list__</text>
<text text-anchor="start" x="241" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:__make_list__</text>
<text text-anchor="start" x="270.5" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- model&#45;&gt;__make_list__ -->
<g id="edge8" class="edge">
<title>model&#45;&gt;__make_list__</title>
<path fill="none" stroke="#002b36" d="M304.6,-383.76C301.18,-370.37 296.36,-351.5 292.36,-335.82"/>
<polygon fill="#002b36" stroke="#002b36" points="295.73,-334.9 289.87,-326.07 288.95,-336.63 295.73,-334.9"/>
<text text-anchor="middle" x="324.5" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">*elts</text>
<text text-anchor="middle" x="324.5" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(12 values)</text>
</g>
<!-- model&#45;&gt;eval_model -->
<g id="edge7" class="edge">
<title>model&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M279.5,-386.69C267.64,-380.65 253.97,-373.34 242,-366 225.24,-355.73 207.38,-343.25 192.54,-332.42"/>
<polygon fill="#002b36" stroke="#002b36" points="194.31,-329.38 184.18,-326.27 190.16,-335.01 194.31,-329.38"/>
<text text-anchor="middle" x="266.5" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">model</text>
<text text-anchor="middle" x="266.5" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(12 values)</text>
</g>
<!-- models -->
<g id="node13" class="node">
<title>models</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M133.5,-228C133.5,-228 98.5,-228 98.5,-228 92.5,-228 86.5,-222 86.5,-216 86.5,-216 86.5,-204 86.5,-204 86.5,-198 92.5,-192 98.5,-192 98.5,-192 133.5,-192 133.5,-192 139.5,-192 145.5,-198 145.5,-204 145.5,-204 145.5,-216 145.5,-216 145.5,-222 139.5,-228 133.5,-228"/>
<text text-anchor="start" x="94.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">models</text>
<text text-anchor="start" x="97.5" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- models&#45;&gt;eval_ensemble -->
<g id="edge9" class="edge">
<title>models&#45;&gt;eval_ensemble</title>
<path fill="none" stroke="#002b36" d="M108.66,-191.76C102.91,-178.24 94.78,-159.14 88.08,-143.38"/>
<polygon fill="#002b36" stroke="#002b36" points="91.25,-141.9 84.12,-134.07 84.81,-144.64 91.25,-141.9"/>
<text text-anchor="middle" x="122.5" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">models</text>
<text text-anchor="middle" x="122.5" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- scale_data&#45;&gt;X -->
<g id="edge30" class="edge">
<title>scale_data&#45;&gt;X</title>
<path fill="none" stroke="#002b36" d="M363.78,-710.11C369.07,-722.19 372.96,-737.79 366,-750 360.83,-759.08 352.27,-766.05 343.25,-771.29"/>
<polygon fill="#002b36" stroke="#002b36" points="341.48,-768.26 334.19,-775.96 344.69,-774.49 341.48,-768.26"/>
<text text-anchor="middle" x="390.5" y="-742" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_scaled</text>
<text text-anchor="middle" x="390.5" y="-731" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- train_random_forest&#45;&gt;model -->
<g id="edge18" class="edge">
<title>train_random_forest&#45;&gt;model</title>
<path fill="none" stroke="#002b36" d="M521.97,-477.98C470.44,-459.84 394.06,-432.95 348.15,-416.78"/>
<polygon fill="#002b36" stroke="#002b36" points="349.15,-413.43 338.56,-413.41 346.83,-420.03 349.15,-413.43"/>
<text text-anchor="middle" x="487.5" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">rf_model</text>
<text text-anchor="middle" x="487.5" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(6 values)</text>
</g>
<!-- __make_list__&#45;&gt;models -->
<g id="edge10" class="edge">
<title>__make_list__&#45;&gt;models</title>
<path fill="none" stroke="#002b36" d="M250.8,-285.98C222.95,-270.49 183.64,-248.62 154.65,-232.5"/>
<polygon fill="#002b36" stroke="#002b36" points="156.07,-229.28 145.63,-227.48 152.67,-235.4 156.07,-229.28"/>
<text text-anchor="middle" x="237.5" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">list</text>
<text text-anchor="middle" x="237.5" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- eval_model&#45;&gt;accuracy -->
<g id="edge3" class="edge">
<title>eval_model&#45;&gt;accuracy</title>
<path fill="none" stroke="#002b36" d="M162.88,-285.82C170.71,-242.98 185.53,-136.79 159,-54 158,-50.87 156.6,-47.78 154.98,-44.82"/>
<polygon fill="#002b36" stroke="#002b36" points="157.91,-42.9 149.58,-36.35 152,-46.66 157.91,-42.9"/>
<text text-anchor="middle" x="199.5" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">accuracy</text>
<text text-anchor="middle" x="199.5" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(12 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;y_test -->
<g id="edge12" class="edge">
<title>get_train_test_split&#45;&gt;y_test</title>
<path fill="none" stroke="#002b36" d="M162.39,-680.12C129.53,-674.06 94.04,-664.79 84,-652 77.51,-643.74 76.23,-632.53 76.96,-622.24"/>
<polygon fill="#002b36" stroke="#002b36" points="80.47,-622.38 78.3,-612.01 73.53,-621.47 80.47,-622.38"/>
<text text-anchor="middle" x="105.5" y="-644" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_test</text>
<text text-anchor="middle" x="105.5" y="-633" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;y_train -->
<g id="edge14" class="edge">
<title>get_train_test_split&#45;&gt;y_train</title>
<path fill="none" stroke="#002b36" d="M287.52,-672.74C291.74,-671.77 295.92,-670.85 300,-670 348.06,-659.96 362.41,-667.49 409,-652 434.96,-643.37 462.27,-629.25 483.19,-617.22"/>
<polygon fill="#002b36" stroke="#002b36" points="485.17,-620.12 492.03,-612.04 481.63,-614.08 485.17,-620.12"/>
<text text-anchor="middle" x="479.5" y="-644" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_train</text>
<text text-anchor="middle" x="479.5" y="-633" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;X_test -->
<g id="edge11" class="edge">
<title>get_train_test_split&#45;&gt;X_test</title>
<path fill="none" stroke="#002b36" d="M202.87,-669.82C188,-655.51 169.44,-634.6 160,-612 134.46,-550.9 135.61,-471.21 138.42,-430.12"/>
<polygon fill="#002b36" stroke="#002b36" points="141.92,-430.31 139.2,-420.07 134.94,-429.77 141.92,-430.31"/>
<text text-anchor="middle" x="166.5" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_test</text>
<text text-anchor="middle" x="166.5" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;X_train -->
<g id="edge13" class="edge">
<title>get_train_test_split&#45;&gt;X_train</title>
<path fill="none" stroke="#002b36" d="M271.17,-669.92C305.97,-655.44 355.07,-634.79 406.14,-612.33"/>
<polygon fill="#002b36" stroke="#002b36" points="407.62,-615.5 415.36,-608.27 404.79,-609.1 407.62,-615.5"/>
<text text-anchor="middle" x="383.5" y="-644" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_train</text>
<text text-anchor="middle" x="383.5" y="-633" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- eval_ensemble&#45;&gt;accuracy -->
<g id="edge6" class="edge">
<title>eval_ensemble&#45;&gt;accuracy</title>
<path fill="none" stroke="#002b36" d="M87.74,-93.98C96.48,-79.81 108.52,-60.3 118.12,-44.74"/>
<polygon fill="#002b36" stroke="#002b36" points="121.18,-46.45 123.45,-36.1 115.22,-42.77 121.18,-46.45"/>
<text text-anchor="middle" x="133.5" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">accuracy</text>
<text text-anchor="middle" x="133.5" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- train_svc&#45;&gt;model -->
<g id="edge25" class="edge">
<title>train_svc&#45;&gt;model</title>
<path fill="none" stroke="#002b36" d="M311.39,-477.98C310.96,-464.34 310.36,-445.75 309.88,-430.5"/>
<polygon fill="#002b36" stroke="#002b36" points="313.36,-429.98 309.55,-420.1 306.37,-430.2 313.36,-429.98"/>
<text text-anchor="middle" x="335" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">svc_model</text>
<text text-anchor="middle" x="335" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(6 values)</text>
</g>
<!-- get_data -->
<g id="node21" class="node">
<title>get_data</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M283,-902C283,-902 229,-902 229,-902 223,-902 217,-896 217,-890 217,-890 217,-874 217,-874 217,-868 223,-862 229,-862 229,-862 283,-862 283,-862 289,-862 295,-868 295,-874 295,-874 295,-890 295,-890 295,-896 289,-902 283,-902"/>
<text text-anchor="start" x="231" y="-889.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">get_data</text>
<text text-anchor="start" x="225" y="-879" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:get_data</text>
<text text-anchor="start" x="241.5" y="-869" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- get_data&#45;&gt;y -->
<g id="edge29" class="edge">
<title>get_data&#45;&gt;y</title>
<path fill="none" stroke="#002b36" d="M236.15,-861.76C231.75,-856.43 227.63,-850.35 225,-844 221.19,-834.8 219.6,-824.06 219.07,-814.4"/>
<polygon fill="#002b36" stroke="#002b36" points="222.56,-814.02 218.87,-804.09 215.57,-814.16 222.56,-814.02"/>
<text text-anchor="middle" x="246.5" y="-836" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y</text>
<text text-anchor="middle" x="246.5" y="-825" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- get_data&#45;&gt;X -->
<g id="edge28" class="edge">
<title>get_data&#45;&gt;X</title>
<path fill="none" stroke="#002b36" d="M266.32,-861.98C273.94,-847.94 284.39,-828.66 292.8,-813.17"/>
<polygon fill="#002b36" stroke="#002b36" points="296.03,-814.56 297.72,-804.1 289.88,-811.22 296.03,-814.56"/>
<text text-anchor="middle" x="309.5" y="-836" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="309.5" y="-825" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
</g>
</svg>
