"""
简单测试程序
验证mandala1框架是否正常工作
"""

import os
import sys

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    print("✅ mandala1导入成功")
except ImportError as e:
    print(f"❌ mandala1导入失败: {e}")
    sys.exit(1)

@op
def add(x, y):
    return x + y

@op
def multiply(x, y):
    return x * y

def main():
    print("🚀 简单测试开始")
    
    try:
        storage = Storage()
        print("✅ Storage创建成功")
        
        with storage:
            a = add(1, 2)
            b = add(3, 4)
            c = multiply(storage.unwrap(a), storage.unwrap(b))
            
            print(f"✅ 计算完成: {storage.unwrap(c)}")
        
        cf = storage.cf(c).expand_back(recursive=True)
        print(f"✅ ComputationFrame创建成功: {len(cf.nodes)} 个节点")
        
        print("🎉 简单测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
