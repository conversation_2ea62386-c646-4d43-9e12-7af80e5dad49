<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="824pt" height="746pt"
 viewBox="0.00 0.00 823.50 746.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 742)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-742 819.5,-742 819.5,4 -4,4"/>
<!-- y_test -->
<g id="node1" class="node">
<title>y_test</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M42,-394C42,-394 12,-394 12,-394 6,-394 0,-388 0,-382 0,-382 0,-370 0,-370 0,-364 6,-358 12,-358 12,-358 42,-358 42,-358 48,-358 54,-364 54,-370 54,-370 54,-382 54,-382 54,-388 48,-394 42,-394"/>
<text text-anchor="start" x="9.5" y="-378.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y_test</text>
<text text-anchor="start" x="8.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- eval_model -->
<g id="node15" class="node">
<title>eval_model</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M135,-134C135,-134 69,-134 69,-134 63,-134 57,-128 57,-122 57,-122 57,-106 57,-106 57,-100 63,-94 69,-94 69,-94 135,-94 135,-94 141,-94 147,-100 147,-106 147,-106 147,-122 147,-122 147,-128 141,-134 135,-134"/>
<text text-anchor="start" x="69" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">eval_model</text>
<text text-anchor="start" x="65" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:eval_model</text>
<text text-anchor="start" x="87.5" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">4 calls</text>
</g>
<!-- y_test&#45;&gt;eval_model -->
<g id="edge4" class="edge">
<title>y_test&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M23.29,-357.92C18.56,-332.85 12.19,-284.86 23,-246 33.94,-206.69 60.2,-167.38 79.45,-142.23"/>
<polygon fill="#002b36" stroke="#002b36" points="82.28,-144.3 85.68,-134.26 76.76,-139.99 82.28,-144.3"/>
<text text-anchor="middle" x="44.5" y="-267" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_test</text>
<text text-anchor="middle" x="44.5" y="-256" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- max_depth -->
<g id="node2" class="node">
<title>max_depth</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M239.5,-448C239.5,-448 158.5,-448 158.5,-448 152.5,-448 146.5,-442 146.5,-436 146.5,-436 146.5,-424 146.5,-424 146.5,-418 152.5,-412 158.5,-412 158.5,-412 239.5,-412 239.5,-412 245.5,-412 251.5,-418 251.5,-424 251.5,-424 251.5,-436 251.5,-436 251.5,-442 245.5,-448 239.5,-448"/>
<text text-anchor="start" x="167" y="-432.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">max_depth</text>
<text text-anchor="start" x="154.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- train_random_forest -->
<g id="node14" class="node">
<title>train_random_forest</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M390.5,-340C390.5,-340 281.5,-340 281.5,-340 275.5,-340 269.5,-334 269.5,-328 269.5,-328 269.5,-312 269.5,-312 269.5,-306 275.5,-300 281.5,-300 281.5,-300 390.5,-300 390.5,-300 396.5,-300 402.5,-306 402.5,-312 402.5,-312 402.5,-328 402.5,-328 402.5,-334 396.5,-340 390.5,-340"/>
<text text-anchor="start" x="277.5" y="-327.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">train_random_forest</text>
<text text-anchor="start" x="280.5" y="-317" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:train_random_forest</text>
<text text-anchor="start" x="321.5" y="-307" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- max_depth&#45;&gt;train_random_forest -->
<g id="edge15" class="edge">
<title>max_depth&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M205.03,-411.87C211.44,-395.94 223.02,-372.52 240,-358 246.01,-352.86 252.82,-348.39 259.97,-344.51"/>
<polygon fill="#002b36" stroke="#002b36" points="261.78,-347.51 269.16,-339.91 258.65,-341.25 261.78,-347.51"/>
<text text-anchor="middle" x="264.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">max_depth</text>
<text text-anchor="middle" x="264.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- y_train -->
<g id="node3" class="node">
<title>y_train</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M512,-448C512,-448 480,-448 480,-448 474,-448 468,-442 468,-436 468,-436 468,-424 468,-424 468,-418 474,-412 480,-412 480,-412 512,-412 512,-412 518,-412 524,-418 524,-424 524,-424 524,-436 524,-436 524,-442 518,-448 512,-448"/>
<text text-anchor="start" x="476" y="-432.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y_train</text>
<text text-anchor="start" x="477.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- y_train&#45;&gt;train_random_forest -->
<g id="edge18" class="edge">
<title>y_train&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M467.73,-415.62C457.79,-409.96 447.1,-402.64 439,-394 426.63,-380.8 433.24,-370.33 420,-358 414.71,-353.07 408.66,-348.73 402.27,-344.92"/>
<polygon fill="#002b36" stroke="#002b36" points="403.86,-341.79 393.41,-340.04 400.49,-347.93 403.86,-341.79"/>
<text text-anchor="middle" x="460.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_train</text>
<text text-anchor="middle" x="460.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- train_svc -->
<g id="node17" class="node">
<title>train_svc</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M602,-340C602,-340 546,-340 546,-340 540,-340 534,-334 534,-328 534,-328 534,-312 534,-312 534,-306 540,-300 546,-300 546,-300 602,-300 602,-300 608,-300 614,-306 614,-312 614,-312 614,-328 614,-328 614,-334 608,-340 602,-340"/>
<text text-anchor="start" x="547.5" y="-327.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">train_svc</text>
<text text-anchor="start" x="542" y="-317" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:train_svc</text>
<text text-anchor="start" x="559.5" y="-307" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- y_train&#45;&gt;train_svc -->
<g id="edge17" class="edge">
<title>y_train&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M524.13,-416.66C534.16,-411.03 544.76,-403.47 552,-394 561.7,-381.3 567.14,-364.35 570.19,-349.98"/>
<polygon fill="#002b36" stroke="#002b36" points="573.65,-350.54 572.01,-340.07 566.76,-349.27 573.65,-350.54"/>
<text text-anchor="middle" x="589.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_train</text>
<text text-anchor="middle" x="589.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- X_test -->
<g id="node4" class="node">
<title>X_test</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M117,-282C117,-282 87,-282 87,-282 81,-282 75,-276 75,-270 75,-270 75,-258 75,-258 75,-252 81,-246 87,-246 87,-246 117,-246 117,-246 123,-246 129,-252 129,-258 129,-258 129,-270 129,-270 129,-276 123,-282 117,-282"/>
<text text-anchor="start" x="83.5" y="-266.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X_test</text>
<text text-anchor="start" x="83.5" y="-256" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- X_test&#45;&gt;eval_model -->
<g id="edge2" class="edge">
<title>X_test&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M102,-245.73C102,-220.88 102,-174.68 102,-144.39"/>
<polygon fill="#002b36" stroke="#002b36" points="105.5,-144.02 102,-134.02 98.5,-144.02 105.5,-144.02"/>
<text text-anchor="middle" x="123.5" y="-213" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_test</text>
<text text-anchor="middle" x="123.5" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- y -->
<g id="node5" class="node">
<title>y</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M187,-640C187,-640 157,-640 157,-640 151,-640 145,-634 145,-628 145,-628 145,-616 145,-616 145,-610 151,-604 157,-604 157,-604 187,-604 187,-604 193,-604 199,-610 199,-616 199,-616 199,-628 199,-628 199,-634 193,-640 187,-640"/>
<text text-anchor="start" x="168.5" y="-624.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">y</text>
<text text-anchor="start" x="153.5" y="-614" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values</text>
</g>
<!-- get_train_test_split -->
<g id="node16" class="node">
<title>get_train_test_split</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M227.5,-546C227.5,-546 126.5,-546 126.5,-546 120.5,-546 114.5,-540 114.5,-534 114.5,-534 114.5,-518 114.5,-518 114.5,-512 120.5,-506 126.5,-506 126.5,-506 227.5,-506 227.5,-506 233.5,-506 239.5,-512 239.5,-518 239.5,-518 239.5,-534 239.5,-534 239.5,-540 233.5,-546 227.5,-546"/>
<text text-anchor="start" x="122.5" y="-533.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">get_train_test_split</text>
<text text-anchor="start" x="123.5" y="-523" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:get_train_test_split</text>
<text text-anchor="start" x="162.5" y="-513" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- y&#45;&gt;get_train_test_split -->
<g id="edge11" class="edge">
<title>y&#45;&gt;get_train_test_split</title>
<path fill="none" stroke="#002b36" d="M153.66,-603.64C149.33,-598.43 145.3,-592.38 143,-586 139.68,-576.8 139.45,-573.11 143,-564 144.35,-560.55 146.18,-557.22 148.31,-554.07"/>
<polygon fill="#002b36" stroke="#002b36" points="151.07,-556.23 154.48,-546.2 145.56,-551.91 151.07,-556.23"/>
<text text-anchor="middle" x="164.5" y="-578" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y</text>
<text text-anchor="middle" x="164.5" y="-567" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- X -->
<g id="node6" class="node">
<title>X</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M274,-640C274,-640 244,-640 244,-640 238,-640 232,-634 232,-628 232,-628 232,-616 232,-616 232,-610 238,-604 244,-604 244,-604 274,-604 274,-604 280,-604 286,-610 286,-616 286,-616 286,-628 286,-628 286,-634 280,-640 274,-640"/>
<text text-anchor="start" x="254.5" y="-624.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X</text>
<text text-anchor="start" x="240.5" y="-614" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- scale_data -->
<g id="node13" class="node">
<title>scale_data</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M337,-546C337,-546 273,-546 273,-546 267,-546 261,-540 261,-534 261,-534 261,-518 261,-518 261,-512 267,-506 273,-506 273,-506 337,-506 337,-506 343,-506 349,-512 349,-518 349,-518 349,-534 349,-534 349,-540 343,-546 337,-546"/>
<text text-anchor="start" x="274" y="-533.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">scale_data</text>
<text text-anchor="start" x="269" y="-523" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:scale_data</text>
<text text-anchor="start" x="290.5" y="-513" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- X&#45;&gt;scale_data -->
<g id="edge10" class="edge">
<title>X&#45;&gt;scale_data</title>
<path fill="none" stroke="#002b36" d="M261.91,-603.86C264.34,-592.18 268.48,-576.6 275,-564 276.7,-560.72 278.71,-557.46 280.89,-554.32"/>
<polygon fill="#002b36" stroke="#002b36" points="283.69,-556.41 286.89,-546.31 278.09,-552.21 283.69,-556.41"/>
<text text-anchor="middle" x="296.5" y="-578" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="296.5" y="-567" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- X&#45;&gt;get_train_test_split -->
<g id="edge9" class="edge">
<title>X&#45;&gt;get_train_test_split</title>
<path fill="none" stroke="#002b36" d="M238.4,-603.96C232.22,-598.48 225.61,-592.21 220,-586 211.17,-576.23 202.43,-564.67 195.15,-554.38"/>
<polygon fill="#002b36" stroke="#002b36" points="197.99,-552.33 189.42,-546.11 192.24,-556.32 197.99,-552.33"/>
<text text-anchor="middle" x="241.5" y="-578" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="241.5" y="-567" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- C -->
<g id="node7" class="node">
<title>C</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M680.5,-448C680.5,-448 599.5,-448 599.5,-448 593.5,-448 587.5,-442 587.5,-436 587.5,-436 587.5,-424 587.5,-424 587.5,-418 593.5,-412 599.5,-412 599.5,-412 680.5,-412 680.5,-412 686.5,-412 692.5,-418 692.5,-424 692.5,-424 692.5,-436 692.5,-436 692.5,-442 686.5,-448 680.5,-448"/>
<text text-anchor="start" x="635" y="-432.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">C</text>
<text text-anchor="start" x="595.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- C&#45;&gt;train_svc -->
<g id="edge20" class="edge">
<title>C&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M637.48,-411.76C634.65,-396.6 628.92,-374.45 618,-358 615.51,-354.25 612.53,-350.67 609.32,-347.33"/>
<polygon fill="#002b36" stroke="#002b36" points="611.66,-344.72 601.99,-340.38 606.84,-349.8 611.66,-344.72"/>
<text text-anchor="middle" x="654.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">C</text>
<text text-anchor="middle" x="654.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- n_estimators -->
<g id="node8" class="node">
<title>n_estimators</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M362.5,-448C362.5,-448 281.5,-448 281.5,-448 275.5,-448 269.5,-442 269.5,-436 269.5,-436 269.5,-424 269.5,-424 269.5,-418 275.5,-412 281.5,-412 281.5,-412 362.5,-412 362.5,-412 368.5,-412 374.5,-418 374.5,-424 374.5,-424 374.5,-436 374.5,-436 374.5,-442 368.5,-448 362.5,-448"/>
<text text-anchor="start" x="284.5" y="-432.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">n_estimators</text>
<text text-anchor="start" x="277.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- n_estimators&#45;&gt;train_random_forest -->
<g id="edge16" class="edge">
<title>n_estimators&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M306.42,-411.65C302.59,-406.35 299.01,-400.26 297,-394 292.12,-378.76 290.68,-372.7 297,-358 298.57,-354.34 300.7,-350.88 303.16,-347.64"/>
<polygon fill="#002b36" stroke="#002b36" points="305.83,-349.9 309.82,-340.09 300.58,-345.27 305.83,-349.9"/>
<text text-anchor="middle" x="325.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">n_estimators</text>
<text text-anchor="middle" x="325.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- kernel -->
<g id="node9" class="node">
<title>kernel</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M803.5,-448C803.5,-448 722.5,-448 722.5,-448 716.5,-448 710.5,-442 710.5,-436 710.5,-436 710.5,-424 710.5,-424 710.5,-418 716.5,-412 722.5,-412 722.5,-412 803.5,-412 803.5,-412 809.5,-412 815.5,-418 815.5,-424 815.5,-424 815.5,-436 815.5,-436 815.5,-442 809.5,-448 803.5,-448"/>
<text text-anchor="start" x="745" y="-432.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">kernel</text>
<text text-anchor="start" x="718.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- kernel&#45;&gt;train_svc -->
<g id="edge21" class="edge">
<title>kernel&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M746.64,-411.71C730.86,-395.88 705.65,-372.74 680,-358 662.6,-348 642.04,-340.05 623.68,-334.13"/>
<polygon fill="#002b36" stroke="#002b36" points="624.62,-330.76 614.03,-331.13 622.54,-337.44 624.62,-330.76"/>
<text text-anchor="middle" x="748.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">kernel</text>
<text text-anchor="middle" x="748.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- X_train -->
<g id="node10" class="node">
<title>X_train</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M437.5,-448C437.5,-448 404.5,-448 404.5,-448 398.5,-448 392.5,-442 392.5,-436 392.5,-436 392.5,-424 392.5,-424 392.5,-418 398.5,-412 404.5,-412 404.5,-412 437.5,-412 437.5,-412 443.5,-412 449.5,-418 449.5,-424 449.5,-424 449.5,-436 449.5,-436 449.5,-442 443.5,-448 437.5,-448"/>
<text text-anchor="start" x="400.5" y="-432.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">X_train</text>
<text text-anchor="start" x="402.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values</text>
</g>
<!-- X_train&#45;&gt;train_random_forest -->
<g id="edge14" class="edge">
<title>X_train&#45;&gt;train_random_forest</title>
<path fill="none" stroke="#002b36" d="M395.76,-411.65C389.19,-406.44 382.44,-400.39 377,-394 370.6,-386.48 359.45,-366.39 350.39,-349.18"/>
<polygon fill="#002b36" stroke="#002b36" points="353.34,-347.27 345.61,-340.02 347.13,-350.5 353.34,-347.27"/>
<text text-anchor="middle" x="398.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_train</text>
<text text-anchor="middle" x="398.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- X_train&#45;&gt;train_svc -->
<g id="edge13" class="edge">
<title>X_train&#45;&gt;train_svc</title>
<path fill="none" stroke="#002b36" d="M449.52,-415.7C464.23,-408.44 480.27,-399.78 486,-394 498.74,-381.15 492.25,-370.83 505,-358 510.73,-352.24 517.54,-347.15 524.62,-342.73"/>
<polygon fill="#002b36" stroke="#002b36" points="526.76,-345.54 533.67,-337.5 523.26,-339.48 526.76,-345.54"/>
<text text-anchor="middle" x="526.5" y="-379" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_train</text>
<text text-anchor="middle" x="526.5" y="-368" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- accuracy -->
<g id="node11" class="node">
<title>accuracy</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M137,-36C137,-36 67,-36 67,-36 61,-36 55,-30 55,-24 55,-24 55,-12 55,-12 55,-6 61,0 67,0 67,0 137,0 137,0 143,0 149,-6 149,-12 149,-12 149,-24 149,-24 149,-30 143,-36 137,-36"/>
<text text-anchor="start" x="75.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">accuracy</text>
<text text-anchor="start" x="63" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values (4 sinks)</text>
</g>
<!-- model -->
<g id="node12" class="node">
<title>model</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M351,-228C351,-228 321,-228 321,-228 315,-228 309,-222 309,-216 309,-216 309,-204 309,-204 309,-198 315,-192 321,-192 321,-192 351,-192 351,-192 357,-192 363,-198 363,-204 363,-204 363,-216 363,-216 363,-222 357,-228 351,-228"/>
<text text-anchor="start" x="318" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">model</text>
<text text-anchor="start" x="317.5" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values</text>
</g>
<!-- model&#45;&gt;eval_model -->
<g id="edge3" class="edge">
<title>model&#45;&gt;eval_model</title>
<path fill="none" stroke="#002b36" d="M308.7,-198.03C271.59,-183.13 204.4,-156.13 156.7,-136.98"/>
<polygon fill="#002b36" stroke="#002b36" points="157.72,-133.61 147.14,-133.13 155.11,-140.11 157.72,-133.61"/>
<text text-anchor="middle" x="268.5" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">model</text>
<text text-anchor="middle" x="268.5" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- scale_data&#45;&gt;X -->
<g id="edge24" class="edge">
<title>scale_data&#45;&gt;X</title>
<path fill="none" stroke="#002b36" d="M315.78,-546.11C321.07,-558.19 324.96,-573.79 318,-586 312.83,-595.08 304.27,-602.05 295.25,-607.29"/>
<polygon fill="#002b36" stroke="#002b36" points="293.48,-604.26 286.19,-611.96 296.69,-610.49 293.48,-604.26"/>
<text text-anchor="middle" x="342.5" y="-578" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_scaled</text>
<text text-anchor="middle" x="342.5" y="-567" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- train_random_forest&#45;&gt;model -->
<g id="edge12" class="edge">
<title>train_random_forest&#45;&gt;model</title>
<path fill="none" stroke="#002b36" d="M336,-299.94C336,-282.85 336,-257.58 336,-238.34"/>
<polygon fill="#002b36" stroke="#002b36" points="339.5,-238.09 336,-228.09 332.5,-238.09 339.5,-238.09"/>
<text text-anchor="middle" x="357.5" y="-267" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">rf_model</text>
<text text-anchor="middle" x="357.5" y="-256" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- eval_model&#45;&gt;accuracy -->
<g id="edge1" class="edge">
<title>eval_model&#45;&gt;accuracy</title>
<path fill="none" stroke="#002b36" d="M102,-93.98C102,-80.34 102,-61.75 102,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="105.5,-46.1 102,-36.1 98.5,-46.1 105.5,-46.1"/>
<text text-anchor="middle" x="123.5" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">accuracy</text>
<text text-anchor="middle" x="123.5" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;y_test -->
<g id="edge6" class="edge">
<title>get_train_test_split&#45;&gt;y_test</title>
<path fill="none" stroke="#002b36" d="M114.47,-517.22C78.77,-511.36 38.98,-501.91 28,-488 9.4,-464.44 13.06,-428.46 18.78,-403.9"/>
<polygon fill="#002b36" stroke="#002b36" points="22.19,-404.69 21.3,-394.14 15.42,-402.94 22.19,-404.69"/>
<text text-anchor="middle" x="49.5" y="-480" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_test</text>
<text text-anchor="middle" x="49.5" y="-469" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;y_train -->
<g id="edge8" class="edge">
<title>get_train_test_split&#45;&gt;y_train</title>
<path fill="none" stroke="#002b36" d="M239.9,-508.51C243.98,-507.61 248.04,-506.76 252,-506 311,-494.6 328.7,-506.09 386,-488 412.26,-479.71 439.78,-465.45 460.72,-453.27"/>
<polygon fill="#002b36" stroke="#002b36" points="462.74,-456.14 469.55,-448.02 459.17,-450.12 462.74,-456.14"/>
<text text-anchor="middle" x="456.5" y="-480" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y_train</text>
<text text-anchor="middle" x="456.5" y="-469" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;X_test -->
<g id="edge5" class="edge">
<title>get_train_test_split&#45;&gt;X_test</title>
<path fill="none" stroke="#002b36" d="M146.01,-505.96C127.11,-492.4 104.61,-472.32 94,-448 71.54,-396.53 83.87,-329.01 93.68,-292.06"/>
<polygon fill="#002b36" stroke="#002b36" points="97.16,-292.63 96.48,-282.05 90.42,-290.74 97.16,-292.63"/>
<text text-anchor="middle" x="115.5" y="-433" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_test</text>
<text text-anchor="middle" x="115.5" y="-422" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_train_test_split&#45;&gt;X_train -->
<g id="edge7" class="edge">
<title>get_train_test_split&#45;&gt;X_train</title>
<path fill="none" stroke="#002b36" d="M231.48,-505.99C271.26,-491.82 326.61,-471.52 382.99,-448.3"/>
<polygon fill="#002b36" stroke="#002b36" points="384.51,-451.45 392.41,-444.39 381.83,-444.99 384.51,-451.45"/>
<text text-anchor="middle" x="360.5" y="-480" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X_train</text>
<text text-anchor="middle" x="360.5" y="-469" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- train_svc&#45;&gt;model -->
<g id="edge19" class="edge">
<title>train_svc&#45;&gt;model</title>
<path fill="none" stroke="#002b36" d="M533.9,-300.8C488.82,-280.35 416.25,-247.42 372.64,-227.63"/>
<polygon fill="#002b36" stroke="#002b36" points="373.82,-224.32 363.27,-223.37 370.93,-230.69 373.82,-224.32"/>
<text text-anchor="middle" x="514" y="-267" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">svc_model</text>
<text text-anchor="middle" x="514" y="-256" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- get_data -->
<g id="node18" class="node">
<title>get_data</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M235,-738C235,-738 181,-738 181,-738 175,-738 169,-732 169,-726 169,-726 169,-710 169,-710 169,-704 175,-698 181,-698 181,-698 235,-698 235,-698 241,-698 247,-704 247,-710 247,-710 247,-726 247,-726 247,-732 241,-738 235,-738"/>
<text text-anchor="start" x="183" y="-725.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">get_data</text>
<text text-anchor="start" x="177" y="-715" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:get_data</text>
<text text-anchor="start" x="193.5" y="-705" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- get_data&#45;&gt;y -->
<g id="edge23" class="edge">
<title>get_data&#45;&gt;y</title>
<path fill="none" stroke="#002b36" d="M188.15,-697.76C183.75,-692.43 179.63,-686.35 177,-680 173.19,-670.8 171.6,-660.06 171.07,-650.4"/>
<polygon fill="#002b36" stroke="#002b36" points="174.56,-650.02 170.87,-640.09 167.57,-650.16 174.56,-650.02"/>
<text text-anchor="middle" x="198.5" y="-672" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">y</text>
<text text-anchor="middle" x="198.5" y="-661" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- get_data&#45;&gt;X -->
<g id="edge22" class="edge">
<title>get_data&#45;&gt;X</title>
<path fill="none" stroke="#002b36" d="M218.32,-697.98C225.94,-683.94 236.39,-664.66 244.8,-649.17"/>
<polygon fill="#002b36" stroke="#002b36" points="248.03,-650.56 249.72,-640.1 241.88,-647.22 248.03,-650.56"/>
<text text-anchor="middle" x="261.5" y="-672" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">X</text>
<text text-anchor="middle" x="261.5" y="-661" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
</g>
</svg>
