{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Natively handling Python collections\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/topics/05_collections.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "A key benefit of `mandala` over straightforward memoization is that it can make\n", "Python collections (lists, dicts, ...) a native & transparent part of the\n", "memoization process:\n", "\n", "- `@op`s can return collections where each item is a separate `Ref`, so that\n", "later `@op` calls can work with individual elements;\n", "- `@op`s can also accept as input collections where each item is a separate\n", "`Ref`, to e.g. implement aggregation operations over them.\n", "- collections can reuse the storage of their items: if two collections share\n", "some elements, each shared element is stored only once in storage.\n", "- the relationship between a collection and each of its items is a native part\n", "of the computational graph of `@op` calls, and can be propagated automatically\n", "by `ComputationFrame`s. Indeed, **collections are implemented as `@op`s\n", "internally**."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Input/output collections must be explicitly annotated\n", "By default, any collection passed as `@op` input or output will be stored as a\n", "single `Ref` with no structure; the object is **opaque** to the `Storage`\n", "instance. To make the collection **transparent** to the `Storage`, you must\n", "override this behavior explicitly by using a custom type annotation, such as\n", "`MList` for lists, `MDict` for dicts, ...:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:17.224717Z", "iopub.status.busy": "2024-07-11T14:32:17.224393Z", "iopub.status.idle": "2024-07-11T14:32:17.235509Z", "shell.execute_reply": "2024-07-11T14:32:17.234994Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:17.237837Z", "iopub.status.busy": "2024-07-11T14:32:17.237642Z", "iopub.status.idle": "2024-07-11T14:32:19.369771Z", "shell.execute_reply": "2024-07-11T14:32:19.368358Z"}}, "outputs": [], "source": ["from mandala.imports import Storage, op, MList\n", "\n", "storage = Storage()\n", "\n", "@op\n", "def average(nums: ML<PERSON>[float]) -> float:\n", "    return sum(nums) / len(nums)\n", "\n", "with storage:\n", "    a = average([1, 2, 3, 4, 5])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can understand how the list was made transparent to the storage by inspecting\n", "the computation frame:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:19.377817Z", "iopub.status.busy": "2024-07-11T14:32:19.376979Z", "iopub.status.idle": "2024-07-11T14:32:19.739480Z", "shell.execute_reply": "2024-07-11T14:32:19.738605Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"756pt\" height=\"50pt\"\n", " viewBox=\"0.00 0.00 756.00 50.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 46)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-46 752,-46 752,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M736,-38C736,-38 666,-38 666,-38 660,-38 654,-32 654,-26 654,-26 654,-14 654,-14 654,-8 660,-2 666,-2 666,-2 736,-2 736,-2 742,-2 748,-8 748,-14 748,-14 748,-26 748,-26 748,-32 742,-38 736,-38\"/>\n", "<text text-anchor=\"start\" x=\"685\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"662\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values (1 sinks)</text>\n", "</g>\n", "<!-- nums -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>nums</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M409,-38C409,-38 379,-38 379,-38 373,-38 367,-32 367,-26 367,-26 367,-14 367,-14 367,-8 373,-2 379,-2 379,-2 409,-2 409,-2 415,-2 421,-8 421,-14 421,-14 421,-26 421,-26 421,-32 415,-38 409,-38\"/>\n", "<text text-anchor=\"start\" x=\"377.5\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">nums</text>\n", "<text text-anchor=\"start\" x=\"375.5\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">1 values</text>\n", "</g>\n", "<!-- average -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>average</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M563,-40C563,-40 512,-40 512,-40 506,-40 500,-34 500,-28 500,-28 500,-12 500,-12 500,-6 506,0 512,0 512,0 563,0 563,0 569,0 575,-6 575,-12 575,-12 575,-28 575,-28 575,-34 569,-40 563,-40\"/>\n", "<text text-anchor=\"start\" x=\"514.5\" y=\"-27.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">average</text>\n", "<text text-anchor=\"start\" x=\"508\" y=\"-17\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:average</text>\n", "<text text-anchor=\"start\" x=\"523\" y=\"-7\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- nums&#45;&gt;average -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>nums&#45;&gt;average</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M421.28,-20C440.48,-20 467.03,-20 489.8,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"489.96,-23.5 499.96,-20 489.96,-16.5 489.96,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"460.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">nums</text>\n", "<text text-anchor=\"middle\" x=\"460.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- elts -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>elts</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-38C93,-38 12,-38 12,-38 6,-38 0,-32 0,-26 0,-26 0,-14 0,-14 0,-8 6,-2 12,-2 12,-2 93,-2 93,-2 99,-2 105,-8 105,-14 105,-14 105,-26 105,-26 105,-32 99,-38 93,-38\"/>\n", "<text text-anchor=\"start\" x=\"42\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">elts</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sources)</text>\n", "</g>\n", "<!-- __make_list__ -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>__make_list__</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M276,-40C276,-40 196,-40 196,-40 190,-40 184,-34 184,-28 184,-28 184,-12 184,-12 184,-6 190,0 196,0 196,0 276,0 276,0 282,0 288,-6 288,-12 288,-12 288,-28 288,-28 288,-34 282,-40 276,-40\"/>\n", "<text text-anchor=\"start\" x=\"195\" y=\"-27.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">__make_list__</text>\n", "<text text-anchor=\"start\" x=\"192\" y=\"-17\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:__make_list__</text>\n", "<text text-anchor=\"start\" x=\"221.5\" y=\"-7\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">1 calls</text>\n", "</g>\n", "<!-- elts&#45;&gt;__make_list__ -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>elts&#45;&gt;__make_list__</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.03,-20C126.47,-20 151.54,-20 173.91,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"173.97,-23.5 183.97,-20 173.97,-16.5 173.97,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">*elts</text>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- __make_list__&#45;&gt;nums -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>__make_list__&#45;&gt;nums</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M288.08,-20C310.48,-20 336.25,-20 356.65,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"356.78,-23.5 366.78,-20 356.78,-16.5 356.78,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"327.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">list</text>\n", "<text text-anchor=\"middle\" x=\"327.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "<!-- average&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>average&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M575.15,-20C595.4,-20 621,-20 643.74,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"643.96,-23.5 653.96,-20 643.96,-16.5 643.96,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"614.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"614.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(1 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x75c173c5b4f0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(average).expand_all(); cf.draw(verbose=True, orientation='LR')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that the internal `__make_list__` operation was automatically applied to\n", "create a list, which is then the `Ref` passed to `average`. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How collections interact with `ComputationFrame`s\n", "In general, CFs are turned into dataframes that capture the joint history of the\n", "final `Ref`s in the CF. When there are collection `@op`s in the CF, a single\n", "`Ref` (such as the element of `nums` above) can depend on multiple `Ref`s in\n", "another variable (such as the `Ref`s in the `elts` variable).\n", "\n", "We can observe this by taking the dataframe of the above CF:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:19.794173Z", "iopub.status.busy": "2024-07-11T14:32:19.793908Z", "iopub.status.idle": "2024-07-11T14:32:19.833089Z", "shell.execute_reply": "2024-07-11T14:32:19.832358Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    | elts                             | __make_list__                   | nums            | average                   |   var_0 |\n", "|---:|:---------------------------------|:--------------------------------|:----------------|:--------------------------|--------:|\n", "|  0 | ValueCollection([2, 4, 1, 3, 5]) | Call(__make_list__, hid=172...) | [1, 2, 3, 4, 5] | Call(average, hid=38e...) |       3 |\n"]}], "source": ["print(cf.df(values='objs').to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There's only a single row, but in the `elts` column we see a `ValueCollection`\n", "object, indicating that there are multiple `Ref`s in `elts` that are\n", "dependencies of `output_0`."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}