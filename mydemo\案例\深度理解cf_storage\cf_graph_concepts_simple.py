"""
ComputationFrame图概念简化演示
专门展示CF的图结构概念：源节点、汇节点、边、图拓扑等
通过具体案例展示这些概念如何变化以及变化的原因
"""

import os
import sys
import tempfile

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


def print_detailed_cf_analysis(cf: ComputationFrame, title: str):
    """打印CF的详细图分析"""
    print(f"\n📊 {title}:")
    
    # 基本信息
    nodes = list(cf.nodes)
    edges = list(cf.edges())
    sources = list(cf.sources)
    sinks = list(cf.sinks)
    
    print(f"  🔢 节点总数: {len(nodes)}")
    print(f"  📦 变量节点: {len(cf.vnames)} 个 - {list(cf.vnames)}")
    print(f"  ⚙️  函数节点: {len(cf.fnames)} 个 - {list(cf.fnames)}")
    print(f"  🔗 边数量: {len(edges)}")
    print(f"  🌱 源节点: {len(sources)} 个 - {sources}")
    print(f"  🎯 汇节点: {len(sinks)} 个 - {sinks}")
    
    # 详细的边分析
    print(f"\n  🔗 边的详细分析:")
    if edges:
        print(f"    📋 所有边:")
        for i, edge in enumerate(edges[:5]):  # 只显示前5条边
            if len(edge) == 2:
                src, dst = edge
            elif len(edge) == 3:
                src, dst, _ = edge  # 忽略第三个元素
            else:
                src, dst = str(edge[0]), str(edge[1]) if len(edge) > 1 else "unknown"

            src_type = "函数" if src in cf.fnames else "变量"
            dst_type = "函数" if dst in cf.fnames else "变量"
            print(f"      {i+1}. {src}({src_type}) → {dst}({dst_type})")
        if len(edges) > 5:
            print(f"      ... 还有{len(edges)-5}条边")

        # 边类型统计
        func_to_var = 0
        var_to_func = 0
        for edge in edges:
            if len(edge) >= 2:
                src, dst = edge[0], edge[1]
                if src in cf.fnames and dst in cf.vnames:
                    func_to_var += 1
                elif src in cf.vnames and dst in cf.fnames:
                    var_to_func += 1

        print(f"    📊 边类型统计:")
        print(f"      • 函数→变量: {func_to_var} 条 (数据生产)")
        print(f"      • 变量→函数: {var_to_func} 条 (数据消费)")
    else:
        print(f"    ⚠️  无边: 图中没有依赖关系")
    
    # 源节点详细分析
    print(f"\n  🌱 源节点详细分析:")
    print(f"    💡 源节点概念: 没有输入边的节点，代表计算的起始点")
    if sources:
        for src in sources:
            out_neighbors = list(cf.out_neighbors(src))
            src_type = "函数" if src in cf.fnames else "变量"
            print(f"    • {src}({src_type}) → 输出到: {out_neighbors}")
            if src_type == "变量":
                print(f"      💡 变量源节点: 外部输入的数据或参数")
            else:
                print(f"      💡 函数源节点: 无参数的计算函数")
    else:
        print(f"    ⚠️  无源节点: 可能存在循环依赖或图不完整")
    
    # 汇节点详细分析
    print(f"\n  🎯 汇节点详细分析:")
    print(f"    💡 汇节点概念: 没有输出边的节点，代表计算的终点")
    if sinks:
        for sink in sinks:
            in_neighbors = list(cf.in_neighbors(sink))
            sink_type = "函数" if sink in cf.fnames else "变量"
            print(f"    • {sink}({sink_type}) ← 输入来自: {in_neighbors}")
            if sink_type == "变量":
                print(f"      💡 变量汇节点: 最终的计算结果")
            else:
                print(f"      💡 函数汇节点: 无返回值的操作函数")
    else:
        print(f"    ⚠️  无汇节点: 所有节点都有输出，可能存在循环")


def print_graph_change_analysis(cf_before: ComputationFrame, cf_after: ComputationFrame, operation: str):
    """分析图变化"""
    print(f"\n🔄 图变化分析 - {operation}:")

    nodes_before = len(cf_before.nodes)
    nodes_after = len(cf_after.nodes)

    # 安全地获取边数量
    try:
        edges_before = len(list(cf_before.edges()))
        edges_after = len(list(cf_after.edges()))
    except Exception:
        edges_before = 0
        edges_after = 0

    sources_before = len(cf_before.sources)
    sources_after = len(cf_after.sources)
    sinks_before = len(cf_before.sinks)
    sinks_after = len(cf_after.sinks)
    
    print(f"  📊 数量变化:")
    print(f"    • 节点: {nodes_before} → {nodes_after} ({nodes_after - nodes_before:+d})")
    print(f"    • 边: {edges_before} → {edges_after} ({edges_after - edges_before:+d})")
    print(f"    • 源节点: {sources_before} → {sources_after} ({sources_after - sources_before:+d})")
    print(f"    • 汇节点: {sinks_before} → {sinks_after} ({sinks_after - sinks_before:+d})")
    
    print(f"  💡 变化原因分析:")
    if operation == "expand_back":
        print(f"    • 向后扩展发现了生成当前结果的上游计算")
        print(f"    • 新增的源节点是最初的输入参数")
        print(f"    • 新增的边表示数据流向关系")
    elif operation == "expand_forward":
        print(f"    • 向前扩展发现了使用当前结果的下游计算")
        print(f"    • 新增的汇节点是最终的输出结果")
        print(f"    • 新增的边表示数据使用关系")
    elif operation == "expand_all":
        print(f"    • 全方向扩展获得了完整的计算上下文")
        print(f"    • 包含了所有相关的计算步骤和数据流")


def demonstrate_graph_concepts():
    """演示图概念的动态变化"""
    print("🎯 ComputationFrame图概念深度理解演示")
    print("=" * 80)
    
    # 创建临时存储
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "graph_concepts_demo.db")
    storage = Storage(db_path=db_path)
    
    try:
        with storage:
            # 定义基础计算函数
            @op
            def input_data(value: int = 42):
                """输入数据 - 源节点"""
                return value

            @op
            def transform_a(data: int):
                """变换A - 中间节点"""
                return data * 2

            @op
            def transform_b(data: int):
                """变换B - 中间节点"""
                return data + 10

            @op
            def combine(a: int, b: int):
                """合并 - 汇聚节点"""
                return a + b

            print("\n🔧 第一阶段：单节点图（最简单的情况）")
            print("💡 概念说明：只有一个节点的图，既是源节点又是汇节点")
            
            # 创建单节点图
            single_data = input_data(42)
            cf_single = storage.cf(single_data)
            print_detailed_cf_analysis(cf_single, "单节点图")
            
            print("\n🔧 第二阶段：线性图（源→中间→汇）")
            print("💡 概念说明：简单的线性流水线，有明确的源节点和汇节点")
            
            # 创建线性图
            data = input_data(42)
            transformed = transform_a(data)
            cf_linear = storage.cf(transformed)
            print_detailed_cf_analysis(cf_linear, "线性图")
            
            # 分析变化
            print_graph_change_analysis(cf_single, cf_linear, "添加线性变换")
            
            print("\n🔧 第三阶段：分支图（一个源，多个汇）")
            print("💡 概念说明：从一个源分出多个分支，产生多个汇节点")
            
            # 创建分支图
            data = input_data(42)
            branch_a = transform_a(data)
            branch_b = transform_b(data)
            cf_branch = storage.cf([branch_a, branch_b])
            print_detailed_cf_analysis(cf_branch, "分支图")
            
            # 分析变化
            print_graph_change_analysis(cf_linear, cf_branch, "添加分支")
            
            print("\n🔧 第四阶段：汇聚图（多个源，一个汇）")
            print("💡 概念说明：多个独立的源汇聚到一个节点")
            
            # 创建汇聚图
            data1 = input_data(42)
            data2 = input_data(24)
            result = combine(data1, data2)
            cf_converge = storage.cf(result)
            print_detailed_cf_analysis(cf_converge, "汇聚图")
            
        # 在context外部进行扩展操作演示
        print("\n🔧 第五阶段：扩展操作演示")
        print("💡 概念说明：展示expand操作如何改变图的结构")
        
        # 从中间节点创建最小CF
        cf_minimal = storage.cf(transformed)
        print_detailed_cf_analysis(cf_minimal, "最小CF（从transformed创建）")
        
        # 向后扩展
        print("\n📈 向后扩展 - 发现数据来源")
        cf_back = cf_minimal.expand_back(recursive=True)
        print_detailed_cf_analysis(cf_back, "向后扩展后的CF")
        print_graph_change_analysis(cf_minimal, cf_back, "expand_back")
        
        # 向前扩展
        print("\n📉 向前扩展 - 发现数据使用")
        cf_forward = cf_minimal.expand_forward(recursive=True)
        print_detailed_cf_analysis(cf_forward, "向前扩展后的CF")
        print_graph_change_analysis(cf_minimal, cf_forward, "expand_forward")
        
        # 全方向扩展
        print("\n🔄 全方向扩展 - 完整的计算上下文")
        cf_all = cf_minimal.expand_all()
        print_detailed_cf_analysis(cf_all, "全方向扩展后的CF")
        print_graph_change_analysis(cf_back, cf_all, "expand_all")
        
        print("\n🎉 图概念演示完成！")
        print("=" * 80)
        print("💡 关键概念总结:")
        print("  🌱 源节点：没有输入的节点，代表计算的起始点")
        print("    • 变化原因：向后扩展会发现更多的数据源")
        print("  🎯 汇节点：没有输出的节点，代表计算的终点")
        print("    • 变化原因：向前扩展会发现更多的数据使用者")
        print("  🔗 边：节点间的依赖关系，表示数据流向")
        print("    • 变化原因：新的计算步骤产生新的依赖关系")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
            print(f"✅ 清理临时目录: {temp_dir}")
        except Exception as e:
            print(f"⚠️  清理临时目录失败: {e}")


if __name__ == "__main__":
    demonstrate_graph_concepts()
