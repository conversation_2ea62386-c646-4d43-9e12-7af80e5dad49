<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="262pt" height="620pt"
 viewBox="0.00 0.00 261.50 620.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 616)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-616 257.5,-616 257.5,4 -4,4"/>
<!-- data_b -->
<g id="node1" class="node">
<title>data_b</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M148.5,-420C148.5,-420 116.5,-420 116.5,-420 110.5,-420 104.5,-414 104.5,-408 104.5,-408 104.5,-396 104.5,-396 104.5,-390 110.5,-384 116.5,-384 116.5,-384 148.5,-384 148.5,-384 154.5,-384 160.5,-390 160.5,-396 160.5,-396 160.5,-408 160.5,-408 160.5,-414 154.5,-420 148.5,-420"/>
<text text-anchor="start" x="112.5" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data_b</text>
<text text-anchor="start" x="114" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values</text>
</g>
<!-- merge_branches -->
<g id="node10" class="node">
<title>merge_branches</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M214,-326C214,-326 125,-326 125,-326 119,-326 113,-320 113,-314 113,-314 113,-298 113,-298 113,-292 119,-286 125,-286 125,-286 214,-286 214,-286 220,-286 226,-292 226,-298 226,-298 226,-314 226,-314 226,-320 220,-326 214,-326"/>
<text text-anchor="start" x="121" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">merge_branches</text>
<text text-anchor="start" x="121" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:merge_branches</text>
<text text-anchor="start" x="155" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- data_b&#45;&gt;merge_branches -->
<g id="edge6" class="edge">
<title>data_b&#45;&gt;merge_branches</title>
<path fill="none" stroke="#002b36" d="M132.16,-383.9C132.51,-372.24 134.12,-356.66 139.5,-344 140.9,-340.7 142.69,-337.46 144.69,-334.35"/>
<polygon fill="#002b36" stroke="#002b36" points="147.71,-336.15 150.75,-326 142.04,-332.04 147.71,-336.15"/>
<text text-anchor="middle" x="161" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data_b</text>
<text text-anchor="middle" x="161" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- merged_data -->
<g id="node2" class="node">
<title>merged_data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M203.5,-228C203.5,-228 135.5,-228 135.5,-228 129.5,-228 123.5,-222 123.5,-216 123.5,-216 123.5,-204 123.5,-204 123.5,-198 129.5,-192 135.5,-192 135.5,-192 203.5,-192 203.5,-192 209.5,-192 215.5,-198 215.5,-204 215.5,-204 215.5,-216 215.5,-216 215.5,-222 209.5,-228 203.5,-228"/>
<text text-anchor="start" x="131.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">merged_data</text>
<text text-anchor="start" x="151" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values</text>
</g>
<!-- final_process -->
<g id="node8" class="node">
<title>final_process</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M122.5,-134C122.5,-134 48.5,-134 48.5,-134 42.5,-134 36.5,-128 36.5,-122 36.5,-122 36.5,-106 36.5,-106 36.5,-100 42.5,-94 48.5,-94 48.5,-94 122.5,-94 122.5,-94 128.5,-94 134.5,-100 134.5,-106 134.5,-106 134.5,-122 134.5,-122 134.5,-128 128.5,-134 122.5,-134"/>
<text text-anchor="start" x="46.5" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">final_process</text>
<text text-anchor="start" x="44.5" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:final_process</text>
<text text-anchor="start" x="71" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">2 calls</text>
</g>
<!-- merged_data&#45;&gt;final_process -->
<g id="edge2" class="edge">
<title>merged_data&#45;&gt;final_process</title>
<path fill="none" stroke="#002b36" d="M154.72,-191.95C144.82,-180.58 131.46,-165.32 119.5,-152 116.47,-148.62 113.26,-145.09 110.08,-141.6"/>
<polygon fill="#002b36" stroke="#002b36" points="112.57,-139.14 103.23,-134.13 107.41,-143.86 112.57,-139.14"/>
<text text-anchor="middle" x="168" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">merged_data</text>
<text text-anchor="middle" x="168" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- data -->
<g id="node3" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M204,-612C204,-612 123,-612 123,-612 117,-612 111,-606 111,-600 111,-600 111,-588 111,-588 111,-582 117,-576 123,-576 123,-576 204,-576 204,-576 210,-576 216,-582 216,-588 216,-588 216,-600 216,-600 216,-606 210,-612 204,-612"/>
<text text-anchor="start" x="151" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="119" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- branch_b -->
<g id="node7" class="node">
<title>branch_b</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M142.5,-518C142.5,-518 86.5,-518 86.5,-518 80.5,-518 74.5,-512 74.5,-506 74.5,-506 74.5,-490 74.5,-490 74.5,-484 80.5,-478 86.5,-478 86.5,-478 142.5,-478 142.5,-478 148.5,-478 154.5,-484 154.5,-490 154.5,-490 154.5,-506 154.5,-506 154.5,-512 148.5,-518 142.5,-518"/>
<text text-anchor="start" x="87" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">branch_b</text>
<text text-anchor="start" x="82.5" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:branch_b</text>
<text text-anchor="start" x="100" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- data&#45;&gt;branch_b -->
<g id="edge8" class="edge">
<title>data&#45;&gt;branch_b</title>
<path fill="none" stroke="#002b36" d="M150.83,-575.99C146.95,-570.41 142.83,-564.07 139.5,-558 134.21,-548.34 129.27,-537.33 125.21,-527.45"/>
<polygon fill="#002b36" stroke="#002b36" points="128.41,-526.04 121.45,-518.05 121.91,-528.64 128.41,-526.04"/>
<text text-anchor="middle" x="161" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="161" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- branch_a -->
<g id="node9" class="node">
<title>branch_a</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M240.5,-518C240.5,-518 184.5,-518 184.5,-518 178.5,-518 172.5,-512 172.5,-506 172.5,-506 172.5,-490 172.5,-490 172.5,-484 178.5,-478 184.5,-478 184.5,-478 240.5,-478 240.5,-478 246.5,-478 252.5,-484 252.5,-490 252.5,-490 252.5,-506 252.5,-506 252.5,-512 246.5,-518 240.5,-518"/>
<text text-anchor="start" x="185" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">branch_a</text>
<text text-anchor="start" x="180.5" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:branch_a</text>
<text text-anchor="start" x="198" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">1 calls</text>
</g>
<!-- data&#45;&gt;branch_a -->
<g id="edge9" class="edge">
<title>data&#45;&gt;branch_a</title>
<path fill="none" stroke="#002b36" d="M172.87,-575.96C175.97,-570.27 179.41,-563.88 182.5,-558 187.77,-547.98 193.42,-536.93 198.39,-527.14"/>
<polygon fill="#002b36" stroke="#002b36" points="201.52,-528.7 202.9,-518.2 195.27,-525.55 201.52,-528.7"/>
<text text-anchor="middle" x="214" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="214" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- mode -->
<g id="node4" class="node">
<title>mode</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-228C93,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 93,-192 93,-192 99,-192 105,-198 105,-204 105,-204 105,-216 105,-216 105,-222 99,-228 93,-228"/>
<text text-anchor="start" x="36" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">mode</text>
<text text-anchor="start" x="8" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- mode&#45;&gt;final_process -->
<g id="edge3" class="edge">
<title>mode&#45;&gt;final_process</title>
<path fill="none" stroke="#002b36" d="M58.55,-191.76C63.25,-178.37 69.88,-159.5 75.38,-143.82"/>
<polygon fill="#002b36" stroke="#002b36" points="78.79,-144.67 78.8,-134.07 72.19,-142.35 78.79,-144.67"/>
<text text-anchor="middle" x="94" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">mode</text>
<text text-anchor="middle" x="94" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- data_a -->
<g id="node5" class="node">
<title>data_a</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M225,-420C225,-420 194,-420 194,-420 188,-420 182,-414 182,-408 182,-408 182,-396 182,-396 182,-390 188,-384 194,-384 194,-384 225,-384 225,-384 231,-384 237,-390 237,-396 237,-396 237,-408 237,-408 237,-414 231,-420 225,-420"/>
<text text-anchor="start" x="190" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data_a</text>
<text text-anchor="start" x="191" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values</text>
</g>
<!-- data_a&#45;&gt;merge_branches -->
<g id="edge5" class="edge">
<title>data_a&#45;&gt;merge_branches</title>
<path fill="none" stroke="#002b36" d="M202.16,-383.76C196.41,-370.24 188.28,-351.14 181.58,-335.38"/>
<polygon fill="#002b36" stroke="#002b36" points="184.75,-333.9 177.62,-326.07 178.31,-336.64 184.75,-333.9"/>
<text text-anchor="middle" x="215" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data_a</text>
<text text-anchor="middle" x="215" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- var_0 -->
<g id="node6" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M120.5,-36C120.5,-36 50.5,-36 50.5,-36 44.5,-36 38.5,-30 38.5,-24 38.5,-24 38.5,-12 38.5,-12 38.5,-6 44.5,0 50.5,0 50.5,0 120.5,0 120.5,0 126.5,0 132.5,-6 132.5,-12 132.5,-12 132.5,-24 132.5,-24 132.5,-30 126.5,-36 120.5,-36"/>
<text text-anchor="start" x="69.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="46.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sinks)</text>
</g>
<!-- branch_b&#45;&gt;data_b -->
<g id="edge10" class="edge">
<title>branch_b&#45;&gt;data_b</title>
<path fill="none" stroke="#002b36" d="M118.14,-477.98C120.78,-464.2 124.38,-445.39 127.32,-430.05"/>
<polygon fill="#002b36" stroke="#002b36" points="130.78,-430.58 129.23,-420.1 123.91,-429.26 130.78,-430.58"/>
<text text-anchor="middle" x="148" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="148" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- final_process&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>final_process&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M85.5,-93.98C85.5,-80.34 85.5,-61.75 85.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="89,-46.1 85.5,-36.1 82,-46.1 89,-46.1"/>
<text text-anchor="middle" x="107" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="107" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- branch_a&#45;&gt;data_a -->
<g id="edge7" class="edge">
<title>branch_a&#45;&gt;data_a</title>
<path fill="none" stroke="#002b36" d="M211.89,-477.98C211.46,-464.34 210.86,-445.75 210.38,-430.5"/>
<polygon fill="#002b36" stroke="#002b36" points="213.86,-429.98 210.05,-420.1 206.87,-430.2 213.86,-429.98"/>
<text text-anchor="middle" x="232" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="232" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- merge_branches&#45;&gt;merged_data -->
<g id="edge4" class="edge">
<title>merge_branches&#45;&gt;merged_data</title>
<path fill="none" stroke="#002b36" d="M169.5,-285.98C169.5,-272.34 169.5,-253.75 169.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="173,-238.1 169.5,-228.1 166,-238.1 173,-238.1"/>
<text text-anchor="middle" x="191" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="191" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
</g>
</svg>
