{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Changing `@op`s and managing versions\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/topic/04_versions.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "It should be easy to change your code and have the storage respond in a correct\n", "way (e.g., recompute a call **only** when the logic behind it has changed).\n", "`mandala` provides the following mechanisms to do that:\n", "\n", "- **automatic per-call dependency tracking**: every `@op` call records the\n", "functions it called along the way. This allows the `storage` to automatically\n", "know, given some inputs, whether a past call for these inputs can be reused \n", "given the current state of the code. This is a very fine-grained notion of\n", "reuse.\n", "- **marking changes as breaking vs non-breaking**: when a change to an `@op` or\n", "its dependencies is detected, you can choose to mark it as breaking the calls that depend on it\n", "or not. However, **breaking changes are generally more fool-proof**; see [caveats of non-breaking changes](#caveats-of-marking-changes-as-non-breaking).\n", "- **content-based versioning**: the current state of the codebase uniquely\n", "determines the version each `@op` is in. There are no arbitrary names attached\n", "to versions. The versions for each `@op` can be inspected in a `git`-like data\n", "structure."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enabling and configuring versioning\n", "Passing a value to the `deps_path` parameter of the `Storage` class enables\n", "dependency tracking and versioning. This means that any time a memoized function\n", "*actually executes* (instead of reusing a past call's results), it keeps track\n", "of the functions and global variables it accesses along the way. \n", "\n", "Usually, the functions we want to track are limited to user-defined ones (you\n", "typically don't want to track changes in installed libraries!):\n", "\n", "- Setting `deps_path` to `\"__main__\"` will only look for dependencies `f`\n", "defined in the current interactive session or process (as determined by\n", "`f.__module__`).\n", "- Setting it to a folder will only look for dependencies defined in this folder."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:25.233275Z", "iopub.status.busy": "2024-07-11T14:31:25.232874Z", "iopub.status.idle": "2024-07-11T14:31:25.259490Z", "shell.execute_reply": "2024-07-11T14:31:25.258016Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:25.265198Z", "iopub.status.busy": "2024-07-11T14:31:25.264749Z", "iopub.status.idle": "2024-07-11T14:31:27.091950Z", "shell.execute_reply": "2024-07-11T14:31:27.091042Z"}}, "outputs": [], "source": ["from mandala.imports import Storage, op, track\n", "\n", "storage = Storage(deps_path='__main__')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The `@track` decorator\n", "The most efficient and reliable implementation of dependency tracking currently\n", "requires you to explicitly put `@track` on non-memoized functions and classes\n", "you want to track. This limitation may be lifted in the future, but at the cost\n", "of more magic (i.e., automatically applying the decorator to functions in the\n", "current local scope that originate in given paths).\n", "\n", "The alternative (experimental) decorator implementation is based on\n", "`sys.settrace`. Limitations are described in this [blog\n", "post](https://amakelov.github.io/blog/deps/#syssettrace))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Examining the captured versions\n", "Let's run a small ML pipeline, where we optionally apply scaling to the data,\n", "introducing a non-`@op` dependency for some of the calls:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:27.095155Z", "iopub.status.busy": "2024-07-11T14:31:27.094781Z", "iopub.status.idle": "2024-07-11T14:31:28.405723Z", "shell.execute_reply": "2024-07-11T14:31:28.405041Z"}}, "outputs": [], "source": ["from sklearn.datasets import load_digits\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "N_CLASS = 10\n", "\n", "@track # to track a non-memoized function as a dependency\n", "def scale_data(X):\n", "    return StandardScaler(with_mean=True, with_std=False).fit_transform(X)\n", "\n", "@op\n", "def load_data():\n", "    X, y = load_digits(n_class=N_CLASS, return_X_y=True)\n", "    return X, y\n", "\n", "@op\n", "def train_model(X, y, scale=False):\n", "    if scale:\n", "        X = scale_data(X)\n", "    return LogisticRegression(max_iter=1000, solver='liblinear').fit(X, y)\n", "\n", "@op\n", "def eval_model(model, X, y, scale=False):\n", "    if scale:\n", "        X = scale_data(X)\n", "    return model.score(X, y)\n", "\n", "with storage:\n", "    X, y = load_data()\n", "    for scale in [Fals<PERSON>, True]:\n", "        model = train_model(X, y, scale=scale)\n", "        acc = eval_model(model, X, y, scale=scale)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now `train_model` and `eval_model` will each have two versions - one that\n", "depends on `scale_data` and one that doesn't. You can confirm this by calling\n", "e.g. `storage.versions(train_model)`:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:28.409329Z", "iopub.status.busy": "2024-07-11T14:31:28.408828Z", "iopub.status.idle": "2024-07-11T14:31:28.505735Z", "shell.execute_reply": "2024-07-11T14:31:28.504919Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function train_model from module __main__</span><span style=\"background-color: #fdf6e3\">                                      </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=db93a1e9c60fb37868575845a7afe47d</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=2acaa8919ddd4b5d8846f1f2d15bc971</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">train_model</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(X, y, scale</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">):</span><span style=\"background-color: #fdf6e3\">                                                                            </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">if</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale:</span><span style=\"background-color: #fdf6e3\">                                                                                                  </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">        X </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> LogisticRegression(max_iter</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1000</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, solver</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">'liblinear'</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">fit(X, y)</span><span style=\"background-color: #fdf6e3\">                                     </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function train_model from module __main__</span><span style=\"background-color: #fdf6e3\">                                      </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=2717c55fbbbb60442535a8dea0c81f67</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=4674057d19bbf217687dd9dabe01df36</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@track</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\"># to track a non-memoized function as a dependency</span><span style=\"background-color: #fdf6e3\">                                                      </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">scale_data</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(X):</span><span style=\"background-color: #fdf6e3\">                                                                                             </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> StandardScaler(with_mean</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">True</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, with_std</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">fit_transform(X)</span><span style=\"background-color: #fdf6e3\">                                     </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">train_model</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(X, y, scale</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">):</span><span style=\"background-color: #fdf6e3\">                                                                            </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">if</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale:</span><span style=\"background-color: #fdf6e3\">                                                                                                  </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">        X </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> LogisticRegression(max_iter</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1000</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, solver</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">'liblinear'</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">fit(X, y)</span><span style=\"background-color: #fdf6e3\">                                     </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function train_model from module __main__\u001b[0m\u001b[48;2;253;246;227m                                      \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=db93a1e9c60fb37868575845a7afe47d\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=2acaa8919ddd4b5d8846f1f2d15bc971\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227mtrain_model\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                            \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227mif\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                  \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m        \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON>urn\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mLogisticRegression\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmax_iter\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1000\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227msolver\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m'\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mliblinear\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m'\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mfit\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                     \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function train_model from module __main__\u001b[0m\u001b[48;2;253;246;227m                                      \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=2717c55fbbbb60442535a8dea0c81f67\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=4674057d19bbf217687dd9dabe01df36\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@track\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[3;38;2;147;161;161;48;2;253;246;227m# to track a non-memoized function as a dependency\u001b[0m\u001b[48;2;253;246;227m                                                      \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                             \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON>urn\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mStandardScaler\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mwith_mean\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mTrue\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mwith_std\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mfit_transform\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                     \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227mtrain_model\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                            \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227mif\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                  \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m        \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON>urn\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mLogisticRegression\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmax_iter\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1000\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227msolver\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m'\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mliblinear\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m'\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mfit\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                     \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["storage.versions(train_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Making changes and sorting them into breaking and non-breaking\n", "Now suppose we make some changes and re-run:\n", "\n", "- we change the value of the global variable `N_CLASS`;\n", "- we change the code of `scale_data` in a semantically meaningful (i.e.,\n", "breaking) way\n", "- we change the code of `eval_model` in a \"cosmetic\" way that can be considered\n", "non-breaking.\n", "\n", "When entering the `storage` block, the storage will detect the changes in\n", "the tracked components, and for each change will present you with the functions\n", "affected:\n", "\n", "- `N_CLASS` is a dependency for `load_data`;\n", "- `scale_data` is a dependency for the calls to `train_model` and `eval_model`\n", "  which had `scale=True`;\n", "- `eval_model` is a dependency for itself."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:28.549950Z", "iopub.status.busy": "2024-07-11T14:31:28.549638Z", "iopub.status.idle": "2024-07-11T14:31:28.821923Z", "shell.execute_reply": "2024-07-11T14:31:28.821045Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHANGE DETECTED in N_CLASS from module __main__\n", "Dependent components:\n", "  Version of \"load_data\" from module \"__main__\" (content: 426c4c8c56d7c0d6374095c7d4a4974f, semantic: 7d0732b9bfb31e5e2211e0122651a624)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">1 </span><span style=\"color: #dc322f; text-decoration-color: #dc322f; background-color: #fdf6e3\">-10</span><span style=\"background-color: #fdf6e3\">                                                                                                        </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">2 </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">+5</span><span style=\"background-color: #fdf6e3\">                                                                                                         </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m1 \u001b[0m\u001b[38;2;220;50;47;48;2;253;246;227m-10\u001b[0m\u001b[48;2;253;246;227m                                                                                                        \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m2 \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m+5\u001b[0m\u001b[48;2;253;246;227m                                                                                                         \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Does this change require recomputation of dependent calls?\n", "WARNING: if the change created new dependencies and you choose 'no', you should add them by hand or risk missing changes in them.\n", "Answer: [y]es/[n]o/[a]bort \n", "You answered: \"y\"\n", "CHANGE DETECTED in eval_model from module __main__\n", "Dependent components:\n", "  Version of \"eval_model\" from module \"__main__\" (content: 955b2a683de8dacf624047c0e020140a, semantic: c847d6dc3f23c176e6c8bf9e7006576a)\n", "  Version of \"eval_model\" from module \"__main__\" (content: 5bdcd6ffc4888990d8922aa85795198d, semantic: 4e1d702e9797ebba156831294de46425)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">1 </span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">     if scale:</span><span style=\"background-color: #fdf6e3\">                                                                                             </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">2 </span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">         X = scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                 </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">3 </span><span style=\"color: #dc322f; text-decoration-color: #dc322f; background-color: #fdf6e3\">-    return model.score(X, y)</span><span style=\"background-color: #fdf6e3\">                                                                              </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">4 </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">+    return round(model.score(X, y), 2)</span><span style=\"background-color: #fdf6e3\">                                                                    </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m1 \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m    if scale:\u001b[0m\u001b[48;2;253;246;227m                                                                                             \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m2 \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m        X = scale_data(X)\u001b[0m\u001b[48;2;253;246;227m                                                                                 \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m3 \u001b[0m\u001b[38;2;220;50;47;48;2;253;246;227m-    return model.score(X, y)\u001b[0m\u001b[48;2;253;246;227m                                                                              \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m4 \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m+    return round(model.score(X, y), 2)\u001b[0m\u001b[48;2;253;246;227m                                                                    \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Does this change require recomputation of dependent calls?\n", "WARNING: if the change created new dependencies and you choose 'no', you should add them by hand or risk missing changes in them.\n", "Answer: [y]es/[n]o/[a]bort \n", "You answered: \"n\"\n", "CHANGE DETECTED in scale_data from module __main__\n", "Dependent components:\n", "  Version of \"train_model\" from module \"__main__\" (content: 2717c55fbbbb60442535a8dea0c81f67, semantic: 4674057d19bbf217687dd9dabe01df36)\n", "  Version of \"eval_model\" from module \"__main__\" (content: 5bdcd6ffc4888990d8922aa85795198d, semantic: 4e1d702e9797ebba156831294de46425)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">1 </span><span style=\"color: #dc322f; text-decoration-color: #dc322f; background-color: #fdf6e3\">-@track # to track a non-memoized function as a dependency</span><span style=\"background-color: #fdf6e3\">                                                 </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">2 </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">+@track</span><span style=\"background-color: #fdf6e3\">                                                                                                    </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">3 </span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> def scale_data(X):</span><span style=\"background-color: #fdf6e3\">                                                                                        </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">4 </span><span style=\"color: #dc322f; text-decoration-color: #dc322f; background-color: #fdf6e3\">-    return StandardScaler(with_mean=True, with_std=False).fit_transform(X)</span><span style=\"background-color: #fdf6e3\">                                </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">5 </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">+    return StandardScaler(with_mean=True, with_std=True).fit_transform(X)</span><span style=\"background-color: #fdf6e3\">                                 </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m1 \u001b[0m\u001b[38;2;220;50;47;48;2;253;246;227m-@track # to track a non-memoized function as a dependency\u001b[0m\u001b[48;2;253;246;227m                                                 \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m2 \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m+@track\u001b[0m\u001b[48;2;253;246;227m                                                                                                    \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m3 \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mdef scale_data(X):\u001b[0m\u001b[48;2;253;246;227m                                                                                        \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m4 \u001b[0m\u001b[38;2;220;50;47;48;2;253;246;227m-    return StandardScaler(with_mean=True, with_std=False).fit_transform(X)\u001b[0m\u001b[48;2;253;246;227m                                \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m5 \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m+    return StandardScaler(with_mean=True, with_std=True).fit_transform(X)\u001b[0m\u001b[48;2;253;246;227m                                 \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Does this change require recomputation of dependent calls?\n", "WARNING: if the change created new dependencies and you choose 'no', you should add them by hand or risk missing changes in them.\n", "Answer: [y]es/[n]o/[a]bort \n", "You answered: \"y\"\n"]}], "source": ["from mandala.utils import mock_input\n", "from unittest.mock import patch\n", "\n", "N_CLASS = 5\n", "\n", "@track\n", "def scale_data(X):\n", "    return StandardScaler(with_mean=True, with_std=True).fit_transform(X)\n", "\n", "@op\n", "def eval_model(model, X, y, scale=False):\n", "    if scale:\n", "        X = scale_data(X)\n", "    return round(model.score(X, y), 2)\n", "\n", "answers = ['y', 'n', 'y']\n", "\n", "with patch('builtins.input', mock_input(answers)):\n", "    with storage:\n", "        X, y = load_data()\n", "        for scale in [Fals<PERSON>, True]:\n", "            model = train_model(X, y, scale=scale)\n", "            acc = eval_model(model, X, y, scale=scale)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When a change is detected, the UI:\n", "\n", "- shows the diffs in each function,\n", "- gives you a list of which `@op`s' versions are affected by each change\n", "- lets you choose if the change is breaking or non-breaking\n", "\n", "We can check what happened by constructing a computation frame:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:28.826129Z", "iopub.status.busy": "2024-07-11T14:31:28.825755Z", "iopub.status.idle": "2024-07-11T14:31:29.312709Z", "shell.execute_reply": "2024-07-11T14:31:29.311997Z"}}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"326pt\" height=\"526pt\"\n", " viewBox=\"0.00 0.00 325.50 526.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 522)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-522 321.5,-522 321.5,4 -4,4\"/>\n", "<!-- model -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>model</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M182.5,-228C182.5,-228 152.5,-228 152.5,-228 146.5,-228 140.5,-222 140.5,-216 140.5,-216 140.5,-204 140.5,-204 140.5,-198 146.5,-192 152.5,-192 152.5,-192 182.5,-192 182.5,-192 188.5,-192 194.5,-198 194.5,-204 194.5,-204 194.5,-216 194.5,-216 194.5,-222 188.5,-228 182.5,-228\"/>\n", "<text text-anchor=\"start\" x=\"149.5\" y=\"-212.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"start\" x=\"149\" y=\"-202\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values</text>\n", "</g>\n", "<!-- eval_model -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>eval_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M183.5,-134C183.5,-134 63.5,-134 63.5,-134 57.5,-134 51.5,-128 51.5,-122 51.5,-122 51.5,-106 51.5,-106 51.5,-100 57.5,-94 63.5,-94 63.5,-94 183.5,-94 183.5,-94 189.5,-94 195.5,-100 195.5,-106 195.5,-106 195.5,-122 195.5,-122 195.5,-128 189.5,-134 183.5,-134\"/>\n", "<text text-anchor=\"start\" x=\"90.5\" y=\"-121.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">eval_model</text>\n", "<text text-anchor=\"start\" x=\"59.5\" y=\"-111\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:eval_model (3 versions)</text>\n", "<text text-anchor=\"start\" x=\"109\" y=\"-101\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- model&#45;&gt;eval_model -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>model&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M159.43,-191.76C153.1,-178.24 144.16,-159.14 136.78,-143.38\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"139.84,-141.64 132.43,-134.07 133.5,-144.61 139.84,-141.64\"/>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-166\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">model</text>\n", "<text text-anchor=\"middle\" x=\"173\" y=\"-155\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- y -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>y</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M47.5,-420C47.5,-420 17.5,-420 17.5,-420 11.5,-420 5.5,-414 5.5,-408 5.5,-408 5.5,-396 5.5,-396 5.5,-390 11.5,-384 17.5,-384 17.5,-384 47.5,-384 47.5,-384 53.5,-384 59.5,-390 59.5,-396 59.5,-396 59.5,-408 59.5,-408 59.5,-414 53.5,-420 47.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"29\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"start\" x=\"14\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- train_model -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>train_model</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M259,-326C259,-326 138,-326 138,-326 132,-326 126,-320 126,-314 126,-314 126,-298 126,-298 126,-292 132,-286 138,-286 138,-286 259,-286 259,-286 265,-286 271,-292 271,-298 271,-298 271,-314 271,-314 271,-320 265,-326 259,-326\"/>\n", "<text text-anchor=\"start\" x=\"164.5\" y=\"-313.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">train_model</text>\n", "<text text-anchor=\"start\" x=\"134\" y=\"-303\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:train_model (3 versions)</text>\n", "<text text-anchor=\"start\" x=\"184\" y=\"-293\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">4 calls</text>\n", "</g>\n", "<!-- y&#45;&gt;train_model -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>y&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M47.3,-383.77C58.82,-371.27 75.78,-354.75 93.5,-344 102.27,-338.68 111.91,-333.98 121.69,-329.87\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"123.1,-333.07 131.08,-326.1 120.49,-326.57 123.1,-333.07\"/>\n", "<text text-anchor=\"middle\" x=\"115\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"115\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- y&#45;&gt;eval_model -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>y&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M35.12,-383.97C39.8,-355.13 50.63,-295.01 66.5,-246 78.29,-209.59 96.57,-169.49 109.24,-143.36\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"112.53,-144.59 113.79,-134.07 106.25,-141.51 112.53,-144.59\"/>\n", "<text text-anchor=\"middle\" x=\"88\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"88\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- var_0 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M158.5,-36C158.5,-36 88.5,-36 88.5,-36 82.5,-36 76.5,-30 76.5,-24 76.5,-24 76.5,-12 76.5,-12 76.5,-6 82.5,0 88.5,0 88.5,0 158.5,0 158.5,0 164.5,0 170.5,-6 170.5,-12 170.5,-12 170.5,-24 170.5,-24 170.5,-30 164.5,-36 158.5,-36\"/>\n", "<text text-anchor=\"start\" x=\"107.5\" y=\"-20.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"84.5\" y=\"-10\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">4 values (4 sinks)</text>\n", "</g>\n", "<!-- X -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>X</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M119.5,-420C119.5,-420 89.5,-420 89.5,-420 83.5,-420 77.5,-414 77.5,-408 77.5,-408 77.5,-396 77.5,-396 77.5,-390 83.5,-384 89.5,-384 89.5,-384 119.5,-384 119.5,-384 125.5,-384 131.5,-390 131.5,-396 131.5,-396 131.5,-408 131.5,-408 131.5,-414 125.5,-420 119.5,-420\"/>\n", "<text text-anchor=\"start\" x=\"100\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"start\" x=\"86\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values</text>\n", "</g>\n", "<!-- X&#45;&gt;train_model -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>X&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M121.74,-383.76C135.9,-369.6 156.17,-349.33 172.33,-333.17\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"174.83,-335.62 179.43,-326.07 169.88,-330.67 174.83,-335.62\"/>\n", "<text text-anchor=\"middle\" x=\"182\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"182\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- X&#45;&gt;eval_model -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>X&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.63,-383.97C108.77,-336.76 117.6,-203.82 121.55,-144.37\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"125.06,-144.37 122.23,-134.16 118.07,-143.91 125.06,-144.37\"/>\n", "<text text-anchor=\"middle\" x=\"137\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">X</text>\n", "<text text-anchor=\"middle\" x=\"137\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- scale -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>scale</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M300,-420C300,-420 219,-420 219,-420 213,-420 207,-414 207,-408 207,-408 207,-396 207,-396 207,-390 213,-384 219,-384 219,-384 300,-384 300,-384 306,-384 312,-390 312,-396 312,-396 312,-408 312,-408 312,-414 306,-420 300,-420\"/>\n", "<text text-anchor=\"start\" x=\"244\" y=\"-404.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">scale</text>\n", "<text text-anchor=\"start\" x=\"215\" y=\"-394\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">2 values (2 sources)</text>\n", "</g>\n", "<!-- scale&#45;&gt;train_model -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>scale&#45;&gt;train_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M248.31,-383.76C239.37,-369.99 226.68,-350.42 216.34,-334.49\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"219.26,-332.56 210.88,-326.07 213.38,-336.37 219.26,-332.56\"/>\n", "<text text-anchor=\"middle\" x=\"258\" y=\"-358\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">scale</text>\n", "<text text-anchor=\"middle\" x=\"258\" y=\"-347\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- scale&#45;&gt;eval_model -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>scale&#45;&gt;eval_model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M274.21,-383.93C277.99,-378.54 281.54,-372.33 283.5,-366 287.79,-352.17 287.31,-307.98 280.5,-286 259.84,-219.31 250.06,-199.08 198.5,-152 193.39,-147.33 187.57,-143.12 181.5,-139.35\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"182.88,-136.11 172.47,-134.15 179.39,-142.17 182.88,-136.11\"/>\n", "<text text-anchor=\"middle\" x=\"296\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">scale</text>\n", "<text text-anchor=\"middle\" x=\"296\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- train_model&#45;&gt;model -->\n", "<g id=\"edge11\" class=\"edge\">\n", "<title>train_model&#45;&gt;model</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M192.23,-285.98C187.64,-272.07 181.36,-253.03 176.28,-237.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"179.59,-236.5 173.14,-228.1 172.95,-238.69 179.59,-236.5\"/>\n", "<text text-anchor=\"middle\" x=\"208\" y=\"-260\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"208\" y=\"-249\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- eval_model&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval_model&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M123.5,-93.98C123.5,-80.34 123.5,-61.75 123.5,-46.5\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"127,-46.1 123.5,-36.1 120,-46.1 127,-46.1\"/>\n", "<text text-anchor=\"middle\" x=\"145\" y=\"-68\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"145\" y=\"-57\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(4 values)</text>\n", "</g>\n", "<!-- load_data -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>load_data</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M125,-518C125,-518 12,-518 12,-518 6,-518 0,-512 0,-506 0,-506 0,-490 0,-490 0,-484 6,-478 12,-478 12,-478 125,-478 125,-478 131,-478 137,-484 137,-490 137,-490 137,-506 137,-506 137,-512 131,-518 125,-518\"/>\n", "<text text-anchor=\"start\" x=\"40\" y=\"-505.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">load_data</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-495\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:load_data (2 versions)</text>\n", "<text text-anchor=\"start\" x=\"54\" y=\"-485\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">2 calls</text>\n", "</g>\n", "<!-- load_data&#45;&gt;y -->\n", "<g id=\"edge10\" class=\"edge\">\n", "<title>load_data&#45;&gt;y</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M47.93,-477.88C43.4,-472.55 39.16,-466.44 36.5,-460 32.7,-450.81 31.27,-440.07 30.93,-430.41\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"34.43,-430.11 30.94,-420.1 27.43,-430.1 34.43,-430.11\"/>\n", "<text text-anchor=\"middle\" x=\"58\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_1</text>\n", "<text text-anchor=\"middle\" x=\"58\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "<!-- load_data&#45;&gt;X -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>load_data&#45;&gt;X</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M75.79,-477.98C81.11,-464.07 88.4,-445.03 94.31,-429.61\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"97.64,-430.69 97.95,-420.1 91.11,-428.18 97.64,-430.69\"/>\n", "<text text-anchor=\"middle\" x=\"113\" y=\"-452\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"113\" y=\"-441\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(2 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x764a80c66ad0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cf = storage.cf(eval_model).expand_all()\n", "cf.draw(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that `load_data` has two versions in use, whereas `train_model` and\n", "`eval_model` both have three. Which ones? Again, call `versions` to find out.\n", "For example, with `eval_model`, we have 4 different content versions, that \n", "overall span 3 semantically different versions:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:31:29.315448Z", "iopub.status.busy": "2024-07-11T14:31:29.315039Z", "iopub.status.idle": "2024-07-11T14:31:29.358010Z", "shell.execute_reply": "2024-07-11T14:31:29.357264Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function eval_model from module __main__</span><span style=\"background-color: #fdf6e3\">                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=955b2a683de8dacf624047c0e020140a</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=c847d6dc3f23c176e6c8bf9e7006576a</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">eval_model</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(model, X, y, scale</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">):</span><span style=\"background-color: #fdf6e3\">                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">if</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale:</span><span style=\"background-color: #fdf6e3\">                                                                                                  </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">        X </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> model</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">score(X, y)</span><span style=\"background-color: #fdf6e3\">                                                                                   </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function eval_model from module __main__</span><span style=\"background-color: #fdf6e3\">                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=5bdcd6ffc4888990d8922aa85795198d</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=4e1d702e9797ebba156831294de46425</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">eval_model</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(model, X, y, scale</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">):</span><span style=\"background-color: #fdf6e3\">                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">if</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale:</span><span style=\"background-color: #fdf6e3\">                                                                                                  </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">        X </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> model</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">score(X, y)</span><span style=\"background-color: #fdf6e3\">                                                                                   </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@track</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\"># to track a non-memoized function as a dependency</span><span style=\"background-color: #fdf6e3\">                                                      </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">scale_data</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(X):</span><span style=\"background-color: #fdf6e3\">                                                                                             </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> StandardScaler(with_mean</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">True</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, with_std</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">fit_transform(X)</span><span style=\"background-color: #fdf6e3\">                                     </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function eval_model from module __main__</span><span style=\"background-color: #fdf6e3\">                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=b50e3e2529b811e226d2bb39a572a5e4</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=c847d6dc3f23c176e6c8bf9e7006576a</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">eval_model</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(model, X, y, scale</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">):</span><span style=\"background-color: #fdf6e3\">                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">if</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale:</span><span style=\"background-color: #fdf6e3\">                                                                                                  </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">        X </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> model</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">score(X, y)</span><span style=\"background-color: #fdf6e3\">                                                                                   </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function eval_model from module __main__</span><span style=\"background-color: #fdf6e3\">                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=136129b20d9a3a3795e88ba8cf89b115</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=f2573de2a6c25b390fc86d665ea85687</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">eval_model</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(model, X, y, scale</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">False</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">):</span><span style=\"background-color: #fdf6e3\">                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">if</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale:</span><span style=\"background-color: #fdf6e3\">                                                                                                  </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">        X </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> scale_data(X)</span><span style=\"background-color: #fdf6e3\">                                                                                      </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> model</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">score(X, y)</span><span style=\"background-color: #fdf6e3\">                                                                                   </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@track</span><span style=\"background-color: #fdf6e3\">                                                                                                         </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">scale_data</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(X):</span><span style=\"background-color: #fdf6e3\">                                                                                             </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> StandardScaler(with_mean</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">True</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, with_std</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">True</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">fit_transform(X)</span><span style=\"background-color: #fdf6e3\">                                      </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function eval_model from module __main__\u001b[0m\u001b[48;2;253;246;227m                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=955b2a683de8dacf624047c0e020140a\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=c847d6dc3f23c176e6c8bf9e7006576a\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227meval_model\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227mif\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                  \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m        \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscore\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                   \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function eval_model from module __main__\u001b[0m\u001b[48;2;253;246;227m                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=5bdcd6ffc4888990d8922aa85795198d\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=4e1d702e9797ebba156831294de46425\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227meval_model\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227mif\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                  \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m        \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscore\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                   \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@track\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[3;38;2;147;161;161;48;2;253;246;227m# to track a non-memoized function as a dependency\u001b[0m\u001b[48;2;253;246;227m                                                      \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                             \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON>urn\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mStandardScaler\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mwith_mean\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mTrue\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mwith_std\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mfit_transform\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                     \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function eval_model from module __main__\u001b[0m\u001b[48;2;253;246;227m                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=b50e3e2529b811e226d2bb39a572a5e4\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=c847d6dc3f23c176e6c8bf9e7006576a\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227meval_model\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227mif\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                  \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m        \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscore\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                   \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function eval_model from module __main__\u001b[0m\u001b[48;2;253;246;227m                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=136129b20d9a3a3795e88ba8cf89b115\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=f2573de2a6c25b390fc86d665ea85687\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227meval_model\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mFalse\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227mif\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                  \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m        \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                      \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mmodel\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mscore\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                   \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@track\u001b[0m\u001b[48;2;253;246;227m                                                                                                         \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227mscale_data\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                             \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON>urn\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mStandardScaler\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mwith_mean\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mTrue\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mwith_std\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227mTrue\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mfit_transform\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mX\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                      \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["storage.versions(eval_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Additional notes\n", "\n", "### So what really is a version of an `@op`?\n", "A **version** for an `@op` is a collection of\n", "\n", "- (hashes of) the source code of functions and methods\n", "- (hashes of) values of global variables\n", "\n", "at the time when a call to this `@op` was executed. Even if you don't change\n", "anything in the code, a single function can have multiple versions if it invokes\n", "different dependencies for different calls. \n", "\n", "### Going back in time\n", "Since the versioning system is content-based, simply restoring an old state of\n", "the code makes the storage automatically recognize which \"world\" it's in, and\n", "which calls are memoized in this world.\n", "\n", "### Caveats of marking changes as non-breaking\n", "The main motivation for allowing non-breaking changes is to maintain the storage\n", "when doing routine code improvements (refactoring, comments, logging).\n", "\n", "**However**, non-semantic changes should be applied with care. Apart from being\n", "prone to errors (you wrongly conclude that a change has no effect on semantics\n", "when it does), they can also introduce **invisible dependencies**: suppose you\n", "factor a function out of some dependency and mark the change non-semantic. Then\n", "the newly extracted function may in reality be a dependency of the existing\n", "calls, but this goes unnoticed by the system. Consequently, changes in this \n", "dependency may go unnoticed by the versioning algorithm."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}