---
description: 
globs: 
alwaysApply: true
---

# 优化的项目需求通用模板（综合开发模式：TDD+CDD+DDD）

## 1. 架构规范

## 1.1 整洁架构与综合开发模式概述
- **设计原则**：采用整洁架构（Clean Architecture）设计原则，结合领域驱动设计（DDD）、测试驱动开发（TDD）、用例驱动开发（CDD）。  
- **分层结构**：`领域层 → 应用层 → 适配器层 → 基础设施层`。  
- **依赖规则**：外层依赖内层，内层不依赖外层。  
- **核心目标**：业务规则独立于框架、UI、数据库或外部代理，确保通过测试与用例驱动开发验证功能完整性。  
- **综合开发模式**：  
  - **领域驱动设计（DDD）**：以业务领域为核心，定义实体、值对象、聚合根和领域服务。  
  - **测试驱动开发（TDD）**：先编写测试用例，验证功能逻辑后再实现代码。  
  - **用例驱动开发（CDD）**：通过具体使用场景（用例）驱动功能设计与验证，确保功能符合实际需求。  



---

## 1.2 架构分层详解

| 层级名称          | 职责                                                                 |
|-------------------|----------------------------------------------------------------------|
| **领域层（Domain）** | 定义业务实体、核心规则、值对象和事件（如 `Document`, `FileChangeEvent`）。 |
| **应用层（Application）** | 实现用例逻辑，通过接口协调内外层（如 `FileManagementUseCase`）。          |
| **适配器层（Interface Adapters）** | 转换数据格式，适配输入/输出（如CLI解析、数据库接口实现）。             |
| **基础设施层（Infrastructure）** | 提供技术实现（如数据库驱动、加密模块）。                              |

---

## 1.3 设计原则

| 原则名称         | 描述                                                                 |
| ------------ | ------------------------------------------------------------------ |
| **关注点分离**    | 每层仅负责单一职责（如领域层只处理业务逻辑）。                                            |
| **依赖注入**     | 通过接口注入依赖（如应用层通过构造函数注入 `PersistencePort`）。                          |
| **接口隔离**     | 接口应简洁，仅暴露必要方法（如 `PersistencePort` 只定义 `save` 和 `load`）。            |
| **单一职责**     | 每个类/模块仅有一个变更理由（如 `Document` 类仅负责文档数据，不处理存储）。                       |
| **可测试性**     | 内层代码易于单元测试，外层通过模拟（Mock）实现测试。                                       |
| **领域核心**     | 业务实体与规则完全独立于技术实现。                                                  |
| **严格分层**     | 依赖方向：`Infrastructure → InterfaceAdapters → Application → Domain`。  |
| **单向依赖**     | 外层可依赖内层，禁止反向依赖。                                                    |
| **接口契约**     | 跨层交互必须通过明确定义的接口（如 `PersistencePort`）。                              |

---

## 1.4 依赖关系规范

- **依赖方向图示**：  
  ```mermaid
  graph LR
      Infrastructure --> InterfaceAdapters
      InterfaceAdapters --> Application
      Application --> Domain
  ```

- **关键规则**：  
  - 内层代码中不可出现外层技术类（如 `领域` 层不可引用 `SQLiteDB`）。  
  - 跨层交互必须通过接口（如 `PersistencePort`）。  
  - **依赖倒置原则**：高层（如应用层）依赖抽象接口，低层（如基础设施层）实现具体逻辑。  
  - - **跨层调用约束**：除通过明确定义的接口（如`PersistencePort`）外，禁止跨层直接调用具体实现类。

