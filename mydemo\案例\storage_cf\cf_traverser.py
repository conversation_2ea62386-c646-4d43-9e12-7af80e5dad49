"""
ComputationFrame遍历器实现
实现计算图遍历打印功能，包含函数名和节点深度的高可读性输出
"""

import os
import sys
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict, deque

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref
except ImportError:
    # 如果mandala1不可用，添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref


class CFTraverser:
    """计算图遍历器，实现计算图的层级遍历"""
    
    def __init__(self):
        self.depth_cache = {}
        
    def traverse_computation_graph(self, cf: ComputationFrame) -> List[Dict]:
        """
        遍历计算图，返回层级结构信息
        
        参数:
            cf: ComputationFrame对象
            
        返回:
            包含层级信息的字典列表
        """
        # 1. 获取所有函数节点
        function_nodes = list(cf.fnames)
        if not function_nodes:
            return []
            
        # 2. 计算每个函数节点的深度
        node_depths = self._calculate_node_depths(cf, function_nodes)
        
        # 3. 构建层级结构
        layers = self._build_layers(cf, function_nodes, node_depths)
        
        # 4. 提取详细信息
        result = []
        for depth, nodes in sorted(layers.items()):
            for node in nodes:
                node_info = self._extract_node_info(cf, node, depth)
                result.append(node_info)
                
        return result
    
    def _calculate_node_depths(self, cf: ComputationFrame, function_nodes: List[str]) -> Dict[str, int]:
        """
        计算每个函数节点的深度
        
        参数:
            cf: ComputationFrame对象
            function_nodes: 函数节点列表
            
        返回:
            节点名到深度的映射
        """
        depths = {}
        
        # 使用拓扑排序来计算深度
        try:
            # 获取拓扑排序结果
            topo_order = cf.topsort_modulo_sccs()
            
            # 初始化所有节点深度为0
            for node in function_nodes:
                depths[node] = 0
            
            # 基于拓扑顺序和依赖关系计算深度
            for node in topo_order:
                if node in function_nodes:
                    # 获取该节点的输入依赖
                    max_input_depth = 0
                    for input_node, _, _ in cf.in_edges(node):
                        if input_node in depths:
                            max_input_depth = max(max_input_depth, depths[input_node] + 1)
                    depths[node] = max_input_depth
                    
        except Exception as e:
            print(f"拓扑排序失败，使用简单深度计算: {e}")
            # 备用方案：简单的BFS深度计算
            depths = self._simple_depth_calculation(cf, function_nodes)
            
        return depths
    
    def _simple_depth_calculation(self, cf: ComputationFrame, function_nodes: List[str]) -> Dict[str, int]:
        """
        简单的深度计算方法（备用方案）
        
        参数:
            cf: ComputationFrame对象
            function_nodes: 函数节点列表
            
        返回:
            节点名到深度的映射
        """
        depths = {}
        
        # 找到源节点（没有输入依赖的节点）
        source_nodes = []
        for node in function_nodes:
            input_edges = list(cf.in_edges(node))
            if not input_edges:
                source_nodes.append(node)
                depths[node] = 0
        
        # 如果没有明确的源节点，将所有节点设为深度0
        if not source_nodes:
            for node in function_nodes:
                depths[node] = 0
            return depths
        
        # BFS计算深度
        queue = deque(source_nodes)
        visited = set(source_nodes)
        
        while queue:
            current_node = queue.popleft()
            current_depth = depths[current_node]
            
            # 检查所有输出边
            for _, target_node, _ in cf.out_edges(current_node):
                if target_node in function_nodes and target_node not in visited:
                    depths[target_node] = current_depth + 1
                    visited.add(target_node)
                    queue.append(target_node)
        
        # 确保所有节点都有深度值
        for node in function_nodes:
            if node not in depths:
                depths[node] = 0
                
        return depths
    
    def _build_layers(self, cf: ComputationFrame, function_nodes: List[str], 
                     node_depths: Dict[str, int]) -> Dict[int, List[str]]:
        """
        根据深度构建层级结构
        
        参数:
            cf: ComputationFrame对象
            function_nodes: 函数节点列表
            node_depths: 节点深度映射
            
        返回:
            深度到节点列表的映射
        """
        layers = defaultdict(list)
        
        for node in function_nodes:
            depth = node_depths.get(node, 0)
            layers[depth].append(node)
            
        return dict(layers)
    
    def _extract_node_info(self, cf: ComputationFrame, node: str, depth: int) -> Dict[str, Any]:
        """
        提取节点的详细信息
        
        参数:
            cf: ComputationFrame对象
            node: 节点名
            depth: 节点深度
            
        返回:
            节点信息字典
        """
        info = {
            'function_name': node,
            'depth': depth,
            'call_count': 0,
            'input_variables': [],
            'output_variables': [],
            'dependencies': []
        }
        
        try:
            # 获取调用次数
            if node in cf.fs:
                info['call_count'] = len(cf.fs[node])
            
            # 获取输入变量
            for input_var, _, _ in cf.in_edges(node):
                if input_var not in cf.fnames:  # 只包含变量节点
                    info['input_variables'].append(input_var)
            
            # 获取输出变量
            for _, output_var, _ in cf.out_edges(node):
                if output_var not in cf.fnames:  # 只包含变量节点
                    info['output_variables'].append(output_var)
            
            # 获取函数依赖
            for dep_func, _, _ in cf.in_edges(node):
                if dep_func in cf.fnames:  # 只包含函数节点
                    info['dependencies'].append(dep_func)
                    
        except Exception as e:
            print(f"提取节点 {node} 信息时出错: {e}")
            
        return info
    
    def print_computation_graph(self, cf: ComputationFrame, show_details: bool = True):
        """
        打印计算图的层级结构
        
        参数:
            cf: ComputationFrame对象
            show_details: 是否显示详细信息
        """
        print("=" * 60)
        print("计算图遍历结果")
        print("=" * 60)
        
        # 获取遍历结果
        traversal_result = self.traverse_computation_graph(cf)
        
        if not traversal_result:
            print("计算图中没有函数节点")
            return
        
        # 按深度分组显示
        layers = defaultdict(list)
        for node_info in traversal_result:
            layers[node_info['depth']].append(node_info)
        
        for depth in sorted(layers.keys()):
            print(f"\n深度 {depth}:")
            nodes_at_depth = layers[depth]
            
            for i, node_info in enumerate(nodes_at_depth):
                is_last = (i == len(nodes_at_depth) - 1)
                prefix = "└─ " if is_last else "├─ "
                
                print(f"{prefix}函数: {node_info['function_name']}")
                
                if show_details:
                    detail_prefix = "   " if is_last else "│  "
                    print(f"{detail_prefix}├─ 调用次数: {node_info['call_count']}")
                    
                    if node_info['dependencies']:
                        deps_str = ", ".join(node_info['dependencies'])
                        print(f"{detail_prefix}├─ 依赖函数: {deps_str}")
                    
                    if node_info['input_variables']:
                        inputs_str = ", ".join(node_info['input_variables'][:3])
                        if len(node_info['input_variables']) > 3:
                            inputs_str += f" (共{len(node_info['input_variables'])}个)"
                        print(f"{detail_prefix}├─ 输入变量: {inputs_str}")
                    
                    if node_info['output_variables']:
                        outputs_str = ", ".join(node_info['output_variables'][:3])
                        if len(node_info['output_variables']) > 3:
                            outputs_str += f" (共{len(node_info['output_variables'])}个)"
                        print(f"{detail_prefix}└─ 输出变量: {outputs_str}")
        
        print(f"\n总计: {len(traversal_result)} 个函数节点，{len(layers)} 个深度层级")
        print("=" * 60)


def demo_cf_traverser():
    """演示ComputationFrame遍历功能"""
    print("ComputationFrame遍历功能演示")
    print("=" * 50)

    # 创建一些示例函数
    @op
    def add(x, y):
        """加法函数"""
        return x + y

    @op
    def multiply(x, y):
        """乘法函数"""
        return x * y

    @op
    def power(x, n):
        """幂函数"""
        return x ** n

    # 创建存储并执行计算
    storage = Storage()

    with storage:
        # 执行一些计算
        a = add(1, 2)
        b = add(3, 4)
        c = multiply(a, b)
        d = power(c, 2)

    # 在Storage上下文外创建计算框架
    cf = storage.cf(d).expand_back(recursive=True)

    # 创建遍历器并打印结果
    traverser = CFTraverser()
    traverser.print_computation_graph(cf, show_details=True)


if __name__ == "__main__":
    demo_cf_traverser()
