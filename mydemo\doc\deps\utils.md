# deps/utils.py 文档

## 文件内容与作用总体说明

`deps/utils.py` 文件是 mandala 框架依赖跟踪模块的工具函数集合，提供了对象分类、函数提取、字节码分析、模块加载等核心功能。该文件定义了全局变量分类器、可调用对象检测、代码对象处理、依赖键生成等工具，支持静态和动态的代码分析，为依赖跟踪和版本控制提供基础设施。

## 文件内的所有变量的作用与说明

### 类型定义
- `DepKey`: Tuple[str, str]，依赖键类型，由模块名和对象地址组成

### 常量
- `UNKNOWN_GLOBAL_VAR`: str，值为 "UNKNOWN_GLOBAL_VAR"，表示未知全局变量的占位符

### 导入的模块和函数
- `types`: Python 类型模块
- `dis`: 字节码反汇编模块
- `importlib`: 动态导入模块
- `gc`: 垃圾回收模块
- `get_content_hash`: 内容哈希计算函数
- `unwrap_decorators`: 装饰器解包函数
- `Config`: 配置类

## 文件内的所有函数的作用与说明

### GlobalClassifier 类（全局变量分类器）

#### is_excluded(obj: Any) -> bool

**作用**: 判断对象是否应该被排除在全局变量跟踪之外

**输入参数**:
- `obj`: Any，要检查的对象

**内部调用的函数**：
- `inspect.ismodule`: 检查是否为模块
- `isinstance`: 检查对象类型
- `inspect.isfunction`: 检查是否为函数

**输出参数**: bool - 是否应该排除

**其他说明**：排除模块、类、函数和记忆化函数

#### is_scalar(obj: Any) -> bool

**作用**: 判断对象是否为标量类型

**输入参数**:
- `obj`: Any，要检查的对象

**内部调用的函数**：
- `isinstance`: 检查对象类型

**输出参数**: bool - 是否为标量

**其他说明**：标量包括 int、float、str、bool、None

#### is_data(obj: Any) -> bool

**作用**: 判断对象是否为数据结构（可递归检查）

**输入参数**:
- `obj`: Any，要检查的对象

**内部调用的函数**：
- `GlobalClassifier.is_scalar`: 检查是否为标量
- `GlobalClassifier.is_data`: 递归调用自身
- `all`: 检查所有元素

**输出参数**: bool - 是否为数据结构

**其他说明**：
- 递归检查容器类型（tuple、list、dict）
- 支持 numpy 数组和 pandas 数据结构
- 对于复合结构，要求所有元素都是数据类型

### 全局变量检测函数

#### is_global_val(obj: Any, allow_only: str = "all") -> bool

**作用**: 判断对象是否应该作为全局变量进行内容跟踪

**输入参数**:
- `obj`: Any，要检查的对象
- `allow_only`: str，允许的类型级别（"scalars"、"data"、"all"）

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `GlobalClassifier.is_scalar`: 检查标量
- `GlobalClassifier.is_data`: 检查数据结构
- `inspect.ismodule`: 检查模块
- `inspect.isfunction`: 检查函数

**输出参数**: bool - 是否应该跟踪为全局变量

**其他说明**：
- Ref 对象总是被跟踪
- 根据 allow_only 参数决定跟踪级别
- 排除模块、类、函数等不应跟踪的对象

### 可调用对象处理函数

#### is_callable_obj(obj: Any, strict: bool) -> bool

**作用**: 判断对象是否为可调用对象

**输入参数**:
- `obj`: Any，要检查的对象
- `strict`: bool，是否严格模式

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `callable`: 检查是否可调用

**输出参数**: bool - 是否为可调用对象

**其他说明**：
- 优先识别记忆化函数（Op 对象）
- 严格模式下只接受函数类型
- 宽松模式下接受所有可调用对象

#### extract_func_obj(obj: Any, strict: bool) -> types.FunctionType

**作用**: 从对象中提取函数对象

**输入参数**:
- `obj`: Any，包含函数的对象
- `strict`: bool，是否严格模式

**内部调用的函数**：
- `unwrap_decorators`: 解包装饰器
- `isinstance`: 检查对象类型
- `hasattr`: 检查属性存在

**输出参数**: types.FunctionType - 提取的函数对象

**其他说明**：
- 处理 Op 对象，提取其 f 属性
- 排除内置函数
- 宽松模式下支持类的 __init__ 方法
- 严格模式下失败时抛出异常

#### extract_code(obj: Callable) -> types.CodeType

**作用**: 从可调用对象中提取代码对象

**输入参数**:
- `obj`: Callable，可调用对象

**内部调用的函数**：
- `unwrap_decorators`: 解包装饰器
- `isinstance`: 检查对象类型
- `logger.debug`: 记录调试信息

**输出参数**: types.CodeType - 代码对象

**其他说明**：
- 处理 Op 对象和属性对象
- 支持函数和方法类型
- 返回 __code__ 属性

### 字节码分析函数

#### get_runtime_description(code: types.CodeType) -> Any

**作用**: 获取代码对象的运行时描述

**输入参数**:
- `code`: types.CodeType，代码对象

**内部调用的函数**：
- `get_sanitized_bytecode_representation`: 获取清理后的字节码表示

**输出参数**: Any - 运行时描述

#### get_global_names_candidates(code: types.CodeType) -> Set[str]

**作用**: 获取代码中可能的全局变量名称

**输入参数**:
- `code`: types.CodeType，代码对象

**内部调用的函数**：
- `dis.get_instructions`: 获取字节码指令
- `get_global_names_candidates`: 递归调用自身

**输出参数**: Set[str] - 全局变量名称集合

**其他说明**：
- 分析 LOAD_GLOBAL 指令
- 递归处理嵌套的代码对象

#### get_sanitized_bytecode_representation(code: types.CodeType) -> List[dis.Instruction]

**作用**: 获取清理后的字节码表示，递归处理嵌套代码

**输入参数**:
- `code`: types.CodeType，代码对象

**内部调用的函数**：
- `dis.get_instructions`: 获取字节码指令
- `dis.Instruction`: 创建指令对象
- `get_sanitized_bytecode_representation`: 递归调用自身

**输出参数**: List[dis.Instruction] - 清理后的指令列表

**其他说明**：递归处理嵌套的代码对象，保持指令结构

### 工具函数

#### unknown_function()

**作用**: 占位符函数，用于无法获取源代码的函数

**输入参数**: 无

**内部调用的函数**：
- 无函数调用

**输出参数**: 无返回值

**其他说明**：空函数，仅作为回退选项

#### get_bytecode(f: Union[types.FunctionType, types.CodeType, str]) -> str

**作用**: 获取函数或代码的字节码字符串表示

**输入参数**:
- `f`: Union[types.FunctionType, types.CodeType, str]，函数、代码对象或字符串

**内部调用的函数**：
- `compile`: 编译字符串
- `dis.get_instructions`: 获取指令
- `str`: 转换为字符串

**输出参数**: str - 字节码字符串

#### hash_dict(d: dict) -> str

**作用**: 计算字典的哈希值，按键排序后计算

**输入参数**:
- `d`: dict，要哈希的字典

**内部调用的函数**：
- `sorted`: 排序键
- `get_content_hash`: 计算内容哈希

**输出参数**: str - 字典的哈希值

#### load_obj(module_name: str, obj_name: str) -> Tuple[Any, bool]

**作用**: 从模块中加载指定的对象

**输入参数**:
- `module_name`: str，模块名称
- `obj_name`: str，对象名称（支持点号分隔的嵌套属性）

**内部调用的函数**：
- `importlib.import_module`: 导入模块
- `hasattr`: 检查属性存在
- `getattr`: 获取属性

**输出参数**: Tuple[Any, bool] - 对象和是否找到的标志

**其他说明**：支持嵌套属性访问，如 "Class.method"

#### get_dep_key_from_func(func: types.FunctionType) -> DepKey

**作用**: 从函数对象生成依赖键

**输入参数**:
- `func`: types.FunctionType，函数对象

**内部调用的函数**：
- 无直接函数调用

**输出参数**: DepKey - 依赖键元组

**其他说明**：使用函数的 __module__ 和 __qualname__ 属性

#### get_func_qualname(func_name: str, code: types.CodeType, frame: types.FrameType) -> str

**作用**: 获取函数的限定名称，使用垃圾回收器查找引用

**输入参数**:
- `func_name`: str，函数名称
- `code`: types.CodeType，代码对象
- `frame`: types.FrameType，栈帧

**内部调用的函数**：
- `gc.get_referrers`: 获取引用者
- `isinstance`: 检查对象类型
- `get_func_qualname_fallback`: 回退方法

**输出参数**: str - 函数的限定名称

**其他说明**：这是一个"邪恶"的方法，使用垃圾回收器内省

#### get_func_qualname_fallback(func_name: str, code: types.CodeType, frame: types.FrameType) -> str

**作用**: 获取函数限定名称的回退方法，通过分析参数推断是否为方法

**输入参数**:
- `func_name`: str，函数名称
- `code`: types.CodeType，代码对象
- `frame`: types.FrameType，栈帧

**内部调用的函数**：
- `hasattr`: 检查属性存在

**输出参数**: str - 函数的限定名称

**其他说明**：
- 检查第一个参数是否为 "self" 来判断是否为方法
- 支持嵌套类的限定名称
- 作为 get_func_qualname 的回退方案
