{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Quickstart\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_source/tutorials/01_hello.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "`mandala` eliminates the developer effort typically required to persist, iterate\n", "on, query, version and reproduce results of computational projects, such as\n", "machine learning experiments. \n", "\n", "It works by automatically capturing inputs, outputs, and code (+dependencies) at\n", "calls of `@op`-decorated functions. A `ComputationFrame` data structure over\n", "this information enables easy queries and high-level operations over program\n", "traces."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:47.029379Z", "iopub.status.busy": "2024-07-11T14:32:47.028563Z", "iopub.status.idle": "2024-07-11T14:32:47.049027Z", "shell.execute_reply": "2024-07-11T14:32:47.048051Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass# Run this if in a Google Colab notebook"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The `@op` decorator: automatic memoization and code tracking\n", "`@op` tracks the inputs, outputs, code and dependencies of calls to Python\n", "functions. The same call is never executed twice:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:47.053817Z", "iopub.status.busy": "2024-07-11T14:32:47.053316Z", "iopub.status.idle": "2024-07-11T14:32:50.937244Z", "shell.execute_reply": "2024-07-11T14:32:50.935769Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello from inc!\n", "Took 1 seconds\n"]}], "source": ["from mandala.imports import *\n", "import time\n", "\n", "storage = Storage( # stores all `@op` calls\n", "    # where to look for dependencies; use `None` to prevent versioning altogether\n", "    deps_path='__main__' \n", "    ) \n", "\n", "@op\n", "def inc(x):\n", "    print(\"Hello from inc!\")\n", "    time.sleep(1) # simulate a long operation\n", "    return x + 1\n", "\n", "with storage: # all `@op` calls inside this block will be stored in `storage`\n", "    start = time.time()\n", "    a = inc(1)\n", "    b = inc(1) # this will not be executed, but reused\n", "    end = time.time()\n", "    print(f'Took {round(end - start)} seconds')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `ComputationFrame`s: generalized dataframes for querying saved computations\n", "**`@op`s are designed to be composed with one another** like ordinary Python\n", "functions. This automatically keeps track of the relationships between all saved\n", "objects. \n", "\n", "The `ComputationFrame` data structure is a natural **high-level view** of these\n", "relationships that can be used to explore storage and extract computation traces\n", "in a format useful for analysis. It groups together saved `@op` calls into\n", "computational graphs: "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:50.942067Z", "iopub.status.busy": "2024-07-11T14:32:50.941394Z", "iopub.status.idle": "2024-07-11T14:32:55.515379Z", "shell.execute_reply": "2024-07-11T14:32:55.514612Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello from inc!\n", "Hello from add!\n", "Hello from inc!\n", "Hello from add!\n", "Hello from inc!\n", "Hello from inc!\n", "Hello from add!\n", "Computation frame for `inc`:\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"419pt\" height=\"50pt\"\n", " viewBox=\"0.00 0.00 419.00 50.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 46)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-46 415,-46 415,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M399,-38C399,-38 329,-38 329,-38 323,-38 317,-32 317,-26 317,-26 317,-14 317,-14 317,-8 323,-2 329,-2 329,-2 399,-2 399,-2 405,-2 411,-8 411,-14 411,-14 411,-26 411,-26 411,-32 405,-38 399,-38\"/>\n", "<text text-anchor=\"start\" x=\"348\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"325\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sinks)</text>\n", "</g>\n", "<!-- x -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>x</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-38C93,-38 12,-38 12,-38 6,-38 0,-32 0,-26 0,-26 0,-14 0,-14 0,-8 6,-2 12,-2 12,-2 93,-2 93,-2 99,-2 105,-8 105,-14 105,-14 105,-26 105,-26 105,-32 99,-38 93,-38\"/>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sources)</text>\n", "</g>\n", "<!-- inc -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>inc</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M226,-40C226,-40 196,-40 196,-40 190,-40 184,-34 184,-28 184,-28 184,-12 184,-12 184,-6 190,0 196,0 196,0 226,0 226,0 232,0 238,-6 238,-12 238,-12 238,-28 238,-28 238,-34 232,-40 226,-40\"/>\n", "<text text-anchor=\"start\" x=\"202\" y=\"-27.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">inc</text>\n", "<text text-anchor=\"start\" x=\"192.5\" y=\"-17\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:inc</text>\n", "<text text-anchor=\"start\" x=\"196.5\" y=\"-7\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- x&#45;&gt;inc -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>x&#45;&gt;inc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.18,-20C127.62,-20 153.37,-20 173.73,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"173.84,-23.5 183.84,-20 173.84,-16.5 173.84,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- inc&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>inc&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M238.3,-20C257.15,-20 283.23,-20 306.67,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"306.87,-23.5 316.87,-20 306.87,-16.5 306.87,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"277.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"277.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x718d64ffe290>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Expanded computation frame for `inc`:\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Title: G Pages: 1 -->\n", "<svg width=\"727pt\" height=\"81pt\"\n", " viewBox=\"0.00 0.00 727.00 81.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 77)\">\n", "<title>G</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-77 723,-77 723,4 -4,4\"/>\n", "<!-- var_0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>var_0</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M399,-68C399,-68 329,-68 329,-68 323,-68 317,-62 317,-56 317,-56 317,-44 317,-44 317,-38 323,-32 329,-32 329,-32 399,-32 399,-32 405,-32 411,-38 411,-44 411,-44 411,-56 411,-56 411,-62 405,-68 399,-68\"/>\n", "<text text-anchor=\"start\" x=\"348\" y=\"-52.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_0</text>\n", "<text text-anchor=\"start\" x=\"325\" y=\"-42\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (2 sinks)</text>\n", "</g>\n", "<!-- add -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>add</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M534,-40C534,-40 502,-40 502,-40 496,-40 490,-34 490,-28 490,-28 490,-12 490,-12 490,-6 496,0 502,0 502,0 534,0 534,0 540,0 546,-6 546,-12 546,-12 546,-28 546,-28 546,-34 540,-40 534,-40\"/>\n", "<text text-anchor=\"start\" x=\"507\" y=\"-27.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">add</text>\n", "<text text-anchor=\"start\" x=\"498\" y=\"-17\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:add</text>\n", "<text text-anchor=\"start\" x=\"503.5\" y=\"-7\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">3 calls</text>\n", "</g>\n", "<!-- var_0&#45;&gt;add -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>var_0&#45;&gt;add</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M411.02,-40.92C432.99,-36.58 458.95,-31.46 479.68,-27.37\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"480.56,-30.76 489.69,-25.39 479.2,-23.89 480.56,-30.76\"/>\n", "<text text-anchor=\"middle\" x=\"450.5\" y=\"-50\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">y</text>\n", "<text text-anchor=\"middle\" x=\"450.5\" y=\"-39\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- x -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>x</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M93,-38C93,-38 12,-38 12,-38 6,-38 0,-32 0,-26 0,-26 0,-14 0,-14 0,-8 6,-2 12,-2 12,-2 93,-2 93,-2 99,-2 105,-8 105,-14 105,-14 105,-26 105,-26 105,-32 99,-38 93,-38\"/>\n", "<text text-anchor=\"start\" x=\"49\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"start\" x=\"8\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">5 values (5 sources)</text>\n", "</g>\n", "<!-- inc -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>inc</title>\n", "<path fill=\"none\" stroke=\"#dc322f\" stroke-width=\"1.5\" d=\"M226,-72C226,-72 196,-72 196,-72 190,-72 184,-66 184,-60 184,-60 184,-44 184,-44 184,-38 190,-32 196,-32 196,-32 226,-32 226,-32 232,-32 238,-38 238,-44 238,-44 238,-60 238,-60 238,-66 232,-72 226,-72\"/>\n", "<text text-anchor=\"start\" x=\"202\" y=\"-59.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">inc</text>\n", "<text text-anchor=\"start\" x=\"192.5\" y=\"-49\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">@op:inc</text>\n", "<text text-anchor=\"start\" x=\"196.5\" y=\"-39\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#dc322f\">5 calls</text>\n", "</g>\n", "<!-- x&#45;&gt;inc -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>x&#45;&gt;inc</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.18,-30.57C127.72,-35.18 153.61,-40.47 174.01,-44.64\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"173.35,-48.08 183.84,-46.65 174.75,-41.22 173.35,-48.08\"/>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-56\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"middle\" x=\"144.5\" y=\"-45\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- x&#45;&gt;add -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>x&#45;&gt;add</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M105.32,-20C199.69,-20 397.42,-20 479.77,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"479.91,-23.5 489.91,-20 479.91,-16.5 479.91,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"277.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">x</text>\n", "<text text-anchor=\"middle\" x=\"277.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "<!-- var_1 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>var_1</title>\n", "<path fill=\"none\" stroke=\"#268bd2\" stroke-width=\"1.5\" d=\"M707,-38C707,-38 637,-38 637,-38 631,-38 625,-32 625,-26 625,-26 625,-14 625,-14 625,-8 631,-2 637,-2 637,-2 707,-2 707,-2 713,-2 719,-8 719,-14 719,-14 719,-26 719,-26 719,-32 713,-38 707,-38\"/>\n", "<text text-anchor=\"start\" x=\"656\" y=\"-22.4\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-weight=\"bold\" font-size=\"12.00\" fill=\"#002b36\">var_1</text>\n", "<text text-anchor=\"start\" x=\"633\" y=\"-12\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#268bd2\">3 values (3 sinks)</text>\n", "</g>\n", "<!-- inc&#45;&gt;var_0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>inc&#45;&gt;var_0</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M238.3,-51.65C257.15,-51.4 283.23,-51.06 306.67,-50.75\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"306.92,-54.24 316.87,-50.61 306.83,-47.24 306.92,-54.24\"/>\n", "<text text-anchor=\"middle\" x=\"277.5\" y=\"-65\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"277.5\" y=\"-54\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(5 values)</text>\n", "</g>\n", "<!-- add&#45;&gt;var_1 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>add&#45;&gt;var_1</title>\n", "<path fill=\"none\" stroke=\"#002b36\" d=\"M546.17,-20C565.22,-20 591.35,-20 614.79,-20\"/>\n", "<polygon fill=\"#002b36\" stroke=\"#002b36\" points=\"614.98,-23.5 624.98,-20 614.98,-16.5 614.98,-23.5\"/>\n", "<text text-anchor=\"middle\" x=\"585.5\" y=\"-34\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">output_0</text>\n", "<text text-anchor=\"middle\" x=\"585.5\" y=\"-23\" font-family=\"Liberation Sans,Helvetica,Arial,sans-serif\" font-size=\"10.00\" fill=\"#002b36\">(3 values)</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.sources.Source at 0x718e22407730>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@op # define a new @op to compose with `inc`\n", "def add(x, y):\n", "    print(\"Hello from add!\")\n", "    return x + y\n", "\n", "with storage:\n", "    for i in range(5):\n", "        j = inc(i)\n", "        if i % 2 == 0:\n", "            k = add(i, j)\n", "\n", "# get & visualize the computation frame for all calls to `inc`\n", "cf = storage.cf(inc) \n", "print('Computation frame for `inc`:')\n", "cf.draw(verbose=True, orientation='LR') # visualize the computation frame\n", "\n", "# expand the computation frame to include all calls connected to the calls of\n", "# `inc` through shared inputs/outputs\n", "cf.expand_all(inplace=True) \n", "print('Expanded computation frame for `inc`:')\n", "cf.draw(verbose=True, orientation='LR', path='test.jpg') # visualize the computation frame"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Computation frames generalize dataframes to operate over computation traces\n", "- **columns are replaced by a computational graph**: functions whose input/output\n", "edges connect to variables.\n", "- **rows are replaced by computation traces**: variable values and function\n", "calls that (possibly partially) follow this graph\n", "\n", "**A dataframe can be extracted from any computation frame** for easier later\n", "analysis:\n", "- the columns are the nodes in the graph (functions and variables)\n", "- each row is a computation trace, possibly padded with `NaN`s where no\n", "value/call is present:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:55.518750Z", "iopub.status.busy": "2024-07-11T14:32:55.518463Z", "iopub.status.idle": "2024-07-11T14:32:55.572235Z", "shell.execute_reply": "2024-07-11T14:32:55.571419Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    |   x | inc                   |   var_0 | add                   |   var_1 |\n", "|---:|----:|:----------------------|--------:|:----------------------|--------:|\n", "|  0 |   3 | Call(inc, hid=f62...) |       4 |                       |     nan |\n", "|  1 |   2 | Call(inc, hid=ec7...) |       3 | Call(add, hid=d3f...) |       5 |\n", "|  2 |   4 | Call(inc, hid=f05...) |       5 | Call(add, hid=5f0...) |       9 |\n", "|  3 |   0 | Call(inc, hid=52f...) |       1 | Call(add, hid=38e...) |       1 |\n", "|  4 |   1 | Call(inc, hid=66c...) |       2 |                       |     nan |\n"]}], "source": ["print(cf.df().to_markdown())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatic per-call versioning w/ dependency tracking\n", "Changing memoized functions may invalidate their past calls - but not all\n", "changes invalidate all calls, and some \"non-semantic\" changes invalidate no\n", "calls at all. \n", "\n", "To help with that, `mandala` can automatically track marked (with `@track`)\n", "dependencies of each call to an `@op`, and watch for changes in their code:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:55.576366Z", "iopub.status.busy": "2024-07-11T14:32:55.575996Z", "iopub.status.idle": "2024-07-11T14:32:55.761576Z", "shell.execute_reply": "2024-07-11T14:32:55.760464Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CHANGE DETECTED in add from module __main__\n", "Dependent components:\n", "  Version of \"add\" from module \"__main__\" (content: 7cd06a0178abc60d137bb47bceafa5f9, semantic: 455b6b8789fb67940e41dbbb135292f7)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">1 </span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> def add(x, y):</span><span style=\"background-color: #fdf6e3\">                                                                                            </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">2 </span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">     print(\"Hello from add!\")</span><span style=\"background-color: #fdf6e3\">                                                                              </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">3 </span><span style=\"color: #dc322f; text-decoration-color: #dc322f; background-color: #fdf6e3\">-    return x + y</span><span style=\"background-color: #fdf6e3\">                                                                                          </span> │\n", "│ <span style=\"color: #74878c; text-decoration-color: #74878c; background-color: #fdf6e3; font-weight: bold\">  </span><span style=\"color: #cfd1c6; text-decoration-color: #cfd1c6; background-color: #fdf6e3\">4 </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">+    return x + square(y)</span><span style=\"background-color: #fdf6e3\">                                                                                  </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭───────────────────────────────────────────────────── Diff ──────────────────────────────────────────────────────╮\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m1 \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mdef add(x, y):\u001b[0m\u001b[48;2;253;246;227m                                                                                            \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m2 \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m    print(\"Hello from add!\")\u001b[0m\u001b[48;2;253;246;227m                                                                              \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m3 \u001b[0m\u001b[38;2;220;50;47;48;2;253;246;227m-    return x + y\u001b[0m\u001b[48;2;253;246;227m                                                                                          \u001b[0m │\n", "│ \u001b[1;38;2;116;135;140;48;2;253;246;227m  \u001b[0m\u001b[38;2;207;209;198;48;2;253;246;227m4 \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m+    return x + square(y)\u001b[0m\u001b[48;2;253;246;227m                                                                                  \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Does this change require recomputation of dependent calls?\n", "WARNING: if the change created new dependencies and you choose 'no', you should add them by hand or risk missing changes in them.\n", "Answer: [y]es/[n]o/[a]bort \n", "You answered: \"y\"\n", "Hello from add!\n", "Hello from add!\n", "Hello from add!\n"]}], "source": ["from unittest.mock import patch\n", "from mandala.utils import mock_input # to simulate user input non-interactively\n", "\n", "@op # define a new @op to compose with `inc`\n", "def add(x, y):\n", "    print(\"Hello from add!\")\n", "    return x + square(y)\n", "\n", "@track # dependency tracking decorator\n", "def square(num):\n", "    return num**2\n", "\n", "# same computations as before, change to `add` will be detected\n", "with patch('builtins.input', mock_input(['y'])):\n", "    with storage:\n", "        for i in range(5):\n", "            j = inc(i)\n", "            if i % 2 == 0:\n", "                k = add(i, j)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we've created a new, semantically distinct version of `add`. The versions\n", "and their dependencies can be inspected with the `.versions` method:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-07-11T14:32:55.767529Z", "iopub.status.busy": "2024-07-11T14:32:55.767074Z", "iopub.status.idle": "2024-07-11T14:32:55.897375Z", "shell.execute_reply": "2024-07-11T14:32:55.893661Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function add from module __main__</span><span style=\"background-color: #fdf6e3\">                                              </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=7cd06a0178abc60d137bb47bceafa5f9</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=455b6b8789fb67940e41dbbb135292f7</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\"># define a new @op to compose with `inc`</span><span style=\"background-color: #fdf6e3\">                                                                   </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">add</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(x, y):</span><span style=\"background-color: #fdf6e3\">                                                                                                 </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">print</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">\"Hello from add!\"</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"background-color: #fdf6e3\">                                                                                   </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> x </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">+</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> y</span><span style=\"background-color: #fdf6e3\">                                                                                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function add from module __main__</span><span style=\"background-color: #fdf6e3\">                                              </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=6001cb6bf4c98e8a1b1a2f9170c7dd14</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=d1bae9c7d7f59e37d04dcb80adc06138</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\"># define a new @op to compose with `inc`</span><span style=\"background-color: #fdf6e3\">                                                                   </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">add</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(x, y):</span><span style=\"background-color: #fdf6e3\">                                                                                                 </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">print</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">\"Hello from add!\"</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">)</span><span style=\"background-color: #fdf6e3\">                                                                                   </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> x </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">+</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> square(y)</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@track</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\"># dependency tracking decorator</span><span style=\"background-color: #fdf6e3\">                                                                         </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">square</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(num):</span><span style=\"background-color: #fdf6e3\">                                                                                               </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> num</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">**</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">2</span><span style=\"background-color: #fdf6e3\">                                                                                              </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function add from module __main__\u001b[0m\u001b[48;2;253;246;227m                                              \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=7cd06a0178abc60d137bb47bceafa5f9\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=455b6b8789fb67940e41dbbb135292f7\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[3;38;2;147;161;161;48;2;253;246;227m# define a new @op to compose with `inc`\u001b[0m\u001b[48;2;253;246;227m                                                                   \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227madd\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mx\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                 \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227<PERSON>rint\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m\"\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227<PERSON><PERSON><PERSON> from add!\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m\"\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                   \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mx\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m+\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[48;2;253;246;227m                                                                                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function add from module __main__\u001b[0m\u001b[48;2;253;246;227m                                              \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=6001cb6bf4c98e8a1b1a2f9170c7dd14\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=d1bae9c7d7f59e37d04dcb80adc06138\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[3;38;2;147;161;161;48;2;253;246;227m# define a new @op to compose with `inc`\u001b[0m\u001b[48;2;253;246;227m                                                                   \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227madd\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mx\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                 \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227<PERSON>rint\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m\"\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227<PERSON><PERSON><PERSON> from add!\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m\"\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                   \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mx\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m+\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227msquare\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227my\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@track\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[3;38;2;147;161;161;48;2;253;246;227m# dependency tracking decorator\u001b[0m\u001b[48;2;253;246;227m                                                                         \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227msquare\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mnum\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                               \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mnum\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m*\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m*\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m2\u001b[0m\u001b[48;2;253;246;227m                                                                                              \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["storage.versions(add)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Conclusion\n", "This was a very brief tour through the three main tools `mandala` offers:\n", "memoization, computation frames, and versioning. Later tutorials will explore\n", "these concepts in more complex situations, as well as in more realistic\n", "settings such as small machine learning projects."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}