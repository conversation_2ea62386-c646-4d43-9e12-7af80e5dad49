"""
Storage执行顺序遍历器实现
实现程序运行后函数执行顺序的遍历打印，包含函数名、节点深度、入参与出参
"""

import os
import sys
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref
except ImportError:
    # 如果mandala1不可用，添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref


class StorageTraverser:
    """存储执行顺序遍历器，按时间顺序遍历函数调用"""
    
    def __init__(self):
        self.call_stack = []  # 用于跟踪调用深度
        self.execution_order = []  # 记录执行顺序
        
    def traverse_execution_order(self, storage: Storage) -> List[Dict]:
        """
        遍历函数执行顺序，返回调用信息
        
        参数:
            storage: Storage对象
            
        返回:
            包含调用信息的字典列表
        """
        # 1. 获取所有Call对象
        all_calls = self._get_all_calls(storage)
        
        if not all_calls:
            return []
        
        # 2. 按调用顺序排序（基于hid或其他标识）
        sorted_calls = self._sort_calls_by_execution_order(all_calls)
        
        # 3. 计算调用深度和提取详细信息
        execution_info = []
        call_depth_map = {}
        
        for i, call in enumerate(sorted_calls):
            call_info = self._extract_call_info(storage, call, i, call_depth_map)
            execution_info.append(call_info)
            
        return execution_info
    
    def _get_all_calls(self, storage: Storage) -> List[Call]:
        """
        获取Storage中的所有Call对象

        参数:
            storage: Storage对象

        返回:
            Call对象列表
        """
        all_calls = []

        try:
            # 方法1: 从storage的调用缓存中获取所有调用
            if hasattr(storage, 'calls') and hasattr(storage.calls, 'cache'):
                cache = storage.calls.cache
                if hasattr(cache, 'items'):
                    for call_hid, call in cache.items():
                        if isinstance(call, Call):
                            all_calls.append(call)
                elif hasattr(cache, '_cache') and hasattr(cache._cache, 'items'):
                    for call_hid, call in cache._cache.items():
                        if isinstance(call, Call):
                            all_calls.append(call)

            # 方法2: 如果缓存为空，尝试从数据库加载
            if not all_calls and hasattr(storage, 'call_storage'):
                try:
                    # 获取所有调用的hid
                    df = storage.call_storage.execute_df("SELECT call_history_id FROM calls")
                    if not df.empty:
                        call_hids = df['call_history_id'].unique().tolist()
                        all_calls = storage.mget_call(hids=call_hids, in_memory=True)
                except Exception as e:
                    print(f"从数据库加载调用失败: {e}")

            # 方法3: 尝试直接访问存储的内部结构
            if not all_calls:
                try:
                    if hasattr(storage, 'call_cache'):
                        cache = storage.call_cache
                        if hasattr(cache, 'items'):
                            for call_hid, call in cache.items():
                                if isinstance(call, Call):
                                    all_calls.append(call)
                        elif hasattr(cache, '_cache'):
                            for call_hid, call in cache._cache.items():
                                if isinstance(call, Call):
                                    all_calls.append(call)
                except Exception as e:
                    print(f"从call_cache获取调用失败: {e}")

        except Exception as e:
            print(f"获取调用列表失败: {e}")

        return all_calls
    
    def _sort_calls_by_execution_order(self, calls: List[Call]) -> List[Call]:
        """
        按执行顺序排序调用
        
        参数:
            calls: Call对象列表
            
        返回:
            排序后的Call对象列表
        """
        # 尝试多种排序策略
        
        # 策略1: 按hid排序（通常hid包含时间信息）
        try:
            return sorted(calls, key=lambda call: call.hid)
        except Exception:
            pass
        
        # 策略2: 按cid排序
        try:
            return sorted(calls, key=lambda call: call.cid)
        except Exception:
            pass
        
        # 策略3: 按函数名排序（备用方案）
        try:
            return sorted(calls, key=lambda call: call.op.name)
        except Exception:
            pass
        
        # 如果所有排序都失败，返回原列表
        return calls
    
    def _extract_call_info(self, storage: Storage, call: Call, 
                          execution_index: int, call_depth_map: Dict[str, int]) -> Dict[str, Any]:
        """
        提取单个调用的详细信息
        
        参数:
            storage: Storage对象
            call: Call对象
            execution_index: 执行索引
            call_depth_map: 调用深度映射
            
        返回:
            调用信息字典
        """
        info = {
            'execution_index': execution_index + 1,
            'function_name': call.op.name,
            'depth': 0,
            'inputs': {},
            'outputs': {},
            'call_id': call.hid[:8] if call.hid else 'unknown'
        }
        
        try:
            # 计算调用深度（简化版本）
            info['depth'] = self._calculate_call_depth(call, call_depth_map)
            
            # 提取输入参数
            info['inputs'] = self._extract_inputs(storage, call)
            
            # 提取输出参数
            info['outputs'] = self._extract_outputs(storage, call)
            
        except Exception as e:
            print(f"提取调用 {call.op.name} 信息时出错: {e}")
            
        return info
    
    def _calculate_call_depth(self, call: Call, call_depth_map: Dict[str, int]) -> int:
        """
        计算调用深度（简化实现）
        
        参数:
            call: Call对象
            call_depth_map: 调用深度映射
            
        返回:
            调用深度
        """
        # 简化的深度计算：基于输入依赖
        max_input_depth = 0
        
        try:
            for input_ref in call.inputs.values():
                if hasattr(input_ref, 'hid') and input_ref.hid in call_depth_map:
                    max_input_depth = max(max_input_depth, call_depth_map[input_ref.hid])
            
            depth = max_input_depth
            call_depth_map[call.hid] = depth
            
        except Exception:
            depth = 0
            
        return depth
    
    def _extract_inputs(self, storage: Storage, call: Call) -> Dict[str, Any]:
        """
        提取调用的输入参数
        
        参数:
            storage: Storage对象
            call: Call对象
            
        返回:
            输入参数字典
        """
        inputs = {}
        
        try:
            for param_name, ref in call.inputs.items():
                try:
                    # 尝试解包引用获取实际值
                    value = storage.unwrap(ref)
                    inputs[param_name] = self._format_value(value)
                except Exception:
                    # 如果解包失败，使用引用信息
                    inputs[param_name] = f"Ref({ref.hid[:8] if hasattr(ref, 'hid') else 'unknown'})"
                    
        except Exception as e:
            print(f"提取输入参数失败: {e}")
            
        return inputs
    
    def _extract_outputs(self, storage: Storage, call: Call) -> Dict[str, Any]:
        """
        提取调用的输出参数
        
        参数:
            storage: Storage对象
            call: Call对象
            
        返回:
            输出参数字典
        """
        outputs = {}
        
        try:
            for output_name, ref in call.outputs.items():
                try:
                    # 尝试解包引用获取实际值
                    value = storage.unwrap(ref)
                    outputs[output_name] = self._format_value(value)
                except Exception:
                    # 如果解包失败，使用引用信息
                    outputs[output_name] = f"Ref({ref.hid[:8] if hasattr(ref, 'hid') else 'unknown'})"
                    
        except Exception as e:
            print(f"提取输出参数失败: {e}")
            
        return outputs
    
    def _format_value(self, value: Any) -> str:
        """
        格式化值用于显示
        
        参数:
            value: 要格式化的值
            
        返回:
            格式化后的字符串
        """
        if value is None:
            return "None"
        elif isinstance(value, (int, float, bool)):
            return str(value)
        elif isinstance(value, str):
            return f"'{value}'" if len(value) <= 20 else f"'{value[:17]}...'"
        elif isinstance(value, (list, tuple)):
            if len(value) <= 3:
                return str(value)
            else:
                return f"[{len(value)} items]"
        elif isinstance(value, dict):
            if len(value) <= 2:
                return str(value)
            else:
                return f"{{{len(value)} items}}"
        else:
            return f"{type(value).__name__}(...)"
    
    def print_execution_order(self, storage: Storage, show_details: bool = True, max_calls: int = 20):
        """
        打印函数执行顺序
        
        参数:
            storage: Storage对象
            show_details: 是否显示详细信息
            max_calls: 最大显示调用数量
        """
        print("=" * 60)
        print("函数执行顺序遍历结果")
        print("=" * 60)
        
        # 获取执行顺序信息
        execution_info = self.traverse_execution_order(storage)
        
        if not execution_info:
            print("没有找到函数调用记录")
            return
        
        # 限制显示数量
        display_calls = execution_info[:max_calls]
        
        for call_info in display_calls:
            depth_prefix = "  " * call_info['depth']
            print(f"{call_info['execution_index']:3d}. {depth_prefix}[深度{call_info['depth']}] {call_info['function_name']}")
            
            if show_details:
                # 显示输入参数
                if call_info['inputs']:
                    inputs_str = ", ".join([f"{k}={v}" for k, v in call_info['inputs'].items()])
                    print(f"     {depth_prefix}├─ 输入: {inputs_str}")
                
                # 显示输出参数
                if call_info['outputs']:
                    outputs_str = ", ".join([f"{k}={v}" for k, v in call_info['outputs'].items()])
                    print(f"     {depth_prefix}└─ 输出: {outputs_str}")
                else:
                    print(f"     {depth_prefix}└─ 输出: (无)")
        
        if len(execution_info) > max_calls:
            print(f"\n... 还有 {len(execution_info) - max_calls} 个调用未显示")
        
        print(f"\n总计: {len(execution_info)} 个函数调用")
        print("=" * 60)


def demo_storage_traverser():
    """演示Storage执行顺序遍历功能"""
    print("Storage执行顺序遍历功能演示")
    print("=" * 50)

    # 创建一些示例函数
    @op
    def add(x, y):
        """加法函数"""
        return x + y

    @op
    def multiply(x, y):
        """乘法函数"""
        return x * y

    @op
    def power(x, n):
        """幂函数"""
        return x ** n

    # 创建存储并执行计算
    storage = Storage()

    with storage:
        # 执行一些计算
        a = add(1, 2)
        b = add(3, 4)
        c = multiply(a, b)
        d = power(c, 2)
        e = add(d, 10)

    # 在Storage上下文外进行遍历
    traverser = StorageTraverser()
    traverser.print_execution_order(storage, show_details=True)


if __name__ == "__main__":
    demo_storage_traverser()
