<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="334pt" height="620pt"
 viewBox="0.00 0.00 334.00 620.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 616)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-616 330,-616 330,4 -4,4"/>
<!-- operation -->
<g id="node1" class="node">
<title>operation</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-612C93,-612 12,-612 12,-612 6,-612 0,-606 0,-600 0,-600 0,-588 0,-588 0,-582 6,-576 12,-576 12,-576 93,-576 93,-576 99,-576 105,-582 105,-588 105,-588 105,-600 105,-600 105,-606 99,-612 93,-612"/>
<text text-anchor="start" x="25" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">operation</text>
<text text-anchor="start" x="8" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- process_numbers -->
<g id="node8" class="node">
<title>process_numbers</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M162,-518C162,-518 65,-518 65,-518 59,-518 53,-512 53,-506 53,-506 53,-490 53,-490 53,-484 59,-478 65,-478 65,-478 162,-478 162,-478 168,-478 174,-484 174,-490 174,-490 174,-506 174,-506 174,-512 168,-518 162,-518"/>
<text text-anchor="start" x="61" y="-505.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">process_numbers</text>
<text text-anchor="start" x="62.5" y="-495" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:process_numbers</text>
<text text-anchor="start" x="99" y="-485" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- operation&#45;&gt;process_numbers -->
<g id="edge9" class="edge">
<title>operation&#45;&gt;process_numbers</title>
<path fill="none" stroke="#002b36" d="M63.69,-575.76C72.63,-561.99 85.32,-542.42 95.66,-526.49"/>
<polygon fill="#002b36" stroke="#002b36" points="98.62,-528.37 101.12,-518.07 92.74,-524.56 98.62,-528.37"/>
<text text-anchor="middle" x="110" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">operation</text>
<text text-anchor="middle" x="110" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- numbers -->
<g id="node2" class="node">
<title>numbers</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M216,-612C216,-612 135,-612 135,-612 129,-612 123,-606 123,-600 123,-600 123,-588 123,-588 123,-582 129,-576 135,-576 135,-576 216,-576 216,-576 222,-576 228,-582 228,-588 228,-588 228,-600 228,-600 228,-606 222,-612 216,-612"/>
<text text-anchor="start" x="149.5" y="-596.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">numbers</text>
<text text-anchor="start" x="131" y="-586" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- numbers&#45;&gt;process_numbers -->
<g id="edge8" class="edge">
<title>numbers&#45;&gt;process_numbers</title>
<path fill="none" stroke="#002b36" d="M164.13,-575.76C155.04,-561.99 142.14,-542.42 131.63,-526.49"/>
<polygon fill="#002b36" stroke="#002b36" points="134.51,-524.49 126.08,-518.07 128.66,-528.35 134.51,-524.49"/>
<text text-anchor="middle" x="173" y="-550" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">numbers</text>
<text text-anchor="middle" x="173" y="-539" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- value -->
<g id="node3" class="node">
<title>value</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M190.5,-228C190.5,-228 160.5,-228 160.5,-228 154.5,-228 148.5,-222 148.5,-216 148.5,-216 148.5,-204 148.5,-204 148.5,-198 154.5,-192 160.5,-192 160.5,-192 190.5,-192 190.5,-192 196.5,-192 202.5,-198 202.5,-204 202.5,-204 202.5,-216 202.5,-216 202.5,-222 196.5,-228 190.5,-228"/>
<text text-anchor="start" x="160" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">value</text>
<text text-anchor="start" x="157" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values</text>
</g>
<!-- final_computation -->
<g id="node10" class="node">
<title>final_computation</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M272,-134C272,-134 177,-134 177,-134 171,-134 165,-128 165,-122 165,-122 165,-106 165,-106 165,-100 171,-94 177,-94 177,-94 272,-94 272,-94 278,-94 284,-100 284,-106 284,-106 284,-122 284,-122 284,-128 278,-134 272,-134"/>
<text text-anchor="start" x="173" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">final_computation</text>
<text text-anchor="start" x="174" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:final_computation</text>
<text text-anchor="start" x="210" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">4 calls</text>
</g>
<!-- value&#45;&gt;final_computation -->
<g id="edge3" class="edge">
<title>value&#45;&gt;final_computation</title>
<path fill="none" stroke="#002b36" d="M180.83,-191.77C184.73,-180.18 190.54,-164.76 197.5,-152 199.19,-148.9 201.1,-145.77 203.11,-142.71"/>
<polygon fill="#002b36" stroke="#002b36" points="206.1,-144.55 208.93,-134.34 200.35,-140.55 206.1,-144.55"/>
<text text-anchor="middle" x="219" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">value</text>
<text text-anchor="middle" x="219" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- method -->
<g id="node4" class="node">
<title>method</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M277,-420C277,-420 196,-420 196,-420 190,-420 184,-414 184,-408 184,-408 184,-396 184,-396 184,-390 190,-384 196,-384 196,-384 277,-384 277,-384 283,-384 289,-390 289,-396 289,-396 289,-408 289,-408 289,-414 283,-420 277,-420"/>
<text text-anchor="start" x="214.5" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">method</text>
<text text-anchor="start" x="192" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sources)</text>
</g>
<!-- aggregate_results -->
<g id="node9" class="node">
<title>aggregate_results</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M223.5,-326C223.5,-326 127.5,-326 127.5,-326 121.5,-326 115.5,-320 115.5,-314 115.5,-314 115.5,-298 115.5,-298 115.5,-292 121.5,-286 127.5,-286 127.5,-286 223.5,-286 223.5,-286 229.5,-286 235.5,-292 235.5,-298 235.5,-298 235.5,-314 235.5,-314 235.5,-320 229.5,-326 223.5,-326"/>
<text text-anchor="start" x="123.5" y="-313.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">aggregate_results</text>
<text text-anchor="start" x="124.5" y="-303" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:aggregate_results</text>
<text text-anchor="start" x="161" y="-293" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">4 calls</text>
</g>
<!-- method&#45;&gt;aggregate_results -->
<g id="edge6" class="edge">
<title>method&#45;&gt;aggregate_results</title>
<path fill="none" stroke="#002b36" d="M225.31,-383.76C216.37,-369.99 203.68,-350.42 193.34,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="196.26,-332.56 187.88,-326.07 190.38,-336.37 196.26,-332.56"/>
<text text-anchor="middle" x="234" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">method</text>
<text text-anchor="middle" x="234" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- factor -->
<g id="node5" class="node">
<title>factor</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M314,-228C314,-228 233,-228 233,-228 227,-228 221,-222 221,-216 221,-216 221,-204 221,-204 221,-198 227,-192 233,-192 233,-192 314,-192 314,-192 320,-192 326,-198 326,-204 326,-204 326,-216 326,-216 326,-222 320,-228 314,-228"/>
<text text-anchor="start" x="256.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">factor</text>
<text text-anchor="start" x="229" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values (4 sources)</text>
</g>
<!-- factor&#45;&gt;final_computation -->
<g id="edge2" class="edge">
<title>factor&#45;&gt;final_computation</title>
<path fill="none" stroke="#002b36" d="M264.51,-191.76C257.47,-178.24 247.51,-159.14 239.29,-143.38"/>
<polygon fill="#002b36" stroke="#002b36" points="242.17,-141.32 234.44,-134.07 235.96,-144.56 242.17,-141.32"/>
<text text-anchor="middle" x="276" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">factor</text>
<text text-anchor="middle" x="276" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- var_0 -->
<g id="node6" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M259.5,-36C259.5,-36 189.5,-36 189.5,-36 183.5,-36 177.5,-30 177.5,-24 177.5,-24 177.5,-12 177.5,-12 177.5,-6 183.5,0 189.5,0 189.5,0 259.5,0 259.5,0 265.5,0 271.5,-6 271.5,-12 271.5,-12 271.5,-24 271.5,-24 271.5,-30 265.5,-36 259.5,-36"/>
<text text-anchor="start" x="208.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="185.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values (4 sinks)</text>
</g>
<!-- data -->
<g id="node7" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M154,-420C154,-420 73,-420 73,-420 67,-420 61,-414 61,-408 61,-408 61,-396 61,-396 61,-390 67,-384 73,-384 73,-384 154,-384 154,-384 160,-384 166,-390 166,-396 166,-396 166,-408 166,-408 166,-414 160,-420 154,-420"/>
<text text-anchor="start" x="101" y="-404.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="69" y="-394" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">4 values (1 sources)</text>
</g>
<!-- data&#45;&gt;aggregate_results -->
<g id="edge5" class="edge">
<title>data&#45;&gt;aggregate_results</title>
<path fill="none" stroke="#002b36" d="M124.87,-383.76C133.96,-369.99 146.86,-350.42 157.37,-334.49"/>
<polygon fill="#002b36" stroke="#002b36" points="160.34,-336.35 162.92,-326.07 154.49,-332.49 160.34,-336.35"/>
<text text-anchor="middle" x="171" y="-358" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="171" y="-347" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- process_numbers&#45;&gt;data -->
<g id="edge7" class="edge">
<title>process_numbers&#45;&gt;data</title>
<path fill="none" stroke="#002b36" d="M113.5,-477.98C113.5,-464.34 113.5,-445.75 113.5,-430.5"/>
<polygon fill="#002b36" stroke="#002b36" points="117,-430.1 113.5,-420.1 110,-430.1 117,-430.1"/>
<text text-anchor="middle" x="135" y="-452" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="135" y="-441" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
<!-- aggregate_results&#45;&gt;value -->
<g id="edge4" class="edge">
<title>aggregate_results&#45;&gt;value</title>
<path fill="none" stroke="#002b36" d="M175.5,-285.98C175.5,-272.34 175.5,-253.75 175.5,-238.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-238.1 175.5,-228.1 172,-238.1 179,-238.1"/>
<text text-anchor="middle" x="197" y="-260" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-249" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
<!-- final_computation&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>final_computation&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M224.5,-93.98C224.5,-80.34 224.5,-61.75 224.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="228,-46.1 224.5,-36.1 221,-46.1 228,-46.1"/>
<text text-anchor="middle" x="246" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="246" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(4 values)</text>
</g>
</g>
</svg>
