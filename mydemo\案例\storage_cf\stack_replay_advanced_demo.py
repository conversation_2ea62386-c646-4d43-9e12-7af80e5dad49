"""
栈重放高级演示程序
展示复杂的函数调用层级和并行分支的分析
"""

import os
import sys
from pathlib import Path
import time

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from mandala1.storage import Storage
from mandala1.model import op
from stack_replay import StackReplayAnalyzer


# 数据处理分支A
@op
def preprocess_branch_a(data: list) -> list:
    """预处理分支A - 数值处理"""
    print("🔄 执行预处理分支A")
    return [x * 2 for x in data if x % 2 == 0]  # 偶数翻倍


@op
def analyze_branch_a(data: list) -> dict:
    """分析分支A - 统计分析"""
    print("📊 执行分析分支A")
    return {
        "type": "even_numbers",
        "sum": sum(data),
        "count": len(data),
        "avg": sum(data) / len(data) if data else 0
    }


# 数据处理分支B
@op
def preprocess_branch_b(data: list) -> list:
    """预处理分支B - 字符串处理"""
    print("🔄 执行预处理分支B")
    return [str(x) + "_processed" for x in data if x % 2 == 1]  # 奇数字符串化


@op
def analyze_branch_b(data: list) -> dict:
    """分析分支B - 文本分析"""
    print("📊 执行分析分支B")
    return {
        "type": "odd_strings",
        "count": len(data),
        "items": data[:5],  # 只保留前5个
        "total_length": sum(len(item) for item in data)
    }


# 合并处理
@op
def merge_results(result_a: dict, result_b: dict) -> dict:
    """合并两个分支的结果"""
    print("🔗 合并分支结果")
    return {
        "branch_a": result_a,
        "branch_b": result_b,
        "combined_count": result_a.get("count", 0) + result_b.get("count", 0),
        "merge_timestamp": time.time()
    }


# 后处理函数
@op
def validate_results(merged_data: dict) -> bool:
    """验证合并结果"""
    print("✅ 验证结果")
    return (
        "branch_a" in merged_data and 
        "branch_b" in merged_data and
        merged_data.get("combined_count", 0) > 0
    )


@op
def format_output(merged_data: dict, format_type: str = "json") -> str:
    """格式化输出"""
    print(f"📝 格式化输出: {format_type}")
    if format_type == "json":
        import json
        return json.dumps(merged_data, indent=2)
    elif format_type == "summary":
        return f"""
数据处理摘要
============
分支A处理: {merged_data['branch_a']['count']} 个偶数
分支B处理: {merged_data['branch_b']['count']} 个奇数
总计处理: {merged_data['combined_count']} 个数据
"""
    else:
        return str(merged_data)


@op
def save_final_result(formatted_data: str, validation: bool, filename: str) -> dict:
    """保存最终结果"""
    print(f"💾 保存最终结果到: {filename}")
    if not validation:
        print("⚠️ 警告: 验证失败，但仍然保存")
    
    return {
        "saved": True,
        "filename": filename,
        "validation_passed": validation,
        "data_size": len(formatted_data),
        "save_timestamp": time.time()
    }


# 辅助函数 - 创建复杂的依赖关系
@op
def generate_config(seed: int = 42) -> dict:
    """生成配置"""
    print(f"⚙️ 生成配置，种子: {seed}")
    return {
        "seed": seed,
        "threshold": seed % 10,
        "multiplier": (seed % 5) + 1,
        "format": "json" if seed % 2 == 0 else "summary"
    }


@op
def load_initial_data(config: dict) -> list:
    """根据配置加载初始数据"""
    print(f"📁 根据配置加载数据")
    seed = config["seed"]
    size = 10 + (seed % 10)
    return list(range(1, size + 1))


def create_complex_computation_pipeline():
    """创建复杂的计算管道，包含并行分支和多层依赖"""
    print("🚀 开始执行复杂计算管道...")
    
    storage = Storage()
    
    with storage:
        print("\n📋 执行复杂计算步骤:")
        
        # 第1层: 配置和数据加载
        config = generate_config(seed=123)
        initial_data = load_initial_data(config)
        
        # 第2层: 并行分支处理
        print("\n🔀 并行分支处理:")
        
        # 分支A: 偶数处理
        processed_a = preprocess_branch_a(initial_data)
        result_a = analyze_branch_a(processed_a)
        
        # 分支B: 奇数处理  
        processed_b = preprocess_branch_b(initial_data)
        result_b = analyze_branch_b(processed_b)
        
        # 第3层: 合并处理
        print("\n🔗 合并处理:")
        merged_results = merge_results(result_a, result_b)
        
        # 第4层: 后处理
        print("\n🔧 后处理:")
        validation_result = validate_results(merged_results)
        
        # 使用配置中的格式
        formatted_json = format_output(merged_results, format_type="json")
        formatted_summary = format_output(merged_results, format_type="summary")
        
        # 第5层: 最终保存
        print("\n💾 最终保存:")
        final_result_json = save_final_result(
            formatted_json, validation_result, "results.json"
        )
        final_result_summary = save_final_result(
            formatted_summary, validation_result, "results.txt"
        )
        
        print("\n✅ 复杂计算管道执行完成!")
        
        return storage, [final_result_json, final_result_summary]


def demonstrate_advanced_stack_replay():
    """演示高级栈重放功能"""
    print("🎯 高级栈重放（Advanced Stack Replay）演示")
    print("="*80)
    
    # 1. 执行复杂计算管道
    storage, results = create_complex_computation_pipeline()
    
    # 2. 创建栈重放分析器
    analyzer = StackReplayAnalyzer(storage)
    
    # 3. 获取完整的计算框架
    print("\n🔍 构建复杂计算框架...")
    cf = storage.cf(save_final_result).expand_all()
    
    print(f"✅ 复杂计算框架构建完成:")
    print(f"   • 函数节点: {len(cf.fnames)}")
    print(f"   • 变量节点: {len(cf.vnames)}")
    print(f"   • 边数: {len(cf.edges())}")
    print(f"   • 层级深度: 预计5-6层")
    
    # 4. 捕获执行信息
    print("\n📊 捕获执行信息...")
    execution_info = analyzer.capture_execution_info(cf)
    
    # 5. 详细分析ComputationFrame
    print("\n" + "🌳" + "="*78)
    print("详细分析 ComputationFrame 层级结构")
    print("="*80)
    analyzer.print_computation_frame_hierarchy(cf, detailed=True)
    
    # 6. 详细分析Storage执行顺序
    print("\n" + "⏰" + "="*78)
    print("详细分析 Storage 执行顺序")
    print("="*80)
    analyzer.print_storage_execution_order(execution_info, detailed=True)
    
    # 7. 生成详细摘要
    summary = analyzer.generate_execution_summary(execution_info)
    print(f"\n📋 详细执行摘要:")
    print("="*50)
    print(summary)
    
    # 8. 分析并行分支
    print(f"\n🔀 并行分支分析:")
    print("="*50)
    
    # 查找并行执行的函数
    hierarchy = execution_info["call_hierarchy"]
    for level_info in hierarchy["levels"]:
        level = level_info["level"]
        functions = level_info["functions"]
        if len(functions) > 1:
            print(f"层级 {level} 包含 {len(functions)} 个并行函数:")
            for fname in functions:
                func_info = execution_info["functions"][fname]
                print(f"   • {fname}: {func_info['call_count']} 次调用")
    
    # 9. 尝试生成可视化
    try:
        svg_path = Path(__file__).parent / "advanced_computation_graph.svg"
        cf.draw(verbose=True, path=str(svg_path))
        print(f"\n🎨 高级计算图已保存到: {svg_path}")
    except Exception as e:
        print(f"\n❌ 无法生成可视化: {e}")
    
    return analyzer, execution_info


def analyze_specific_functions():
    """分析特定函数的执行模式"""
    print("\n" + "🔬" + "="*78)
    print("特定函数执行模式分析")
    print("="*80)
    
    storage = Storage()
    
    with storage:
        # 多次执行相同函数，不同参数
        configs = [
            generate_config(seed=10),
            generate_config(seed=20),
            generate_config(seed=30)
        ]
        
        results = []
        for i, config in enumerate(configs, 1):
            print(f"\n🔄 执行配置 {i}:")
            data = load_initial_data(config)
            processed = preprocess_branch_a(data)
            result = analyze_branch_a(processed)
            results.append(result)
    
    # 分析这个特定的执行模式
    analyzer = StackReplayAnalyzer(storage)
    cf = storage.cf(analyze_branch_a).expand_all()
    
    print(f"\n📊 特定函数分析结果:")
    print(f"   • analyze_branch_a 被调用了 3 次")
    print(f"   • 计算框架包含 {len(cf.fnames)} 个函数")
    
    execution_info = analyzer.capture_execution_info(cf)
    
    # 简化显示，专注于特定函数
    func_info = execution_info["functions"].get("analyze_branch_a", {})
    if func_info:
        print(f"\n🎯 analyze_branch_a 详细分析:")
        print(f"   • 调用次数: {func_info['call_count']}")
        print(f"   • 输入参数: {func_info['inputs']}")
        print(f"   • 输出结果: {func_info['outputs']}")
        
        print(f"\n📝 各次调用详情:")
        for i, call in enumerate(func_info["calls"], 1):
            print(f"   调用 {i}:")
            print(f"     输入: {call['inputs']}")
            print(f"     输出: {call['outputs']}")
    
    return analyzer, execution_info


if __name__ == "__main__":
    print("🎬 高级栈重放演示程序启动")
    print("="*80)
    
    # 演示复杂的计算管道
    analyzer1, info1 = demonstrate_advanced_stack_replay()
    
    # 分析特定函数
    analyzer2, info2 = analyze_specific_functions()
    
    print("\n🎉 高级演示完成!")
    print("="*80)
    print("📝 高级功能总结:")
    print("   • ✅ 成功分析了复杂的多层级计算管道")
    print("   • ✅ 识别了并行分支的执行模式")
    print("   • ✅ 展示了函数间的复杂依赖关系")
    print("   • ✅ 提供了详细的执行历史和数据流分析")
    print("   • ✅ 支持特定函数的深度分析")
    print("   • ✅ 实现了高可读性的层级遍历显示")
