# 栈重放（Stack Replay）功能

## 概述

基于mandala1框架实现的栈重放功能，能够捕获完整的函数执行信息，并提供两种遍历方式：

1. **ComputationFrame遍历**：计算图结构遍历，展示函数层级关系和依赖关系
2. **Storage执行顺序遍历**：按时间顺序遍历函数调用，展示实际执行流程

## 功能特点

### ComputationFrame遍历功能
- ✅ 节点的函数名显示
- ✅ 节点的深度计算
- ✅ 函数调用次数统计
- ✅ 输入输出变量展示
- ✅ 函数依赖关系分析
- ✅ 高可读性的树形结构输出

### Storage执行顺序遍历功能
- ✅ 节点的函数名显示
- ✅ 节点的深度计算
- ✅ 入参与出参详细信息
- ✅ 执行顺序编号
- ✅ 参数值格式化显示
- ✅ 高可读性的列表输出

## 文件结构

```
mydemo/案例/storage_cf/
├── stack_replay_architecture.md  # 架构设计文档
├── cf_traverser.py               # ComputationFrame遍历器
├── storage_traverser.py          # Storage执行顺序遍历器
├── stack_replay_demo.py          # 完整演示程序
└── README.md                     # 本文件
```

## 使用方法

### 1. 基本使用

```python
from cf_traverser import CFTraverser
from storage_traverser import StorageTraverser
from mandala1.imports import Storage, op

# 定义函数
@op
def add(x, y):
    return x + y

@op
def multiply(x, y):
    return x * y

# 执行计算
storage = Storage()
with storage:
    a = add(1, 2)
    b = add(3, 4)
    result = multiply(a, b)

# ComputationFrame遍历
cf = storage.cf(result).expand_back(recursive=True)
cf_traverser = CFTraverser()
cf_traverser.print_computation_graph(cf, show_details=True)

# Storage执行顺序遍历
storage_traverser = StorageTraverser()
storage_traverser.print_execution_order(storage, show_details=True)
```

### 2. 运行演示程序

```bash
# 运行完整演示
python stack_replay_demo.py

# 单独测试ComputationFrame遍历
python cf_traverser.py

# 单独测试Storage执行顺序遍历
python storage_traverser.py
```

## 输出示例

### ComputationFrame遍历输出
```
计算图遍历结果
============================================================

深度 0:
├─ 函数: add
│  ├─ 调用次数: 2
│  ├─ 输入变量: x, y
│  └─ 输出变量: result1, result2
└─ 函数: multiply
   ├─ 调用次数: 1
   ├─ 依赖函数: add
   ├─ 输入变量: a, b
   └─ 输出变量: final_result

总计: 2 个函数节点，1 个深度层级
```

### Storage执行顺序遍历输出
```
函数执行顺序遍历结果
============================================================
  1. [深度0] add
     ├─ 输入: x=1, y=2
     └─ 输出: output_0=3
  2. [深度0] add
     ├─ 输入: x=3, y=4
     └─ 输出: output_0=7
  3. [深度1] multiply
     ├─ 输入: x=3, y=7
     └─ 输出: output_0=21

总计: 3 个函数调用
```

## 核心类说明

### CFTraverser类
- `traverse_computation_graph(cf)`: 遍历计算图，返回层级结构信息
- `print_computation_graph(cf, show_details=True)`: 打印计算图的层级结构

### StorageTraverser类
- `traverse_execution_order(storage)`: 遍历函数执行顺序，返回调用信息
- `print_execution_order(storage, show_details=True, max_calls=20)`: 打印函数执行顺序

## 技术实现

### 深度计算算法
1. **ComputationFrame深度**: 基于计算图的拓扑结构，使用拓扑排序计算节点深度
2. **Storage执行深度**: 基于函数调用的依赖关系，简化的深度计算

### 遍历策略
1. **计算图遍历**: 利用mandala1的ComputationFrame.topsort_modulo_sccs()方法
2. **执行顺序遍历**: 从Storage缓存中获取Call对象，按hid排序

### 输出格式化
- 使用树形结构显示层级关系
- 清晰的缩进和符号标识（├─ └─ │）
- 关键信息突出显示
- 支持详细信息开关

## 注意事项

1. **Storage上下文**: 必须在Storage上下文外调用expand_back等方法
2. **递归函数**: 当前版本对递归函数支持有限，建议使用简单函数
3. **大规模计算**: 对于大量函数调用，建议使用max_calls参数限制显示数量
4. **依赖路径**: 确保mandala1模块路径正确，代码中已包含自动路径添加逻辑

## 扩展功能

可以基于现有框架扩展以下功能：
- 函数执行时间统计
- 内存使用分析
- 调用图可视化
- 交互式遍历模式
- 性能瓶颈分析

## 相关文档

- [架构设计文档](stack_replay_architecture.md)
- [mandala1框架文档](../../doc/)
- [ComputationFrame详细说明](../../doc/cf.md)
- [Storage详细说明](../../doc/storage.md)
