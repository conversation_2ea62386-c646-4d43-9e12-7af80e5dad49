# deps/shallow_versions.py 文档

## 文件内容与作用总体说明

`deps/shallow_versions.py` 文件是 mandala 框架依赖跟踪模块的浅层版本管理组件，实现了单个组件的版本跟踪功能。该文件定义了 `Commit` 和 `DAG` 类，用于管理组件的"浅层"内容版本（不包括依赖关系），支持版本历史记录、差异计算、版本合并等功能。浅层版本主要关注组件自身的源代码或内容变化，而不考虑其依赖关系的变化。

## 文件内的所有变量的作用与说明

### 导入的模块和函数
- `Literal`: 字面量类型注解
- `textwrap`: 文本包装模块
- `get_content_hash`: 内容哈希计算函数
- `Config`: 配置类
- `ask_user`: 用户交互函数
- `_get_colorized_diff`: 彩色差异显示函数
- `_get_diff`: 差异计算函数
- `Tree`, `Panel`, `Text`, `Syntax`: Rich 库组件（可选）

### Commit 类的实例变量
- `parents`: List[str]，父提交的内容哈希列表
- `diffs`: List[Any]，与父提交的差异列表
- `content_hash`: str，内容哈希
- `semantic_hash`: str，语义哈希
- `content`: Optional[str]，实际内容

### DAG 类的实例变量
- `commits`: Dict[str, Commit]，提交哈希到提交对象的映射
- `heads`: Set[str]，头提交集合
- `tails`: Set[str]，尾提交集合

## 文件内的所有函数的作用与说明

### 工具函数

#### get_diff(a: str, b: str) -> Tuple[str, str]

**作用**: 计算两个字符串之间的差异

**输入参数**:
- `a`: str，第一个字符串
- `b`: str，第二个字符串

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Tuple[str, str] - 差异元组

**其他说明**：当前实现为简单返回，TODO 中提到需要实现紧凑差异

#### apply_diff(b: str, diff: Tuple[str, str]) -> str

**作用**: 将差异应用到字符串

**输入参数**:
- `b`: str，基础字符串
- `diff`: Tuple[str, str]，差异

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 应用差异后的字符串

**其他说明**：当前实现为简单返回差异的第一个元素

### Commit 类（提交）

#### __init__(self, parents: List[str], diffs: List[Any], content_hash: str, semantic_hash: str, content: Optional[str])

**作用**: 初始化提交对象，跟踪单个组件的浅层内容版本

**输入参数**:
- `parents`: List[str]，父提交的内容哈希列表
- `diffs`: List[Any]，与父提交的差异列表
- `content_hash`: str，内容哈希
- `semantic_hash`: str，语义哈希
- `content`: Optional[str]，实际内容

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：
- 目前最多支持一个父提交
- 对于函数/方法，这是源代码的版本
- 对于全局变量，这是变量值的内容哈希

#### __repr__(self) -> str

**作用**: 返回提交的字符串表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 提交的字符串表示

#### get_content(self, dag: "DAG") -> str

**作用**: 获取提交的完整内容，通过应用差异重构

**输入参数**:
- `dag`: DAG，包含此提交的有向无环图

**内部调用的函数**：
- `apply_diff`: 应用差异
- `dag.get_content`: 递归获取父提交内容

**输出参数**: str - 完整内容

**其他说明**：递归地从父提交重构内容

#### diff_representation(self, dag: "DAG") -> str

**作用**: 获取提交的差异表示

**输入参数**:
- `dag`: DAG，包含此提交的有向无环图

**内部调用的函数**：
- `self.get_content`: 获取内容
- `dag.get_content`: 获取父提交内容
- `_get_colorized_diff`: 获取彩色差异

**输出参数**: str - 差异表示

### DAG 类（有向无环图）

#### __init__(self)

**作用**: 初始化空的有向无环图

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### __repr__(self) -> str

**作用**: 返回 DAG 的字符串表示

**输入参数**: 无

**内部调用的函数**：
- `len`: 获取长度

**输出参数**: str - DAG 的字符串表示

#### add_commit(self, commit: Commit) -> str

**作用**: 向 DAG 添加新提交

**输入参数**:
- `commit`: Commit，要添加的提交

**内部调用的函数**：
- `get_content_hash`: 计算内容哈希

**输出参数**: str - 提交的哈希值

**其他说明**：自动更新头和尾集合

#### get_content(self, commit_hash: str) -> str

**作用**: 获取指定提交的内容

**输入参数**:
- `commit_hash`: str，提交哈希

**内部调用的函数**：
- `commit.get_content`: 获取提交内容

**输出参数**: str - 提交内容

#### get_presentable_content(self, commit_hash: str) -> str

**作用**: 获取提交的可展示内容，包含元数据

**输入参数**:
- `commit_hash`: str，提交哈希

**内部调用的函数**：
- `self.get_content`: 获取内容
- `textwrap.indent`: 缩进文本

**输出参数**: str - 可展示的内容

#### show_history(self, commit_hash: Optional[str] = None, show_diffs: bool = False) -> str

**作用**: 显示提交历史

**输入参数**:
- `commit_hash`: Optional[str]，起始提交哈希
- `show_diffs`: bool，是否显示差异

**内部调用的函数**：
- `Tree`: 创建树结构（如果有 rich）
- `Panel`: 创建面板（如果有 rich）
- `Syntax`: 语法高亮（如果有 rich）

**输出参数**: str - 历史表示

#### get_history(self, commit_hash: Optional[str] = None) -> List[str]

**作用**: 获取提交历史列表

**输入参数**:
- `commit_hash`: Optional[str]，起始提交哈希

**内部调用的函数**：
- 递归遍历父提交

**输出参数**: List[str] - 提交哈希列表

#### load_all(self) -> Dict[str, str]

**作用**: 加载所有提交的内容

**输入参数**: 无

**内部调用的函数**：
- `self.get_content`: 获取每个提交的内容

**输出参数**: Dict[str, str] - 提交哈希到内容的映射

#### merge_commits(self, commit_hashes: List[str], strategy: Literal["manual", "auto"] = "manual") -> str

**作用**: 合并多个提交

**输入参数**:
- `commit_hashes`: List[str]，要合并的提交哈希列表
- `strategy`: Literal["manual", "auto"]，合并策略

**内部调用的函数**：
- `ask_user`: 询问用户选择（手动模式）
- `self.get_content`: 获取提交内容
- `self.add_commit`: 添加合并后的提交

**输出参数**: str - 合并后的提交哈希

**其他说明**：
- 手动模式下让用户选择保留哪个版本
- 自动模式下选择最新的提交

### 设计模式和架构

#### 版本控制模式
- **Git 风格的提交模型**：使用提交、父子关系、差异等概念
- **内容寻址**：通过内容哈希唯一标识提交
- **增量存储**：通过差异减少存储空间

#### 图结构管理
- **DAG 结构**：确保版本历史的有向无环性
- **头尾跟踪**：维护图的入口和出口点
- **历史重构**：通过差异链重构完整内容

#### 合并策略
- **手动合并**：用户交互式选择
- **自动合并**：基于时间戳的自动选择
- **冲突处理**：提供多种合并策略

### 使用场景

1. **函数版本跟踪**：跟踪函数源代码的变化历史
2. **全局变量版本跟踪**：跟踪全局变量值的变化
3. **版本比较**：比较不同版本之间的差异
4. **历史查询**：查询组件的版本历史
5. **版本合并**：处理版本分支的合并

### 与其他模块的关系

- **deep_versions.py**：提供深层版本管理（包含依赖关系）
- **model.py**：使用节点类型进行内容表示
- **utils.py**：使用工具函数进行哈希计算
- **viz.py**：使用可视化函数显示差异和历史
