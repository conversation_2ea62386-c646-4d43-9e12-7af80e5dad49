# config.py 文档

## 文件内容与作用总体说明

`config.py` 文件是 mandala 框架的配置模块，定义了全局配置类 `Config`，用于管理框架的各种设置和可选依赖的检测。该文件负责检测系统中安装的可选库（如 PIL、torch、rich、prettytable），并提供相应的配置标志。同时还包含了一些与特定库相关的工具函数，如 PyTorch 张量到 numpy 数组的转换函数。

## 文件内的所有变量的作用与说明

### 全局函数变量
- `get_mandala_path`: 函数，用于获取 mandala 包的安装路径

### Config 类的类变量
- `func_interface_cls_name`: str，值为 "Op"，函数接口类的名称
- `mandala_path`: Path，mandala 包的安装路径
- `module_name`: str，值为 "mandala"，模块名称
- `tests_module_name`: str，值为 "mandala.tests"，测试模块名称
- `has_pil`: bool，是否安装了 PIL（Python Imaging Library）
- `has_torch`: bool，是否安装了 PyTorch
- `has_rich`: bool，是否安装了 rich（终端美化库）
- `has_prettytable`: bool，是否安装了 prettytable（表格美化库）

### 条件导入的变量
- `torch`: PyTorch 模块（仅在安装时可用）
- `tensor_to_numpy`: 函数，PyTorch 张量转换函数（仅在安装 PyTorch 时可用）

## 文件内的所有函数的作用与说明

### 路径获取函数

#### get_mandala_path() -> Path

**作用**: 获取 mandala 包的安装路径

**输入参数**: 无

**内部调用的函数**：
- import mandala: 导入 mandala 模块
- os.path.dirname: 获取目录路径
- Path: 创建路径对象

**输出参数**: Path - mandala 包的安装路径

**其他说明**：用于确定框架文件的位置

### PyTorch 相关函数（条件定义）

#### tensor_to_numpy(obj: Union[torch.Tensor, dict, list, tuple, Any]) -> Any

**作用**: 递归地将数据结构中的 PyTorch 张量转换为 numpy 数组

**输入参数**:
- `obj`: Union[torch.Tensor, dict, list, tuple, Any]，输入数据结构

**内部调用的函数**：
- isinstance: 检查对象类型
- obj.detach: 分离张量的梯度
- obj.cpu: 将张量移动到 CPU
- obj.numpy: 转换为 numpy 数组
- tensor_to_numpy: 递归调用自身

**输出参数**: Any - 转换后的数据结构

**其他说明**：
- 仅在安装了 PyTorch 时定义
- 递归处理嵌套的数据结构（字典、列表、元组）
- 对于 PyTorch 张量，先分离梯度并移动到 CPU，然后转换为 numpy 数组
- 对于其他类型的对象，直接返回

### Config 类

#### 类属性说明

**func_interface_cls_name**: 
- 作用：定义函数接口类的名称，用于识别被 @op 装饰的函数
- 值："Op"

**mandala_path**: 
- 作用：存储 mandala 包的安装路径
- 通过调用 get_mandala_path() 函数获得

**module_name**: 
- 作用：定义主模块名称
- 值："mandala"

**tests_module_name**: 
- 作用：定义测试模块名称
- 值："mandala.tests"

**has_pil**: 
- 作用：标识是否安装了 PIL（Python Imaging Library）
- 通过 try-except 块检测 PIL 的可用性

**has_torch**: 
- 作用：标识是否安装了 PyTorch
- 通过 try-except 块检测 torch 的可用性

**has_rich**: 
- 作用：标识是否安装了 rich 库
- 通过 try-except 块检测 rich 的可用性
- rich 用于终端输出美化

**has_prettytable**: 
- 作用：标识是否安装了 prettytable 库
- 通过 try-except 块检测 prettytable 的可用性
- prettytable 用于表格输出美化

### 依赖检测机制

配置文件使用 try-except 块来检测可选依赖：

```python
try:
    import library_name
    has_library = True
except ImportError:
    has_library = False
```

这种模式确保了：
1. 框架的核心功能不依赖于可选库
2. 当可选库可用时，提供增强功能
3. 当可选库不可用时，优雅降级或提供替代方案

### 条件功能定义

对于某些库（如 PyTorch），配置文件不仅检测其可用性，还在库可用时定义相关的工具函数：

- 如果 PyTorch 可用，定义 `tensor_to_numpy` 函数用于张量转换
- 这种模式允许框架在不同环境中提供不同级别的功能支持

### 使用场景

Config 类在框架中的主要使用场景：

1. **功能开关**：根据可选依赖的可用性启用或禁用特定功能
2. **路径解析**：提供框架文件的标准路径
3. **类型识别**：通过 func_interface_cls_name 识别装饰的函数
4. **测试支持**：为测试模块提供标准化的模块名称

### 扩展性

配置系统的设计支持：
- 添加新的可选依赖检测
- 扩展库特定的工具函数
- 修改框架的默认行为
- 支持不同部署环境的配置需求
