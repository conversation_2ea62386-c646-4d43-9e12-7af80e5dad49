<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.50.0 (0)
 -->
<!-- Title: G Pages: 1 -->
<svg width="359pt" height="236pt"
 viewBox="0.00 0.00 359.00 236.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 232)">
<title>G</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-232 355,-232 355,4 -4,4"/>
<!-- var_0 -->
<g id="node1" class="node">
<title>var_0</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M210.5,-36C210.5,-36 140.5,-36 140.5,-36 134.5,-36 128.5,-30 128.5,-24 128.5,-24 128.5,-12 128.5,-12 128.5,-6 134.5,0 140.5,0 140.5,0 210.5,0 210.5,0 216.5,0 222.5,-6 222.5,-12 222.5,-12 222.5,-24 222.5,-24 222.5,-30 216.5,-36 210.5,-36"/>
<text text-anchor="start" x="159.5" y="-20.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">var_0</text>
<text text-anchor="start" x="136.5" y="-10" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">3 values (3 sinks)</text>
</g>
<!-- force_update -->
<g id="node2" class="node">
<title>force_update</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M93,-228C93,-228 12,-228 12,-228 6,-228 0,-222 0,-216 0,-216 0,-204 0,-204 0,-198 6,-192 12,-192 12,-192 93,-192 93,-192 99,-192 105,-198 105,-204 105,-204 105,-216 105,-216 105,-222 99,-228 93,-228"/>
<text text-anchor="start" x="14.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">force_update</text>
<text text-anchor="start" x="8" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- hash_controlled_computation -->
<g id="node5" class="node">
<title>hash_controlled_computation</title>
<path fill="none" stroke="#dc322f" stroke-width="1.5" d="M257.5,-134C257.5,-134 93.5,-134 93.5,-134 87.5,-134 81.5,-128 81.5,-122 81.5,-122 81.5,-106 81.5,-106 81.5,-100 87.5,-94 93.5,-94 93.5,-94 257.5,-94 257.5,-94 263.5,-94 269.5,-100 269.5,-106 269.5,-106 269.5,-122 269.5,-122 269.5,-128 263.5,-134 257.5,-134"/>
<text text-anchor="start" x="89.5" y="-121.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">hash_controlled_computation</text>
<text text-anchor="start" x="100" y="-111" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">@op:hash_controlled_computation</text>
<text text-anchor="start" x="161" y="-101" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#dc322f">3 calls</text>
</g>
<!-- force_update&#45;&gt;hash_controlled_computation -->
<g id="edge3" class="edge">
<title>force_update&#45;&gt;hash_controlled_computation</title>
<path fill="none" stroke="#002b36" d="M69.33,-191.81C81.37,-179.96 98.25,-164.21 114.5,-152 120.29,-147.65 126.61,-143.36 132.92,-139.33"/>
<polygon fill="#002b36" stroke="#002b36" points="134.83,-142.26 141.48,-134.01 131.14,-136.32 134.83,-142.26"/>
<text text-anchor="middle" x="143" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">force_update</text>
<text text-anchor="middle" x="143" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- param_hash -->
<g id="node3" class="node">
<title>param_hash</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M216,-228C216,-228 135,-228 135,-228 129,-228 123,-222 123,-216 123,-216 123,-204 123,-204 123,-198 129,-192 135,-192 135,-192 216,-192 216,-192 222,-192 228,-198 228,-204 228,-204 228,-216 228,-216 228,-222 222,-228 216,-228"/>
<text text-anchor="start" x="139.5" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">param_hash</text>
<text text-anchor="start" x="131" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">1 values (1 sources)</text>
</g>
<!-- param_hash&#45;&gt;hash_controlled_computation -->
<g id="edge4" class="edge">
<title>param_hash&#45;&gt;hash_controlled_computation</title>
<path fill="none" stroke="#002b36" d="M175.5,-191.76C175.5,-178.5 175.5,-159.86 175.5,-144.27"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-144.07 175.5,-134.07 172,-144.07 179,-144.07"/>
<text text-anchor="middle" x="202.5" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">param_hash</text>
<text text-anchor="middle" x="202.5" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(1 values)</text>
</g>
<!-- data -->
<g id="node4" class="node">
<title>data</title>
<path fill="none" stroke="#268bd2" stroke-width="1.5" d="M339,-228C339,-228 258,-228 258,-228 252,-228 246,-222 246,-216 246,-216 246,-204 246,-204 246,-198 252,-192 258,-192 258,-192 339,-192 339,-192 345,-192 351,-198 351,-204 351,-204 351,-216 351,-216 351,-222 345,-228 339,-228"/>
<text text-anchor="start" x="286" y="-212.4" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-weight="bold" font-size="12.00" fill="#002b36">data</text>
<text text-anchor="start" x="254" y="-202" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#268bd2">2 values (2 sources)</text>
</g>
<!-- data&#45;&gt;hash_controlled_computation -->
<g id="edge2" class="edge">
<title>data&#45;&gt;hash_controlled_computation</title>
<path fill="none" stroke="#002b36" d="M280.36,-191.76C267.66,-180.03 250.07,-164.43 233.5,-152 227.98,-147.86 222,-143.7 216.04,-139.74"/>
<polygon fill="#002b36" stroke="#002b36" points="217.68,-136.64 207.39,-134.12 213.87,-142.51 217.68,-136.64"/>
<text text-anchor="middle" x="280" y="-166" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">data</text>
<text text-anchor="middle" x="280" y="-155" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(2 values)</text>
</g>
<!-- hash_controlled_computation&#45;&gt;var_0 -->
<g id="edge1" class="edge">
<title>hash_controlled_computation&#45;&gt;var_0</title>
<path fill="none" stroke="#002b36" d="M175.5,-93.98C175.5,-80.34 175.5,-61.75 175.5,-46.5"/>
<polygon fill="#002b36" stroke="#002b36" points="179,-46.1 175.5,-36.1 172,-46.1 179,-46.1"/>
<text text-anchor="middle" x="197" y="-68" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">output_0</text>
<text text-anchor="middle" x="197" y="-57" font-family="Liberation Sans,Helvetica,Arial,sans-serif" font-size="10.00" fill="#002b36">(3 values)</text>
</g>
</g>
</svg>
