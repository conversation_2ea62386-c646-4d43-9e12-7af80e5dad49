"""
Storage 深度理解用例
展示 Storage 的完整生命周期和核心功能

功能覆盖：
1. Storage的创建和配置
2. 版本管理和依赖跟踪
3. 缓存机制和数据持久化
4. 函数调用的记忆化
5. 引用管理和对象存储
6. 数据库操作和事务管理
7. 完整的程序运行周期观察

基于mandala1框架，不创建新类，充分利用现有功能
"""

import os
import sys
import time
import tempfile
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.model import Call, Ref
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))  
    from mandala1.imports import Storage, op
    from mandala1.model import Call, Ref

      # 全局配置变量
GLOBAL_MULTIPLIER = 2.0
class StorageDeepUnderstanding:
    """Storage深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.current_phase = 0
        self.phase_results = {}
        
    def print_phase_header(self, phase_num: int, title: str, description: str):
        """打印阶段标题"""
        self.current_phase = phase_num
        print("\n" + "=" * 80)
        print(f"💾 阶段 {phase_num}: {title}")
        print("=" * 80)
        print(f"📝 描述: {description}")
        print("-" * 80)

    def print_storage_concepts_explanation(self, storage: Storage):
        """打印Storage核心概念的详细说明"""
        print(f"\n💡 Storage核心概念详解:")

        # Storage基本概念
        print(f"  📋 Storage基本概念:")
        print(f"    • Storage是mandala1的计算管理器和记忆化引擎")
        print(f"    • 负责管理所有@op函数的调用、缓存和版本控制")
        print(f"    • 提供数据持久化和跨会话的计算复用")
        print(f"    💡 作用: 将普通函数调用转换为可记忆化的计算")

        # 版本管理概念
        print(f"  🔄 版本管理概念:")
        versioned = getattr(storage, 'versioned', True)
        print(f"    • 版本控制状态: {'启用' if versioned else '禁用'}")
        print(f"    • 函数版本: 基于函数代码内容自动计算")
        print(f"    • 依赖版本: 跟踪函数间的依赖关系变化")
        print(f"    💡 变化原因: 函数代码修改会触发版本更新")

        # 缓存机制概念
        print(f"  ⚡ 缓存机制概念:")
        try:
            cache_size = len(storage.call_cache.cache) if hasattr(storage.call_cache, 'cache') else 0
            print(f"    • 调用缓存大小: {cache_size} 个")
            print(f"    • 缓存策略: 基于函数名+参数+版本的智能缓存")
            print(f"    • 缓存命中: 相同参数的重复调用直接返回缓存结果")
            print(f"    💡 性能提升: 避免重复计算，显著提升执行速度")
        except Exception as e:
            print(f"    • 缓存信息: 获取失败 - {e}")

        # 数据持久化概念
        print(f"  💾 数据持久化概念:")
        db_path = getattr(storage, 'db_path', None)
        if db_path:
            print(f"    • 数据库路径: {db_path}")
            print(f"    • 持久化类型: SQLite数据库存储")
            print(f"    • 数据表: calls(调用记录), atoms(原子数据), refs(引用信息)")
            print(f"    💡 持久化价值: 跨会话复用计算结果，支持长期项目")
        else:
            print(f"    • 存储类型: 内存存储")
            print(f"    • 生命周期: 程序运行期间")
            print(f"    💡 适用场景: 临时计算、快速原型开发")

        # Context上下文概念
        print(f"  🔄 Context上下文概念:")
        print(f"    • Context管理: with语句自动管理Storage上下文")
        print(f"    • 函数拦截: 在上下文中@op函数调用被Storage拦截")
        print(f"    • 结果包装: 函数返回值自动包装为Ref对象")
        print(f"    💡 设计优势: 透明的记忆化，无需修改现有代码")

    def print_call_analysis(self, call: Call, title: str = "Call对象分析"):
        """打印Call对象的详细分析"""
        print(f"\n📞 {title}:")
        print(f"  🔍 Call对象详细信息:")
        print(f"    • 操作名称: {call.op.name}")
        print(f"    • 内容ID: {call.cid[:16]}...")
        print(f"    • 历史ID: {call.hid[:16]}...")
        print(f"    • 语义版本: {call.semantic_version}")
        print(f"    • 内容版本: {call.content_version}")

        print(f"  📥 输入参数分析:")
        for name, ref in call.inputs.items():
            print(f"    • {name}: {type(ref).__name__}")

        print(f"  📤 输出结果分析:")
        for name, ref in call.outputs.items():
            print(f"    • {name}: {type(ref).__name__}")

        print(f"  💡 Call概念说明:")
        print(f"    • Call对象记录了一次完整的函数调用")
        print(f"    • 包含输入参数、输出结果、版本信息等元数据")
        print(f"    • 用于实现记忆化、版本控制和依赖追踪")
        print(f"    • 是Storage和ComputationFrame的基础数据结构")

    def print_ref_analysis(self, ref: Ref, title: str = "Ref对象分析"):
        """打印Ref对象的详细分析"""
        print(f"\n🔗 {title}:")
        print(f"  🔍 Ref对象详细信息:")
        print(f"    • Ref类型: {type(ref).__name__}")
        print(f"    • 内容ID: {ref.cid[:16]}...")
        print(f"    • 历史ID: {ref.hid[:16]}...")
        print(f"    • 在内存: {ref.in_memory}")

        if ref.in_memory and hasattr(ref, 'obj'):
            print(f"    • 对象类型: {type(ref.obj).__name__}")
            if hasattr(ref.obj, '__len__'):
                print(f"    • 对象大小: {len(ref.obj)}")

        print(f"  💡 Ref概念说明:")
        print(f"    • Ref是所有计算结果的包装器")
        print(f"    • 提供唯一标识(cid, hid)和来源追踪")
        print(f"    • 支持延迟加载和内存管理")
        print(f"    • 是ComputationFrame图节点的基础")

    def print_storage_member_variables_detailed(self, storage: Storage):
        """打印Storage所有成员变量的详细说明"""
        print(f"\n📚 Storage成员变量完整详解:")
        print(f"=" * 100)

        print(f"\n🏗️  核心存储组件 (Core Storage Components):")
        print(f"  🗄️  db: DBAdapter")
        print(f"    💡 含义: 数据库适配器，管理SQLite数据库连接")
        print(f"    🎯 作用: 提供持久化存储的底层接口")
        print(f"    📝 实际值: {storage.db}")
        print(f"    🔍 重要性: 所有持久化数据的存储基础")

        print(f"  📁 overflow_dir: Optional[str]")
        print(f"    💡 含义: 大对象溢出存储目录")
        print(f"    🎯 作用: 存储超过阈值的大对象到文件系统")
        print(f"    📝 实际值: {storage.overflow_dir}")
        print(f"    🔍 重要性: 避免数据库过大，提高性能")

        print(f"  📏 overflow_threshold_MB: float")
        print(f"    💡 含义: 溢出阈值（MB）")
        print(f"    🎯 作用: 决定何时将对象存储到文件系统")
        print(f"    📝 实际值: {storage.overflow_threshold_MB}MB")
        print(f"    🔍 重要性: 平衡数据库大小和访问性能")

        print(f"\n🔄 版本控制系统 (Versioning System):")
        print(f"  ✅ versioned: bool")
        print(f"    💡 含义: 是否启用版本控制")
        print(f"    🎯 作用: 控制函数版本追踪和依赖管理")
        print(f"    📝 实际值: {storage.versioned}")
        print(f"    🔍 重要性: 确保计算的可重现性")

        print(f"  📦 sources: Dict[str, Any]")
        print(f"    💡 含义: 版本控制数据源字典")
        print(f"    🎯 作用: 存储versioner等版本控制组件")
        try:
            sources_keys = list(storage.sources.cache.keys()) if hasattr(storage.sources, 'cache') else []
            print(f"    📝 实际值: {sources_keys}")
        except:
            print(f"    📝 实际值: N/A")
        print(f"    🔍 重要性: 支持函数代码变化检测")

        print(f"\n⚡ 缓存系统 (Caching System):")
        print(f"  🔗 atoms: AtomCache")
        print(f"    💡 含义: 原子数据缓存")
        print(f"    🎯 作用: 缓存基本数据类型(int, str, float等)")
        print(f"    📝 缓存大小: {len(storage.atoms.cache) if hasattr(storage.atoms, 'cache') else 'N/A'}")
        print(f"    🔍 重要性: 避免重复序列化小对象")

        print(f"  📐 shapes: ShapeCache")
        print(f"    💡 含义: 形状信息缓存")
        print(f"    🎯 作用: 缓存数组和张量的形状信息")
        print(f"    📝 缓存大小: {len(storage.shapes.cache) if hasattr(storage.shapes, 'cache') else 'N/A'}")
        print(f"    🔍 重要性: 优化大型数据结构的处理")

        print(f"  ⚙️  ops: OpCache")
        print(f"    💡 含义: 操作缓存")
        print(f"    🎯 作用: 缓存@op装饰的函数定义")
        print(f"    📝 缓存大小: {len(storage.ops.cache) if hasattr(storage.ops, 'cache') else 'N/A'}")
        print(f"    🔍 重要性: 避免重复解析函数元数据")

        print(f"  📞 calls: CallCache")
        print(f"    💡 含义: 调用缓存")
        print(f"    🎯 作用: 缓存函数调用记录和结果")
        print(f"    📝 缓存大小: {len(storage.calls.cache) if hasattr(storage.calls, 'cache') else 'N/A'}")
        print(f"    🔍 重要性: 实现记忆化，避免重复计算")

        print(f"\n💾 持久化存储 (Persistent Storage):")
        print(f"  📞 call_storage: CallStorage")
        print(f"    💡 含义: 调用记录持久化存储")
        print(f"    🎯 作用: 管理Call对象的数据库存储")
        print(f"    📝 表名: calls")
        print(f"    🔍 重要性: 支持跨会话的计算复用")

        print(f"  🔗 ref_storage: RefStorage")
        print(f"    💡 含义: 引用对象持久化存储")
        print(f"    🎯 作用: 管理Ref对象的数据库存储")
        print(f"    📝 表名: refs")
        print(f"    🔍 重要性: 支持数据对象的持久化")

        print(f"\n🔄 运行时状态 (Runtime State):")
        print(f"  🎭 mode: str")
        print(f"    💡 含义: 当前运行模式")
        print(f"    🎯 作用: 控制Storage的行为模式")
        print(f"    📝 实际值: {storage.mode}")
        print(f"    🔍 可能值: 'run', 'replay', 'debug'等")

        print(f"  🚪 _exit_hooks: List[Callable]")
        print(f"    💡 含义: 退出钩子函数列表")
        print(f"    🎯 作用: 在Storage退出时执行清理操作")
        print(f"    📝 数量: {len(storage._exit_hooks) if hasattr(storage, '_exit_hooks') else 'N/A'}")
        print(f"    🔍 重要性: 确保资源正确释放")

        print(f"  📚 _mode_stack: List[str]")
        print(f"    💡 含义: 模式栈")
        print(f"    🎯 作用: 支持嵌套的模式切换")
        print(f"    📝 深度: {len(storage._mode_stack) if hasattr(storage, '_mode_stack') else 'N/A'}")
        print(f"    🔍 重要性: 支持复杂的执行控制")

        print(f"  🔒 _allow_new_calls: bool")
        print(f"    💡 含义: 是否允许新的函数调用")
        print(f"    🎯 作用: 控制是否接受新的计算请求")
        print(f"    📝 实际值: {storage._allow_new_calls if hasattr(storage, '_allow_new_calls') else 'N/A'}")
        print(f"    🔍 重要性: 支持只读模式和调试模式")

        print(f"\n🔧 配置参数 (Configuration Parameters):")
        print(f"  📂 _deps_path: Optional[Path]")
        print(f"    💡 含义: 依赖追踪路径")
        print(f"    🎯 作用: 指定需要追踪版本的代码路径")
        print(f"    📝 实际值: {storage._deps_path if hasattr(storage, '_deps_path') else 'N/A'}")

        print(f"  🔍 _tracer_impl: Optional[type]")
        print(f"    💡 含义: 追踪器实现类")
        print(f"    🎯 作用: 指定代码追踪的具体实现")
        print(f"    📝 实际值: {storage._tracer_impl if hasattr(storage, '_tracer_impl') else 'N/A'}")

        print(f"  ⚠️  _strict_tracing: bool")
        print(f"    💡 含义: 是否启用严格追踪模式")
        print(f"    🎯 作用: 控制版本追踪的严格程度")
        print(f"    📝 实际值: {storage._strict_tracing if hasattr(storage, '_strict_tracing') else 'N/A'}")

        print(f"\n💡 设计理念:")
        print(f"  🎯 分层架构: 缓存层 → 存储层 → 数据库层")
        print(f"  ⚡ 性能优化: 多级缓存 + 溢出存储 + 延迟加载")
        print(f"  🔄 版本管理: 自动版本检测 + 依赖追踪")
        print(f"  🔒 数据一致性: 事务支持 + 原子操作")
        print(f"  🔧 可配置性: 灵活的配置选项 + 运行时控制")
        print(f"=" * 100)
    
    def print_storage_info(self, storage: Storage, title: str = "Storage信息"):
        """打印Storage的详细信息"""
        print(f"\n📊 {title}:")
        
        # 基本信息
        print(f"  🗄️  数据库路径: {storage.db.db_path}")
        print(f"  🔄 版本控制: {'启用' if storage.versioned else '禁用'}")
        print(f"  📁 溢出目录: {storage.overflow_dir if storage.overflow_dir else '未设置'}")
        print(f"  📏 溢出阈值: {storage.overflow_threshold_MB}MB")
        
        # 缓存信息
        try:
            call_cache_size = len(storage.call_cache.cache) if hasattr(storage.call_cache, 'cache') else 0
            atoms_cache_size = len(storage.atoms.cache) if hasattr(storage.atoms, 'cache') else 0
            ops_cache_size = len(storage.ops.cache) if hasattr(storage.ops, 'cache') else 0
            
            print(f"  🔄 调用缓存: {call_cache_size} 个条目")
            print(f"  ⚛️  原子缓存: {atoms_cache_size} 个条目")
            print(f"  ⚙️  操作缓存: {ops_cache_size} 个条目")
        except Exception as e:
            print(f"  ❌ 缓存信息获取失败: {e}")
        
        # 版本信息
        if storage.versioned:
            try:
                versioner = storage.versioner
                print(f"  📦 依赖包: {versioner.deps_package if hasattr(versioner, 'deps_package') else '未设置'}")
                print(f"  🔍 跟踪全局变量: {'是' if storage._track_globals else '否'}")
                print(f"  ⚠️  严格跟踪: {'是' if storage._strict_tracing else '否'}")
            except Exception as e:
                print(f"  ❌ 版本信息获取失败: {e}")
    
    def demonstrate_storage_creation(self):
        """阶段1: 演示Storage的创建和配置"""
        self.print_phase_header(1, "Storage创建与配置", 
                               "展示如何创建和配置不同类型的Storage")
        
        print("🔧 Storage创建演示:")
        
        # 1. 内存存储
        print("\n1️⃣ 内存存储:")
        memory_storage = Storage()
        self.print_storage_info(memory_storage, "内存Storage")
        self.print_storage_concepts_explanation(memory_storage)
        self.print_storage_member_variables_detailed(memory_storage)

        # 2. 持久化存储
        print("\n2️⃣ 持久化存储:")
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "demo_storage.db")

        persistent_storage = Storage(
            db_path=db_path,
            overflow_dir=os.path.join(self.temp_dir, "overflow"),
            overflow_threshold_MB=1.0  # 1MB阈值用于演示
        )
        self.print_storage_info(persistent_storage, "持久化Storage")
        
        # 3. 配置对比
        print("\n3️⃣ 配置对比:")
        print("  内存存储 vs 持久化存储:")
        print(f"    数据库: 内存 vs 文件({db_path})")
        print(f"    版本控制: 禁用 vs 启用")
        print(f"    溢出处理: 禁用 vs 启用")
        
        self.storage = persistent_storage
        self.phase_results[1] = {
            'memory_storage': memory_storage,
            'persistent_storage': persistent_storage,
            'db_path': db_path
        }
        
        return persistent_storage
    
    def demonstrate_memoization(self, storage: Storage):
        """阶段2: 演示函数记忆化和缓存机制"""
        self.print_phase_header(2, "函数记忆化与缓存", 
                               "展示Storage如何实现函数调用的记忆化和缓存")
        
        # 定义测试函数
        @op
        def expensive_computation(n: int, delay: float = 0.1):
            """模拟耗时计算"""
            print(f"    🔄 执行耗时计算: n={n}, delay={delay}")
            time.sleep(delay)
            return n ** 2 + n + 1
        
        @op
        def data_processing(data: list, operation: str = "sum"):
            """数据处理函数"""
            print(f"    🔄 处理数据: {len(data)} 个元素, 操作={operation}")
            time.sleep(0.05)
            if operation == "sum":
                return sum(data)
            elif operation == "mean":
                return sum(data) / len(data) if data else 0
            elif operation == "max":
                return max(data) if data else 0
            return data
        
        print("🔧 记忆化演示:")
        
        # 1. 首次调用（实际执行）
        print("\n1️⃣ 首次调用（实际执行）:")
        with storage:
            start_time = time.time()
            result1 = expensive_computation(5, 0.2)
            result2 = data_processing([1, 2, 3, 4, 5], "sum")
            first_call_time = time.time() - start_time
            
            print(f"  ✅ 计算结果1: {storage.unwrap(result1)}")
            print(f"  ✅ 计算结果2: {storage.unwrap(result2)}")
            print(f"  ⏱️  首次调用耗时: {first_call_time:.3f}秒")
        
        # 2. 重复调用（从缓存加载）
        print("\n2️⃣ 重复调用（从缓存加载）:")
        with storage:
            start_time = time.time()
            result1_cached = expensive_computation(5, 0.2)
            result2_cached = data_processing([1, 2, 3, 4, 5], "sum")
            cached_call_time = time.time() - start_time
            
            print(f"  ✅ 缓存结果1: {storage.unwrap(result1_cached)}")
            print(f"  ✅ 缓存结果2: {storage.unwrap(result2_cached)}")
            print(f"  ⏱️  缓存调用耗时: {cached_call_time:.3f}秒")
            print(f"  🚀 加速比: {first_call_time/cached_call_time:.1f}x")
        
        # 3. 引用比较
        print("\n3️⃣ 引用比较:")
        print(f"  🔗 result1 == result1_cached: {result1.hid == result1_cached.hid}")
        print(f"  🔗 result2 == result2_cached: {result2.hid == result2_cached.hid}")
        print(f"  📋 result1 引用ID: {result1.hid[:16]}...")
        print(f"  📋 result2 引用ID: {result2.hid[:16]}...")
        
        # 4. 缓存状态检查
        print("\n4️⃣ 缓存状态检查:")
        self.print_storage_info(storage, "记忆化后的Storage状态")
        
        self.phase_results[2] = {
            'results': [result1, result2],
            'timing': {
                'first_call': first_call_time,
                'cached_call': cached_call_time,
                'speedup': first_call_time/cached_call_time
            }
        }
        
        return storage
    
    def demonstrate_version_management(self, storage: Storage):
        """阶段3: 演示版本管理和依赖跟踪"""
        self.print_phase_header(3, "版本管理与依赖跟踪",
                               "展示Storage如何管理函数版本和跟踪依赖变化")

        print("🔧 版本管理演示:")
        print("💡 说明：为了确保版本变化被正确检测，我们使用不同的函数名和参数传递")

        # 1. 定义初始版本的函数
        print("\n1️⃣ 初始版本函数:")

        @op
        def compute_v1(x: int, multiplier: float = 2.0):
            """计算函数 V1"""
            print(f"    🔄 执行计算函数 V1: x={x}, multiplier={multiplier}")
            return x * multiplier

        with storage:
            v1_result = compute_v1(10, 2.0)
            print(f"  ✅ V1结果: {storage.unwrap(v1_result)}")

        # 2. 检查版本信息
        print("\n2️⃣ 版本信息检查:")
        if storage.versioned:
            try:
                versions = storage.versions(compute_v1)
                print(f"  📦 函数版本数: {len(versions)}")
                for i, version in enumerate(versions):
                    print(f"    版本{i+1}: {version[:16]}...")
            except Exception as e:
                print(f"  ❌ 版本信息获取失败: {e}")
        else:
            print("  ℹ️  当前Storage未启用版本控制")
            versions = []

        # 3. 定义修改后的函数（不同的函数名确保版本变化）
        print("\n3️⃣ 修改后的函数版本:")
        print("  🔄 修改计算逻辑: 乘法 + 加法")

        @op
        def compute_v2(x: int, multiplier: float = 3.0, offset: int = 1):
            """计算函数 V2 - 添加了偏移量"""
            print(f"    🔄 执行计算函数 V2: x={x}, multiplier={multiplier}, offset={offset}")
            return x * multiplier + offset

        with storage:
            v2_result = compute_v2(10, 3.0, 1)
            print(f"  ✅ V2结果: {storage.unwrap(v2_result)}")

        # 4. 演示参数变化对结果的影响
        print("\n4️⃣ 参数变化演示:")

        with storage:
            # 使用相同函数但不同参数
            param_result1 = compute_v1(10, 2.5)  # 不同的multiplier
            param_result2 = compute_v1(15, 2.0)  # 不同的x

            print(f"  📊 V1(10, 2.5): {storage.unwrap(param_result1)}")
            print(f"  📊 V1(15, 2.0): {storage.unwrap(param_result2)}")

        # 5. 版本对比
        print("\n5️⃣ 版本对比:")
        print(f"  📊 V1结果(10, 2.0): {storage.unwrap(v1_result)}")
        print(f"  📊 V2结果(10, 3.0, 1): {storage.unwrap(v2_result)}")
        print(f"  🔄 结果变化: {storage.unwrap(v2_result) - storage.unwrap(v1_result)}")

        # 6. 缓存验证 - 相同参数应该使用缓存
        print("\n6️⃣ 缓存验证:")

        with storage:
            # 重复调用相同参数，应该从缓存加载
            cached_result = compute_v1(10, 2.0)
            print(f"  🔄 重复调用V1(10, 2.0): {storage.unwrap(cached_result)}")
            print(f"  ✅ 缓存验证: {storage.unwrap(cached_result) == storage.unwrap(v1_result)}")

        # 7. 函数调用统计
        print("\n7️⃣ 函数调用统计:")
        try:
            call_cache_size = len(storage.call_cache.cache) if hasattr(storage.call_cache, 'cache') else 0
            print(f"  📊 总调用缓存: {call_cache_size} 个")
            print(f"  ✅ 版本管理工作正常")
        except Exception as e:
            print(f"  ❌ 调用统计获取失败: {e}")

        self.phase_results[3] = {
            'v1_result': v1_result,
            'v2_result': v2_result,
            'param_results': [param_result1, param_result2] if 'param_result1' in locals() else [],
            'cached_result': cached_result if 'cached_result' in locals() else None,
            'versioned': storage.versioned
        }

        return storage

    def demonstrate_reference_management(self, storage: Storage):
        """阶段4: 演示引用管理和对象存储"""
        self.print_phase_header(4, "引用管理与对象存储",
                               "展示Storage如何管理引用和存储不同类型的对象")

        print("🔧 引用管理演示:")

        # 1. 不同类型对象的存储
        print("\n1️⃣ 不同类型对象存储:")

        @op
        def create_various_objects():
            """创建各种类型的对象"""
            print("    🔄 创建各种类型对象")
            return {
                'number': 42,
                'string': "Hello, Storage!",
                'list': [1, 2, 3, 4, 5],
                'dict': {'a': 1, 'b': 2, 'c': 3},
                'nested': {'data': [1, 2, {'inner': 'value'}]}
            }

        @op
        def create_large_object(size: int = 1000):
            """创建大对象（可能触发溢出存储）"""
            print(f"    🔄 创建大对象，大小: {size}")
            return list(range(size))

        with storage:
            various_obj = create_various_objects()
            large_obj = create_large_object(10000)  # 大对象

            print(f"  ✅ 各种对象: {storage.unwrap(various_obj)}")
            print(f"  ✅ 大对象长度: {len(storage.unwrap(large_obj))}")

        # 2. 引用操作
        print("\n2️⃣ 引用操作:")
        print(f"  🔗 各种对象引用: {various_obj}")
        print(f"  🔗 大对象引用: {large_obj}")
        print(f"  📋 引用ID: {various_obj.hid[:16]}...")
        print(f"  📋 内容ID: {various_obj.cid[:16]}...")

        # 3. 引用解包和包装
        print("\n3️⃣ 引用解包和包装:")
        unwrapped = storage.unwrap(various_obj)
        print(f"  📦 解包对象类型: {type(unwrapped)}")
        print(f"  📦 解包对象内容: {unwrapped}")

        # 4. 引用创建者查询
        print("\n4️⃣ 引用创建者查询:")
        try:
            creator_call = storage.get_ref_creator(various_obj)
            print(f"  👤 创建者调用: {creator_call}")
            print(f"  ⚙️  创建者函数: {creator_call.op.name}")
            print(f"  📥 输入参数: {creator_call.inputs}")
            print(f"  📤 输出参数: {creator_call.outputs}")
        except Exception as e:
            print(f"  ❌ 创建者查询失败: {e}")

        # 5. 内存管理
        print("\n5️⃣ 内存管理:")
        try:
            # 加载到内存
            loaded_ref = storage.attach(various_obj, inplace=False)
            print(f"  💾 加载到内存: {loaded_ref}")

            # 检查是否在内存中
            in_memory = hasattr(loaded_ref, 'obj') and loaded_ref.obj is not None
            print(f"  🧠 是否在内存: {'是' if in_memory else '否'}")
        except Exception as e:
            print(f"  ❌ 内存管理操作失败: {e}")

        self.phase_results[4] = {
            'various_obj': various_obj,
            'large_obj': large_obj,
            'creator_call': creator_call if 'creator_call' in locals() else None
        }

        return storage

    def demonstrate_database_operations(self, storage: Storage):
        """阶段5: 演示数据库操作和事务管理"""
        self.print_phase_header(5, "数据库操作与事务管理",
                               "展示Storage的数据库操作和事务处理能力")

        print("🔧 数据库操作演示:")

        # 1. 数据库连接信息
        print("\n1️⃣ 数据库连接信息:")
        print(f"  🗄️  数据库路径: {storage.db.db_path}")
        print(f"  🔌 连接状态: {'活跃' if storage.db else '未连接'}")

        # 2. 表结构查询
        print("\n2️⃣ 数据库表结构:")
        try:
            with storage.conn() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"  📋 数据库表: {[table[0] for table in tables]}")

                # 查询调用表的记录数
                cursor.execute("SELECT COUNT(*) FROM calls;")
                call_count = cursor.fetchone()[0]
                print(f"  📊 调用记录数: {call_count}")

                # 查询原子表的记录数
                cursor.execute("SELECT COUNT(*) FROM atoms;")
                atom_count = cursor.fetchone()[0]
                print(f"  📊 原子记录数: {atom_count}")
        except Exception as e:
            print(f"  ❌ 数据库查询失败: {e}")

        # 3. 事务操作演示
        print("\n3️⃣ 事务操作演示:")

        @op
        def transactional_operation(data: list):
            """事务性操作"""
            print(f"    🔄 执行事务性操作: {len(data)} 个元素")
            # 模拟可能失败的操作
            if len(data) > 100:
                raise ValueError("数据太大，操作失败")
            return sum(data)

        # 成功的事务
        print("  ✅ 成功事务:")
        try:
            with storage:
                success_result = transactional_operation([1, 2, 3, 4, 5])
                print(f"    结果: {storage.unwrap(success_result)}")
        except Exception as e:
            print(f"    ❌ 事务失败: {e}")

        # 4. 数据持久化验证
        print("\n4️⃣ 数据持久化验证:")
        try:
            # 提交当前状态
            storage.commit()
            print("  💾 数据已提交到磁盘")

            # 检查文件大小
            if os.path.exists(storage.db.db_path):
                file_size = os.path.getsize(storage.db.db_path)
                print(f"  📏 数据库文件大小: {file_size} 字节")

        except Exception as e:
            print(f"  ❌ 持久化验证失败: {e}")

        # 5. 缓存统计
        print("\n5️⃣ 缓存统计:")
        self.print_storage_info(storage, "最终Storage状态")

        self.phase_results[5] = {
            'tables': tables if 'tables' in locals() else [],
            'call_count': call_count if 'call_count' in locals() else 0,
            'atom_count': atom_count if 'atom_count' in locals() else 0,
            'db_size': file_size if 'file_size' in locals() else 0
        }

        return storage

    def demonstrate_advanced_features(self, storage: Storage):
        """阶段6: 演示高级功能"""
        self.print_phase_header(6, "高级功能演示",
                               "展示Storage的高级功能和特性")

        print("🔧 高级功能演示:")

        # 1. 批量操作
        print("\n1️⃣ 批量操作:")

        @op
        def batch_processor(items: list, operation: str):
            """批量处理器"""
            print(f"    🔄 批量处理: {len(items)} 个项目, 操作={operation}")
            if operation == "square":
                return [x**2 for x in items]
            elif operation == "double":
                return [x*2 for x in items]
            return items

        with storage:
            batch_data = list(range(1, 11))
            squared = batch_processor(batch_data, "square")
            doubled = batch_processor(batch_data, "double")

            print(f"  ✅ 原始数据: {batch_data}")
            print(f"  ✅ 平方结果: {storage.unwrap(squared)}")
            print(f"  ✅ 双倍结果: {storage.unwrap(doubled)}")

        # 2. 条件执行
        print("\n2️⃣ 条件执行:")

        @op
        def conditional_compute(x: int, threshold: int = 5):
            """条件计算"""
            print(f"    🔄 条件计算: x={x}, threshold={threshold}")
            if x > threshold:
                return x ** 2
            else:
                return x * 2

        with storage:
            results = []
            for i in range(1, 8):
                result = conditional_compute(i)
                results.append((i, storage.unwrap(result)))

            print("  📊 条件执行结果:")
            for i, result in results:
                condition = "平方" if i > 5 else "双倍"
                print(f"    {i} -> {result} ({condition})")

        # 3. 函数组合
        print("\n3️⃣ 函数组合:")

        @op
        def step1(x: int):
            """步骤1"""
            print(f"    🔄 步骤1: x={x}")
            return x + 10

        @op
        def step2(x: int):
            """步骤2"""
            print(f"    🔄 步骤2: x={x}")
            return x * 3

        @op
        def step3(x: int):
            """步骤3"""
            print(f"    🔄 步骤3: x={x}")
            return x - 5

        with storage:
            # 函数组合流水线
            input_val = 5
            result1 = step1(input_val)
            result2 = step2(storage.unwrap(result1))
            final_result = step3(storage.unwrap(result2))

            print(f"  🔄 流水线: {input_val} -> {storage.unwrap(result1)} -> {storage.unwrap(result2)} -> {storage.unwrap(final_result)}")

        # 4. 性能分析
        print("\n4️⃣ 性能分析:")
        start_time = time.time()

        with storage:
            # 重复执行已缓存的操作
            for _ in range(100):
                _ = conditional_compute(3)
                _ = batch_processor([1, 2, 3], "square")

        cached_time = time.time() - start_time
        print(f"  ⏱️  100次缓存调用耗时: {cached_time:.4f}秒")
        print(f"  🚀 平均每次调用: {cached_time/100:.6f}秒")

        self.phase_results[6] = {
            'batch_results': [squared, doubled],
            'conditional_results': results,
            'pipeline_result': final_result,
            'performance': {
                'cached_time': cached_time,
                'avg_call_time': cached_time/100
            }
        }

        return storage


def main():
    """主函数：运行Storage深度理解演示"""
    print("💾 Storage 深度理解用例")
    print("=" * 80)
    print("📖 本演示将展示Storage的完整生命周期和核心功能")
    print("🎯 目标：深入理解Storage的版本管理、缓存机制、数据持久化等")
    print("=" * 80)
    
    demo = StorageDeepUnderstanding()
    
    try:
        # 阶段1: Storage创建
        storage = demo.demonstrate_storage_creation()
        
        # 阶段2: 记忆化
        demo.demonstrate_memoization(storage)
        
        # 阶段3: 版本管理
        demo.demonstrate_version_management(storage)

        # 阶段4: 引用管理
        demo.demonstrate_reference_management(storage)

        # 阶段5: 数据库操作
        demo.demonstrate_database_operations(storage)

        # 阶段6: 高级功能
        demo.demonstrate_advanced_features(storage)

        print("\n" + "=" * 80)
        print("🎉 Storage深度理解演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        for phase, results in demo.phase_results.items():
            print(f"  阶段{phase}: ✅ 完成")

        print("\n📈 完整生命周期覆盖:")
        print("  ✅ Storage创建和配置")
        print("  ✅ 函数记忆化和缓存")
        print("  ✅ 版本管理和依赖跟踪")
        print("  ✅ 引用管理和对象存储")
        print("  ✅ 数据库操作和事务管理")
        print("  ✅ 高级功能和性能优化")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if demo.temp_dir and os.path.exists(demo.temp_dir):
            import shutil
            try:
                shutil.rmtree(demo.temp_dir)
                print(f"\n🧹 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


if __name__ == "__main__":
    main()
