"""
栈重放功能测试程序
测试StackReplayAnalyzer的各项功能
"""

import os
import sys
import unittest
from pathlib import Path

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from mandala1.storage import Storage
from mandala1.model import op
from stack_replay import StackReplayAnalyzer


class TestStackReplay(unittest.TestCase):
    """栈重放功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.storage = Storage()
        self.analyzer = StackReplayAnalyzer(self.storage)
        
        # 定义测试函数
        @op
        def test_func_a(x: int) -> int:
            return x * 2
        
        @op
        def test_func_b(x: int, y: int) -> int:
            return x + y
        
        @op
        def test_func_c(x: int) -> dict:
            return {"value": x, "squared": x * x}
        
        self.test_func_a = test_func_a
        self.test_func_b = test_func_b
        self.test_func_c = test_func_c
    
    def test_basic_capture(self):
        """测试基本的执行信息捕获"""
        print("\n🧪 测试基本执行信息捕获")
        
        with self.storage:
            # 执行一些函数调用
            result1 = self.test_func_a(5)
            result2 = self.test_func_b(3, 4)
            result3 = self.test_func_c(6)
        
        # 获取计算框架
        cf = self.storage.cf(self.test_func_c).expand_all()
        
        # 捕获执行信息
        execution_info = self.analyzer.capture_execution_info(cf)
        
        # 验证基本信息
        self.assertIn("functions", execution_info)
        self.assertIn("variables", execution_info)
        self.assertIn("call_hierarchy", execution_info)
        
        # 验证函数信息
        functions = execution_info["functions"]
        self.assertGreater(len(functions), 0)
        
        print(f"✅ 捕获了 {len(functions)} 个函数的执行信息")
        
        for fname, func_info in functions.items():
            self.assertIn("call_count", func_info)
            self.assertIn("calls", func_info)
            print(f"   • {fname}: {func_info['call_count']} 次调用")
    
    def test_hierarchy_analysis(self):
        """测试层级分析功能"""
        print("\n🧪 测试层级分析功能")
        
        with self.storage:
            # 创建有依赖关系的调用
            x = self.test_func_a(10)  # 第一层
            y = self.test_func_b(x, 5)  # 第二层，依赖x
            z = self.test_func_c(y)  # 第三层，依赖y
        
        cf = self.storage.cf(self.test_func_c).expand_all()
        execution_info = self.analyzer.capture_execution_info(cf)
        
        # 验证层级信息
        hierarchy = execution_info["call_hierarchy"]
        self.assertIn("levels", hierarchy)
        self.assertIn("max_depth", hierarchy)
        
        levels = hierarchy["levels"]
        self.assertGreater(len(levels), 0)
        
        print(f"✅ 分析出 {len(levels)} 个层级")
        
        for level_info in levels:
            level = level_info["level"]
            functions = level_info["functions"]
            print(f"   层级 {level}: {functions}")
    
    def test_computation_frame_printing(self):
        """测试ComputationFrame打印功能"""
        print("\n🧪 测试ComputationFrame打印功能")
        
        with self.storage:
            # 执行一些调用
            for i in range(3):
                x = self.test_func_a(i)
                y = self.test_func_b(x, i)
        
        cf = self.storage.cf(self.test_func_b).expand_all()
        
        # 测试打印功能（不会抛出异常即为成功）
        try:
            self.analyzer.print_computation_frame_hierarchy(cf, detailed=True)
            print("✅ ComputationFrame打印功能正常")
        except Exception as e:
            self.fail(f"ComputationFrame打印失败: {e}")
    
    def test_storage_execution_printing(self):
        """测试Storage执行顺序打印功能"""
        print("\n🧪 测试Storage执行顺序打印功能")
        
        with self.storage:
            # 执行一些调用
            results = []
            for i in range(2):
                x = self.test_func_a(i + 1)
                y = self.test_func_b(x, i + 2)
                z = self.test_func_c(y)
                results.append(z)
        
        cf = self.storage.cf(self.test_func_c).expand_all()
        execution_info = self.analyzer.capture_execution_info(cf)
        
        # 测试打印功能
        try:
            self.analyzer.print_storage_execution_order(execution_info, detailed=True)
            print("✅ Storage执行顺序打印功能正常")
        except Exception as e:
            self.fail(f"Storage执行顺序打印失败: {e}")
    
    def test_summary_generation(self):
        """测试摘要生成功能"""
        print("\n🧪 测试摘要生成功能")
        
        with self.storage:
            # 执行一些调用
            x = self.test_func_a(100)
            y = self.test_func_b(x, 50)
            z = self.test_func_c(y)
        
        cf = self.storage.cf(self.test_func_c).expand_all()
        execution_info = self.analyzer.capture_execution_info(cf)
        
        # 生成摘要
        summary = self.analyzer.generate_execution_summary(execution_info)
        
        # 验证摘要内容
        self.assertIsInstance(summary, str)
        self.assertIn("执行摘要报告", summary)
        self.assertIn("函数总数", summary)
        self.assertIn("调用总数", summary)
        
        print("✅ 摘要生成功能正常")
        print(f"摘要长度: {len(summary)} 字符")
    
    def test_multiple_calls_same_function(self):
        """测试同一函数多次调用的分析"""
        print("\n🧪 测试同一函数多次调用分析")
        
        with self.storage:
            # 多次调用同一函数，不同参数
            results = []
            for i in range(5):
                result = self.test_func_a(i * 10)
                results.append(result)
        
        cf = self.storage.cf(self.test_func_a)
        execution_info = self.analyzer.capture_execution_info(cf)
        
        # 验证调用次数
        func_info = execution_info["functions"]["test_func_a"]
        self.assertEqual(func_info["call_count"], 5)
        self.assertEqual(len(func_info["calls"]), 5)
        
        print(f"✅ 正确分析了 {func_info['call_count']} 次函数调用")
        
        # 验证每次调用的参数不同
        call_inputs = [call["inputs"] for call in func_info["calls"]]
        self.assertEqual(len(set(str(inputs) for inputs in call_inputs)), 5)
        print("✅ 正确记录了不同的调用参数")


def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 栈重放功能综合测试")
    print("="*60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestStackReplay)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    print("\n📊 测试结果统计:")
    print(f"   • 运行测试: {result.testsRun}")
    print(f"   • 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   • 失败: {len(result.failures)}")
    print(f"   • 错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   • {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   • {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


def test_integration_with_demos():
    """测试与演示程序的集成"""
    print("\n🔗 集成测试 - 与演示程序")
    print("="*50)
    
    try:
        # 测试导入
        from simple_demo import demonstrate_simple_case
        from stack_replay_advanced_demo import analyze_specific_functions
        
        print("✅ 成功导入演示模块")
        
        # 测试简单演示
        print("\n🧪 测试简单演示...")
        analyzer1, info1 = demonstrate_simple_case()
        print("✅ 简单演示运行成功")
        
        # 测试高级演示
        print("\n🧪 测试特定函数分析...")
        analyzer2, info2 = analyze_specific_functions()
        print("✅ 特定函数分析运行成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🎬 栈重放测试程序启动")
    print("="*80)
    
    # 运行单元测试
    unit_test_success = run_comprehensive_test()
    
    # 运行集成测试
    integration_test_success = test_integration_with_demos()
    
    print("\n🎉 测试完成!")
    print("="*80)
    print("📝 测试总结:")
    print(f"   • 单元测试: {'✅ 通过' if unit_test_success else '❌ 失败'}")
    print(f"   • 集成测试: {'✅ 通过' if integration_test_success else '❌ 失败'}")
    
    if unit_test_success and integration_test_success:
        print("\n🎊 所有测试通过！栈重放功能工作正常。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
