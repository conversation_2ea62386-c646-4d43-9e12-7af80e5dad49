"""
ComputationFrame图概念深度理解演示
专门展示CF的图结构概念：源节点、汇节点、边、图拓扑等
通过具体案例展示这些概念如何变化以及变化的原因

重点概念：
1. 源节点(Sources) - 没有输入的节点，计算的起始点
2. 汇节点(Sinks) - 没有输出的节点，计算的终点
3. 边(Edges) - 节点间的依赖关系
4. 图拓扑 - 图的结构特征
5. 强连通分量 - 循环依赖检测
6. 关键路径 - 影响性能的路径

变量节点命名规则详解：
- var_xx: 自动生成的变量名，xx是递增的数字（0,1,2...）
- 命名来源：get_new_vname()方法根据name_hint生成唯一名称
- 生成规则：如果name_hint已存在，则添加_0, _1, _2...后缀
- 特殊处理：output_xx会被转换为var_xx格式

生产者与消费者概念：
- 生产者(Producer)：函数节点，通过计算产生数据
- 消费者(Consumer)：函数节点，使用数据进行计算
- 数据流向：函数→变量（生产），变量→函数（消费）
- 变化时机：当新的计算步骤加入图时，会产生新的生产/消费关系

通过动态构建不同的计算图来展示概念变化
"""

import os
import sys
import tempfile
from typing import Dict, List, Any

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


class CFGraphConceptsDemo:
    """ComputationFrame图概念深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.demo_results = {}
        
    def print_section_header(self, title: str, description: str):
        """打印章节标题"""
        print("\n" + "=" * 100)
        print(f"🔍 {title}")
        print("=" * 100)
        print(f"📝 {description}")
        print("-" * 100)

    def _analyze_variable_type(self, vname: str) -> str:
        """分析变量类型"""
        if vname.startswith('var_') and '_' in vname:
            parts = vname.split('_')
            if len(parts) >= 2 and parts[-1].isdigit():
                return f"自动生成变量(序号{parts[-1]})"
        elif vname == 'v':
            return "通用变量名"
        elif vname == 'source':
            return "数据源变量"
        elif any(keyword in vname for keyword in ['data', 'raw', 'processed']):
            return "数据类型变量"
        elif any(keyword in vname for keyword in ['clean', 'algorithm', 'model', 'features']):
            return "计算参数变量"
        else:
            return "自定义变量"

    def _analyze_graph_topology(self, cf: ComputationFrame, graph_name: str):
        """分析图的拓扑结构和形状"""
        nodes = list(cf.nodes)
        edges = list(cf.edges())
        sources = list(cf.sources)
        sinks = list(cf.sinks)

        print(f"      📐 图形状特征:")

        # 计算图的基本特征
        node_count = len(nodes)
        edge_count = len(edges)
        source_count = len(sources)
        sink_count = len(sinks)

        # 计算图的密度
        max_edges = node_count * (node_count - 1) if node_count > 1 else 0
        density = edge_count / max_edges if max_edges > 0 else 0

        print(f"        🔢 基本指标: {node_count}节点, {edge_count}边, 密度{density:.3f}")
        print(f"        🌱 源汇比例: {source_count}源({source_count/node_count:.1%}), {sink_count}汇({sink_count/node_count:.1%})")

        # 分析图的形状类型
        if source_count == 1 and sink_count == 1:
            if edge_count == node_count - 1:
                shape_type = "线性链式"
                print(f"        📏 图形状: {shape_type} - 单一的数据处理流水线")
            else:
                shape_type = "树状结构"
                print(f"        📏 图形状: {shape_type} - 有分支的计算树")
        elif source_count > 1 and sink_count == 1:
            shape_type = "汇聚型"
            print(f"        📏 图形状: {shape_type} - 多个输入汇聚到单一输出")
        elif source_count == 1 and sink_count > 1:
            shape_type = "分散型"
            print(f"        📏 图形状: {shape_type} - 单一输入分散到多个输出")
        elif source_count > 1 and sink_count > 1:
            shape_type = "网状结构"
            print(f"        📏 图形状: {shape_type} - 复杂的多输入多输出网络")
        else:
            shape_type = "特殊结构"
            print(f"        📏 图形状: {shape_type} - 可能存在循环或孤立节点")

        # 计算拓扑层次
        try:
            # 简单的层次分析：从源节点开始的最长路径
            max_depth = 0
            layers = {}

            # BFS计算每个节点的层次
            from collections import deque, defaultdict

            # 构建邻接表
            adj = defaultdict(list)
            in_degree = defaultdict(int)
            for src, dst, _ in edges:
                adj[src].append(dst)
                in_degree[dst] += 1

            # 拓扑排序计算层次
            queue = deque()
            for node in nodes:
                if in_degree[node] == 0:
                    queue.append((node, 0))
                    layers[node] = 0

            while queue:
                node, depth = queue.popleft()
                max_depth = max(max_depth, depth)

                for neighbor in adj[node]:
                    in_degree[neighbor] -= 1
                    if in_degree[neighbor] == 0:
                        layers[neighbor] = depth + 1
                        queue.append((neighbor, depth + 1))

            print(f"        📊 拓扑层次: {max_depth + 1}层深度")

            # 分析每层的节点
            layer_nodes = defaultdict(list)
            for node, layer in layers.items():
                layer_nodes[layer].append(node)

            print(f"        🔄 层次结构:")
            for layer in sorted(layer_nodes.keys()):
                nodes_in_layer = layer_nodes[layer]
                var_nodes = [n for n in nodes_in_layer if n in cf.vnames]
                func_nodes = [n for n in nodes_in_layer if n in cf.fnames]
                print(f"          第{layer}层: {len(nodes_in_layer)}个节点")
                if var_nodes:
                    print(f"            📦 变量: {var_nodes}")
                if func_nodes:
                    print(f"            ⚙️  函数: {func_nodes}")

        except Exception as e:
            print(f"        ⚠️  拓扑分析失败: 可能存在循环依赖")

        # 分析并行度
        max_parallel = 0
        func_nodes = [n for n in nodes if n in cf.fnames]
        if func_nodes:
            # 计算每个函数的依赖深度
            func_depths = {}
            for func in func_nodes:
                if func in layers:
                    func_depths[func] = layers[func]

            if func_depths:
                # 按层次分组函数
                func_by_layer = defaultdict(list)
                for func, depth in func_depths.items():
                    func_by_layer[depth].append(func)

                max_parallel = max(len(funcs) for funcs in func_by_layer.values()) if func_by_layer else 0
                print(f"        ⚡ 并行度分析: 最大{max_parallel}个函数可并行执行")

                if max_parallel > 1:
                    for layer, funcs in func_by_layer.items():
                        if len(funcs) > 1:
                            print(f"          第{layer}层可并行: {funcs}")

        # 关键路径分析
        if sources and sinks:
            print(f"        🎯 关键路径分析:")
            print(f"          起点: {sources}")
            print(f"          终点: {sinks}")
            if max_depth >= 0:
                print(f"          最长路径: {max_depth + 1}步")
                print(f"          💡 优化建议: {'可考虑并行优化' if max_parallel > 1 else '主要为串行计算'}")
    
    def print_detailed_cf_analysis(self, cf: ComputationFrame, title: str):
        """打印CF的详细图分析"""
        print(f"\n📊 {title}:")

        # 基本信息
        nodes = list(cf.nodes)
        edges = list(cf.edges())
        sources = list(cf.sources)
        sinks = list(cf.sinks)

        print(f"  🔢 节点总数: {len(nodes)}")
        print(f"  📦 变量节点: {len(cf.vnames)} 个 - {list(cf.vnames)}")
        print(f"  ⚙️  函数节点: {len(cf.fnames)} 个 - {list(cf.fnames)}")
        print(f"  🔗 边数量: {len(edges)}")
        print(f"  🌱 源节点: {len(sources)} 个 - {sources}")
        print(f"  🎯 汇节点: {len(sinks)} 个 - {sinks}")

        # 变量节点命名规则详解
        print(f"\n  📝 变量节点命名规则详解:")
        print(f"    💡 命名规则概念: ComputationFrame自动为变量节点生成唯一名称")
        if cf.vnames:
            var_names = list(cf.vnames)
            print(f"    🏷️  当前变量名分析:")
            for vname in sorted(var_names):
                if vname.startswith('var_') and '_' in vname:
                    parts = vname.split('_')
                    if len(parts) >= 2 and parts[-1].isdigit():
                        print(f"      • {vname}: 自动生成变量，序号={parts[-1]}")
                        print(f"        💡 生成规则: get_new_vname()从'var_0'开始递增")
                elif vname == 'v':
                    print(f"      • {vname}: 通用变量名，通常是用户指定的起始节点")
                elif vname == 'source':
                    print(f"      • {vname}: 描述性变量名，表示数据来源")
                elif any(keyword in vname for keyword in ['data', 'raw', 'processed']):
                    print(f"      • {vname}: 描述性变量名，反映数据性质")
                else:
                    print(f"      • {vname}: 自定义变量名")

            print(f"    🔧 命名生成机制:")
            print(f"      • get_new_vname(name_hint)方法负责生成唯一变量名")
            print(f"      • 如果name_hint不存在，直接使用")
            print(f"      • 如果name_hint已存在，添加_0, _1, _2...后缀")
            print(f"      • 特殊处理: 'output_xx'会被转换为'var_xx'格式")
            print(f"      • 确保图中所有变量名唯一，避免命名冲突")
        else:
            print(f"    ⚠️  无变量节点: 当前图中没有变量节点")

        # 详细的边分析
        print(f"\n  🔗 边的详细分析:")
        if edges:
            print(f"    📋 所有边:")
            for i, (src, dst, label) in enumerate(edges[:10]):  # 只显示前10条边
                src_type = "函数" if src in cf.fnames else "变量"
                dst_type = "函数" if dst in cf.fnames else "变量"
                print(f"      {i+1}. {src}({src_type}) --[{label}]--> {dst}({dst_type})")
            if len(edges) > 10:
                print(f"      ... 还有{len(edges)-10}条边")

            # 边类型统计
            func_to_var = sum(1 for src, dst, _ in edges if src in cf.fnames and dst in cf.vnames)
            var_to_func = sum(1 for src, dst, _ in edges if src in cf.vnames and dst in cf.fnames)
            func_to_func = sum(1 for src, dst, _ in edges if src in cf.fnames and dst in cf.fnames)
            var_to_var = sum(1 for src, dst, _ in edges if src in cf.vnames and dst in cf.vnames)

            print(f"    📊 边类型统计:")
            print(f"      • 函数→变量: {func_to_var} 条 (数据生产)")
            print(f"      • 变量→函数: {var_to_func} 条 (数据消费)")
            if func_to_func > 0:
                print(f"      • 函数→函数: {func_to_func} 条 (直接调用)")
            if var_to_var > 0:
                print(f"      • 变量→变量: {var_to_var} 条 (数据传递)")

            # 生产者与消费者概念详解
            print(f"\n    🏭 生产者与消费者概念详解:")
            print(f"      💡 核心概念: 描述数据在计算图中的流动方式")
            print(f"      🔧 生产者(Producer): 函数节点通过计算产生数据")
            print(f"        • 表现形式: 函数→变量的边")
            print(f"        • 数量统计: {func_to_var} 个生产关系")
            print(f"        • 实际含义: 函数执行后将结果存储到变量中")
            print(f"        • 变化时机: 当新函数被执行并产生输出时增加")

            print(f"      🍽️  消费者(Consumer): 函数节点使用数据进行计算")
            print(f"        • 表现形式: 变量→函数的边")
            print(f"        • 数量统计: {var_to_func} 个消费关系")
            print(f"        • 实际含义: 函数从变量中读取数据作为输入")
            print(f"        • 变化时机: 当函数需要使用已有数据时增加")

            print(f"      🔄 数据流向规律:")
            print(f"        • 正常流向: 数据 → 函数(消费) → 新数据(生产) → 下一个函数")
            print(f"        • 图特征: 函数和变量节点交替出现")
            print(f"        • 平衡关系: 生产和消费数量通常相近")

            if func_to_var > 0 or var_to_func > 0:
                print(f"      📈 当前图的生产消费特征:")
                if func_to_var > var_to_func:
                    print(f"        • 生产主导型: 更多函数在产生新数据")
                    print(f"        • 可能原因: 图包含更多的数据源或中间结果")
                elif var_to_func > func_to_var:
                    print(f"        • 消费主导型: 更多函数在使用已有数据")
                    print(f"        • 可能原因: 图包含更多的数据处理或分析步骤")
                else:
                    print(f"        • 平衡型: 生产和消费关系均衡")
                    print(f"        • 特征: 典型的数据处理流水线结构")
        else:
            print(f"    ⚠️  无边: 图中没有依赖关系")
            print(f"    💡 无边的含义:")
            print(f"      • 所有节点都是孤立的")
            print(f"      • 没有数据流动关系")
            print(f"      • 可能是单个函数调用的最小图")

        # 源节点详细分析
        print(f"\n  🌱 源节点详细分析:")
        print(f"    💡 源节点概念: 没有输入边的节点，代表计算的起始点")
        if sources:
            for src in sources:
                out_neighbors = list(cf.out_neighbors(src))
                src_type = "函数" if src in cf.fnames else "变量"
                print(f"    • {src}({src_type}) → 输出到: {out_neighbors}")
                if src_type == "变量":
                    print(f"      💡 变量源节点: 外部输入的数据或参数")
                else:
                    print(f"      💡 函数源节点: 无参数的计算函数")
        else:
            print(f"    ⚠️  无源节点: 可能存在循环依赖或图不完整")
            print(f"    💡 无源节点的原因:")
            print(f"      • 所有节点都有输入依赖")
            print(f"      • 存在循环依赖关系")
            print(f"      • 图是从中间节点开始构建的")

        # 汇节点详细分析
        print(f"\n  🎯 汇节点详细分析:")
        print(f"    💡 汇节点概念: 没有输出边的节点，代表计算的终点")
        if sinks:
            for sink in sinks:
                in_neighbors = list(cf.in_neighbors(sink))
                sink_type = "函数" if sink in cf.fnames else "变量"
                print(f"    • {sink}({sink_type}) ← 输入来自: {in_neighbors}")
                if sink_type == "变量":
                    print(f"      💡 变量汇节点: 最终的计算结果")
                else:
                    print(f"      💡 函数汇节点: 无返回值的操作函数")
        else:
            print(f"    ⚠️  无汇节点: 所有节点都有输出，可能存在循环")
            print(f"    💡 无汇节点的原因:")
            print(f"      • 所有节点的输出都被其他节点使用")
            print(f"      • 存在循环依赖关系")
            print(f"      • 图包含了下游的所有使用者")
        
        # 图拓扑分析
        print(f"\n  📊 图拓扑分析:")
        print(f"    💡 拓扑分析概念: 分析图的结构特征和计算特性")
        try:
            # 计算图的基本拓扑特征
            node_count = len(nodes)
            edge_count = len(edges)

            if node_count > 1:
                max_edges = node_count * (node_count - 1)
                density = edge_count / max_edges
                avg_degree = 2 * edge_count / node_count

                print(f"    • 图密度: {density:.3f} (边数/最大可能边数)")
                print(f"      💡 密度含义: 反映节点间连接的紧密程度")
                print(f"    • 平均度数: {avg_degree:.2f} (每个节点的平均连接数)")
                print(f"      💡 度数含义: 反映节点的连接活跃程度")

                # 图类型判断
                if density < 0.1:
                    print(f"    💡 稀疏图: 节点间连接较少，计算相对独立")
                    print(f"      • 优势: 易于并行化，模块化程度高")
                    print(f"      • 特点: 计算步骤相对独立，依赖关系简单")
                elif density > 0.5:
                    print(f"    💡 密集图: 节点间连接较多，计算高度相关")
                    print(f"      • 优势: 数据复用率高，计算效率高")
                    print(f"      • 特点: 计算步骤紧密耦合，依赖关系复杂")
                else:
                    print(f"    💡 中等密度图: 适中的计算复杂度")
                    print(f"      • 特点: 平衡的模块化和耦合度")

                # 图形状分析
                print(f"    📐 图形状分析:")
                if len(sources) == 1 and len(sinks) == 1:
                    print(f"      🔄 线性图: 单一输入到单一输出的流水线")
                    print(f"        • 特点: 顺序执行，无并行机会")
                    print(f"        • 适用: 数据处理流水线")
                elif len(sources) == 1 and len(sinks) > 1:
                    print(f"      🌳 树形图: 单一输入，多个输出分支")
                    print(f"        • 特点: 扇出结构，分支可并行")
                    print(f"        • 适用: 一对多的数据分发")
                elif len(sources) > 1 and len(sinks) == 1:
                    print(f"      🔀 汇聚图: 多个输入汇聚到单一输出")
                    print(f"        • 特点: 扇入结构，输入可并行")
                    print(f"        • 适用: 多对一的数据聚合")
                elif len(sources) > 1 and len(sinks) > 1:
                    print(f"      🕸️  网状图: 多输入多输出的复杂网络")
                    print(f"        • 特点: 复杂的数据流，多种并行机会")
                    print(f"        • 适用: 复杂的数据处理网络")
                else:
                    print(f"      ⚠️  特殊图: 无源或无汇的特殊结构")
                    print(f"        • 可能原因: 循环依赖或不完整的图")

            # 拓扑排序分析
            print(f"    🔄 拓扑排序分析:")
            print(f"      💡 拓扑排序概念: 确定无环图中节点的执行顺序")
            try:
                topo_order = cf.topsort_modulo_sccs()
                unique_levels = len(set(topo_order))
                level_counts = [list(topo_order).count(level) for level in set(topo_order)]
                max_parallel = max(level_counts) if level_counts else 0

                print(f"      • 拓扑层次: {unique_levels} 个不同层次")
                print(f"        💡 层次含义: 计算的执行阶段数")
                print(f"      • 最大并行度: {max_parallel}")
                print(f"        💡 并行度含义: 同时可执行的最大节点数")
                print(f"      • 层次分布: {dict(zip(set(topo_order), level_counts))}")
                print(f"        💡 分布含义: 每个层次的节点数量")
                print(f"      💡 并行机会: 同层次的节点可以并行执行")

                # 关键路径分析
                if unique_levels > 1:
                    print(f"      • 关键路径长度: {unique_levels} 步")
                    print(f"        💡 关键路径: 影响整体执行时间的最长路径")

            except Exception as e:
                print(f"      ❌ 拓扑排序失败: {e}")
                print(f"        💡 失败原因: 可能存在循环依赖")

        except Exception as e:
            print(f"    ❌ 拓扑分析失败: {e}")

        # 连通性分析
        print(f"\n  🔗 连通性分析:")
        print(f"    💡 连通性概念: 分析图中节点的可达性")
        try:
            # 分析强连通分量
            print(f"    • 强连通分量分析:")
            print(f"      💡 强连通分量: 相互可达的节点集合，用于检测循环依赖")

            # 简单的连通性检查
            if len(sources) > 0 and len(sinks) > 0:
                print(f"      ✅ 图具有明确的输入输出结构")
            elif len(sources) == 0 and len(sinks) == 0:
                print(f"      ⚠️  可能存在强连通分量（循环依赖）")
            else:
                print(f"      ⚠️  图结构不完整或特殊")

        except Exception as e:
            print(f"    ❌ 连通性分析失败: {e}")
    
    def print_graph_change_analysis(self, cf_before: ComputationFrame, cf_after: ComputationFrame, operation: str):
        """分析图变化"""
        print(f"\n🔄 图变化分析 - {operation}:")

        nodes_before = len(cf_before.nodes)
        nodes_after = len(cf_after.nodes)
        edges_before = len(list(cf_before.edges()))
        edges_after = len(list(cf_after.edges()))
        sources_before = len(cf_before.sources)
        sources_after = len(cf_after.sources)
        sinks_before = len(cf_before.sinks)
        sinks_after = len(cf_after.sinks)

        print(f"  📊 数量变化:")
        print(f"    • 节点: {nodes_before} → {nodes_after} ({nodes_after - nodes_before:+d})")
        print(f"    • 边: {edges_before} → {edges_after} ({edges_after - edges_before:+d})")
        print(f"    • 源节点: {sources_before} → {sources_after} ({sources_after - sources_before:+d})")
        print(f"    • 汇节点: {sinks_before} → {sinks_after} ({sinks_after - sinks_before:+d})")

        # 详细的节点溯源分析
        print(f"\n  🔍 节点详细溯源分析:")
        print(f"    📊 变化前图状态 ({nodes_before}个节点):")
        print(f"      📦 变量节点: {sorted(cf_before.vnames)} ({len(cf_before.vnames)}个)")
        print(f"      ⚙️  函数节点: {sorted(cf_before.fnames)} ({len(cf_before.fnames)}个)")
        print(f"      🌱 源节点: {sorted(cf_before.sources)} ({len(cf_before.sources)}个)")
        print(f"      🎯 汇节点: {sorted(cf_before.sinks)} ({len(cf_before.sinks)}个)")

        print(f"    📊 变化后图状态 ({nodes_after}个节点):")
        print(f"      📦 变量节点: {sorted(cf_after.vnames)} ({len(cf_after.vnames)}个)")
        print(f"      ⚙️  函数节点: {sorted(cf_after.fnames)} ({len(cf_after.fnames)}个)")
        print(f"      🌱 源节点: {sorted(cf_after.sources)} ({len(cf_after.sources)}个)")
        print(f"      🎯 汇节点: {sorted(cf_after.sinks)} ({len(cf_after.sinks)}个)")

        # 分析新增和删除的节点
        nodes_before_set = set(cf_before.nodes)
        nodes_after_set = set(cf_after.nodes)
        added_nodes = nodes_after_set - nodes_before_set
        removed_nodes = nodes_before_set - nodes_after_set
        common_nodes = nodes_before_set & nodes_after_set

        print(f"\n  🔄 节点变化详细分析:")
        print(f"    🔄 保持不变的节点 ({len(common_nodes)}个):")
        if common_nodes:
            for node in sorted(common_nodes):
                node_type = "变量" if node in cf_after.vnames else "函数"

                # 详细的入度分析
                before_in_edges = [e for e in cf_before.edges() if e[1] == node]
                after_in_edges = [e for e in cf_after.edges() if e[1] == node]
                before_in_nodes = [e[0] for e in before_in_edges]
                after_in_nodes = [e[0] for e in after_in_edges]

                # 详细的出度分析
                before_out_edges = [e for e in cf_before.edges() if e[0] == node]
                after_out_edges = [e for e in cf_after.edges() if e[0] == node]
                before_out_nodes = [e[1] for e in before_out_edges]
                after_out_nodes = [e[1] for e in after_out_edges]

                print(f"      • {node}({node_type}): 入度{len(before_in_edges)}→{len(after_in_edges)}, 出度{len(before_out_edges)}→{len(after_out_edges)}")

                # 详细的连接变化分析
                if before_in_nodes != after_in_nodes:
                    added_in = set(after_in_nodes) - set(before_in_nodes)
                    removed_in = set(before_in_nodes) - set(after_in_nodes)
                    if added_in:
                        print(f"        ⬅️  新增输入: {sorted(added_in)}")
                    if removed_in:
                        print(f"        ❌ 移除输入: {sorted(removed_in)}")
                    if set(before_in_nodes) & set(after_in_nodes):
                        common_in = set(before_in_nodes) & set(after_in_nodes)
                        print(f"        ⬅️  保持输入: {sorted(common_in)}")
                elif after_in_nodes:
                    print(f"        ⬅️  输入来源: {after_in_nodes}")

                if before_out_nodes != after_out_nodes:
                    added_out = set(after_out_nodes) - set(before_out_nodes)
                    removed_out = set(before_out_nodes) - set(after_out_nodes)
                    if added_out:
                        print(f"        ➡️  新增输出: {sorted(added_out)}")
                    if removed_out:
                        print(f"        ❌ 移除输出: {sorted(removed_out)}")
                    if set(before_out_nodes) & set(after_out_nodes):
                        common_out = set(before_out_nodes) & set(after_out_nodes)
                        print(f"        ➡️  保持输出: {sorted(common_out)}")
                elif after_out_nodes:
                    print(f"        ➡️  输出目标: {after_out_nodes}")

                # 分析连接变化的原因
                if len(after_in_edges) != len(before_in_edges) or len(after_out_edges) != len(before_out_edges):
                    print(f"        💡 连接变化原因: 图结构调整导致的连接关系变化")
        else:
            print(f"      • 无保持不变的节点")

        if added_nodes:
            print(f"    🆕 新增节点详细分析 ({len(added_nodes)}个):")
            for node in sorted(added_nodes):
                node_type = "变量" if node in cf_after.vnames else "函数"
                in_degree = len([e for e in cf_after.edges() if e[1] == node])
                out_degree = len([e for e in cf_after.edges() if e[0] == node])
                in_neighbors = [e[0] for e in cf_after.edges() if e[1] == node]
                out_neighbors = [e[1] for e in cf_after.edges() if e[0] == node]

                print(f"      • {node}({node_type}):")
                print(f"        � 连接度: 入度{in_degree}, 出度{out_degree}")
                # 详细的输入分析
                if in_neighbors:
                    print(f"        ⬅️  输入来源详情:")
                    in_edges = [e for e in cf_after.edges() if e[1] == node]
                    for i, (src, dst, label) in enumerate(in_edges):
                        src_type = "函数" if src in cf_after.fnames else "变量"
                        print(f"          {i+1}. {src}({src_type}) --[{label}]--> {node}")

                        # 分析输入来源的性质
                        if src in cf_after.sources:
                            print(f"             💡 {src}是源节点，提供外部输入")
                        elif src in added_nodes:
                            print(f"             💡 {src}是新增节点，提供新计算的数据")
                        else:
                            print(f"             💡 {src}是现有节点，提供已有的数据")
                else:
                    print(f"        ⬅️  无输入来源 (源节点)")

                # 详细的输出分析
                if out_neighbors:
                    print(f"        ➡️  输出目标详情:")
                    out_edges = [e for e in cf_after.edges() if e[0] == node]
                    for i, (src, dst, label) in enumerate(out_edges):
                        dst_type = "函数" if dst in cf_after.fnames else "变量"
                        print(f"          {i+1}. {node} --[{label}]--> {dst}({dst_type})")

                        # 分析输出目标的性质
                        if dst in cf_after.sinks:
                            print(f"             💡 {dst}是汇节点，作为最终输出")
                        elif dst in added_nodes:
                            print(f"             💡 {dst}是新增节点，参与新的计算")
                        else:
                            print(f"             💡 {dst}是现有节点，继续现有的计算流程")
                else:
                    print(f"        ➡️  无输出目标 (汇节点)")

                # 深度分析节点来源和作用
                print(f"        🔍 节点深度分析:")
                if node_type == "变量":
                    if node in cf_after.sources:
                        print(f"          💡 节点性质: 外部输入的源变量节点")
                        print(f"          🎯 作用: 为计算图提供初始数据或参数")
                    elif in_neighbors:
                        creators = [n for n in in_neighbors if n in cf_after.fnames]
                        if creators:
                            print(f"          💡 节点性质: 由函数 {creators} 生成的中间数据变量")
                            print(f"          🎯 作用: 存储计算结果，传递给下游函数")
                        else:
                            print(f"          💡 节点性质: 由其他变量传递的数据")
                            print(f"          🎯 作用: 数据中转和传递")
                    else:
                        print(f"          💡 节点性质: 孤立的变量节点")
                        print(f"          🎯 作用: 独立的数据存储")

                    # 分析变量的数据流特征
                    if node in cf_after.sinks:
                        print(f"          📤 数据流特征: 终端数据，不再被其他节点使用")
                    elif out_neighbors:
                        consumers = [n for n in out_neighbors if n in cf_after.fnames]
                        if consumers:
                            print(f"          📤 数据流特征: 被函数 {consumers} 消费的中间数据")

                else:  # 函数节点
                    if node in cf_after.sources:
                        print(f"          💡 节点性质: 无参数的源函数节点")
                        print(f"          🎯 作用: 生成初始数据，启动计算流程")
                    else:
                        print(f"          💡 节点性质: 新加入的计算函数")
                        print(f"          🎯 作用: 处理输入数据，产生新的计算结果")

                    # 分析函数的计算特征
                    if len(in_neighbors) == 0:
                        print(f"          ⚙️  计算特征: 无输入的数据生成函数")
                    elif len(in_neighbors) == 1:
                        print(f"          ⚙️  计算特征: 单输入的数据转换函数")
                    else:
                        print(f"          ⚙️  计算特征: 多输入的数据聚合函数")

                    if len(out_neighbors) == 0:
                        print(f"          📤 输出特征: 无输出的终端函数")
                    elif len(out_neighbors) == 1:
                        print(f"          📤 输出特征: 单输出的标准函数")
                    else:
                        print(f"          📤 输出特征: 多输出的数据分发函数")

        if removed_nodes:
            print(f"    🗑️  删除节点详细分析 ({len(removed_nodes)}个):")
            for node in sorted(removed_nodes):
                node_type = "变量" if node in cf_before.vnames else "函数"
                in_degree = len([e for e in cf_before.edges() if e[1] == node])
                out_degree = len([e for e in cf_before.edges() if e[0] == node])
                print(f"      • {node}({node_type}): 原入度{in_degree}, 原出度{out_degree}")
                print(f"        💡 删除原因: 在新的计算上下文中不再需要")

        # 详细的边变化分析
        edges_before_set = set(cf_before.edges())
        edges_after_set = set(cf_after.edges())
        added_edges = edges_after_set - edges_before_set
        removed_edges = edges_before_set - edges_after_set
        common_edges = edges_before_set & edges_after_set

        print(f"\n  🔗 边变化详细分析:")
        print(f"    🔄 保持不变的边 ({len(common_edges)}条):")
        if common_edges:
            for src, dst, label in sorted(common_edges)[:10]:  # 显示前10条
                src_type = "函数" if src in cf_after.fnames else "变量"
                dst_type = "函数" if dst in cf_after.fnames else "变量"
                print(f"      • {src}({src_type}) --[{label}]--> {dst}({dst_type})")
            if len(common_edges) > 10:
                print(f"      • ... 还有{len(common_edges)-10}条保持不变的边")
        else:
            print(f"      • 无保持不变的边")

        if added_edges:
            print(f"    🆕 新增边详细分析 ({len(added_edges)}条):")
            for src, dst, label in sorted(added_edges):
                src_type = "函数" if src in cf_after.fnames else "变量"
                dst_type = "函数" if dst in cf_after.fnames else "变量"
                print(f"      • {src}({src_type}) --[{label}]--> {dst}({dst_type})")

                # 分析边的含义
                if src_type == "函数" and dst_type == "变量":
                    print(f"        💡 含义: 数据生产关系，函数{src}产生数据存储到变量{dst}")
                elif src_type == "变量" and dst_type == "函数":
                    print(f"        💡 含义: 数据消费关系，变量{src}的数据被函数{dst}使用")
                elif src_type == "函数" and dst_type == "函数":
                    print(f"        💡 含义: 函数调用关系，函数{src}直接调用函数{dst}")
                else:  # 变量到变量
                    print(f"        💡 含义: 数据传递关系，变量{src}的数据传递给变量{dst}")

                # 分析边的来源
                if src in added_nodes and dst in added_nodes:
                    print(f"        🔍 来源: 两个新增节点之间的新连接")
                elif src in added_nodes:
                    print(f"        🔍 来源: 新增节点{src}与现有节点{dst}的连接")
                elif dst in added_nodes:
                    print(f"        🔍 来源: 现有节点{src}与新增节点{dst}的连接")
                else:
                    print(f"        🔍 来源: 现有节点间的新连接关系")

        if removed_edges:
            print(f"    🗑️  删除边详细分析 ({len(removed_edges)}条):")
            for src, dst, label in sorted(removed_edges):
                src_type = "函数" if src in cf_before.fnames else "变量"
                dst_type = "函数" if dst in cf_before.fnames else "变量"
                print(f"      • {src}({src_type}) --[{label}]--> {dst}({dst_type})")
                print(f"        💡 删除原因: 节点删除或连接关系改变")

        print(f"\n  💡 变化原因分析:")
        if operation == "expand_back":
            print(f"    • 向后扩展发现了生成当前结果的上游计算")
            print(f"    • 新增的源节点是最初的输入参数")
            print(f"    • 新增的边表示数据流向关系")
        elif operation == "expand_forward":
            print(f"    • 向前扩展发现了使用当前结果的下游计算")
            print(f"    • 新增的汇节点是最终的输出结果")
            print(f"    • 新增的边表示数据使用关系")
        elif operation == "expand_all":
            print(f"    • 全方向扩展获得了完整的计算上下文")
            print(f"    • 包含了所有相关的计算步骤和数据流")
        elif operation == "merge":
            print(f"    • 合并操作组合了多个计算图")
            print(f"    • 可能产生新的连接和依赖关系")
        elif operation == "filter":
            print(f"    • 过滤操作移除了不相关的节点和边")
            print(f"    • 图结构变得更加简洁和专注")
        else:
            print(f"    • {operation}: 图结构发生了相应的变化")
            print(f"    • 新增节点来源于新的计算步骤或数据流")
            print(f"    • 边的变化反映了数据依赖关系的调整")

    def demonstrate_graph_concepts_changes(self):
        """演示图概念的动态变化"""
        self.print_section_header("ComputationFrame图概念变化演示",
                                 "通过不同的操作展示源节点、汇节点、边等概念如何变化")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "graph_concepts_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            # 定义基础计算函数
            @op
            def input_data(value: int = 42):
                """输入数据 - 源节点"""
                return value

            @op
            def transform_a(data: int):
                """变换A - 中间节点"""
                return data * 2

            @op
            def transform_b(data: int):
                """变换B - 中间节点"""
                return data + 10

            @op
            def combine(a: int, b: int):
                """合并 - 汇聚节点"""
                return a + b

            @op
            def final_output(data: int):
                """最终输出 - 汇节点"""
                return f"Result: {data}"

            print("\n🔧 第一阶段：单节点图（最简单的情况）")
            print("💡 概念说明：只有一个节点的图，既是源节点又是汇节点")

            # 创建单节点图
            single_data = input_data(42)
            cf_single = self.storage.cf(single_data)
            self.print_detailed_cf_analysis(cf_single, "单节点图")

            print("\n🔧 第二阶段：线性图（源→中间→汇）")
            print("💡 概念说明：简单的线性流水线，有明确的源节点和汇节点")

            # 创建线性图
            data = input_data(42)
            transformed = transform_a(data)
            cf_linear = self.storage.cf(transformed)
            self.print_detailed_cf_analysis(cf_linear, "线性图")

            # 分析变化
            self.print_graph_change_analysis(cf_single, cf_linear, "添加线性变换")

            print("\n🔧 第三阶段：分支图（一个源，多个汇）")
            print("💡 概念说明：从一个源分出多个分支，产生多个汇节点")

            # 创建分支图
            data = input_data(42)
            branch_a = transform_a(data)
            branch_b = transform_b(data)
            cf_branch = self.storage.cf([branch_a, branch_b])
            self.print_detailed_cf_analysis(cf_branch, "分支图")

            # 分析变化
            self.print_graph_change_analysis(cf_linear, cf_branch, "添加分支")

            print("\n🔧 第四阶段：汇聚图（多个源，一个汇）")
            print("💡 概念说明：多个独立的源汇聚到一个节点")

            # 创建汇聚图
            data1 = input_data(42)
            data2 = input_data(24)
            result = combine(data1, data2)
            cf_converge = self.storage.cf(result)
            self.print_detailed_cf_analysis(cf_converge, "汇聚图")

            print("\n🔧 第五阶段：复杂网络图（多源多汇）")
            print("💡 概念说明：复杂的数据流网络，多个输入和输出")

            # 创建复杂网络图
            data1 = input_data(42)
            data2 = input_data(24)
            branch_a1 = transform_a(data1)
            branch_b1 = transform_b(data1)
            branch_a2 = transform_a(data2)
            combined = combine(branch_a1, branch_a2)
            final1 = final_output(combined)
            final2 = final_output(branch_b1)

            cf_complex = self.storage.cf([final1, final2])
            self.print_detailed_cf_analysis(cf_complex, "复杂网络图")

            # 分析变化
            self.print_graph_change_analysis(cf_converge, cf_complex, "构建复杂网络")

    def demonstrate_graph_evolution(self):
        """演示图的演化过程"""
        self.print_section_header("ComputationFrame图演化演示",
                                 "通过构建不同复杂度的计算图，展示图概念的动态变化")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "graph_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            print("\n🔧 第一阶段：构建简单线性图")

            # 定义简单的线性计算
            @op
            def load_data(size: int = 100):
                """加载数据 - 图的起始点"""
                return list(range(size))

            @op
            def process_data(data: list):
                """处理数据 - 图的中间节点"""
                return [x * 2 for x in data]

            @op
            def compute_result(data: list):
                """计算结果 - 图的终点"""
                return sum(data)

            # 执行线性计算
            raw_data = load_data(10)
            processed_data = process_data(raw_data)
            final_result = compute_result(processed_data)

            # 分析线性图
            cf_linear = self.storage.cf(final_result)
            self.print_detailed_cf_analysis(cf_linear, "线性图分析")

            print("\n🔧 第二阶段：构建分支图")

    def demonstrate_expansion_effects(self):
        """演示扩展操作对图概念的影响"""
        self.print_section_header("扩展操作对图概念的影响",
                                 "展示expand_back、expand_forward、expand_all如何改变图的结构")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "expansion_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            # 定义完整的计算流水线
            @op
            def load_raw_data(source: str = "database"):
                """加载原始数据"""
                return f"raw_data_from_{source}"

            @op
            def preprocess(raw_data: str, clean: bool = True):
                """预处理数据"""
                return f"preprocessed_{raw_data}_clean_{clean}"

            @op
            def extract_features(data: str):
                """提取特征"""
                return f"features_from_{data}"

            @op
            def train_model(features: str, algorithm: str = "svm"):
                """训练模型"""
                return f"model_{algorithm}_trained_on_{features}"

            @op
            def evaluate_model(model: str, test_data: str):
                """评估模型"""
                return f"evaluation_of_{model}_on_{test_data}"

            @op
            def generate_report(evaluation: str):
                """生成报告"""
                return f"report_based_on_{evaluation}"

            # 执行完整流水线
            raw_data = load_raw_data("database")
            processed_data = preprocess(raw_data, True)
            features = extract_features(processed_data)
            model = train_model(features, "svm")
            test_data = preprocess(load_raw_data("test_set"), True)
            evaluation = evaluate_model(model, test_data)
            report = generate_report(evaluation)

            print("\n🔧 第一步：从中间节点创建最小CF")
            print("💡 概念说明：最小CF只包含指定的节点，不包含计算历史")

            # 从中间节点创建最小CF
            cf_minimal = self.storage.cf(features)
            self.print_detailed_cf_analysis(cf_minimal, "最小CF（从features创建）")

        # 在context外部进行扩展操作
        print("\n🔧 第二步：向后扩展 - 发现数据来源")
        print("💡 概念说明：expand_back追溯数据的生成历史，会增加源节点")

        # 向后扩展
        cf_back = cf_minimal.expand_back(recursive=True)
        self.print_detailed_cf_analysis(cf_back, "向后扩展后的CF")
        self.print_graph_change_analysis(cf_minimal, cf_back, "expand_back")

        print("\n🔧 第三步：向前扩展 - 发现数据使用")
        print("💡 概念说明：expand_forward追踪数据的使用情况，会增加汇节点")

        # 向前扩展
        cf_forward = cf_minimal.expand_forward(recursive=True)
        self.print_detailed_cf_analysis(cf_forward, "向前扩展后的CF")
        self.print_graph_change_analysis(cf_minimal, cf_forward, "expand_forward")

        print("\n🔧 第四步：全方向扩展 - 完整的计算上下文")
        print("💡 概念说明：expand_all获取完整的计算图，包含所有相关节点")

        # 全方向扩展
        cf_all = cf_minimal.expand_all()
        self.print_detailed_cf_analysis(cf_all, "全方向扩展后的CF")
        self.print_graph_change_analysis(cf_back, cf_all, "expand_all")

        print("\n🔧 第五步：对比不同起始点的扩展效果")
        print("💡 概念说明：从不同节点开始扩展会产生不同的图结构")

        # 从源节点开始
        cf_from_source = self.storage.cf(raw_data).expand_all()
        self.print_detailed_cf_analysis(cf_from_source, "从源节点扩展的完整CF")

        # 从汇节点开始
        cf_from_sink = self.storage.cf(report).expand_all()
        self.print_detailed_cf_analysis(cf_from_sink, "从汇节点扩展的完整CF")

        print("\n📊 扩展效果详细对比分析:")

        # 定义所有CF和其描述
        cf_configs = [
            ("最小CF", cf_minimal, "从features创建的最小图"),
            ("向后扩展", cf_back, "追溯数据生成历史"),
            ("向前扩展", cf_forward, "追踪数据使用情况"),
            ("全方向扩展", cf_all, "完整的计算上下文"),
            ("从源扩展", cf_from_source, "从raw_data开始的完整图"),
            ("从汇扩展", cf_from_sink, "从report开始的完整图")
        ]

        for name, cf, description in cf_configs:
            print(f"\n  🔍 {name} ({description}):")
            print(f"    📊 基本统计: {len(cf.nodes)}个节点, {len(cf.sources)}个源, {len(cf.sinks)}个汇, {len(list(cf.edges()))}条边")

            # 详细节点分析
            print(f"    📦 变量节点详情 ({len(cf.vnames)}个):")
            if cf.vnames:
                for vname in sorted(cf.vnames):
                    node_type = self._analyze_variable_type(vname)
                    ref_count = len(cf.vs.get(vname, set()))

                    # 详细的入度分析
                    in_edges = [e for e in cf.edges() if e[1] == vname]
                    in_degree = len(in_edges)
                    in_sources = [e[0] for e in in_edges]
                    in_functions = [src for src in in_sources if src in cf.fnames]
                    in_variables = [src for src in in_sources if src in cf.vnames]

                    # 详细的出度分析
                    out_edges = [e for e in cf.edges() if e[0] == vname]
                    out_degree = len(out_edges)
                    out_targets = [e[1] for e in out_edges]
                    out_functions = [dst for dst in out_targets if dst in cf.fnames]
                    out_variables = [dst for dst in out_targets if dst in cf.vnames]

                    print(f"      • {vname}: {node_type}, {ref_count}个引用")
                    print(f"        📊 连接统计: 入度{in_degree}, 出度{out_degree}")

                    if in_degree > 0:
                        print(f"        ⬅️  输入来源:")
                        for i, (src, dst, label) in enumerate(in_edges):
                            src_type = "函数" if src in cf.fnames else "变量"
                            print(f"          {i+1}. {src}({src_type}) --[{label}]--> {vname}")
                        if in_functions:
                            print(f"        🏭 生产者函数: {in_functions}")
                        if in_variables:
                            print(f"        📦 来源变量: {in_variables}")
                    else:
                        print(f"        ⬅️  无输入来源 (源节点)")

                    if out_degree > 0:
                        print(f"        ➡️  输出目标:")
                        for i, (src, dst, label) in enumerate(out_edges):
                            dst_type = "函数" if dst in cf.fnames else "变量"
                            print(f"          {i+1}. {vname} --[{label}]--> {dst}({dst_type})")
                        if out_functions:
                            print(f"        🍽️  消费者函数: {out_functions}")
                        if out_variables:
                            print(f"        📤 目标变量: {out_variables}")
                    else:
                        print(f"        ➡️  无输出目标 (汇节点)")
            else:
                print(f"      • 无变量节点")

            print(f"    ⚙️  函数节点详情 ({len(cf.fnames)}个):")
            if cf.fnames:
                for fname in sorted(cf.fnames):
                    call_count = len(cf.fs.get(fname, set()))

                    # 详细的入度分析
                    in_edges = [e for e in cf.edges() if e[1] == fname]
                    in_degree = len(in_edges)
                    in_sources = [e[0] for e in in_edges]
                    in_functions = [src for src in in_sources if src in cf.fnames]
                    in_variables = [src for src in in_sources if src in cf.vnames]

                    # 详细的出度分析
                    out_edges = [e for e in cf.edges() if e[0] == fname]
                    out_degree = len(out_edges)
                    out_targets = [e[1] for e in out_edges]
                    out_functions = [dst for dst in out_targets if dst in cf.fnames]
                    out_variables = [dst for dst in out_targets if dst in cf.vnames]

                    print(f"      • {fname}: {call_count}个调用")
                    print(f"        📊 连接统计: 入度{in_degree}, 出度{out_degree}")

                    if in_degree > 0:
                        print(f"        ⬅️  输入参数:")
                        for i, (src, dst, label) in enumerate(in_edges):
                            src_type = "函数" if src in cf.fnames else "变量"
                            print(f"          {i+1}. {src}({src_type}) --[{label}]--> {fname}")
                        if in_variables:
                            print(f"        📦 输入变量: {in_variables}")
                        if in_functions:
                            print(f"        🔗 依赖函数: {in_functions}")
                    else:
                        print(f"        ⬅️  无输入参数 (源函数)")

                    if out_degree > 0:
                        print(f"        ➡️  输出结果:")
                        for i, (src, dst, label) in enumerate(out_edges):
                            dst_type = "函数" if dst in cf.fnames else "变量"
                            print(f"          {i+1}. {fname} --[{label}]--> {dst}({dst_type})")
                        if out_variables:
                            print(f"        📤 输出变量: {out_variables}")
                        if out_functions:
                            print(f"        🔗 调用函数: {out_functions}")
                    else:
                        print(f"        ➡️  无输出结果 (终端函数)")
            else:
                print(f"      • 无函数节点")

            # 源节点分析
            print(f"    🌱 源节点分析 ({len(cf.sources)}个):")
            if cf.sources:
                for source in sorted(cf.sources):
                    source_type = "函数" if source in cf.fnames else "变量"
                    out_neighbors = list(cf.out_neighbors(source))
                    print(f"      • {source}({source_type}): 输出到 {out_neighbors}")
                    if source_type == "变量":
                        print(f"        💡 含义: 外部输入的数据或参数")
                    else:
                        print(f"        💡 含义: 无参数的计算函数")
            else:
                print(f"      • 无源节点 (可能存在循环依赖)")

            # 汇节点分析
            print(f"    🎯 汇节点分析 ({len(cf.sinks)}个):")
            if cf.sinks:
                for sink in sorted(cf.sinks):
                    sink_type = "函数" if sink in cf.fnames else "变量"
                    in_neighbors = list(cf.in_neighbors(sink))
                    print(f"      • {sink}({sink_type}): 输入来自 {in_neighbors}")
                    if sink_type == "变量":
                        print(f"        💡 含义: 最终的计算结果")
                    else:
                        print(f"        💡 含义: 无返回值的操作函数")
            else:
                print(f"      • 无汇节点 (所有节点都有输出)")

            # 边的详细分析
            edges = list(cf.edges())
            if edges:
                func_to_var = [(s, d, l) for s, d, l in edges if s in cf.fnames and d in cf.vnames]
                var_to_func = [(s, d, l) for s, d, l in edges if s in cf.vnames and d in cf.fnames]

                print(f"    🔗 边类型统计:")
                print(f"      • 生产关系 (函数→变量): {len(func_to_var)}条")
                if func_to_var:
                    for src, dst, label in func_to_var[:10]:  # 显示前10条
                        print(f"        - {src} --[{label}]--> {dst}")
                    if len(func_to_var) > 10:
                        print(f"        - ... 还有{len(func_to_var)-10}条生产关系")

                print(f"      • 消费关系 (变量→函数): {len(var_to_func)}条")
                if var_to_func:
                    for src, dst, label in var_to_func[:10]:  # 显示前10条
                        print(f"        - {src} --[{label}]--> {dst}")
                    if len(var_to_func) > 10:
                        print(f"        - ... 还有{len(var_to_func)-10}条消费关系")
            else:
                print(f"    🔗 边类型统计: 无边连接")

            # 图的拓扑结构和形状分析
            print(f"    🔄 图的拓扑结构分析:")
            self._analyze_graph_topology(cf, name)

        # 扩展效果对比分析
        print(f"\n  📈 扩展效果对比分析:")
        print(f"    🔢 节点数量变化:")
        base_nodes = len(cf_minimal.nodes)
        for name, cf, _ in cf_configs[1:]:
            change = len(cf.nodes) - base_nodes
            percentage = (change / base_nodes) * 100 if base_nodes > 0 else 0
            print(f"      • {name}: +{change}个节点 ({percentage:.0f}%增长)")

        print(f"    🌱 源节点变化分析:")
        base_sources = len(cf_minimal.sources)
        for name, cf, _ in cf_configs[1:]:
            change = len(cf.sources) - base_sources
            sources_list = list(cf.sources)
            print(f"      • {name}: {len(cf.sources)}个源节点 ({change:+d}) - {sources_list}")

        print(f"    🎯 汇节点变化分析:")
        base_sinks = len(cf_minimal.sinks)
        for name, cf, _ in cf_configs[1:]:
            change = len(cf.sinks) - base_sinks
            sinks_list = list(cf.sinks)
            print(f"      • {name}: {len(cf.sinks)}个汇节点 ({change:+d}) - {sinks_list}")

        print(f"    � 边数量变化:")
        base_edges = len(list(cf_minimal.edges()))
        for name, cf, _ in cf_configs[1:]:
            change = len(list(cf.edges())) - base_edges
            print(f"      • {name}: +{change}条边")

        # 节点集合对比分析
        print(f"\n  🔍 节点集合对比分析:")
        all_vars = set()
        all_funcs = set()
        for _, cf, _ in cf_configs:
            all_vars.update(cf.vnames)
            all_funcs.update(cf.fnames)

        print(f"    📦 变量节点出现情况:")
        for var in sorted(all_vars):
            appearances = []
            for name, cf, _ in cf_configs:
                if var in cf.vnames:
                    appearances.append(name)
            print(f"      • {var}: 出现在 {', '.join(appearances)}")

        print(f"    ⚙️  函数节点出现情况:")
        for func in sorted(all_funcs):
            appearances = []
            for name, cf, _ in cf_configs:
                if func in cf.fnames:
                    appearances.append(name)
            print(f"      • {func}: 出现在 {', '.join(appearances)}")

        print("\n�💡 关键洞察:")
        print("  • 向后扩展主要增加源节点（发现数据来源）")
        print("  • 向前扩展主要增加汇节点（发现数据使用）")
        print("  • 全方向扩展结合了两者的效果")
        print("  • 不同起始点会影响扩展的范围和结果")
        print("  • 从汇节点扩展通常发现更多的输入参数和配置")
        print("  • 从源节点扩展通常发现更完整的数据处理流水线")

        # 定义分支计算
        @op
        def compute_stats(data):
            """计算统计信息 - 分支1"""
            print(f"    📊 计算统计信息")
            return {"type": "stats", "source": str(data)[:20]}

        @op
        def compute_summary(data):
            """计算摘要信息 - 分支2"""
            print(f"    📋 计算摘要信息")
            return {"type": "summary", "source": str(data)[:20]}

        # 定义汇聚计算
        @op
        def combine_results(result, stats, summary):
            """汇聚多个结果 - 汇聚节点"""
            print(f"    🔄 汇聚计算结果")
            return {
                "final_result": str(result)[:20],
                "statistics": str(stats)[:20],
                "summary": str(summary)[:20],
                "analysis": "complete"
            }

        # 定义更复杂的计算
        @op
        def advanced_analysis(combined_data, raw_data):
            """高级分析 - 使用多个输入"""
            print(f"    🔬 高级分析")
            return {
                "advanced_stats": "computed",
                "combined_info": str(combined_data)[:20]
            }

        @op
        def final_report(advanced, stats):
            """最终报告 - 另一个汇聚点"""
            print(f"    📋 生成最终报告")
            return {
                "report": "Final Analysis Complete",
                "advanced": str(advanced)[:20],
                "basic_stats": str(stats)[:20]
            }

        with self.storage:
            # 执行分支计算
            stats = compute_stats(processed_data)
            summary = compute_summary(processed_data)

            print("\n🔧 第三阶段：构建汇聚图")
            # 执行汇聚计算
            combined = combine_results(processed_data, stats, summary)

            print("\n🔧 第四阶段：构建复杂网状图")
            # 执行复杂计算
            advanced = advanced_analysis(combined, raw_data)
            report = final_report(advanced, stats)

        # 分析分支图 - 使用字典形式传递多个变量
        cf_branch = self.storage.cf({"stats": stats, "summary": summary})
        cf_branch_expanded = cf_branch.expand_back(recursive=True)
        self.print_detailed_cf_analysis(cf_branch_expanded, "分支图分析")

        # 对比线性图和分支图
        self.print_graph_change_analysis(cf_all, cf_branch_expanded, "添加分支")

        # 分析汇聚图
        cf_converge = self.storage.cf(combined)
        cf_converge_expanded = cf_converge.expand_back(recursive=True)
        self.print_detailed_cf_analysis(cf_converge_expanded, "汇聚图分析")

        # 对比分支图和汇聚图
        self.print_graph_change_analysis(cf_branch_expanded, cf_converge_expanded, "添加汇聚")



        # 分析复杂图
        cf_complex = self.storage.cf(report)
        cf_complex_expanded = cf_complex.expand_back(recursive=True)
        self.print_detailed_cf_analysis(cf_complex_expanded, "复杂网状图分析")

        # 对比汇聚图和复杂图
        self.print_graph_change_analysis(cf_converge_expanded, cf_complex_expanded, "构建复杂网络")

        return {
            "linear": cf_all,
            "branch": cf_branch_expanded,
            "converge": cf_converge_expanded,
            "complex": cf_complex_expanded
        }

    def demonstrate_source_sink_changes(self):
        """演示源节点和汇节点的变化"""
        self.print_section_header("源节点和汇节点变化演示",
                                 "通过不同的图操作展示源节点和汇节点如何变化")

        with self.storage:
            print("\n🔧 构建基础计算图")

            # 定义多输入计算
            @op
            def multi_input_func(a: int, b: int, c: int):
                """多输入函数"""
                return a + b + c

            @op
            def single_input_func(x: int):
                """单输入函数"""
                return x * 2

            @op
            def no_input_func():
                """无输入函数 - 常量生成器"""
                return 42

            # 执行计算
            const_val = no_input_func()
            result1 = multi_input_func(1, 2, 3)
            result2 = single_input_func(result1)
            result3 = multi_input_func(result2, const_val, 10)

            # 分析不同的CF创建方式
            print("\n1️⃣ 从单个结果创建CF")
            cf_single = self.storage.cf(result1)
            self.print_detailed_cf_analysis(cf_single, "单结果CF - 最小图")

            print("\n2️⃣ 从多个结果创建CF")
            cf_multi = self.storage.cf([result1, result2, result3])
            self.print_detailed_cf_analysis(cf_multi, "多结果CF - 最小图")

            print("\n3️⃣ 扩展后的完整图")
            cf_expanded = cf_multi.expand_back(recursive=True)
            self.print_detailed_cf_analysis(cf_expanded, "扩展后的完整图")

            # 分析源节点变化
            print("\n🔍 源节点变化分析:")
            print(f"  • 单结果CF源节点: {list(cf_single.sources)}")
            print(f"  • 多结果CF源节点: {list(cf_multi.sources)}")
            print(f"  • 扩展CF源节点: {list(cf_expanded.sources)}")
            print(f"  💡 变化原因:")
            print(f"    - 单结果CF: 只包含指定结果，源节点就是结果本身")
            print(f"    - 多结果CF: 包含多个结果，每个结果都可能是源节点")
            print(f"    - 扩展CF: 追溯到真正的输入参数，发现实际的数据源")

            # 分析汇节点变化
            print("\n🔍 汇节点变化分析:")
            print(f"  • 单结果CF汇节点: {list(cf_single.sinks)}")
            print(f"  • 多结果CF汇节点: {list(cf_multi.sinks)}")
            print(f"  • 扩展CF汇节点: {list(cf_expanded.sinks)}")
            print(f"  💡 变化原因:")
            print(f"    - 单结果CF: 结果本身就是汇节点")
            print(f"    - 多结果CF: 多个结果都是汇节点")
            print(f"    - 扩展CF: 可能发现更多的终端节点或中间结果")

            return {
                "single": cf_single,
                "multi": cf_multi,
                "expanded": cf_expanded
            }


if __name__ == "__main__":
    print("🎯 ComputationFrame图概念深度理解演示")
    print("=" * 80)

    demo = CFGraphConceptsDemo()

    try:
        # 演示图概念的动态变化
        print("\n🔍 第一部分：图概念变化演示")
        demo.demonstrate_graph_concepts_changes()

        # 演示扩展操作的影响
        print("\n🔍 第二部分：扩展操作影响演示")
        demo.demonstrate_expansion_effects()

        print("\n🎉 图概念演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        print("  ✅ 图概念变化：展示了源节点、汇节点、边的动态变化")
        print("  ✅ 扩展操作影响：展示了不同扩展操作对图结构的影响")

        print("\n💡 关键概念总结:")
        print("  🌱 源节点：没有输入的节点，代表计算的起始点")
        print("    • 变化原因：向后扩展会发现更多的数据源")
        print("    • 实际意义：外部输入、参数、配置等")
        print("  🎯 汇节点：没有输出的节点，代表计算的终点")
        print("    • 变化原因：向前扩展会发现更多的数据使用者")
        print("    • 实际意义：最终结果、输出文件、显示内容等")
        print("  🔗 边：节点间的依赖关系，表示数据流向")
        print("    • 变化原因：新的计算步骤产生新的依赖关系")
        print("    • 类型：函数→变量（数据生产）、变量→函数（数据消费）")
        print("  📊 图拓扑：反映计算的结构特征和并行机会")
        print("    • 密度：反映节点间连接的紧密程度")
        print("    • 形状：线性、分支、汇聚、网状等不同结构")
        print("    • 层次：拓扑排序确定的执行顺序")

        print("\n🏷️  变量节点命名规则总结:")
        print("  📝 var_xx命名来源：")
        print("    • 来源：ComputationFrame.get_new_vname()方法自动生成")
        print("    • 规则：从name_hint开始，如果冲突则添加递增数字后缀")
        print("    • 示例：'var_0' → 'var_1' → 'var_2' → ...")
        print("    • 特殊：'output_xx'会被转换为'var_xx'格式")
        print("  🔧 生成时机：")
        print("    • 函数执行产生新的输出变量时")
        print("    • 扩展操作发现新的中间变量时")
        print("    • 手动添加变量节点时")
        print("  💡 命名意义：")
        print("    • 确保图中所有变量名唯一")
        print("    • 提供可预测的命名模式")
        print("    • 支持自动化的图操作")

        print("\n🏭 生产者与消费者概念总结:")
        print("  🔧 生产者(Producer)：")
        print("    • 定义：函数节点通过计算产生数据")
        print("    • 表现：函数→变量的边")
        print("    • 作用：将计算结果存储为变量")
        print("    • 变化：新函数执行时增加生产关系")
        print("  🍽️  消费者(Consumer)：")
        print("    • 定义：函数节点使用数据进行计算")
        print("    • 表现：变量→函数的边")
        print("    • 作用：从变量中读取数据作为输入")
        print("    • 变化：函数需要输入数据时增加消费关系")
        print("  🔄 关系变化：")
        print("    • 触发时机：新的计算步骤加入图时")
        print("    • 影响范围：改变图的连通性和拓扑结构")
        print("    • 平衡特征：健康的图通常生产消费关系相对平衡")

        print("\n🔍 扩展操作总结:")
        print("  📈 expand_back：向后追溯，增加源节点，发现数据来源")
        print("  📉 expand_forward：向前追踪，增加汇节点，发现数据使用")
        print("  🔄 expand_all：全方向扩展，获取完整计算上下文")
        print("  💡 关键洞察：不同起始点和扩展方向产生不同的图结构")

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理临时文件
        if demo.temp_dir and os.path.exists(demo.temp_dir):
            try:
                import shutil
                shutil.rmtree(demo.temp_dir)
                print(f"✅ 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"⚠️  清理临时目录失败: {e}")
