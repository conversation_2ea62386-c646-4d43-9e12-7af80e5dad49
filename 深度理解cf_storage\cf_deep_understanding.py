"""
ComputationFrame 深度理解用例
展示 ComputationFrame 的完整生命周期和核心功能

功能覆盖：
1. CF的创建和初始化
2. 计算图的构建和连接
3. 拓扑排序和图分析
4. 节点的增删查改操作
5. 图的扩展和合并
6. 数据提取和可视化
7. 完整的程序运行周期观察

基于mandala1框架，不创建新类，充分利用现有功能
"""

import os
import sys
from typing import Dict, List, Any, Optional
import time

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


class CFDeepUnderstanding:
    """ComputationFrame深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.current_phase = 0
        self.phase_results = {}
        
    def print_phase_header(self, phase_num: int, title: str, description: str):
        """打印阶段标题"""
        self.current_phase = phase_num
        print("\n" + "=" * 80)
        print(f"🔍 阶段 {phase_num}: {title}")
        print("=" * 80)
        print(f"📝 描述: {description}")
        print("-" * 80)
    
    def print_cf_info(self, cf: ComputationFrame, title: str = "ComputationFrame信息"):
        """打印ComputationFrame的详细信息"""
        print(f"\n📊 {title}:")
        print(f"  🔢 节点总数: {len(cf.nodes)}")
        print(f"  📦 变量节点: {len(cf.vnames)} 个 - {list(cf.vnames)[:5]}{'...' if len(cf.vnames) > 5 else ''}")
        print(f"  ⚙️  函数节点: {len(cf.fnames)} 个 - {list(cf.fnames)}")
        print(f"  🔗 边数量: {len(list(cf.edges()))}")
        print(f"  🌱 源节点: {len(cf.sources)} 个 - {list(cf.sources)[:3]}{'...' if len(cf.sources) > 3 else ''}")
        print(f"  🎯 汇节点: {len(cf.sinks)} 个 - {list(cf.sinks)[:3]}{'...' if len(cf.sinks) > 3 else ''}")
        
        # 显示图描述
        try:
            graph_desc = cf.get_graph_desc()
            print(f"  📋 图描述: {graph_desc[:100]}{'...' if len(graph_desc) > 100 else ''}")
        except Exception as e:
            print(f"  📋 图描述: 获取失败 - {e}")
    
    def demonstrate_cf_creation(self):
        """阶段1: 演示ComputationFrame的创建"""
        self.print_phase_header(1, "ComputationFrame创建", 
                               "展示如何从不同的计算结果创建ComputationFrame")
        
        # 定义计算函数
        @op
        def load_data(size: int):
            """模拟数据加载"""
            print(f"    🔄 加载数据，大小: {size}")
            time.sleep(0.1)
            return list(range(size))
        
        @op
        def preprocess(data: list, normalize: bool = True):
            """数据预处理"""
            print(f"    🔄 预处理数据，标准化: {normalize}")
            time.sleep(0.1)
            if normalize:
                return [x / max(data) if max(data) > 0 else 0 for x in data]
            return data
        
        @op
        def compute_stats(data: list):
            """计算统计信息"""
            print(f"    🔄 计算统计信息")
            time.sleep(0.1)
            return {
                'mean': sum(data) / len(data) if data else 0,
                'max': max(data) if data else 0,
                'min': min(data) if data else 0,
                'count': len(data)
            }
        
        # 创建存储并执行计算
        self.storage = Storage()
        
        with self.storage:
            print("🚀 执行计算流程...")
            
            # 执行计算
            raw_data = load_data(10)
            processed_data = preprocess(raw_data, normalize=True)
            stats = compute_stats(processed_data)
            
            print(f"  ✅ 原始数据: {self.storage.unwrap(raw_data)[:5]}...")
            print(f"  ✅ 处理后数据: {self.storage.unwrap(processed_data)[:5]}...")
            print(f"  ✅ 统计信息: {self.storage.unwrap(stats)}")
        
        # 从不同结果创建ComputationFrame
        print("\n🔧 创建ComputationFrame:")
        
        # 1. 从单个引用创建
        cf_stats = self.storage.cf(stats)
        self.print_cf_info(cf_stats, "从stats创建的CF")
        
        # 2. 从多个引用创建
        cf_multi = self.storage.cf([raw_data, processed_data, stats])
        self.print_cf_info(cf_multi, "从多个引用创建的CF")
        
        # 3. 从函数创建
        cf_func = self.storage.cf(compute_stats)
        self.print_cf_info(cf_func, "从函数创建的CF")
        
        self.phase_results[1] = {
            'cf_stats': cf_stats,
            'cf_multi': cf_multi,
            'cf_func': cf_func,
            'refs': {'raw_data': raw_data, 'processed_data': processed_data, 'stats': stats}
        }
        
        return cf_multi
    
    def demonstrate_cf_expansion(self, cf: ComputationFrame):
        """阶段2: 演示ComputationFrame的扩展和连接"""
        self.print_phase_header(2, "ComputationFrame扩展与连接", 
                               "展示如何扩展计算图以包含更多的计算历史")
        
        print("🔧 扩展操作演示:")
        
        # 1. 向后扩展
        print("\n1️⃣ 向后扩展 (expand_back):")
        cf_back = cf.expand_back(recursive=True)
        self.print_cf_info(cf_back, "向后扩展后的CF")
        
        # 2. 向前扩展
        print("\n2️⃣ 向前扩展 (expand_forward):")
        cf_forward = cf.expand_forward(recursive=True)
        self.print_cf_info(cf_forward, "向前扩展后的CF")
        
        # 3. 全方向扩展
        print("\n3️⃣ 全方向扩展 (expand_all):")
        cf_all = cf.expand_all()
        self.print_cf_info(cf_all, "全方向扩展后的CF")
        
        # 4. 上游下游分析
        print("\n4️⃣ 上游下游分析:")
        if cf_all.fnames:
            sample_func = list(cf_all.fnames)[0]
            print(f"  🎯 分析函数: {sample_func}")
            
            try:
                upstream_cf = cf_all.upstream(sample_func)
                self.print_cf_info(upstream_cf, f"{sample_func}的上游CF")
            except Exception as e:
                print(f"  ❌ 上游分析失败: {e}")
            
            try:
                downstream_cf = cf_all.downstream(sample_func)
                self.print_cf_info(downstream_cf, f"{sample_func}的下游CF")
            except Exception as e:
                print(f"  ❌ 下游分析失败: {e}")
        
        self.phase_results[2] = {
            'cf_back': cf_back,
            'cf_forward': cf_forward,
            'cf_all': cf_all
        }
        
        return cf_all
    
    def demonstrate_topology_analysis(self, cf: ComputationFrame):
        """阶段3: 演示拓扑排序和图分析"""
        self.print_phase_header(3, "拓扑排序与图分析", 
                               "展示计算图的拓扑结构分析和遍历方法")
        
        print("🔧 拓扑分析演示:")
        
        # 1. 拓扑排序
        print("\n1️⃣ 拓扑排序:")
        try:
            topo_order = cf.topsort_modulo_sccs()
            print(f"  📋 拓扑顺序: {topo_order[:10]}{'...' if len(topo_order) > 10 else ''}")
            print(f"  🔢 节点总数: {len(topo_order)}")
        except Exception as e:
            print(f"  ❌ 拓扑排序失败: {e}")
        
        # 2. 邻居分析
        print("\n2️⃣ 邻居关系分析:")
        if cf.nodes:
            sample_nodes = list(cf.nodes)[:3]
            for node in sample_nodes:
                print(f"  🎯 节点: {node}")
                try:
                    in_neighbors = cf.in_neighbors(node)
                    out_neighbors = cf.out_neighbors(node)
                    print(f"    ⬅️  输入邻居: {list(in_neighbors)[:3]}{'...' if len(in_neighbors) > 3 else ''}")
                    print(f"    ➡️  输出邻居: {list(out_neighbors)[:3]}{'...' if len(out_neighbors) > 3 else ''}")
                except Exception as e:
                    print(f"    ❌ 邻居分析失败: {e}")
        
        # 3. 边分析
        print("\n3️⃣ 边关系分析:")
        edges = list(cf.edges())
        print(f"  🔗 边总数: {len(edges)}")
        if edges:
            print("  📋 边示例:")
            for i, (src, dst, label) in enumerate(edges[:5]):
                print(f"    {i+1}. {src} --[{label}]--> {dst}")
        
        # 4. 函数调用分析
        print("\n4️⃣ 函数调用分析:")
        try:
            calls_by_func = cf.calls_by_func()
            print(f"  ⚙️  函数调用映射:")
            for fname, calls in calls_by_func.items():
                print(f"    {fname}: {len(calls)} 次调用")
                if calls:
                    sample_call = next(iter(calls))
                    print(f"      📋 示例调用ID: {sample_call.hid[:8]}...")
        except Exception as e:
            print(f"  ❌ 函数调用分析失败: {e}")
        
        self.phase_results[3] = {
            'topo_order': topo_order if 'topo_order' in locals() else [],
            'edges_count': len(edges),
            'calls_by_func': calls_by_func if 'calls_by_func' in locals() else {}
        }
        
        return cf

    def demonstrate_node_operations(self, cf: ComputationFrame):
        """阶段4: 演示节点的增删查改操作"""
        self.print_phase_header(4, "节点增删查改操作",
                               "展示如何对ComputationFrame中的节点进行各种操作")

        print("🔧 节点操作演示:")

        # 1. 复制CF进行安全实验
        print("\n1️⃣ 复制ComputationFrame进行实验:")
        cf_copy = cf.copy()
        self.print_cf_info(cf_copy, "复制的CF")

        # 2. 删除节点操作
        print("\n2️⃣ 删除节点操作:")
        if cf_copy.vnames:
            # 删除一个变量节点
            var_to_delete = list(cf_copy.vnames)[0]
            print(f"  🗑️  删除变量节点: {var_to_delete}")
            cf_deleted = cf_copy.drop_node(var_to_delete, inplace=False)
            self.print_cf_info(cf_deleted, "删除节点后的CF")

        # 3. 批量删除操作
        print("\n3️⃣ 批量删除操作:")
        if len(cf_copy.vnames) > 2:
            nodes_to_delete = list(cf_copy.vnames)[:2]
            print(f"  🗑️  批量删除节点: {nodes_to_delete}")
            cf_batch_deleted = cf_copy.drop(nodes_to_delete, inplace=False)
            self.print_cf_info(cf_batch_deleted, "批量删除后的CF")

        # 4. 清理操作
        print("\n4️⃣ 清理操作:")
        cf_cleaned = cf_copy.cleanup(inplace=False)
        self.print_cf_info(cf_cleaned, "清理后的CF")

        # 5. 节点选择操作
        print("\n5️⃣ 节点选择操作:")
        if len(cf.nodes) > 3:
            selected_nodes = list(cf.nodes)[:3]
            print(f"  🎯 选择节点: {selected_nodes}")
            cf_selected = cf.select_nodes(selected_nodes)
            self.print_cf_info(cf_selected, "选择节点后的CF")

        self.phase_results[4] = {
            'cf_copy': cf_copy,
            'cf_deleted': cf_deleted if 'cf_deleted' in locals() else None,
            'cf_cleaned': cf_cleaned
        }

        return cf_copy

    def demonstrate_cf_merging(self, cf: ComputationFrame):
        """阶段5: 演示ComputationFrame的合并操作"""
        self.print_phase_header(5, "ComputationFrame合并操作",
                               "展示如何合并多个ComputationFrame")

        # 创建新的计算来生成另一个CF
        @op
        def additional_compute(data: list, factor: float = 2.0):
            """额外的计算函数"""
            print(f"    🔄 执行额外计算，因子: {factor}")
            time.sleep(0.1)
            return [x * factor for x in data]

        @op
        def final_analysis(processed: list, stats: dict):
            """最终分析"""
            print(f"    🔄 执行最终分析")
            time.sleep(0.1)
            return {
                'processed_count': len(processed),
                'original_stats': stats,
                'analysis_complete': True
            }

        with self.storage:
            # 获取之前的结果
            refs = self.phase_results[1]['refs']
            processed_data = refs['processed_data']
            stats = refs['stats']

            # 执行新的计算
            additional_result = additional_compute(self.storage.unwrap(processed_data))
            final_result = final_analysis(self.storage.unwrap(additional_result),
                                        self.storage.unwrap(stats))

        # 创建新的CF
        cf_new = self.storage.cf(final_result).expand_back(recursive=True)

        print("🔧 合并操作演示:")

        # 1. 并集操作
        print("\n1️⃣ 并集操作 (|):")
        cf_union = cf | cf_new
        self.print_cf_info(cf_union, "并集CF")

        # 2. 交集操作
        print("\n2️⃣ 交集操作 (&):")
        cf_intersection = cf & cf_new
        self.print_cf_info(cf_intersection, "交集CF")

        # 3. 差集操作
        print("\n3️⃣ 差集操作 (-):")
        cf_difference = cf - cf_new
        self.print_cf_info(cf_difference, "差集CF")

        self.phase_results[5] = {
            'cf_new': cf_new,
            'cf_union': cf_union,
            'cf_intersection': cf_intersection,
            'cf_difference': cf_difference
        }

        return cf_union

    def demonstrate_data_extraction(self, cf: ComputationFrame):
        """阶段6: 演示数据提取和分析"""
        self.print_phase_header(6, "数据提取与分析",
                               "展示如何从ComputationFrame中提取和分析数据")

        print("🔧 数据提取演示:")

        # 1. 函数调用表
        print("\n1️⃣ 函数调用表:")
        for fname in cf.fnames:
            try:
                func_table = cf.get_func_table(fname)
                print(f"  ⚙️  函数 {fname}:")
                print(f"    📊 调用表形状: {func_table.shape}")
                if not func_table.empty:
                    print(f"    📋 列名: {list(func_table.columns)}")
                    print(f"    📝 前3行预览:")
                    print(func_table.head(3).to_string(index=False))
                else:
                    print("    📝 无调用记录")
            except Exception as e:
                print(f"    ❌ 获取失败: {e}")

        # 2. 变量引用映射
        print("\n2️⃣ 变量引用映射:")
        try:
            refs_by_var = cf.refs_by_var()
            print(f"  📦 变量引用映射:")
            for vname, refs in list(refs_by_var.items())[:5]:
                print(f"    {vname}: {len(refs)} 个引用")
                if refs:
                    sample_ref = next(iter(refs))
                    print(f"      📋 示例引用: {sample_ref.hid[:8]}...")
        except Exception as e:
            print(f"  ❌ 获取变量引用映射失败: {e}")

        # 3. 数据框转换
        print("\n3️⃣ 数据框转换:")
        try:
            if cf.vnames:
                sample_vars = list(cf.vnames)[:3]
                print(f"  📊 转换变量: {sample_vars}")
                df = cf.df(*sample_vars)
                print(f"  📋 数据框形状: {df.shape}")
                print(f"  📝 数据框预览:")
                print(df.head(3).to_string(index=False))
        except Exception as e:
            print(f"  ❌ 数据框转换失败: {e}")

        # 4. 统计信息
        print("\n4️⃣ 统计信息:")
        try:
            func_stats = cf.get_func_stats()
            var_stats = cf.get_var_stats()
            print(f"  ⚙️  函数统计: {func_stats}")
            print(f"  📦 变量统计: {var_stats}")
        except Exception as e:
            print(f"  ❌ 获取统计信息失败: {e}")

        self.phase_results[6] = {
            'func_tables': {},
            'refs_by_var': refs_by_var if 'refs_by_var' in locals() else {},
            'stats': {
                'func_stats': func_stats if 'func_stats' in locals() else {},
                'var_stats': var_stats if 'var_stats' in locals() else {}
            }
        }

        return cf


def main():
    """主函数：运行ComputationFrame深度理解演示"""
    print("🚀 ComputationFrame 深度理解用例")
    print("=" * 80)
    print("📖 本演示将展示ComputationFrame的完整生命周期和核心功能")
    print("🎯 目标：深入理解CF的创建、连接、拓扑排序、增删查改等操作")
    print("=" * 80)
    
    demo = CFDeepUnderstanding()
    
    try:
        # 阶段1: CF创建
        cf = demo.demonstrate_cf_creation()
        
        # 阶段2: CF扩展
        cf_expanded = demo.demonstrate_cf_expansion(cf)
        
        # 阶段3: 拓扑分析
        cf_analyzed = demo.demonstrate_topology_analysis(cf_expanded)

        # 阶段4: 节点操作
        cf_modified = demo.demonstrate_node_operations(cf_analyzed)

        # 阶段5: CF合并
        cf_merged = demo.demonstrate_cf_merging(cf_analyzed)

        # 阶段6: 数据提取
        demo.demonstrate_data_extraction(cf_merged)

        print("\n" + "=" * 80)
        print("🎉 ComputationFrame深度理解演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        for phase, results in demo.phase_results.items():
            print(f"  阶段{phase}: ✅ 完成")

        print("\n📈 完整生命周期覆盖:")
        print("  ✅ CF创建和初始化")
        print("  ✅ 计算图构建和连接")
        print("  ✅ 拓扑排序和图分析")
        print("  ✅ 节点增删查改操作")
        print("  ✅ 图的扩展和合并")
        print("  ✅ 数据提取和可视化")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
