"""
调试导入工具
用于检查和调试 mandala1 模块的导入问题
"""

import os
import sys
from pathlib import Path


def check_python_path():
    """检查Python路径设置"""
    print("🔍 检查Python路径设置")
    print("="*50)
    
    print("当前工作目录:")
    print(f"   {os.getcwd()}")
    
    print("\nPython路径 (sys.path):")
    for i, path in enumerate(sys.path, 1):
        print(f"   {i:2d}. {path}")
    
    # 检查mandala1路径
    mandala_path = os.path.join(os.path.dirname(__file__), '../../..')
    mandala_abs_path = os.path.abspath(mandala_path)
    
    print(f"\n预期mandala1路径:")
    print(f"   相对路径: {mandala_path}")
    print(f"   绝对路径: {mandala_abs_path}")
    print(f"   路径存在: {os.path.exists(mandala_abs_path)}")
    
    if mandala_abs_path not in sys.path:
        print(f"⚠️ mandala1路径不在sys.path中，正在添加...")
        sys.path.append(mandala_abs_path)
        print(f"✅ 已添加mandala1路径到sys.path")
    else:
        print(f"✅ mandala1路径已在sys.path中")


def check_mandala1_structure():
    """检查mandala1目录结构"""
    print("\n🏗️ 检查mandala1目录结构")
    print("="*50)
    
    mandala_path = os.path.join(os.path.dirname(__file__), '../../../mandala1')
    mandala_abs_path = os.path.abspath(mandala_path)
    
    if not os.path.exists(mandala_abs_path):
        print(f"❌ mandala1目录不存在: {mandala_abs_path}")
        return False
    
    print(f"✅ mandala1目录存在: {mandala_abs_path}")
    
    # 检查关键文件
    key_files = [
        '__init__.py',
        'storage.py',
        'model.py',
        'cf.py',
        'common_imports.py',
        'config.py'
    ]
    
    print("\n关键文件检查:")
    all_files_exist = True
    for file in key_files:
        file_path = os.path.join(mandala_abs_path, file)
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"   {status} {file}")
        if not exists:
            all_files_exist = False
    
    return all_files_exist


def test_mandala1_imports():
    """测试mandala1模块导入"""
    print("\n📦 测试mandala1模块导入")
    print("="*50)
    
    import_results = {}
    
    # 测试基本导入
    modules_to_test = [
        'mandala1',
        'mandala1.storage',
        'mandala1.model',
        'mandala1.cf',
        'mandala1.common_imports',
        'mandala1.config'
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
            import_results[module_name] = True
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            import_results[module_name] = False
        except Exception as e:
            print(f"⚠️ {module_name}: {e}")
            import_results[module_name] = False
    
    return import_results


def test_specific_classes():
    """测试特定类的导入"""
    print("\n🎯 测试特定类的导入")
    print("="*50)
    
    class_results = {}
    
    # 测试Storage类
    try:
        from mandala1.storage import Storage
        print("✅ Storage 类导入成功")
        
        # 测试创建Storage实例
        storage = Storage()
        print("✅ Storage 实例创建成功")
        class_results['Storage'] = True
    except Exception as e:
        print(f"❌ Storage 类导入失败: {e}")
        class_results['Storage'] = False
    
    # 测试op装饰器
    try:
        from mandala1.model import op
        print("✅ op 装饰器导入成功")
        
        # 测试使用op装饰器
        @op
        def test_function(x: int) -> int:
            return x * 2
        
        print("✅ op 装饰器使用成功")
        class_results['op'] = True
    except Exception as e:
        print(f"❌ op 装饰器导入失败: {e}")
        class_results['op'] = False
    
    # 测试ComputationFrame类
    try:
        from mandala1.cf import ComputationFrame
        print("✅ ComputationFrame 类导入成功")
        class_results['ComputationFrame'] = True
    except Exception as e:
        print(f"❌ ComputationFrame 类导入失败: {e}")
        class_results['ComputationFrame'] = False
    
    return class_results


def test_stack_replay_import():
    """测试stack_replay模块导入"""
    print("\n🔄 测试stack_replay模块导入")
    print("="*50)
    
    try:
        from stack_replay import StackReplayAnalyzer
        print("✅ StackReplayAnalyzer 类导入成功")
        
        # 测试创建实例（需要Storage）
        from mandala1.storage import Storage
        storage = Storage()
        analyzer = StackReplayAnalyzer(storage)
        print("✅ StackReplayAnalyzer 实例创建成功")
        
        return True
    except Exception as e:
        print(f"❌ stack_replay 模块导入失败: {e}")
        return False


def run_comprehensive_check():
    """运行综合检查"""
    print("🚀 mandala1 导入调试工具")
    print("="*80)
    
    # 1. 检查Python路径
    check_python_path()
    
    # 2. 检查目录结构
    structure_ok = check_mandala1_structure()
    
    # 3. 测试模块导入
    import_results = test_mandala1_imports()
    
    # 4. 测试特定类
    class_results = test_specific_classes()
    
    # 5. 测试stack_replay导入
    stack_replay_ok = test_stack_replay_import()
    
    # 生成总结报告
    print("\n📊 检查结果总结")
    print("="*50)
    
    print(f"目录结构: {'✅ 正常' if structure_ok else '❌ 异常'}")
    
    successful_imports = sum(import_results.values())
    total_imports = len(import_results)
    print(f"模块导入: {successful_imports}/{total_imports} 成功")
    
    successful_classes = sum(class_results.values())
    total_classes = len(class_results)
    print(f"类导入: {successful_classes}/{total_classes} 成功")
    
    print(f"stack_replay: {'✅ 正常' if stack_replay_ok else '❌ 异常'}")
    
    # 整体状态
    overall_ok = (structure_ok and 
                  successful_imports == total_imports and 
                  successful_classes == total_classes and 
                  stack_replay_ok)
    
    print(f"\n🎯 整体状态: {'✅ 所有检查通过' if overall_ok else '❌ 存在问题'}")
    
    if not overall_ok:
        print("\n🔧 建议解决方案:")
        if not structure_ok:
            print("   • 检查mandala1目录是否完整")
        if successful_imports < total_imports:
            print("   • 检查mandala1模块的依赖是否安装")
        if successful_classes < total_classes:
            print("   • 检查mandala1模块的内部依赖")
        if not stack_replay_ok:
            print("   • 确保stack_replay.py文件存在且语法正确")
    
    return overall_ok


if __name__ == "__main__":
    success = run_comprehensive_check()
    
    if success:
        print("\n🎉 所有检查通过！可以正常使用栈重放功能。")
    else:
        print("\n⚠️ 存在问题，请根据上述建议进行修复。")
    
    sys.exit(0 if success else 1)
