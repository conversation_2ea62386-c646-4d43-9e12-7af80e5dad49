"""
ComputationFrame图概念完整深度理解演示
专门展示CF的图结构概念：源节点、汇节点、边、图拓扑等
通过具体案例展示这些概念如何变化以及变化的原因

重点概念：
1. 源节点(Sources) - 没有输入的节点，计算的起始点
2. 汇节点(Sinks) - 没有输出的节点，计算的终点  
3. 边(Edges) - 节点间的依赖关系
4. 图拓扑 - 图的结构特征
5. 图密度 - 连接紧密程度
6. 并行度 - 可并行执行的节点数

通过动态构建不同的计算图来展示概念变化
"""

import os
import sys
import tempfile
from typing import Dict, List, Any

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


class CFGraphConceptsComplete:
    """ComputationFrame图概念完整深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.demo_results = {}
        
    def print_section_header(self, title: str, description: str):
        """打印章节标题"""
        print("\n" + "=" * 100)
        print(f"🔍 {title}")
        print("=" * 100)
        print(f"📝 {description}")
        print("-" * 100)
    
    def print_detailed_cf_analysis(self, cf: ComputationFrame, title: str):
        """打印CF的详细图分析"""
        print(f"\n📊 {title}:")

        # 基本信息
        nodes = list(cf.nodes)
        edges = list(cf.edges())
        sources = list(cf.sources)
        sinks = list(cf.sinks)

        print(f"  🔢 节点总数: {len(nodes)}")
        print(f"  📦 变量节点: {len(cf.vnames)} 个 - {list(cf.vnames)}")
        print(f"  ⚙️  函数节点: {len(cf.fnames)} 个 - {list(cf.fnames)}")
        print(f"  🔗 边数量: {len(edges)}")
        print(f"  🌱 源节点: {len(sources)} 个 - {sources}")
        print(f"  🎯 汇节点: {len(sinks)} 个 - {sinks}")

        # 详细的边分析
        print(f"\n  🔗 边的详细分析:")
        print(f"    💡 边的概念: 表示节点间的依赖关系，数据流的方向")
        if edges:
            print(f"    📋 边的详细信息:")
            try:
                # 处理不同格式的边
                edge_list = []
                for edge in edges[:8]:  # 只显示前8条边
                    if isinstance(edge, tuple) and len(edge) >= 2:
                        src, dst = edge[0], edge[1]
                        edge_list.append((src, dst))
                    elif hasattr(edge, 'source') and hasattr(edge, 'target'):
                        edge_list.append((edge.source, edge.target))
                    else:
                        # 如果格式不明确，跳过详细显示
                        print(f"      边格式: {type(edge)} - {edge}")
                        continue

                for i, (src, dst) in enumerate(edge_list):
                    src_type = "函数" if src in cf.fnames else "变量"
                    dst_type = "函数" if dst in cf.fnames else "变量"
                    print(f"      {i+1}. {src}({src_type}) → {dst}({dst_type})")

                if len(edges) > 8:
                    print(f"      ... 还有{len(edges)-8}条边")

                # 边类型统计
                func_to_var = 0
                var_to_func = 0
                for edge in edges:
                    try:
                        if isinstance(edge, tuple) and len(edge) >= 2:
                            src, dst = edge[0], edge[1]
                        elif hasattr(edge, 'source') and hasattr(edge, 'target'):
                            src, dst = edge.source, edge.target
                        else:
                            continue

                        if src in cf.fnames and dst in cf.vnames:
                            func_to_var += 1
                        elif src in cf.vnames and dst in cf.fnames:
                            var_to_func += 1
                    except Exception:
                        continue

            except Exception as e:
                print(f"      ❌ 边分析失败: {e}")
                func_to_var = 0
                var_to_func = 0
            
            print(f"    📊 边类型统计:")
            print(f"      • 函数→变量: {func_to_var} 条 (数据生产边)")
            print(f"        💡 含义: 函数执行产生数据，存储在变量中")
            print(f"      • 变量→函数: {var_to_func} 条 (数据消费边)")
            print(f"        💡 含义: 函数读取变量中的数据作为输入")
            
            # 边密度分析
            max_edges = len(nodes) * (len(nodes) - 1) if len(nodes) > 1 else 0
            edge_density = len(edges) / max_edges if max_edges > 0 else 0
            print(f"    📈 边密度分析:")
            print(f"      • 当前边密度: {edge_density:.3f}")
            print(f"      • 最大可能边数: {max_edges}")
            print(f"      • 实际边数: {len(edges)}")
            if edge_density < 0.1:
                print(f"      💡 稀疏连接: 节点间依赖较少，计算相对独立")
            elif edge_density > 0.5:
                print(f"      💡 密集连接: 节点间依赖较多，计算高度耦合")
            else:
                print(f"      💡 中等连接: 适中的依赖关系")
        else:
            print(f"    ⚠️  无边: 图中没有依赖关系")
            print(f"    💡 无边的原因:")
            print(f"      • 只有单个孤立节点")
            print(f"      • 节点间没有数据流动")

        # 源节点详细分析
        print(f"\n  🌱 源节点详细分析:")
        print(f"    💡 源节点概念: 没有输入边的节点，代表计算的起始点")
        print(f"    🎯 源节点作用: 提供外部输入数据或执行无参数计算")
        if sources:
            for src in sources:
                out_neighbors = list(cf.out_neighbors(src))
                src_type = "函数" if src in cf.fnames else "变量"
                print(f"    • {src}({src_type})")
                print(f"      📤 输出到: {out_neighbors}")
                if src_type == "变量":
                    print(f"      💡 变量源节点: 外部输入的数据、参数或常量")
                    print(f"      🔍 特征: 不依赖其他计算，直接提供数据")
                else:
                    print(f"      💡 函数源节点: 无参数的计算函数或数据生成器")
                    print(f"      🔍 特征: 不需要输入，直接产生数据")
        else:
            print(f"    ⚠️  无源节点: 图中所有节点都有输入依赖")
            print(f"    💡 无源节点的可能原因:")
            print(f"      • 存在循环依赖关系")
            print(f"      • 图是从中间节点开始构建的")
            print(f"      • 所有节点都依赖外部未包含的输入")

        # 汇节点详细分析
        print(f"\n  🎯 汇节点详细分析:")
        print(f"    💡 汇节点概念: 没有输出边的节点，代表计算的终点")
        print(f"    🎯 汇节点作用: 产生最终结果或执行无返回值的操作")
        if sinks:
            for sink in sinks:
                in_neighbors = list(cf.in_neighbors(sink))
                sink_type = "函数" if sink in cf.fnames else "变量"
                print(f"    • {sink}({sink_type})")
                print(f"      📥 输入来自: {in_neighbors}")
                if sink_type == "变量":
                    print(f"      💡 变量汇节点: 最终的计算结果或中间结果")
                    print(f"      🔍 特征: 存储计算结果，不被其他计算使用")
                else:
                    print(f"      💡 函数汇节点: 无返回值的操作或副作用函数")
                    print(f"      🔍 特征: 执行操作但不产生供其他计算使用的数据")
        else:
            print(f"    ⚠️  无汇节点: 图中所有节点的输出都被使用")
            print(f"    💡 无汇节点的可能原因:")
            print(f"      • 存在循环依赖关系")
            print(f"      • 图包含了所有下游的使用者")
            print(f"      • 所有计算结果都有后续使用")
        
        # 图拓扑分析
        print(f"\n  📊 图拓扑分析:")
        print(f"    💡 拓扑分析概念: 分析图的结构特征和计算特性")
        try:
            node_count = len(nodes)
            edge_count = len(edges)

            if node_count > 1:
                # 图形状分析
                print(f"    📐 图形状分析:")
                if len(sources) == 1 and len(sinks) == 1:
                    print(f"      🔄 线性图: 单一输入到单一输出的流水线")
                    print(f"        • 特点: 顺序执行，无并行机会")
                    print(f"        • 适用: 数据处理流水线")
                elif len(sources) == 1 and len(sinks) > 1:
                    print(f"      🌳 扇出图: 单一输入，多个输出分支")
                    print(f"        • 特点: 分支可并行执行")
                    print(f"        • 适用: 一对多的数据分发")
                elif len(sources) > 1 and len(sinks) == 1:
                    print(f"      🔀 扇入图: 多个输入汇聚到单一输出")
                    print(f"        • 特点: 输入可并行处理")
                    print(f"        • 适用: 多对一的数据聚合")
                elif len(sources) > 1 and len(sinks) > 1:
                    print(f"      🕸️  网状图: 多输入多输出的复杂网络")
                    print(f"        • 特点: 复杂的数据流，多种并行机会")
                    print(f"        • 适用: 复杂的数据处理网络")
                else:
                    print(f"      ⚠️  特殊图: 无源或无汇的特殊结构")

            # 拓扑排序分析
            print(f"    🔄 拓扑排序分析:")
            print(f"      💡 拓扑排序概念: 确定无环图中节点的执行顺序")
            try:
                topo_order = cf.topsort_modulo_sccs()
                unique_levels = len(set(topo_order))
                level_counts = {}
                for level in topo_order:
                    level_counts[level] = level_counts.get(level, 0) + 1
                max_parallel = max(level_counts.values()) if level_counts else 0

                print(f"      • 拓扑层次: {unique_levels} 个不同层次")
                print(f"        💡 层次含义: 计算的执行阶段数")
                print(f"      • 最大并行度: {max_parallel}")
                print(f"        💡 并行度含义: 同时可执行的最大节点数")
                print(f"      • 层次分布: {level_counts}")
                print(f"        💡 分布含义: 每个层次的节点数量")
                
                if unique_levels > 1:
                    print(f"      • 关键路径长度: {unique_levels} 步")
                    print(f"        💡 关键路径: 影响整体执行时间的最长路径")
                    print(f"      💡 并行机会: 同层次的节点可以并行执行")

            except Exception as e:
                print(f"      ❌ 拓扑排序失败: {e}")
                print(f"        💡 失败原因: 可能存在循环依赖")

        except Exception as e:
            print(f"    ❌ 拓扑分析失败: {e}")
    
    def print_graph_change_analysis(self, cf_before: ComputationFrame, cf_after: ComputationFrame, operation: str):
        """分析图变化"""
        print(f"\n🔄 图变化分析 - {operation}:")
        
        nodes_before = len(cf_before.nodes)
        nodes_after = len(cf_after.nodes)
        edges_before = len(list(cf_before.edges()))
        edges_after = len(list(cf_after.edges()))
        sources_before = len(cf_before.sources)
        sources_after = len(cf_after.sources)
        sinks_before = len(cf_before.sinks)
        sinks_after = len(cf_after.sinks)
        
        print(f"  📊 数量变化:")
        print(f"    • 节点: {nodes_before} → {nodes_after} ({nodes_after - nodes_before:+d})")
        print(f"    • 边: {edges_before} → {edges_after} ({edges_after - edges_before:+d})")
        print(f"    • 源节点: {sources_before} → {sources_after} ({sources_after - sources_before:+d})")
        print(f"    • 汇节点: {sinks_before} → {sinks_after} ({sinks_after - sinks_before:+d})")
        
        # 分析变化原因
        print(f"  💡 变化原因分析:")
        if nodes_after > nodes_before:
            print(f"    ✅ 节点增加: {operation}发现了新的计算步骤或数据对象")
            print(f"      🔍 新增节点: {nodes_after - nodes_before}个")
        elif nodes_after < nodes_before:
            print(f"    ⚠️  节点减少: {operation}移除了部分计算步骤或数据对象")
            print(f"      🔍 减少节点: {nodes_before - nodes_after}个")
        else:
            print(f"    ➡️  节点不变: {operation}没有改变图的节点数量")
            
        if edges_after > edges_before:
            print(f"    ✅ 边增加: 新发现的节点带来了新的依赖关系")
            print(f"      🔍 新增边: {edges_after - edges_before}条")
        elif edges_after < edges_before:
            print(f"    ⚠️  边减少: 移除节点导致相关依赖关系消失")
            print(f"      🔍 减少边: {edges_before - edges_after}条")
        else:
            print(f"    ➡️  边不变: 依赖关系结构保持稳定")
            
        if sources_after != sources_before:
            print(f"    🌱 源节点变化: 计算起始点发生了变化")
            if sources_after > sources_before:
                print(f"      • 新增源节点: 发现了新的外部输入或无参数函数")
                print(f"      🔍 新增数量: {sources_after - sources_before}个")
            else:
                print(f"      • 减少源节点: 某些输入被其他计算替代")
                print(f"      🔍 减少数量: {sources_before - sources_after}个")
                
        if sinks_after != sinks_before:
            print(f"    🎯 汇节点变化: 计算终点发生了变化")
            if sinks_after > sinks_before:
                print(f"      • 新增汇节点: 发现了新的最终结果或未使用的中间结果")
                print(f"      🔍 新增数量: {sinks_after - sinks_before}个")
            else:
                print(f"      • 减少汇节点: 某些结果被进一步使用")
                print(f"      🔍 减少数量: {sinks_before - sinks_after}个")
        
        # 图结构变化分析
        print(f"  📐 图结构变化:")
        density_before = edges_before / (nodes_before * (nodes_before - 1)) if nodes_before > 1 else 0
        density_after = edges_after / (nodes_after * (nodes_after - 1)) if nodes_after > 1 else 0
        
        if abs(density_after - density_before) > 0.01:
            if density_after > density_before:
                print(f"    📈 图密度增加: {density_before:.3f} → {density_after:.3f}")
                print(f"      💡 含义: 节点间连接更加紧密，计算更加耦合")
            else:
                print(f"    📉 图密度减少: {density_before:.3f} → {density_after:.3f}")
                print(f"      💡 含义: 节点间连接更加稀疏，计算更加独立")
        else:
            print(f"    ➡️  图密度基本不变: {density_after:.3f}")
            print(f"      💡 含义: 图的整体结构特征保持稳定")

    def demonstrate_basic_graph_concepts(self):
        """演示基本图概念"""
        self.print_section_header("基本图概念演示",
                                 "通过构建不同类型的计算图，展示源节点、汇节点、边等基本概念")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "basic_graph_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            print("\n🔧 第一阶段：单节点图")
            print("💡 概念说明：最简单的图，只有一个孤立节点")

            @op
            def single_node():
                """单个节点的计算"""
                return "single_result"

            # 创建单节点图
            result = single_node()
            cf_single = self.storage.cf(result)
            self.print_detailed_cf_analysis(cf_single, "单节点图")

            print("\n🔧 第二阶段：线性图（链式结构）")
            print("💡 概念说明：节点按顺序连接，形成计算链")

            @op
            def input_data(value: int = 42):
                """输入数据 - 源节点"""
                return value

            @op
            def transform(data: int):
                """转换数据 - 中间节点"""
                return data * 2

            @op
            def output_result(data: int):
                """输出结果 - 汇节点"""
                return f"result_{data}"

            # 创建线性图
            data = input_data(42)
            transformed = transform(data)
            final = output_result(transformed)
            cf_linear = self.storage.cf(final)
            self.print_detailed_cf_analysis(cf_linear, "线性图")
            self.print_graph_change_analysis(cf_single, cf_linear, "构建线性图")

            print("\n🔧 第三阶段：分支图（扇出结构）")
            print("💡 概念说明：一个源节点分出多个分支，增加汇节点")

            @op
            def branch_a(data: int):
                """分支A - 计算平方"""
                return data ** 2

            @op
            def branch_b(data: int):
                """分支B - 计算立方"""
                return data ** 3

            # 创建分支图
            data = input_data(42)
            result_a = branch_a(data)
            result_b = branch_b(data)
            cf_branch = self.storage.cf([result_a, result_b])
            self.print_detailed_cf_analysis(cf_branch, "分支图")
            self.print_graph_change_analysis(cf_linear, cf_branch, "添加分支")

            print("\n🔧 第四阶段：汇聚图（扇入结构）")
            print("💡 概念说明：多个源节点汇聚到一个节点，减少源节点")

            @op
            def combine(data1: int, data2: int):
                """汇聚数据"""
                return data1 + data2

            # 创建汇聚图
            data1 = input_data(42)
            data2 = input_data(24)
            combined = combine(data1, data2)
            cf_converge = self.storage.cf(combined)
            self.print_detailed_cf_analysis(cf_converge, "汇聚图")
            self.print_graph_change_analysis(cf_branch, cf_converge, "构建汇聚图")

            print("\n🔧 第五阶段：复杂网络图")
            print("💡 概念说明：多源多汇的复杂网络结构")

            @op
            def process_a(data: int):
                """处理A"""
                return data * 2

            @op
            def process_b(data: int):
                """处理B"""
                return data + 10

            @op
            def final_combine(a: int, b: int, c: int):
                """最终汇聚"""
                return a + b + c

            # 创建复杂网络图
            data1 = input_data(42)
            data2 = input_data(24)
            data3 = input_data(10)

            processed_a = process_a(data1)
            processed_b = process_b(data2)
            branch_a_result = branch_a(data3)
            branch_b_result = branch_b(data3)

            final_result = final_combine(processed_a, processed_b, branch_a_result)

            cf_complex = self.storage.cf([final_result, branch_b_result])
            self.print_detailed_cf_analysis(cf_complex, "复杂网络图")
            self.print_graph_change_analysis(cf_converge, cf_complex, "构建复杂网络")

            print("\n📊 图演化总结:")
            graphs = [
                ("单节点图", cf_single),
                ("线性图", cf_linear),
                ("分支图", cf_branch),
                ("汇聚图", cf_converge),
                ("复杂网络图", cf_complex)
            ]

            for name, cf in graphs:
                nodes = len(cf.nodes)
                edges = len(list(cf.edges()))
                sources = len(cf.sources)
                sinks = len(cf.sinks)
                density = edges / (nodes * (nodes - 1)) if nodes > 1 else 0
                print(f"  • {name}: {nodes}节点, {edges}边, {sources}源, {sinks}汇, 密度{density:.3f}")

    def demonstrate_expansion_effects(self):
        """演示扩展操作对图概念的影响"""
        self.print_section_header("扩展操作对图概念的影响",
                                 "展示expand_back、expand_forward、expand_all如何改变图的结构")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "expansion_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            # 定义完整的计算流水线
            @op
            def load_raw_data(source: str = "database"):
                """加载原始数据 - 源节点"""
                return f"raw_data_from_{source}"

            @op
            def preprocess(raw_data: str, clean: bool = True):
                """预处理数据 - 中间节点"""
                return f"preprocessed_{raw_data}_clean_{clean}"

            @op
            def extract_features(data: str):
                """提取特征 - 中间节点"""
                return f"features_from_{data}"

            @op
            def train_model(features: str, algorithm: str = "svm"):
                """训练模型 - 中间节点"""
                return f"model_{algorithm}_trained_on_{features}"

            @op
            def evaluate_model(model: str, test_data: str):
                """评估模型 - 中间节点"""
                return f"evaluation_of_{model}_on_{test_data}"

            @op
            def generate_report(evaluation: str):
                """生成报告 - 汇节点"""
                return f"report_based_on_{evaluation}"

            # 执行完整流水线
            raw_data = load_raw_data("database")
            processed_data = preprocess(raw_data, True)
            features = extract_features(processed_data)
            model = train_model(features, "svm")
            test_data = preprocess(load_raw_data("test_set"), True)
            evaluation = evaluate_model(model, test_data)
            report = generate_report(evaluation)

            print("\n🔧 第一步：从中间节点创建最小CF")
            print("💡 概念说明：最小CF只包含指定的节点，不包含计算历史")

            # 从中间节点创建最小CF
            cf_minimal = self.storage.cf(features)
            self.print_detailed_cf_analysis(cf_minimal, "最小CF（从features创建）")

        # 在context外部进行扩展操作
        print("\n🔧 第二步：向后扩展 - 发现数据来源")
        print("💡 概念说明：expand_back追溯数据的生成历史，会增加源节点")

        # 向后扩展
        cf_back = cf_minimal.expand_back(recursive=True)
        self.print_detailed_cf_analysis(cf_back, "向后扩展后的CF")
        self.print_graph_change_analysis(cf_minimal, cf_back, "expand_back")

        print("\n🔧 第三步：向前扩展 - 发现数据使用")
        print("💡 概念说明：expand_forward追踪数据的使用情况，会增加汇节点")

        # 向前扩展
        cf_forward = cf_minimal.expand_forward(recursive=True)
        self.print_detailed_cf_analysis(cf_forward, "向前扩展后的CF")
        self.print_graph_change_analysis(cf_minimal, cf_forward, "expand_forward")

        print("\n🔧 第四步：全方向扩展 - 完整的计算上下文")
        print("💡 概念说明：expand_all获取完整的计算图，包含所有相关节点")

        # 全方向扩展
        cf_all = cf_minimal.expand_all()
        self.print_detailed_cf_analysis(cf_all, "全方向扩展后的CF")
        self.print_graph_change_analysis(cf_back, cf_all, "expand_all vs expand_back")

        print("\n📊 扩展效果总结:")
        expansions = [
            ("最小CF", cf_minimal),
            ("向后扩展", cf_back),
            ("向前扩展", cf_forward),
            ("全方向扩展", cf_all)
        ]

        for name, cf in expansions:
            nodes = len(cf.nodes)
            edges = len(list(cf.edges()))
            sources = len(cf.sources)
            sinks = len(cf.sinks)
            print(f"  • {name}: {nodes}节点, {edges}边, {sources}源, {sinks}汇")

        print("\n💡 关键洞察:")
        print("  • 向后扩展主要增加源节点（发现数据来源）")
        print("  • 向前扩展主要增加汇节点（发现数据使用）")
        print("  • 全方向扩展结合了两者的效果")
        print("  • 扩展操作是理解计算图结构的重要工具")

    def demonstrate_edge_concepts(self):
        """演示边概念的详细分析"""
        self.print_section_header("边概念深度分析",
                                 "专门分析边的类型、方向、密度等概念，展示边如何变化")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "edge_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            print("\n🔧 第一阶段：无边图")
            print("💡 概念说明：孤立节点，没有任何边")

            @op
            def isolated_node():
                """孤立节点"""
                return "isolated"

            result = isolated_node()
            cf_no_edge = self.storage.cf(result)
            self.print_detailed_cf_analysis(cf_no_edge, "无边图")

            print("\n🔧 第二阶段：单边图")
            print("💡 概念说明：最简单的有向边，一个函数产生一个变量")

            @op
            def producer():
                """生产者函数"""
                return "produced_data"

            data = producer()
            cf_single_edge = self.storage.cf(data)
            self.print_detailed_cf_analysis(cf_single_edge, "单边图")
            self.print_graph_change_analysis(cf_no_edge, cf_single_edge, "添加第一条边")

            print("\n🔧 第三阶段：链式边")
            print("💡 概念说明：多个边连接形成链式结构")

            @op
            def processor(data: str):
                """处理器函数"""
                return f"processed_{data}"

            @op
            def finalizer(data: str):
                """最终处理函数"""
                return f"final_{data}"

            data = producer()
            processed = processor(data)
            final = finalizer(processed)
            cf_chain = self.storage.cf(final)
            self.print_detailed_cf_analysis(cf_chain, "链式边图")
            self.print_graph_change_analysis(cf_single_edge, cf_chain, "构建链式边")

            print("\n🔧 第四阶段：分支边")
            print("💡 概念说明：一个节点连接到多个节点，增加边密度")

            @op
            def branch_processor_a(data: str):
                """分支处理器A"""
                return f"branch_a_{data}"

            @op
            def branch_processor_b(data: str):
                """分支处理器B"""
                return f"branch_b_{data}"

            data = producer()
            branch_a = branch_processor_a(data)
            branch_b = branch_processor_b(data)
            cf_branch_edges = self.storage.cf([branch_a, branch_b])
            self.print_detailed_cf_analysis(cf_branch_edges, "分支边图")
            self.print_graph_change_analysis(cf_chain, cf_branch_edges, "添加分支边")

            print("\n🔧 第五阶段：汇聚边")
            print("💡 概念说明：多个节点连接到一个节点，形成汇聚")

            @op
            def merger(data_a: str, data_b: str):
                """合并器函数"""
                return f"merged_{data_a}_and_{data_b}"

            data = producer()
            branch_a = branch_processor_a(data)
            branch_b = branch_processor_b(data)
            merged = merger(branch_a, branch_b)
            cf_merge_edges = self.storage.cf(merged)
            self.print_detailed_cf_analysis(cf_merge_edges, "汇聚边图")
            self.print_graph_change_analysis(cf_branch_edges, cf_merge_edges, "添加汇聚边")

            print("\n🔧 第六阶段：复杂边网络")
            print("💡 概念说明：多种边类型组合，形成复杂的依赖网络")

            @op
            def secondary_processor(data: str):
                """二级处理器"""
                return f"secondary_{data}"

            @op
            def cross_processor(data_a: str, data_b: str):
                """交叉处理器"""
                return f"cross_{data_a}_with_{data_b}"

            # 构建复杂边网络
            data = producer()
            branch_a = branch_processor_a(data)
            branch_b = branch_processor_b(data)
            secondary = secondary_processor(branch_a)
            cross = cross_processor(branch_b, secondary)
            merged = merger(branch_a, cross)

            cf_complex_edges = self.storage.cf([merged, secondary])
            self.print_detailed_cf_analysis(cf_complex_edges, "复杂边网络图")
            self.print_graph_change_analysis(cf_merge_edges, cf_complex_edges, "构建复杂边网络")

            print("\n📊 边演化总结:")
            edge_graphs = [
                ("无边图", cf_no_edge),
                ("单边图", cf_single_edge),
                ("链式边图", cf_chain),
                ("分支边图", cf_branch_edges),
                ("汇聚边图", cf_merge_edges),
                ("复杂边网络图", cf_complex_edges)
            ]

            for name, cf in edge_graphs:
                nodes = len(cf.nodes)
                edges = len(list(cf.edges()))
                density = edges / (nodes * (nodes - 1)) if nodes > 1 else 0
                avg_degree = 2 * edges / nodes if nodes > 0 else 0
                print(f"  • {name}: {edges}边, 密度{density:.3f}, 平均度{avg_degree:.1f}")

            print("\n💡 边概念关键洞察:")
            print("  • 边数量反映计算的复杂度和依赖关系")
            print("  • 边密度反映节点间的连接紧密程度")
            print("  • 平均度数反映每个节点的平均连接数")
            print("  • 边的方向反映数据流的方向")
            print("  • 边的类型（函数→变量，变量→函数）反映计算的性质")

    def demonstrate_source_sink_dynamics(self):
        """演示源节点和汇节点的动态变化"""
        self.print_section_header("源节点和汇节点动态变化",
                                 "展示源节点和汇节点如何随着图的构建而变化")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "source_sink_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            print("\n🔧 第一阶段：单源单汇")
            print("💡 概念说明：最简单的源汇结构")

            @op
            def data_source():
                """数据源 - 源节点"""
                return "source_data"

            @op
            def data_sink(data: str):
                """数据汇 - 汇节点"""
                return f"sink_{data}"

            source_data = data_source()
            sink_result = data_sink(source_data)
            cf_single_source_sink = self.storage.cf(sink_result)
            self.print_detailed_cf_analysis(cf_single_source_sink, "单源单汇图")

            print("\n🔧 第二阶段：多源单汇")
            print("💡 概念说明：多个源节点汇聚到一个汇节点")

            @op
            def data_source_a():
                """数据源A"""
                return "source_a"

            @op
            def data_source_b():
                """数据源B"""
                return "source_b"

            @op
            def multi_input_sink(data_a: str, data_b: str):
                """多输入汇节点"""
                return f"sink_{data_a}_and_{data_b}"

            source_a = data_source_a()
            source_b = data_source_b()
            multi_sink = multi_input_sink(source_a, source_b)
            cf_multi_source = self.storage.cf(multi_sink)
            self.print_detailed_cf_analysis(cf_multi_source, "多源单汇图")
            self.print_graph_change_analysis(cf_single_source_sink, cf_multi_source, "增加源节点")

            print("\n🔧 第三阶段：单源多汇")
            print("💡 概念说明：一个源节点分发到多个汇节点")

            @op
            def multi_output_processor(data: str):
                """多输出处理器"""
                return f"processed_{data}"

            @op
            def sink_a(data: str):
                """汇节点A"""
                return f"sink_a_{data}"

            @op
            def sink_b(data: str):
                """汇节点B"""
                return f"sink_b_{data}"

            source_data = data_source()
            processed = multi_output_processor(source_data)
            sink_result_a = sink_a(processed)
            sink_result_b = sink_b(processed)
            cf_multi_sink = self.storage.cf([sink_result_a, sink_result_b])
            self.print_detailed_cf_analysis(cf_multi_sink, "单源多汇图")
            self.print_graph_change_analysis(cf_multi_source, cf_multi_sink, "增加汇节点")

            print("\n🔧 第四阶段：多源多汇")
            print("💡 概念说明：复杂的多源多汇网络")

            @op
            def cross_processor(data_a: str, data_b: str):
                """交叉处理器"""
                return f"cross_{data_a}_{data_b}"

            source_a = data_source_a()
            source_b = data_source_b()
            cross_result = cross_processor(source_a, source_b)
            sink_result_a = sink_a(source_a)
            sink_result_b = sink_b(cross_result)
            cf_multi_source_sink = self.storage.cf([sink_result_a, sink_result_b])
            self.print_detailed_cf_analysis(cf_multi_source_sink, "多源多汇图")
            self.print_graph_change_analysis(cf_multi_sink, cf_multi_source_sink, "构建多源多汇")

            print("\n🔧 第五阶段：源汇转换")
            print("💡 概念说明：通过扩展操作，汇节点可能变成中间节点")

            @op
            def post_processor(data: str):
                """后处理器 - 将汇节点变成中间节点"""
                return f"post_{data}"

            # 对之前的汇节点进行后处理
            post_result = post_processor(sink_result_a)
            cf_transformed = self.storage.cf(post_result)
            self.print_detailed_cf_analysis(cf_transformed, "源汇转换后的图")
            self.print_graph_change_analysis(cf_multi_source_sink, cf_transformed, "源汇转换")

            print("\n📊 源汇演化总结:")
            source_sink_graphs = [
                ("单源单汇", cf_single_source_sink),
                ("多源单汇", cf_multi_source),
                ("单源多汇", cf_multi_sink),
                ("多源多汇", cf_multi_source_sink),
                ("源汇转换", cf_transformed)
            ]

            for name, cf in source_sink_graphs:
                nodes = len(cf.nodes)
                sources = len(cf.sources)
                sinks = len(cf.sinks)
                source_ratio = sources / nodes if nodes > 0 else 0
                sink_ratio = sinks / nodes if nodes > 0 else 0
                print(f"  • {name}: {sources}源({source_ratio:.2f}), {sinks}汇({sink_ratio:.2f})")

            print("\n💡 源汇概念关键洞察:")
            print("  • 源节点数量反映外部输入的复杂度")
            print("  • 汇节点数量反映输出结果的多样性")
            print("  • 源汇比例反映图的输入输出特征")
            print("  • 扩展操作可以改变节点的源汇性质")
            print("  • 源汇结构影响计算的并行性和复杂度")

    def demonstrate_graph_topology_analysis(self):
        """演示图拓扑分析"""
        self.print_section_header("图拓扑分析深度演示",
                                 "分析图的拓扑特征：层次结构、并行度、关键路径等")

        # 创建临时存储
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "topology_demo.db")
        self.storage = Storage(db_path=db_path)

        with self.storage:
            print("\n🔧 第一阶段：线性拓扑")
            print("💡 概念说明：严格的线性执行顺序，无并行机会")

            @op
            def step1():
                """步骤1"""
                return "step1_result"

            @op
            def step2(data: str):
                """步骤2"""
                return f"step2_{data}"

            @op
            def step3(data: str):
                """步骤3"""
                return f"step3_{data}"

            @op
            def step4(data: str):
                """步骤4"""
                return f"step4_{data}"

            # 构建线性拓扑
            result1 = step1()
            result2 = step2(result1)
            result3 = step3(result2)
            result4 = step4(result3)
            cf_linear_topo = self.storage.cf(result4)
            self.print_detailed_cf_analysis(cf_linear_topo, "线性拓扑图")

            print("\n🔧 第二阶段：并行拓扑")
            print("💡 概念说明：多个独立分支可以并行执行")

            @op
            def parallel_step_a(data: str):
                """并行步骤A"""
                return f"parallel_a_{data}"

            @op
            def parallel_step_b(data: str):
                """并行步骤B"""
                return f"parallel_b_{data}"

            @op
            def parallel_step_c(data: str):
                """并行步骤C"""
                return f"parallel_c_{data}"

            @op
            def sync_step(data_a: str, data_b: str, data_c: str):
                """同步步骤"""
                return f"sync_{data_a}_{data_b}_{data_c}"

            # 构建并行拓扑
            result1 = step1()
            parallel_a = parallel_step_a(result1)
            parallel_b = parallel_step_b(result1)
            parallel_c = parallel_step_c(result1)
            sync_result = sync_step(parallel_a, parallel_b, parallel_c)
            cf_parallel_topo = self.storage.cf(sync_result)
            self.print_detailed_cf_analysis(cf_parallel_topo, "并行拓扑图")
            self.print_graph_change_analysis(cf_linear_topo, cf_parallel_topo, "构建并行拓扑")

            print("\n🔧 第三阶段：层次拓扑")
            print("💡 概念说明：多层次的复杂拓扑结构")

            @op
            def layer2_step_a(data: str):
                """第二层步骤A"""
                return f"layer2_a_{data}"

            @op
            def layer2_step_b(data: str):
                """第二层步骤B"""
                return f"layer2_b_{data}"

            @op
            def layer3_step(data_a: str, data_b: str):
                """第三层步骤"""
                return f"layer3_{data_a}_{data_b}"

            # 构建层次拓扑
            result1 = step1()
            parallel_a = parallel_step_a(result1)
            parallel_b = parallel_step_b(result1)
            layer2_a = layer2_step_a(parallel_a)
            layer2_b = layer2_step_b(parallel_b)
            layer3_result = layer3_step(layer2_a, layer2_b)
            cf_layered_topo = self.storage.cf(layer3_result)
            self.print_detailed_cf_analysis(cf_layered_topo, "层次拓扑图")
            self.print_graph_change_analysis(cf_parallel_topo, cf_layered_topo, "构建层次拓扑")

            print("\n📊 拓扑特征对比:")
            topo_graphs = [
                ("线性拓扑", cf_linear_topo),
                ("并行拓扑", cf_parallel_topo),
                ("层次拓扑", cf_layered_topo)
            ]

            for name, cf in topo_graphs:
                try:
                    topo_order = cf.topsort_modulo_sccs()
                    unique_levels = len(set(topo_order))
                    level_counts = {}
                    for level in topo_order:
                        level_counts[level] = level_counts.get(level, 0) + 1
                    max_parallel = max(level_counts.values()) if level_counts else 0

                    print(f"  • {name}: {unique_levels}层次, 最大并行度{max_parallel}")
                except Exception as e:
                    print(f"  • {name}: 拓扑分析失败 - {e}")

    def run_all_demonstrations(self):
        """运行所有演示"""
        print("🚀 ComputationFrame图概念完整深度理解演示")
        print("=" * 100)
        print("本演示将通过具体案例展示CF图的各种概念及其变化")
        print("重点关注：源节点、汇节点、边、图拓扑、扩展操作等")
        print("=" * 100)

        try:
            # 1. 基本图概念演示
            self.demonstrate_basic_graph_concepts()

            # 2. 扩展操作演示
            self.demonstrate_expansion_effects()

            # 3. 边概念演示
            self.demonstrate_edge_concepts()

            # 4. 源汇动态演示
            self.demonstrate_source_sink_dynamics()

            # 5. 拓扑分析演示
            self.demonstrate_graph_topology_analysis()

            print("\n🎉 演示完成总结:")
            print("=" * 100)
            print("通过以上演示，我们深入理解了ComputationFrame的图概念：")
            print("• 节点：表示计算实体（函数和变量）")
            print("• 边：表示依赖关系和数据流")
            print("• 源节点：计算的起始点，提供外部输入")
            print("• 汇节点：计算的终点，产生最终结果")
            print("• 图拓扑：决定执行顺序和并行机会")
            print("• 扩展操作：动态发现计算历史和使用关系")
            print("• 图密度：反映计算的复杂度和耦合程度")
            print("• 并行度：反映可同时执行的计算数量")
            print("=" * 100)
            print("这些概念的变化反映了计算图的动态特性和优化机会")

        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理临时文件
            if self.temp_dir and os.path.exists(self.temp_dir):
                import shutil
                try:
                    shutil.rmtree(self.temp_dir)
                    print(f"🧹 已清理临时目录: {self.temp_dir}")
                except Exception as e:
                    print(f"⚠️  清理临时目录失败: {e}")


def main():
    """主函数"""
    demo = CFGraphConceptsComplete()
    demo.run_all_demonstrations()


if __name__ == "__main__":
    main()
