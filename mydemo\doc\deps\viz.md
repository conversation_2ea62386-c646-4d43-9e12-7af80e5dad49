# deps/viz.py 文档

## 文件内容与作用总体说明

`deps/viz.py` 文件是 mandala 框架依赖跟踪模块的可视化组件，提供了依赖图的文本和图形化表示功能。该文件实现了将 `DependencyGraph` 对象转换为可读文本格式和 DOT 图形格式的功能，支持按模块和类进行分组显示，使用不同颜色区分不同类型的节点（全局变量、函数、方法），为依赖关系分析提供直观的可视化支持。

## 文件内的所有变量的作用与说明

### 导入的模块和类型
- `textwrap`: 文本包装模块，用于格式化输出
- `DotNode`, `DotEdge`, `DotGroup`: DOT 图形节点、边和分组类
- `to_dot_string`: DOT 字符串生成函数
- `SOLARIZED_LIGHT`: Solarized 配色方案
- `DepKey`: 依赖键类型

### 函数内部变量
- `module_groups`: Dict[str, List["model.Node"]]，按模块分组的节点
- `global_nodes`: List["model.GlobalVarNode"]，全局变量节点列表
- `callable_nodes`: List["model.CallableNode"]，可调用节点列表
- `method_nodes`: List["model.CallableNode"]，方法节点列表
- `func_nodes`: List["model.CallableNode"]，函数节点列表
- `methods_by_class`: Dict[str, List["model.CallableNode"]]，按类分组的方法
- `nodes`: Dict[DepKey, DotNode]，依赖键到DOT节点的映射
- `edges`: Dict[Tuple[DotNode, DotNode], DotEdge]，DOT边的映射

## 文件内的所有函数的作用与说明

### to_string(graph: "model.DependencyGraph") -> str

**作用**: 将依赖图转换为美观的文本字符串表示，按模块和类进行分组显示

**输入参数**:
- `graph`: model.DependencyGraph，要转换的依赖图

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `node.readable_content`: 获取节点的可读内容
- `textwrap.indent`: 缩进文本
- `textwrap.dedent`: 去除缩进

**输出参数**: str - 依赖图的文本表示

**其他说明**：
- 按模块分组显示节点
- 区分全局变量和函数/方法
- 方法按类进一步分组
- 使用缩进表示层次结构

### to_dot(graph: "model.DependencyGraph") -> str

**作用**: 将依赖图转换为 DOT 格式字符串，用于图形化可视化

**输入参数**:
- `graph`: model.DependencyGraph，要转换的依赖图

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `DotNode`: 创建DOT节点
- `DotGroup`: 创建DOT分组
- `DotEdge`: 创建DOT边
- `to_dot_string`: 生成DOT字符串

**输出参数**: str - DOT 格式的图形描述字符串

**其他说明**：
- 使用不同颜色区分节点类型：
  - 全局变量：红色 (SOLARIZED_LIGHT["red"])
  - 函数：蓝色 (SOLARIZED_LIGHT["blue"])
  - 方法：紫色 (SOLARIZED_LIGHT["violet"])
  - 其他：深灰色 (SOLARIZED_LIGHT["base03"])
- 创建模块级别的分组
- 为方法创建类级别的分组
- 设置图形方向为从下到上 (rankdir="BT")

### 可视化设计原则

#### 层次化组织
- **模块分组**：按 Python 模块组织节点
- **类分组**：方法按所属类进一步分组
- **类型区分**：使用不同颜色和符号区分节点类型

#### 颜色编码
- **语义化配色**：使用 Solarized 配色方案提供良好的视觉体验
- **类型区分**：不同类型的节点使用不同颜色
- **一致性**：在文本和图形表示中保持一致的分类逻辑

#### 布局优化
- **自底向上**：依赖关系从下到上显示，符合依赖流向
- **分组显示**：相关节点聚集在一起，减少视觉混乱
- **层次缩进**：文本表示使用缩进显示层次关系

### 输出格式

#### 文本格式
```
MODULE: module_name
-------------------
===Global Variables===
    variable_name = value_representation

===Functions===
    class ClassName:
        method_content
    
    function_content
```

#### DOT 格式
- 生成标准的 Graphviz DOT 语言
- 支持节点分组和颜色设置
- 可以被 Graphviz 工具渲染为各种图形格式

### 使用场景

1. **依赖关系分析**：可视化组件间的依赖关系
2. **代码结构理解**：帮助理解项目的模块和类结构
3. **调试和诊断**：通过可视化发现依赖问题
4. **文档生成**：为项目生成依赖关系文档
5. **重构支持**：在重构时分析影响范围

### 与其他模块的关系

- **model.py**：使用 DependencyGraph、Node 等类型
- **../viz.py**：使用主可视化模块的 DOT 生成功能
- **utils.py**：使用 DepKey 类型定义
- **versioner.py**：为版本管理器提供可视化支持

### 扩展性

#### 自定义样式
- 颜色方案可以通过修改 SOLARIZED_LIGHT 配置调整
- 节点形状和样式可以通过 DotNode 参数定制
- 布局方向可以通过 rankdir 参数调整

#### 输出格式
- 支持多种图形输出格式（PNG、SVG、PDF等）
- 文本格式可以进一步定制缩进和分隔符
- 可以添加更多元数据显示（如版本信息、哈希值等）

#### 交互功能
- 可以与 Web 界面集成提供交互式可视化
- 支持节点点击和悬停显示详细信息
- 可以添加过滤和搜索功能

### 性能考虑

- **大图处理**：对于大型依赖图，可能需要分页或分层显示
- **渲染优化**：DOT 格式生成针对 Graphviz 优化
- **内存效率**：使用字典映射避免重复创建对象

### 配置选项

- **显示级别**：可以控制显示的详细程度
- **过滤选项**：可以过滤特定类型的节点或边
- **布局选项**：支持不同的图形布局算法
- **主题选择**：支持不同的颜色主题
