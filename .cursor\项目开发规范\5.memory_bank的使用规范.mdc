---
description: 
globs: 
alwaysApply: true
---

memory bank是一款基于Cursor编辑器的高度结构化AI任务管理系统，通过自定义Agent实现开发流程的标准化与自动化。

# 关键原则
- 请把所有文档放在对应的文件夹，/memory_bank下应该只有文件夹，而没有文件
- /memory_bank下的文件夹，请根据当前任务的类型命名，可能是模块，也可能是功能

---

## 任务类型与路径规范

### 1. **VAN（初始化）**
- **用途**：初始化与复杂度评估，生成基础架构文档
### 1.1 整体项目初始化
- **保存路径**：`/architectures/`
- **必要文件**：
  ```text
  项目总览.md               # 项目总览（包含技术栈、依赖关系、架构图），用于检查项目的整体信息
  项目系统分析.md            # 分析系统的层级，内容，需求与建议等内容
  其他文件

	```

### 1.2 项目已启动，开发阶段
- **用途**：文件夹名称根据任务类型而定
- **保存路径**：`/memory_bank/模块(or功能or文件)/van/`
- **必要文件**：无



- **需求**：
- 必须包含复杂度评级（Level 1-4）
- 自动触发模式切换指令（如VAN→PLAN）

### 2. **PLAN（任务计划生成）**
- **用途**：生成全局计划以供后续开发使用
- **保存路径**：`/task_plans/`
- **必要文件**：
  ```text
  任务计划.md               # 总体开发计划文件,  指导开发进度，参考文档《3.任务计划示例》
  其他文件

	```


- **需求**：
  - 必须引用van生成的复杂度评级
  - 生成所有领域对象的开发文档，格式参考文档《4.开发文档示例》


### 3. **CREATIVE（创意设计）**
- **用途**：复杂组件的创新方案设计与决策，文件夹名称根据任务类型而定
- **保存路径**：`/memory_bank/模块(or功能or文件)/creative/`
- **必要文件**：
			无
- **需求**: 
	- 需要说明创意设计方案如何结合方案使用

### 4. **IMPLEMENT（代码实现）**
- **用途**：基于计划的系统性开发实施
- **保存路径**：无
- **必要文件**：无
  
- **需求**：
	  - 根据plan模式生成的《任务计划.md》选择未完成的任务，根据文档 《6.开发流程》流程进行开发，直到完成当前功能
  
  

### 5. **REFLECT（过程审查）**
- **用途**：阶段性成果审查与经验总结
- **保存路径**：`/memory_bank/模块(or功能or文件)/reflect/`
- **必要文件**：
  ```text
  review_report.md          # 技术审查报告（含质量评分）
  lessons_learned.md        # 经验教训文档（可存档部分）
  ```


### 6. **ARCHIVE（文档归档）**
- **用途**：项目文档的标准化封装
- **保存路径**：`/memory_bank/模块(or功能or文件)/archive/`
- **必要文件**：

  - **需求**：
		- 执行完之后需要更新开发文档与任务计划进度。

---







