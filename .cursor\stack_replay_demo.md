想实现一个栈重放（stack replay，时间旅行器)。不另外生成新类，只确保是否能以下完成功能 。
要求
# 功能描述：
使用manadala1，实现程序运行后，能捕获完整的函数执行信息。
# 代码规范
1.认真分析需求与相关文档、源代码，确保实现优雅，完成需求且没有重复实现与框架相同的功能。
## 关于computationFrame要实现的功能 ：
1.计算图遍历打印，要求高可读性，有函数层级的关系。
2.计算图遍历的每个节点内容包括但不限于：
- 节点的函数名
- 节点的深度

## 关于storage要实现的功能 
1.程序运行后的函数执行顺序的遍历打印，要求高可读性，有函数层级的关系。
2.运行图遍历的每个节点内容包括但不限于：
- 节点的函数名
- 节点的深度
- 入参与出参

# 注意事项
1.如 @mandala1 不可用，请添加代码sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))。
2.为方便观察是否正确运行，不使用try。。。except逻辑

