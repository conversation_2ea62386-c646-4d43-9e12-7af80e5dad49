site_name: Mandala Documentation

plugins:
  - search

theme:
  name: material
  font:
    text: Roboto
    code: Roboto Mono
  features:
    - content.code.copy  # Adds a copy button to code blocks
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: Switch to light mode

markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

docs_dir: 'docs/docs'

extra_css:
  - stylesheets/extra.css
