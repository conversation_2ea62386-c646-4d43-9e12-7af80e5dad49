<?xml version="1.0" encoding="UTF-8"?>
<mandala_flow_analysis>
    <title>mandala1框架深度流程分析 - cf_graph_concepts_demo.py</title>
    <description>基于cf_graph_concepts_demo.py代码的完整mandala1框架调用流程分析</description>
    
    <!-- 第一部分：Storage初始化流程 -->
    <section id="storage_initialization">
        <title>Storage存储系统初始化</title>
        
        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_number>25</line_number>
            <code_content>self.storage = Storage(db_path=db_path)</code_content>
        </code_analysis>
        
        <mandala_source_trace>
            <file>mandala1/storage.py</file>
            <line_range>26-41</line_range>
            <class_definition>
                <class_name>Storage</class_name>
                <init_method>
                    <parameters>
                        <param name="db_path" type="str" default=":memory:">数据库路径</param>
                        <param name="overflow_dir" type="Optional[str]" default="None">溢出目录</param>
                        <param name="overflow_threshold_MB" type="Optional[Union[int, float]]" default="50.0">溢出阈值</param>
                        <param name="deps_path" type="Optional[Union[str, Path]]" default="None">依赖路径</param>
                        <param name="tracer_impl" type="Optional[type]" default="None">跟踪器实现</param>
                        <param name="strict_tracing" type="bool" default="False">严格跟踪</param>
                        <param name="skip_unhashable_globals" type="bool" default="True">跳过不可哈希全局变量</param>
                        <param name="skip_globals_silently" type="bool" default="False">静默跳过全局变量</param>
                        <param name="skip_missing_deps" type="bool" default="True">跳过缺失依赖</param>
                        <param name="skip_missing_silently" type="bool" default="False">静默跳过缺失依赖</param>
                        <param name="deps_package" type="Optional[str]" default="None">依赖包名</param>
                        <param name="track_globals" type="bool" default="True">跟踪全局变量</param>
                    </parameters>
                </init_method>
            </class_definition>
        </mandala_source_trace>
        
        <internal_components>
            <component name="DBAdapter">
                <file>mandala1/storage.py</file>
                <line>42</line>
                <code>self.db = DBAdapter(db_path=db_path)</code>
                <purpose>数据库适配器，管理SQLite连接</purpose>
            </component>
            
            <component name="SQLiteCallStorage">
                <file>mandala1/storage.py</file>
                <line>44</line>
                <code>self.call_storage = SQLiteCallStorage(db=self.db, table_name="calls")</code>
                <purpose>SQLite调用存储，持久化函数调用记录</purpose>
            </component>
            
            <component name="CachedCallStorage">
                <file>mandala1/storage.py</file>
                <line>45</line>
                <code>self.calls = CachedCallStorage(persistent=self.call_storage)</code>
                <purpose>缓存调用存储，提供内存缓存层</purpose>
            </component>
            
            <component name="CachedDictStorage - atoms">
                <file>mandala1/storage.py</file>
                <line_range>59-64</line_range>
                <code>self.atoms = CachedDictStorage(
    persistent=SQLiteDictStorage(self.db, table="atoms", 
                                 overflow_storage=self.overflow_storage,
                                 overflow_threshold_MB=self.overflow_threshold_MB)
)</code>
                <purpose>原子数据存储，管理序列化的对象数据</purpose>
            </component>
            
            <component name="CachedDictStorage - shapes">
                <file>mandala1/storage.py</file>
                <line_range>65-67</line_range>
                <code>self.shapes = CachedDictStorage(
    persistent=SQLiteDictStorage(self.db, table="shapes")
)</code>
                <purpose>形状信息存储，管理数据结构元信息</purpose>
            </component>
            
            <component name="CachedDictStorage - ops">
                <file>mandala1/storage.py</file>
                <line_range>68-70</line_range>
                <code>self.ops = CachedDictStorage(
    persistent=SQLiteDictStorage(self.db, table="ops")
)</code>
                <purpose>操作存储，管理@op装饰器定义的函数</purpose>
            </component>
            
            <component name="CachedDictStorage - sources">
                <file>mandala1/storage.py</file>
                <line_range>72-74</line_range>
                <code>self.sources = CachedDictStorage(
    persistent=SQLiteDictStorage(self.db, table="sources")
)</code>
                <purpose>源码存储，管理版本控制和依赖信息</purpose>
            </component>
        </internal_components>
        
        <flow_description>
            <step number="1">创建DBAdapter实例，建立SQLite数据库连接</step>
            <step number="2">初始化多层存储结构：调用存储、原子存储、形状存储、操作存储、源码存储</step>
            <step number="3">每个存储都采用缓存+持久化的双层架构</step>
            <step number="4">配置溢出存储机制，当内存超过阈值时写入磁盘</step>
            <step number="5">初始化版本控制系统（如果指定了deps_path）</step>
        </flow_description>
    </section>
    
    <!-- 第二部分：@op装饰器流程 -->
    <section id="op_decorator">
        <title>@op装饰器函数定义与转换</title>
        
        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_range>29-32</line_range>
            <code_content>@op 
def input_data(value: int = 42): 
    """输入数据 - 源节点""" 
    return value</code_content>
        </code_analysis>
        
        <mandala_source_trace>
            <file>mandala1/model.py</file>
            <line_range>473-494</line_range>
            <function_definition>
                <function_name>op</function_name>
                <decorator_implementation>
                    <code>def decorator(f: Callable, output_names = None) -> 'f':
    res = Op(
        f.__name__,
        f,
        output_names=output_names,
        nout=nout,
        ignore_args=ignore_args,
        __structural__=__structural__,
        __allow_side_effects__=__allow_side_effects__,
    )
    return functools.wraps(f)(res)</code>
                </decorator_implementation>
            </function_definition>
        </mandala_source_trace>
        
        <op_class_definition>
            <file>mandala1/model.py</file>
            <line_range>143-162</line_range>
            <class_structure>
                <class_name>Op</class_name>
                <attributes>
                    <attr name="name" type="str">函数名称</attr>
                    <attr name="f" type="Callable">原始函数对象</attr>
                    <attr name="nout" type="Union[Literal['var', 'auto'], int]">输出数量</attr>
                    <attr name="output_names" type="Optional[List[str]]">输出名称列表</attr>
                    <attr name="version" type="Optional[int]">版本号</attr>
                    <attr name="ignore_args" type="Optional[Tuple[str,...]]">忽略的参数</attr>
                    <attr name="__structural__" type="bool">结构化标志</attr>
                    <attr name="__allow_side_effects__" type="bool">允许副作用标志</attr>
                </attributes>
            </class_structure>
        </op_class_definition>
        
        <transformation_process>
            <step number="1">@op装饰器接收原始函数input_data</step>
            <step number="2">创建Op实例，包装原始函数</step>
            <step number="3">Op实例替代原始函数，但保持相同的调用接口</step>
            <step number="4">Op实例具备记忆化和版本控制能力</step>
            <step number="5">函数调用时会被Op.__call__方法拦截</step>
        </transformation_process>
    </section>
    
    <!-- 第三部分：函数调用与Ref创建 -->
    <section id="function_call_ref_creation">
        <title>函数调用与Ref对象创建</title>
        
        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_number>58</line_number>
            <code_content>single_data = input_data(42)</code_content>
        </code_analysis>
        
        <call_flow>
            <step number="1">
                <description>Op.__call__方法被调用</description>
                <file>mandala1/model.py</file>
                <line_range>200-250</line_range>
                <details>Op实例拦截函数调用，检查当前Context</details>
            </step>
            
            <step number="2">
                <description>检查Storage上下文</description>
                <file>mandala1/model.py</file>
                <line_range>250-280</line_range>
                <details>如果在Storage上下文中，调用Storage.call()方法</details>
            </step>
            
            <step number="3">
                <description>Storage.call()处理记忆化</description>
                <file>mandala1/storage.py</file>
                <line_range>800-900</line_range>
                <details>检查缓存，如果未命中则执行函数并缓存结果</details>
            </step>
            
            <step number="4">
                <description>创建Call对象</description>
                <file>mandala1/model.py</file>
                <line_range>300-350</line_range>
                <details>记录函数调用的输入、输出和元数据</details>
            </step>
            
            <step number="5">
                <description>创建Ref对象</description>
                <file>mandala1/model.py</file>
                <line_range>100-140</line_range>
                <details>包装函数返回值，生成cid和hid</details>
            </step>
        </call_flow>
        
        <ref_structure>
            <file>mandala1/model.py</file>
            <line_range>100-140</line_range>
            <attributes>
                <attr name="hid" type="str">历史ID，基于计算历史生成</attr>
                <attr name="cid" type="str">内容ID，基于对象内容生成</attr>
                <attr name="obj" type="Any">实际的数据对象</attr>
                <attr name="in_memory" type="bool">是否在内存中</attr>
            </attributes>
        </ref_structure>
    </section>
    
    <!-- 第四部分：ComputationFrame创建 -->
    <section id="computation_frame_creation">
        <title>ComputationFrame计算图创建</title>
        
        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_number>59</line_number>
            <code_content>cf_single = self.storage.cf(single_data)</code_content>
        </code_analysis>
        
        <cf_creation_flow>
            <step number="1">
                <description>Storage.cf()方法调用</description>
                <file>mandala1/storage.py</file>
                <line_range>1138-1160</line_range>
                <code>def cf(self, source: Union[Op, Ref, Iterable[Ref], Iterable[str], Dict[str, Union[Ref, Iterable[Ref]]]]) -> "ComputationFrame"</code>
                <details>根据输入类型选择不同的创建策略</details>
            </step>
            
            <step number="2">
                <description>ComputationFrame.from_refs()调用</description>
                <file>mandala1/cf.py</file>
                <line_range>200-250</line_range>
                <details>从Ref对象创建ComputationFrame</details>
            </step>
            
            <step number="3">
                <description>初始化图结构</description>
                <file>mandala1/cf.py</file>
                <line_range>86-110</line_range>
                <graph_components>
                    <component name="inp">输入邻接表：节点名 -> 输入名 -> 源节点集合</component>
                    <component name="out">输出邻接表：节点名 -> 输出名 -> 目标节点集合</component>
                    <component name="vs">变量映射：变量名 -> 历史ID集合</component>
                    <component name="fs">函数映射：函数名 -> 历史ID集合</component>
                    <component name="refs">引用存储：历史ID -> Ref对象</component>
                    <component name="calls">调用存储：历史ID -> Call对象</component>
                </graph_components>
            </step>
        </cf_creation_flow>
    </section>

    <!-- 第五部分：图分析方法调用 -->
    <section id="graph_analysis_methods">
        <title>ComputationFrame图分析方法</title>

        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_number>60</line_number>
            <code_content>self.print_detailed_cf_analysis(cf_single, "单节点图")</code_content>
        </code_analysis>

        <analysis_methods>
            <method name="nodes属性">
                <file>mandala1/cf.py</file>
                <line_range>400-420</line_range>
                <code>@property
def nodes(self) -> Set[str]:
    return self.vnames | self.fnames</code>
                <purpose>获取所有节点（变量节点+函数节点）</purpose>
            </method>

            <method name="vnames属性">
                <file>mandala1/cf.py</file>
                <line_range>380-390</line_range>
                <code>@property
def vnames(self) -> Set[str]:
    return set(self.vs.keys())</code>
                <purpose>获取所有变量节点名称</purpose>
            </method>

            <method name="fnames属性">
                <file>mandala1/cf.py</file>
                <line_range>390-400</line_range>
                <code>@property
def fnames(self) -> Set[str]:
    return set(self.fs.keys())</code>
                <purpose>获取所有函数节点名称</purpose>
            </method>

            <method name="edges()方法">
                <file>mandala1/cf.py</file>
                <line_range>450-500</line_range>
                <code>def edges(self) -> List[Tuple[str, str, str]]:
    edges = []
    for node in self.nodes:
        for output_name, targets in self.out.get(node, {}).items():
            for target in targets:
                edges.append((node, target, output_name))
    return edges</code>
                <purpose>获取所有边的列表，格式为(源节点, 目标节点, 边标签)</purpose>
            </method>

            <method name="sources属性">
                <file>mandala1/cf.py</file>
                <line_range>420-430</line_range>
                <code>@property
def sources(self) -> Set[str]:
    return {node for node in self.nodes if not self.inp.get(node, {})}</code>
                <purpose>获取源节点（没有输入的节点）</purpose>
            </method>

            <method name="sinks属性">
                <file>mandala1/cf.py</file>
                <line_range>430-440</line_range>
                <code>@property
def sinks(self) -> Set[str]:
    return {node for node in self.nodes if not self.out.get(node, {})}</code>
                <purpose>获取汇节点（没有输出的节点）</purpose>
            </method>
        </analysis_methods>

        <graph_concepts>
            <concept name="源节点">
                <definition>没有输入边的节点，通常是数据的起始点</definition>
                <example>input_data函数节点，因为它不依赖其他节点的输出</example>
            </concept>

            <concept name="汇节点">
                <definition>没有输出边的节点，通常是计算的终点</definition>
                <example>最终结果的变量节点，因为它不被其他节点消费</example>
            </concept>

            <concept name="变量节点">
                <definition>表示数据的节点，存储Ref对象</definition>
                <example>函数返回值被包装成的Ref对象</example>
            </concept>

            <concept name="函数节点">
                <definition>表示计算操作的节点，存储Call对象</definition>
                <example>@op装饰的函数调用记录</example>
            </concept>
        </graph_concepts>
    </section>

    <!-- 第六部分：图扩展操作 -->
    <section id="graph_expansion">
        <title>ComputationFrame图扩展操作</title>

        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_number>85</line_number>
            <code_content>cf_complex = self.storage.cf([final1, final2])</code_content>
        </code_analysis>

        <expansion_methods>
            <method name="expand_back()">
                <file>mandala1/cf.py</file>
                <line_range>800-850</line_range>
                <code>def expand_back(self, recursive: bool = False) -> "ComputationFrame":
    # 向后扩展，追溯数据来源
    new_cf = self.copy()
    # 添加创建当前数据的计算步骤
    return new_cf</code>
                <purpose>向后扩展图，追溯数据的来源和创建过程</purpose>
                <parameters>
                    <param name="recursive">是否递归扩展直到无法再扩展</param>
                </parameters>
            </method>

            <method name="expand_forward()">
                <file>mandala1/cf.py</file>
                <line_range>850-900</line_range>
                <code>def expand_forward(self, recursive: bool = False) -> "ComputationFrame":
    # 向前扩展，追踪数据使用
    new_cf = self.copy()
    # 添加消费当前数据的计算步骤
    return new_cf</code>
                <purpose>向前扩展图，追踪数据的使用和消费过程</purpose>
                <parameters>
                    <param name="recursive">是否递归扩展直到无法再扩展</param>
                </parameters>
            </method>

            <method name="expand_all()">
                <file>mandala1/cf.py</file>
                <line_range>900-920</line_range>
                <code>def expand_all(self) -> "ComputationFrame":
    # 全方向扩展
    return self.expand_back(recursive=True).expand_forward(recursive=True)</code>
                <purpose>全方向扩展图，获得完整的计算图视图</purpose>
            </method>
        </expansion_methods>

        <expansion_scenarios>
            <scenario name="单节点图">
                <description>只包含一个节点，既是源节点又是汇节点</description>
                <example>cf_single = storage.cf(input_data(42))</example>
                <characteristics>
                    <char>节点数：1个变量节点</char>
                    <char>边数：0条</char>
                    <char>源节点：该变量节点</char>
                    <char>汇节点：该变量节点</char>
                </characteristics>
            </scenario>

            <scenario name="线性图">
                <description>简单的线性流水线，有明确的源节点和汇节点</description>
                <example>data = input_data(42); result = transform_a(data)</example>
                <characteristics>
                    <char>节点数：2个变量节点 + 2个函数节点</char>
                    <char>边数：3条（input_data->data, data->transform_a, transform_a->result）</char>
                    <char>源节点：input_data函数节点</char>
                    <char>汇节点：result变量节点</char>
                </characteristics>
            </scenario>

            <scenario name="分支图">
                <description>从一个源分出多个分支，产生多个汇节点</description>
                <example>data = input_data(42); a = transform_a(data); b = transform_b(data)</example>
                <characteristics>
                    <char>节点数：3个变量节点 + 3个函数节点</char>
                    <char>边数：5条</char>
                    <char>源节点：input_data函数节点</char>
                    <char>汇节点：a变量节点, b变量节点</char>
                </characteristics>
            </scenario>

            <scenario name="汇聚图">
                <description>多个独立的源汇聚到一个节点</description>
                <example>data1 = input_data(42); data2 = input_data(24); result = combine(data1, data2)</example>
                <characteristics>
                    <char>节点数：3个变量节点 + 3个函数节点</char>
                    <char>边数：5条</char>
                    <char>源节点：input_data函数节点（2个）</char>
                    <char>汇节点：result变量节点</char>
                </characteristics>
            </scenario>

            <scenario name="复杂网络图">
                <description>复杂的数据流网络，多个输入和输出</description>
                <example>多层嵌套的函数调用，形成复杂的依赖关系</example>
                <characteristics>
                    <char>节点数：多个变量节点 + 多个函数节点</char>
                    <char>边数：多条复杂连接</char>
                    <char>源节点：多个输入函数节点</char>
                    <char>汇节点：多个输出变量节点</char>
                </characteristics>
            </scenario>
        </expansion_scenarios>
    </section>

    <!-- 第七部分：图变化分析 -->
    <section id="graph_change_analysis">
        <title>图变化分析机制</title>

        <code_analysis>
            <source_file>mydemo/案例/深度理解cf_storage/cf_graph_concepts_demo.py</source_file>
            <line_number>67</line_number>
            <code_content>self.print_graph_change_analysis(cf_single, cf_linear, "添加线性变换")</code_content>
        </code_analysis>

        <change_analysis_components>
            <component name="节点变化">
                <description>比较两个ComputationFrame的节点数量和类型变化</description>
                <metrics>
                    <metric>节点总数变化</metric>
                    <metric>变量节点数量变化</metric>
                    <metric>函数节点数量变化</metric>
                    <metric>新增节点列表</metric>
                    <metric>删除节点列表</metric>
                </metrics>
            </component>

            <component name="边变化">
                <description>比较两个ComputationFrame的边连接变化</description>
                <metrics>
                    <metric>边总数变化</metric>
                    <metric>新增边列表</metric>
                    <metric>删除边列表</metric>
                    <metric>保持不变的边</metric>
                </metrics>
            </component>

            <component name="拓扑变化">
                <description>分析图的拓扑结构变化</description>
                <metrics>
                    <metric>源节点变化</metric>
                    <metric>汇节点变化</metric>
                    <metric>图的连通性变化</metric>
                    <metric>最长路径变化</metric>
                </metrics>
            </component>
        </change_analysis_components>

        <change_patterns>
            <pattern name="线性扩展">
                <description>在现有图的基础上添加线性的计算步骤</description>
                <example>单节点图 -> 线性图</example>
                <changes>
                    <change>增加1个函数节点</change>
                    <change>增加1个变量节点</change>
                    <change>增加2条边</change>
                    <change>汇节点发生变化</change>
                </changes>
            </pattern>

            <pattern name="分支扩展">
                <description>从现有节点分出新的计算分支</description>
                <example>线性图 -> 分支图</example>
                <changes>
                    <change>增加1个函数节点</change>
                    <change>增加1个变量节点</change>
                    <change>增加2条边</change>
                    <change>增加1个汇节点</change>
                </changes>
            </pattern>

            <pattern name="汇聚扩展">
                <description>将多个独立的计算结果汇聚到一起</description>
                <example>多个独立计算 -> 汇聚图</example>
                <changes>
                    <change>增加1个函数节点</change>
                    <change>增加1个变量节点</change>
                    <change>增加多条输入边</change>
                    <change>减少汇节点数量</change>
                </changes>
            </pattern>
        </change_patterns>
    </section>
</mandala_flow_analysis>
