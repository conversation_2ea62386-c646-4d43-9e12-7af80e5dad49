# 文件名
只修改后缀，如cf.py，对应cf.md，
# 内容
 1.文件的内容，作用总体说明。
2.文件内的所有的变量的作用与说明。
3.文件内的所有的函数，包括方法，私有方法的作用与说明，包括输入参数的详细说明，输出参数的详细说明，内部调用函数的说明。
注意：
# 着重说明
cf.py与storage.py
# 函数格式：
#### drop_calls(calls_or_hids: Union[Iterable[str], Iterable[Call]], delete_dependents: bool, conn: Optional[sqlite3.Connection] = None)

**作用**: 详细说明作用

**输入参数**:

- `calls_or_hids`: 要删除的调用或历史ID

- `delete_dependents`: bool，是否删除依赖调用

- `conn`: 可选的数据库连接
**内部调用的函数**：
- SQLiteCallStorage.exists：(作用)xxx
**输出参数**: 参数说明
**其他说明**：其他必须说明的内容 

# 完成工作
提取所有代码文件函数与说明文档的变量，函数列表，生成对应名称的csv文件，对照并检查是否全部生成完毕

