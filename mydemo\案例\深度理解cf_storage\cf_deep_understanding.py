"""
ComputationFrame 深度理解用例
展示 ComputationFrame 的完整生命周期和核心功能

功能覆盖：
1. CF的创建和初始化
2. 计算图的构建和连接
3. 拓扑排序和图分析
4. 节点的增删查改操作
5. 图的扩展和合并
6. 数据提取和可视化
7. 完整的程序运行周期观察

基于mandala1框架，不创建新类，充分利用现有功能
"""

import os
import sys
from typing import Dict, List, Any, Optional
import time

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


class CFDeepUnderstanding:
    """ComputationFrame深度理解演示类"""
    
    def __init__(self):
        self.storage = None
        self.current_phase = 0
        self.phase_results = {}
        
    def print_phase_header(self, phase_num: int, title: str, description: str):
        """打印阶段标题"""
        self.current_phase = phase_num
        print("\n" + "=" * 80)
        print(f"🔍 阶段 {phase_num}: {title}")
        print("=" * 80)
        print(f"📝 描述: {description}")
        print("-" * 80)
    
    def print_cf_info(self, cf: ComputationFrame, title: str = "ComputationFrame信息"):
        """打印ComputationFrame的详细信息"""
        print(f"\n📊 {title}:")
        print(f"  🔢 节点总数: {len(cf.nodes)}")
        print(f"  📦 变量节点: {len(cf.vnames)} 个 - {list(cf.vnames)[:5]}{'...' if len(cf.vnames) > 5 else ''}")
        print(f"  ⚙️  函数节点: {len(cf.fnames)} 个 - {list(cf.fnames)}")
        print(f"  🔗 边数量: {len(list(cf.edges()))}")
        print(f"  🌱 源节点: {len(cf.sources)} 个 - {list(cf.sources)[:3]}{'...' if len(cf.sources) > 3 else ''}")
        print(f"  🎯 汇节点: {len(cf.sinks)} 个 - {list(cf.sinks)[:3]}{'...' if len(cf.sinks) > 3 else ''}")
        
        # 显示图描述
        try:
            graph_desc = cf.get_graph_desc()
            print(f"  📋 图描述: {graph_desc[:100]}{'...' if len(graph_desc) > 100 else ''}")
        except Exception as e:
            print(f"  📋 图描述: 获取失败 - {e}")

        # 添加详细的概念说明
        self.print_cf_concepts_explanation(cf)

    def print_cf_concepts_explanation(self, cf: ComputationFrame):
        """打印ComputationFrame核心概念的详细说明"""
        print(f"\n💡 ComputationFrame核心概念详解:")

        # 节点概念说明
        print(f"  📋 节点(Nodes)概念:")
        print(f"    • 总节点数: {len(cf.nodes)} - 表示计算图中的所有实体")
        print(f"    • 变量节点: {len(cf.vnames)} 个 - 表示数据对象(Ref)")
        print(f"    • 函数节点: {len(cf.fnames)} 个 - 表示计算操作(@op函数)")
        print(f"    💡 节点变化原因: 扩展操作会发现新的计算步骤和数据对象")

        # 边概念说明
        edges_list = list(cf.edges())
        print(f"  🔗 边(Edges)概念:")
        print(f"    • 边数量: {len(edges_list)} - 表示节点间的依赖关系")
        print(f"    • 边类型: 函数→变量(数据生产), 变量→函数(数据消费)")
        print(f"    💡 边变化原因: 新发现的计算步骤会产生新的依赖关系")

        # 源节点和汇节点说明
        print(f"  🌱 源节点(Sources)概念:")
        print(f"    • 数量: {len(cf.sources)} 个 - 没有输入的节点")
        print(f"    • 含义: 计算的起始点(输入参数、外部数据)")
        print(f"    💡 变化原因: 向后扩展会发现更多的数据源")

        print(f"  🎯 汇节点(Sinks)概念:")
        print(f"    • 数量: {len(cf.sinks)} 个 - 没有输出的节点")
        print(f"    • 含义: 计算的终点(最终结果、未被使用的中间结果)")
        print(f"    💡 变化原因: 向前扩展会发现更多的数据使用者")

        # 图结构分析
        print(f"  📊 图结构分析:")
        if len(cf.sources) == 0:
            print(f"    ⚠️  无源节点: 可能存在循环依赖或图不完整")
        elif len(cf.sources) == 1:
            print(f"    ✅ 单源结构: 典型的线性计算流水线")
        else:
            print(f"    🔀 多源结构: 多个输入的复杂计算图")

        if len(cf.sinks) == 0:
            print(f"    ⚠️  无汇节点: 可能存在循环或所有结果都被使用")
        elif len(cf.sinks) == 1:
            print(f"    ✅ 单汇结构: 典型的汇聚型计算")
        else:
            print(f"    🔀 多汇结构: 多个输出的分散型计算")

        # 计算复杂度分析
        node_count = len(cf.nodes)
        edge_count = len(edges_list)
        if node_count > 0:
            density = edge_count / (node_count * (node_count - 1)) if node_count > 1 else 0
            print(f"  📈 复杂度分析:")
            print(f"    • 图密度: {density:.3f} - 边数相对于最大可能边数的比例")
            print(f"    • 平均度数: {2 * edge_count / node_count:.2f} - 每个节点的平均连接数")

            if density < 0.1:
                print(f"    💡 稀疏图: 节点间连接较少，计算相对独立")
            elif density > 0.5:
                print(f"    💡 密集图: 节点间连接较多，计算高度相关")
            else:
                print(f"    💡 中等密度: 适中的计算复杂度")
    
    def demonstrate_cf_creation(self):
        """阶段1: 演示ComputationFrame的创建"""
        self.print_phase_header(1, "ComputationFrame创建", 
                               "展示如何从不同的计算结果创建ComputationFrame")
        
        # 定义计算函数
        @op
        def load_data(size: int):
            """模拟数据加载"""
            print(f"    🔄 加载数据，大小: {size}")
            time.sleep(0.1)
            return list(range(size))
        
        @op
        def preprocess(data: list, normalize: bool = True):
            """数据预处理"""
            print(f"    🔄 预处理数据，标准化: {normalize}")
            time.sleep(0.1)
            if normalize:
                return [x / max(data) if max(data) > 0 else 0 for x in data]
            return data
        
        @op
        def compute_stats(data: list):
            """计算统计信息"""
            print(f"    🔄 计算统计信息")
            time.sleep(0.1)
            return {
                'mean': sum(data) / len(data) if data else 0,
                'max': max(data) if data else 0,
                'min': min(data) if data else 0,
                'count': len(data)
            }
        
        # 创建存储并执行计算
        self.storage = Storage()
        
        with self.storage:
            print("🚀 执行计算流程...")
            
            # 执行计算
            raw_data = load_data(10)
            processed_data = preprocess(raw_data, normalize=True)
            stats = compute_stats(processed_data)
            
            print(f"  ✅ 原始数据: {self.storage.unwrap(raw_data)[:5]}...")
            print(f"  ✅ 处理后数据: {self.storage.unwrap(processed_data)[:5]}...")
            print(f"  ✅ 统计信息: {self.storage.unwrap(stats)}")
        
        # 从不同结果创建ComputationFrame
        print("\n🔧 创建ComputationFrame:")
        print("📝 说明：默认情况下，cf()只创建包含指定引用的最小CF，不包含计算历史")

        # 1. 从单个引用创建（最小CF）
        cf_stats = self.storage.cf(stats)
        self.print_cf_info(cf_stats, "从stats创建的CF（最小CF）")

        # 2. 从多个引用创建（最小CF）
        cf_multi = self.storage.cf([raw_data, processed_data, stats])
        self.print_cf_info(cf_multi, "从多个引用创建的CF（最小CF）")
        print("💡 注意：多个引用的最小CF可能只包含这些引用本身，不包含计算关系")

        # 3. 从函数创建
        cf_func = self.storage.cf(compute_stats)
        self.print_cf_info(cf_func, "从函数创建的CF")

        print("💡 从函数创建CF的特点：")
        print("  📋 只显示函数的直接输入输出关系（函数签名视图）")
        print("  🔍 不包含输入数据的生成历史")
        print("  🎯 适用于理解函数接口和参数依赖")
        print(f"  📊 节点构成：{len([n for n in cf_func.nodes if n in cf_func.fnames])}个函数 + {len([n for n in cf_func.nodes if n in cf_func.vnames])}个变量")

        # 4. 展示扩展后的差异
        print("\n🔍 对比：扩展后的CF差异")
        cf_stats_expanded = self.storage.cf(stats).expand_back(recursive=True)
        self.print_cf_info(cf_stats_expanded, "从stats扩展后的CF")

        print("💡 从结果扩展CF的特点：")
        print("  📋 包含生成结果的完整计算历史（数据血缘视图）")
        print("  🔍 追溯到最初的输入参数和数据源")
        print("  🎯 适用于调试问题、数据溯源、依赖分析")
        print(f"  📊 节点构成：{len([n for n in cf_stats_expanded.nodes if n in cf_stats_expanded.fnames])}个函数 + {len([n for n in cf_stats_expanded.nodes if n in cf_stats_expanded.vnames])}个变量")

        print("\n📈 扩展效果对比：")
        print(f"  🔢 从函数创建：{len(cf_func.nodes)}个节点 → 局部视图（函数接口）")
        print(f"  🔢 从结果扩展：{len(cf_stats_expanded.nodes)}个节点 → 全局视图（完整历史）")
        print(f"  📊 信息增量：{len(cf_stats_expanded.nodes) - len(cf_func.nodes)}个额外节点")

        # 分析额外节点的来源
        func_nodes = set(cf_func.nodes)
        expanded_nodes = set(cf_stats_expanded.nodes)
        extra_nodes = expanded_nodes - func_nodes
        if extra_nodes:
            print(f"  🆕 额外节点：{list(extra_nodes)}")
            print("  💡 这些节点代表了数据的完整生成路径")

        cf_multi_expanded = self.storage.cf([raw_data, processed_data, stats]).expand_back(recursive=True)
        self.print_cf_info(cf_multi_expanded, "从多个引用扩展后的CF")
        
        self.phase_results[1] = {
            'cf_stats': cf_stats,
            'cf_multi': cf_multi,
            'cf_func': cf_func,
            'refs': {'raw_data': raw_data, 'processed_data': processed_data, 'stats': stats}
        }
        
        return cf_multi
    
    def demonstrate_cf_expansion(self, cf: ComputationFrame):
        """阶段2: 演示ComputationFrame的扩展和连接"""
        self.print_phase_header(2, "ComputationFrame扩展与连接",
                               "展示如何扩展计算图以包含更多的计算历史")

        print("📚 ComputationFrame扩展概念详解:")
        print("=" * 80)
        print("🔍 核心概念：")
        print("  • ComputationFrame默认只包含指定的引用节点（最小CF）")
        print("  • 扩展操作用于包含更多的计算历史和依赖关系")
        print("  • 不同扩展方向揭示不同的计算依赖模式")
        print("")
        print("🎯 扩展方向说明：")
        print("  📈 expand_back：向后扩展，包含生成当前结果的所有计算步骤")
        print("     - 追溯数据来源和计算历史")
        print("     - 回答'这个结果是怎么来的？'")
        print("     - 用于：调试问题、理解数据来源、依赖分析")
        print("  📉 expand_forward：向前扩展，包含使用当前结果的所有计算步骤")
        print("     - 追踪数据去向和后续使用")
        print("     - 回答'这个结果被用来做什么？'")
        print("     - 用于：影响分析、变更评估、下游影响")
        print("  🔄 expand_all：全方向扩展，包含所有相关的计算步骤")
        print("     - 完整的计算上下文")
        print("     - 回答'完整的计算图是什么样的？'")
        print("     - 用于：完整视图、系统理解、全局优化")
        print("=" * 80)

        print("\n🔧 扩展操作详细演示:")

        # 0. 显示原始CF状态
        print("\n0️⃣ 原始CF状态分析:")
        print("💡 这是最小CF，只包含指定的引用节点，不包含计算历史")
        self.print_cf_info(cf, "原始CF（最小CF）")

        original_nodes = len(cf.nodes)
        original_funcs = len([n for n in cf.nodes if n in cf.fnames])
        original_vars = len([n for n in cf.nodes if n in cf.vnames])

        print(f"📊 原始CF特征分析:")
        print(f"  🔢 总节点数: {original_nodes}")
        print(f"  ⚙️  函数节点: {original_funcs} 个")
        print(f"  📦 变量节点: {original_vars} 个")
        print(f"  💡 信息量: 最小（只有起始点，无历史）")

        # 1. 向后扩展
        print("\n1️⃣ 向后扩展 (expand_back) 详细分析:")
        print("💡 作用：向后追溯，找到生成当前结果的所有上游计算")
        print("🎯 目标：回答'这个数据是怎么来的？'")

        cf_back = cf.expand_back(recursive=True)
        self.print_cf_info(cf_back, "向后扩展后的CF")

        back_nodes = len(cf_back.nodes)
        back_funcs = len([n for n in cf_back.nodes if n in cf_back.fnames])
        back_vars = len([n for n in cf_back.nodes if n in cf_back.vnames])

        print(f"📊 向后扩展效果分析:")
        print(f"  🔢 节点增长: {original_nodes} → {back_nodes} (+{back_nodes - original_nodes})")
        print(f"  ⚙️  函数发现: {original_funcs} → {back_funcs} (+{back_funcs - original_funcs})")
        print(f"  📦 变量发现: {original_vars} → {back_vars} (+{back_vars - original_vars})")
        print(f"  💡 信息增益: 发现了完整的数据生成路径")
        print(f"  🔍 实际意义: 可以追踪到最初的输入参数和数据源")

        # 2. 向前扩展
        print("\n2️⃣ 向前扩展 (expand_forward) 详细分析:")
        print("💡 作用：向前追踪，找到使用当前结果的所有下游计算")
        print("🎯 目标：回答'这个数据被用来做什么？'")

        cf_forward = cf.expand_forward(recursive=True)
        self.print_cf_info(cf_forward, "向前扩展后的CF")

        forward_nodes = len(cf_forward.nodes)
        forward_funcs = len([n for n in cf_forward.nodes if n in cf_forward.fnames])
        forward_vars = len([n for n in cf_forward.nodes if n in cf_forward.vnames])

        print(f"📊 向前扩展效果分析:")
        print(f"  🔢 节点增长: {original_nodes} → {forward_nodes} (+{forward_nodes - original_nodes})")
        print(f"  ⚙️  函数发现: {original_funcs} → {forward_funcs} (+{forward_funcs - original_funcs})")
        print(f"  📦 变量发现: {original_vars} → {forward_vars} (+{forward_vars - original_vars})")

        if forward_nodes == original_nodes:
            print(f"  💡 信息增益: 无（当前节点是终端节点，没有下游使用者）")
            print(f"  🔍 实际意义: 确认这是计算链的终点")
        else:
            print(f"  💡 信息增益: 发现了数据的后续使用路径")
            print(f"  🔍 实际意义: 可以评估修改当前数据的影响范围")

        # 3. 全方向扩展
        print("\n3️⃣ 全方向扩展 (expand_all) 详细分析:")
        print("💡 作用：双向扩展，获取完整的计算上下文")
        print("🎯 目标：回答'完整的计算生态系统是什么样的？'")

        cf_all = cf.expand_all()
        self.print_cf_info(cf_all, "全方向扩展后的CF")

        all_nodes = len(cf_all.nodes)
        all_funcs = len([n for n in cf_all.nodes if n in cf_all.fnames])
        all_vars = len([n for n in cf_all.nodes if n in cf_all.vnames])

        print(f"📊 全方向扩展效果分析:")
        print(f"  🔢 节点增长: {original_nodes} → {all_nodes} (+{all_nodes - original_nodes})")
        print(f"  ⚙️  函数发现: {original_funcs} → {all_funcs} (+{all_funcs - original_funcs})")
        print(f"  📦 变量发现: {original_vars} → {all_vars} (+{all_vars - original_vars})")
        print(f"  💡 信息增益: 获得了完整的计算上下文")
        print(f"  🔍 实际意义: 可以进行系统性分析和全局优化")

        # 数学关系验证
        print("\n🔢 扩展操作的数学关系验证:")
        print(f"  📊 expand_back节点数: {back_nodes}")
        print(f"  📊 expand_forward节点数: {forward_nodes}")
        print(f"  📊 expand_all节点数: {all_nodes}")

        # 计算重叠
        back_set = set(cf_back.nodes)
        forward_set = set(cf_forward.nodes)
        all_set = set(cf_all.nodes)
        overlap = back_set & forward_set
        union_size = len(back_set | forward_set)

        print(f"  🔗 back ∩ forward重叠: {len(overlap)} 个节点")
        print(f"  🔗 back ∪ forward并集: {union_size} 个节点")
        print(f"  ✅ 数学验证: expand_all = expand_back ∪ expand_forward")
        print(f"     {all_nodes} = {back_nodes} ∪ {forward_nodes} (理论并集: {union_size})")

        if all_nodes == back_nodes:
            print("  ⚠️  特殊情况：全方向扩展 = 向后扩展")
            print("  📝 原因：当前节点是终端节点，没有下游使用者")
            print("  💡 这是正确的！expand_all = expand_back ∪ ∅ = expand_back")
        elif all_nodes == union_size:
            print("  ✅ 标准情况：全方向扩展 = 向后扩展 ∪ 向前扩展")
        else:
            print(f"  🔍 复杂情况：存在额外的连接关系")

        # 分析为什么expand_all与expand_back相同
        print("\n🔍 扩展结果深度分析:")
        if len(cf_all.nodes) == len(cf_back.nodes):
            print("  ⚠️  观察：全方向扩展与向后扩展结果相同")
            print("  📝 根本原因：当前CF的起始节点是计算链的终端节点")
            print("  🔍 详细解释：")
            print("    • 向后扩展：发现了完整的数据生成历史")
            print("    • 向前扩展：没有发现下游使用者（终端节点特性）")
            print("    • 全方向扩展：back ∪ forward = back ∪ ∅ = back")
            print("  💡 这是完全正确的行为！")
            print("  🎯 实际意义：确认了数据流的终点位置")
        else:
            print("  ✅ 全方向扩展包含了额外的下游计算")
            extra_nodes = all_nodes - back_nodes
            print(f"    🆕 额外发现: {extra_nodes} 个下游节点")
            print("  💡 这表明当前节点不是终端节点，有后续使用")

        # 4. 上游下游分析
        print("\n4️⃣ 上游下游分析:")
        print("📚 概念详解：")
        print("  🔼 upstream：获取指定节点的所有上游依赖")
        print("  🔽 downstream：获取指定节点的所有下游使用")
        print("  🎯 意义：精确分析特定节点的影响范围")

        if cf_all.fnames:
            sample_func = list(cf_all.fnames)[0]
            print(f"\n  🎯 分析函数: {sample_func}")

            try:
                upstream_cf = cf_all.upstream(sample_func)
                self.print_cf_info(upstream_cf, f"{sample_func}的上游CF")
                print(f"  📈 上游分析：{sample_func}依赖的所有计算步骤")

                # 详细分析上游CF
                print("  💡 上游CF详细分析：")
                print(f"    📊 包含{len([n for n in upstream_cf.nodes if n in upstream_cf.fnames])}个函数，{len([n for n in upstream_cf.nodes if n in upstream_cf.vnames])}个变量")
                print(f"    🔗 表示生成{sample_func}输入所需的完整计算链")
                print(f"    🎯 用途：理解{sample_func}的数据依赖和前置条件")

            except Exception as e:
                print(f"  ❌ 上游分析失败: {e}")

            try:
                downstream_cf = cf_all.downstream(sample_func)
                self.print_cf_info(downstream_cf, f"{sample_func}的下游CF")
                print(f"  📉 下游分析：使用{sample_func}结果的所有计算步骤")

                # 详细分析下游CF
                print("  💡 下游CF详细分析：")
                print(f"    📊 包含{len([n for n in downstream_cf.nodes if n in downstream_cf.fnames])}个函数，{len([n for n in downstream_cf.nodes if n in downstream_cf.vnames])}个变量")
                print(f"    🔗 表示使用{sample_func}输出的所有计算路径")
                print(f"    🎯 用途：评估修改{sample_func}的影响范围")

                # 分析上游下游的关系
                print("\n  🔍 上游下游关系分析：")
                upstream_nodes = len(upstream_cf.nodes)
                downstream_nodes = len(downstream_cf.nodes)
                total_nodes = len(cf_all.nodes)

                print(f"    📈 上游节点数：{upstream_nodes}")
                print(f"    📉 下游节点数：{downstream_nodes}")
                print(f"    🔄 完整CF节点数：{total_nodes}")

                # 验证数学关系
                overlap_estimate = upstream_nodes + downstream_nodes - total_nodes
                if overlap_estimate > 0:
                    print(f"    🔗 估计重叠节点：{overlap_estimate}个")
                    print(f"    💡 关系验证：upstream ∪ downstream = 完整CF（考虑重叠）")

            except Exception as e:
                print(f"  ❌ 下游分析失败: {e}")

        # 5. 演示有下游计算的情况
        print("\n5️⃣ 演示有下游计算的扩展差异:")
        print("💡 为了展示expand_all与expand_back的区别，我们创建有下游使用的计算")

        # 定义额外的下游计算
        @op
        def analyze_stats(stats_data: dict):
            """分析统计数据"""
            print(f"    🔄 分析统计数据")
            return {
                'is_normalized': stats_data.get('max', 0) <= 1.0,
                'data_quality': 'good' if stats_data.get('count', 0) > 5 else 'poor',
                'summary': f"数据包含{stats_data.get('count', 0)}个元素"
            }

        @op
        def generate_report(stats_data: dict, analysis_result: dict):
            """生成报告"""
            print(f"    🔄 生成报告")
            return {
                'stats': stats_data,
                'analysis': analysis_result,
                'timestamp': '2024-01-01',
                'report_type': 'data_analysis'
            }

        # 执行下游计算
        with self.storage:
            # 获取之前的stats结果
            refs = self.phase_results[1]['refs']
            stats_ref = refs['stats']

            # 执行下游计算
            analysis = analyze_stats(self.storage.unwrap(stats_ref))
            report = generate_report(self.storage.unwrap(stats_ref),
                                   self.storage.unwrap(analysis))

        # 从中间节点创建CF来演示差异
        print("\n📊 从中间节点(processed_data)创建CF:")
        processed_data_ref = refs['processed_data']
        cf_middle = self.storage.cf(processed_data_ref)

        print("  🔍 中间节点的扩展对比:")
        cf_middle_back = cf_middle.expand_back(recursive=True)
        cf_middle_forward = cf_middle.expand_forward(recursive=True)
        cf_middle_all = cf_middle.expand_all()

        print(f"    📈 向后扩展: {len(cf_middle_back.nodes)} 个节点")
        print(f"    📉 向前扩展: {len(cf_middle_forward.nodes)} 个节点")
        print(f"    🔄 全方向扩展: {len(cf_middle_all.nodes)} 个节点")

        # 详细分析扩展结果
        print("\n  💡 扩展结果详细分析:")

        # 向后扩展分析
        back_funcs = len([n for n in cf_middle_back.nodes if n in cf_middle_back.fnames])
        back_vars = len([n for n in cf_middle_back.nodes if n in cf_middle_back.vnames])
        print(f"    📈 向后扩展构成：{back_funcs}个函数 + {back_vars}个变量")
        print(f"      🔍 含义：生成processed_data的完整历史路径")
        print(f"      🎯 用途：理解数据来源，调试数据质量问题")

        # 向前扩展分析
        forward_funcs = len([n for n in cf_middle_forward.nodes if n in cf_middle_forward.fnames])
        forward_vars = len([n for n in cf_middle_forward.nodes if n in cf_middle_forward.vnames])
        print(f"    📉 向前扩展构成：{forward_funcs}个函数 + {forward_vars}个变量")
        print(f"      🔍 含义：使用processed_data的所有下游计算")
        print(f"      � 用途：评估修改processed_data的影响范围")

        # 全方向扩展分析
        all_funcs = len([n for n in cf_middle_all.nodes if n in cf_middle_all.fnames])
        all_vars = len([n for n in cf_middle_all.nodes if n in cf_middle_all.vnames])
        print(f"    🔄 全方向扩展构成：{all_funcs}个函数 + {all_vars}个变量")
        print(f"      🔍 含义：processed_data的完整计算生态系统")
        print(f"      🎯 用途：系统性理解和全局优化")

        # 数学关系验证
        print("\n  🔢 数学关系验证:")
        print(f"    📊 expand_back: {len(cf_middle_back.nodes)} 个节点")
        print(f"    📊 expand_forward: {len(cf_middle_forward.nodes)} 个节点")
        print(f"    📊 expand_all: {len(cf_middle_all.nodes)} 个节点")

        # 计算重叠节点
        back_nodes = set(cf_middle_back.nodes)
        forward_nodes = set(cf_middle_forward.nodes)
        all_nodes = set(cf_middle_all.nodes)

        overlap_nodes = back_nodes & forward_nodes
        union_size = len(back_nodes | forward_nodes)

        print(f"    🔗 重叠节点数: {len(overlap_nodes)}")
        print(f"    🔗 并集大小: {union_size}")
        print(f"    ✅ 验证: expand_all = expand_back ∪ expand_forward")
        print(f"       {len(all_nodes)} = {len(back_nodes)} ∪ {len(forward_nodes)} (重叠{len(overlap_nodes)}个)")

        if len(cf_middle_all.nodes) > len(cf_middle_back.nodes):
            print("  ✅ 现在可以看到expand_all包含了更多节点！")
            print(f"    🆕 额外节点数: {len(cf_middle_all.nodes) - len(cf_middle_back.nodes)}")

            # 显示额外的节点
            extra_nodes = all_nodes - back_nodes
            if extra_nodes:
                print(f"    🆕 额外节点: {list(extra_nodes)}")
                print(f"    💡 这些节点来自向前扩展，代表下游计算路径")

        # 6. 扩展策略对比
        print("\n6️⃣ 扩展策略对比总结:")
        print("📊 原始终端节点CF的节点数量对比：")
        print(f"  原始CF: {len(cf.nodes)} 个节点")
        print(f"  向后扩展: {len(cf_back.nodes)} 个节点")
        print(f"  向前扩展: {len(cf_forward.nodes)} 个节点")
        print(f"  全方向扩展: {len(cf_all.nodes)} 个节点")

        print("\n📊 中间节点CF的节点数量对比：")
        print(f"  中间节点原始CF: {len(cf_middle.nodes)} 个节点")
        print(f"  中间节点向后扩展: {len(cf_middle_back.nodes)} 个节点")
        print(f"  中间节点向前扩展: {len(cf_middle_forward.nodes)} 个节点")
        print(f"  中间节点全方向扩展: {len(cf_middle_all.nodes)} 个节点")

        print("\n🎯 使用场景建议：")
        print("  📈 expand_back：调试问题、理解数据来源、依赖分析")
        print("  📉 expand_forward：影响分析、变更评估、下游影响")
        print("  🔄 expand_all：完整视图、系统理解、全局优化")

        print("\n💡 关键洞察：")
        print("  • 从终端节点创建的CF：expand_all ≈ expand_back（因为没有下游）")
        print("  • 从中间节点创建的CF：expand_all = expand_back ∪ expand_forward")
        print("  • 选择合适的起始节点和扩展策略对分析很重要")

        self.phase_results[2] = {
            'cf_back': cf_back,
            'cf_forward': cf_forward,
            'cf_all': cf_all,
            'cf_middle': cf_middle if 'cf_middle' in locals() else None,
            'cf_middle_back': cf_middle_back if 'cf_middle_back' in locals() else None,
            'cf_middle_forward': cf_middle_forward if 'cf_middle_forward' in locals() else None,
            'cf_middle_all': cf_middle_all if 'cf_middle_all' in locals() else None,
            'downstream_results': [analysis, report] if 'analysis' in locals() and 'report' in locals() else []
        }

        return cf_all
    
    def demonstrate_topology_analysis(self, cf: ComputationFrame):
        """阶段3: 演示拓扑排序和图分析"""
        self.print_phase_header(3, "拓扑排序与图分析",
                               "展示计算图的拓扑结构分析和遍历方法")

        print("📚 拓扑排序与图分析概念详解:")
        print("=" * 60)
        print("� 拓扑排序核心概念：")
        print("  • 拓扑排序：对有向无环图(DAG)的节点进行线性排序")
        print("  • 排序规则：如果存在边A→B，则A在排序中出现在B之前")
        print("  • 计算意义：确定计算的执行顺序，保证依赖关系正确")
        print("  • SCC处理：强连通分量(Strongly Connected Components)的特殊处理")
        print("")
        print("🎯 图分析的重要性：")
        print("  📊 依赖分析：理解数据和计算的依赖关系")
        print("  🔄 执行顺序：确定正确的计算执行顺序")
        print("  🐛 调试支持：追踪问题的根源和影响范围")
        print("  ⚡ 优化机会：识别并行计算和缓存机会")
        print("=" * 60)

        print("\n🔧 拓扑分析演示:")

        # 1. 拓扑排序
        print("\n1️⃣ 拓扑排序 (topsort_modulo_sccs):")
        print("💡 概念：将计算图中的节点按依赖关系排序")
        print("🎯 意义：确定计算的正确执行顺序")
        try:
            topo_order = cf.topsort_modulo_sccs()
            print(f"  📋 拓扑顺序: {topo_order[:10]}{'...' if len(topo_order) > 10 else ''}")
            print(f"  🔢 节点总数: {len(topo_order)}")

            # 分析拓扑顺序的含义
            print("  📊 拓扑顺序分析：")
            func_nodes = [node for node in topo_order if node in cf.fnames]
            var_nodes = [node for node in topo_order if node in cf.vnames]
            print(f"    ⚙️  函数节点: {len(func_nodes)} 个")
            print(f"    📦 变量节点: {len(var_nodes)} 个")

            if func_nodes:
                print(f"    🔄 函数执行顺序: {func_nodes}")
                print("    💡 解释：按此顺序执行函数可保证依赖关系正确")

        except Exception as e:
            print(f"  ❌ 拓扑排序失败: {e}")

        # 2. 邻居关系分析
        print("\n2️⃣ 邻居关系分析:")
        print("💡 概念：分析节点间的直接连接关系")
        print("🎯 意义：理解数据流和依赖的局部结构")
        if cf.nodes:
            sample_nodes = list(cf.nodes)[:3]
            for node in sample_nodes:
                print(f"\n  🎯 节点: {node}")
                node_type = "函数" if node in cf.fnames else "变量"
                print(f"    🏷️  类型: {node_type}")

                try:
                    in_neighbors = list(cf.in_neighbors(node))
                    out_neighbors = list(cf.out_neighbors(node))
                    print(f"    ⬅️  输入邻居: {in_neighbors[:3]}{'...' if len(in_neighbors) > 3 else ''}")
                    print(f"    ➡️  输出邻居: {out_neighbors[:3]}{'...' if len(out_neighbors) > 3 else ''}")

                    # 分析邻居关系的含义
                    if node in cf.fnames:
                        print(f"    📊 函数分析：")
                        print(f"      📥 输入数据源: {len(in_neighbors)} 个")
                        print(f"      📤 输出数据用户: {len(out_neighbors)} 个")
                    else:
                        print(f"    📊 变量分析：")
                        producers = [n for n in in_neighbors if n in cf.fnames]
                        consumers = [n for n in out_neighbors if n in cf.fnames]
                        print(f"      🏭 生产者函数: {len(producers)} 个")
                        print(f"      🍽️  消费者函数: {len(consumers)} 个")

                except Exception as e:
                    print(f"    ❌ 邻居分析失败: {e}")

        # 3. 边关系分析
        print("\n3️⃣ 边关系分析:")
        print("💡 概念：分析图中所有的连接关系")
        print("🎯 意义：理解完整的数据流和依赖网络")
        edges = list(cf.edges())
        print(f"  🔗 边总数: {len(edges)}")

        if edges:
            print("  📋 边示例:")
            func_to_var = 0
            var_to_func = 0

            for i, (src, dst, label) in enumerate(edges[:5]):
                print(f"    {i+1}. {src} --[{label}]--> {dst}")

                # 统计边的类型
                if src in cf.fnames and dst in cf.vnames:
                    func_to_var += 1
                elif src in cf.vnames and dst in cf.fnames:
                    var_to_func += 1

            # 统计所有边的类型
            for src, dst, _ in edges:
                if src in cf.fnames and dst in cf.vnames:
                    func_to_var += 1
                elif src in cf.vnames and dst in cf.fnames:
                    var_to_func += 1

            print(f"  📊 边类型统计:")
            print(f"    ⚙️➡️📦 函数→变量: {func_to_var} 条（数据生产）")
            print(f"    📦➡️⚙️ 变量→函数: {var_to_func} 条（数据消费）")

        # 4. 函数调用分析
        print("\n4️⃣ 函数调用分析:")
        print("💡 概念：分析函数的实际调用情况")
        print("🎯 意义：理解计算的执行历史和频率")
        try:
            calls_by_func = cf.calls_by_func()
            print(f"  ⚙️  函数调用映射:")
            total_calls = 0

            for fname, calls in calls_by_func.items():
                call_count = len(calls)
                total_calls += call_count
                print(f"    {fname}: {call_count} 次调用")

                if calls:
                    sample_call = next(iter(calls))
                    print(f"      📋 示例调用ID: {sample_call.hid[:8]}...")
                    print(f"      🔧 函数对象: {sample_call.op.name}")

            print(f"  📊 调用统计总结:")
            print(f"    🔢 总调用次数: {total_calls}")
            print(f"    ⚙️  活跃函数数: {len(calls_by_func)}")
            print(f"    📈 平均每函数调用: {total_calls/len(calls_by_func):.1f} 次" if calls_by_func else "    📈 平均每函数调用: 0 次")

        except Exception as e:
            print(f"  ❌ 函数调用分析失败: {e}")

        # 5. 图结构特征分析
        print("\n5️⃣ 图结构特征分析:")
        print("💡 概念：分析计算图的整体结构特征")
        print("🎯 意义：理解计算的复杂度和特征")

        print(f"  📊 结构特征:")
        print(f"    🔢 节点总数: {len(cf.nodes)}")
        print(f"    🔗 边总数: {len(edges)}")
        print(f"    📊 平均度数: {len(edges)*2/len(cf.nodes):.2f}" if cf.nodes else "    📊 平均度数: 0")
        print(f"    🌱 源节点数: {len(cf.sources)}")
        print(f"    🎯 汇节点数: {len(cf.sinks)}")

        # 计算图的深度（最长路径）
        if 'topo_order' in locals() and cf.fnames:
            func_positions = {node: i for i, node in enumerate(topo_order) if node in cf.fnames}
            if func_positions:
                max_depth = max(func_positions.values()) - min(func_positions.values()) + 1
                print(f"    📏 计算深度: {max_depth} 层")
                print("    💡 解释：从输入到输出需要的最大计算步骤数")

        self.phase_results[3] = {
            'topo_order': topo_order if 'topo_order' in locals() else [],
            'edges_count': len(edges),
            'calls_by_func': calls_by_func if 'calls_by_func' in locals() else {}
        }

        return cf

    def demonstrate_node_operations(self, cf: ComputationFrame):
        """阶段4: 演示节点的增删查改操作"""
        self.print_phase_header(4, "节点增删查改操作",
                               "展示如何对ComputationFrame中的节点进行各种操作")

        print("🔧 节点操作演示:")
        print("💡 说明：ComputationFrame的节点操作主要通过图的合并、选择和扩展来实现")

        # 1. 复制CF进行安全实验
        print("\n1️⃣ 复制ComputationFrame进行实验:")
        cf_copy = cf.copy()
        self.print_cf_info(cf_copy, "复制的CF")

        # 2. 查找节点操作
        print("\n2️⃣ 查找节点操作:")
        print("  🔍 查找变量节点:")
        for vname in list(cf.vnames)[:3]:  # 只显示前3个
            refs = cf.vs.get(vname, set())
            print(f"    变量 {vname}: {len(refs)} 个引用")
            if refs:
                sample_ref = next(iter(refs))
                print(f"      示例引用ID: {sample_ref[:8]}...")

        print("  🔍 查找函数节点:")
        for fname in list(cf.fnames)[:3]:  # 只显示前3个
            calls = cf.fs.get(fname, set())
            print(f"    函数 {fname}: {len(calls)} 次调用")
            if calls:
                sample_call = next(iter(calls))
                print(f"      示例调用ID: {sample_call[:8]}...")

        # 3. 节点邻居查询
        print("\n3️⃣ 节点邻居查询:")
        if cf.nodes:
            sample_node = list(cf.nodes)[0]
            print(f"  🎯 分析节点: {sample_node}")

            try:
                in_neighbors = list(cf.in_neighbors(sample_node))
                out_neighbors = list(cf.out_neighbors(sample_node))
                print(f"    ⬅️  输入邻居: {in_neighbors[:3]}{'...' if len(in_neighbors) > 3 else ''}")
                print(f"    ➡️  输出邻居: {out_neighbors[:3]}{'...' if len(out_neighbors) > 3 else ''}")

                # 分析邻居关系
                if sample_node in cf.fnames:
                    print(f"    📊 函数节点分析：")
                    print(f"      📥 输入数据源: {len(in_neighbors)} 个")
                    print(f"      📤 输出数据用户: {len(out_neighbors)} 个")
                else:
                    print(f"    📊 变量节点分析：")
                    producers = [n for n in in_neighbors if n in cf.fnames]
                    consumers = [n for n in out_neighbors if n in cf.fnames]
                    print(f"      🏭 生产者函数: {len(producers)} 个")
                    print(f"      🍽️  消费者函数: {len(consumers)} 个")

            except Exception as e:
                print(f"    ❌ 邻居分析失败: {e}")

        # 4. 增加节点操作（通过创建新计算）
        print("\n4️⃣ 增加节点操作:")
        print("  💡 通过执行新计算来增加节点到图中")

        # 创建新的计算函数
        @op
        def new_computation(data: list, multiplier: float = 1.5):
            """新增的计算函数"""
            print(f"    🆕 执行新计算: 数据长度={len(data)}, 乘数={multiplier}")
            return [x * multiplier for x in data]

        @op
        def derived_analysis(original_stats: dict, new_data: list):
            """基于新数据的派生分析"""
            print(f"    🔄 执行派生分析")
            return {
                'original_count': original_stats.get('count', 0),
                'new_count': len(new_data),
                'ratio': len(new_data) / max(original_stats.get('count', 1), 1)
            }

        # 执行新计算
        with self.storage:
            # 获取现有数据
            refs = self.phase_results[1]['refs']
            processed_data = refs['processed_data']
            stats = refs['stats']

            # 执行新计算
            new_result = new_computation(self.storage.unwrap(processed_data), 1.5)
            derived_result = derived_analysis(self.storage.unwrap(stats),
                                            self.storage.unwrap(new_result))

        # 正确的增加节点方法：合并原始CF和新计算的CF
        new_computation_cf = self.storage.cf(derived_result).expand_back(recursive=True)
        cf_with_new_nodes = cf | new_computation_cf  # 使用并集操作合并

        print(f"  📊 原始CF节点数: {len(cf.nodes)}")
        print(f"  📊 新计算CF节点数: {len(new_computation_cf.nodes)}")
        print(f"  📊 合并后CF节点数: {len(cf_with_new_nodes.nodes)}")
        print(f"  ➕ 实际新增节点数: {len(cf_with_new_nodes.nodes) - len(cf.nodes)}")

        # 显示新增的节点
        original_nodes = set(cf.nodes)
        new_nodes = set(cf_with_new_nodes.nodes) - original_nodes
        if new_nodes:
            print(f"  🆕 新增的节点: {list(new_nodes)}")
        else:
            print("  ℹ️  没有新增节点（可能存在重叠）")

        # 5. 修改节点操作（删除后重新添加）
        print("\n5️⃣ 修改节点操作:")
        print("  💡 通过删除旧节点并添加新节点来实现修改")

        # 选择要修改的节点
        if cf_copy.vnames:
            target_node = list(cf_copy.vnames)[0]
            print(f"  🎯 目标节点: {target_node}")

            # 步骤1：删除节点
            cf_without_target = cf_copy.drop_node(target_node, inplace=False)
            print(f"    🗑️  删除后节点数: {len(cf_without_target.nodes)}")

            # 步骤2：正确的修改方法 - 保持原有节点顺序并添加新节点
            # 首先保留原始CF的所有其他节点，然后添加新节点
            cf_modified = cf_without_target | new_computation_cf
            print(f"    ➕ 合并后节点数: {len(cf_modified.nodes)}")

            # 验证节点保留情况
            original_nodes_except_target = set(cf_copy.nodes) - {target_node}
            modified_nodes = set(cf_modified.nodes)
            preserved_nodes = original_nodes_except_target & modified_nodes
            new_added_nodes = modified_nodes - original_nodes_except_target

            print(f"    ✅ 保留原节点数: {len(preserved_nodes)}")
            print(f"    🆕 新增节点数: {len(new_added_nodes)}")
            if new_added_nodes:
                print(f"    🆕 新增节点: {list(new_added_nodes)}")

            self.print_cf_info(cf_modified, "修改后的CF")

        # 6. 删除节点操作
        print("\n6️⃣ 删除节点操作:")
        if cf_copy.vnames:
            # 删除一个变量节点
            var_to_delete = list(cf_copy.vnames)[0]
            print(f"  🗑️  删除变量节点: {var_to_delete}")
            cf_deleted = cf_copy.drop_node(var_to_delete, inplace=False)
            self.print_cf_info(cf_deleted, "删除节点后的CF")

        # 7. 批量删除操作
        print("\n7️⃣ 批量删除操作:")
        if len(cf_copy.vnames) > 2:
            nodes_to_delete = list(cf_copy.vnames)[:2]
            print(f"  🗑️  批量删除节点: {nodes_to_delete}")
            cf_batch_deleted = cf_copy.drop(nodes_to_delete, inplace=False)
            self.print_cf_info(cf_batch_deleted, "批量删除后的CF")

        # 8. 清理操作
        print("\n8️⃣ 清理操作:")
        cf_cleaned = cf_copy.cleanup(inplace=False)
        self.print_cf_info(cf_cleaned, "清理后的CF")

        # 9. 节点选择操作
        print("\n9️⃣ 节点选择操作:")
        if len(cf.nodes) > 3:
            selected_nodes = list(cf.nodes)[:3]
            print(f"  🎯 选择节点: {selected_nodes}")
            cf_selected = cf.select_nodes(selected_nodes)
            self.print_cf_info(cf_selected, "选择节点后的CF")

        self.phase_results[4] = {
            'cf_copy': cf_copy,
            'cf_with_new_nodes': cf_with_new_nodes,
            'new_computation_cf': new_computation_cf,
            'cf_modified': cf_modified if 'cf_modified' in locals() else None,
            'cf_deleted': cf_deleted if 'cf_deleted' in locals() else None,
            'cf_cleaned': cf_cleaned if 'cf_cleaned' in locals() else None,
            'new_results': [new_result, derived_result]
        }

        return cf_copy

    def demonstrate_cf_merging(self, cf: ComputationFrame):
        """阶段5: 演示ComputationFrame的合并操作"""
        self.print_phase_header(5, "ComputationFrame合并操作",
                               "展示如何合并多个ComputationFrame")

        # 创建新的计算来生成另一个CF
        @op
        def additional_compute(data: list, factor: float = 2.0):
            """额外的计算函数"""
            print(f"    🔄 执行额外计算，因子: {factor}")
            time.sleep(0.1)
            return [x * factor for x in data]

        @op
        def final_analysis(processed: list, stats: dict):
            """最终分析"""
            print(f"    🔄 执行最终分析")
            time.sleep(0.1)
            return {
                'processed_count': len(processed),
                'original_stats': stats,
                'analysis_complete': True
            }

        with self.storage:
            # 获取之前的结果
            refs = self.phase_results[1]['refs']
            processed_data = refs['processed_data']
            stats = refs['stats']

            # 执行新的计算
            additional_result = additional_compute(self.storage.unwrap(processed_data))
            final_result = final_analysis(self.storage.unwrap(additional_result),
                                        self.storage.unwrap(stats))

        # 创建新的CF
        cf_new = self.storage.cf(final_result).expand_back(recursive=True)

        print("🔧 合并操作演示:")

        # 1. 并集操作
        print("\n1️⃣ 并集操作 (|):")
        cf_union = cf | cf_new
        self.print_cf_info(cf_union, "并集CF")

        # 2. 交集操作
        print("\n2️⃣ 交集操作 (&):")
        cf_intersection = cf & cf_new
        self.print_cf_info(cf_intersection, "交集CF")

        # 3. 差集操作
        print("\n3️⃣ 差集操作 (-):")
        cf_difference = cf - cf_new
        self.print_cf_info(cf_difference, "差集CF")

        self.phase_results[5] = {
            'cf_new': cf_new,
            'cf_union': cf_union,
            'cf_intersection': cf_intersection,
            'cf_difference': cf_difference
        }

        return cf_union

    def demonstrate_data_extraction(self, cf: ComputationFrame):
        """阶段6: 演示数据提取和分析"""
        self.print_phase_header(6, "数据提取与分析",
                               "展示如何从ComputationFrame中提取和分析数据")

        print("🔧 数据提取演示:")

        # 1. 函数调用表
        print("\n1️⃣ 函数调用表:")
        for fname in cf.fnames:
            try:
                func_table = cf.get_func_table(fname)
                print(f"  ⚙️  函数 {fname}:")
                print(f"    📊 调用表形状: {func_table.shape}")
                if not func_table.empty:
                    print(f"    📋 列名: {list(func_table.columns)}")
                    print(f"    📝 前3行预览:")
                    print(func_table.head(3).to_string(index=False))
                else:
                    print("    📝 无调用记录")
            except Exception as e:
                print(f"    ❌ 获取失败: {e}")

        # 2. 变量引用映射
        print("\n2️⃣ 变量引用映射:")
        try:
            refs_by_var = cf.refs_by_var()
            print(f"  📦 变量引用映射:")
            for vname, refs in list(refs_by_var.items())[:5]:
                print(f"    {vname}: {len(refs)} 个引用")
                if refs:
                    sample_ref = next(iter(refs))
                    print(f"      📋 示例引用: {sample_ref.hid[:8]}...")
        except Exception as e:
            print(f"  ❌ 获取变量引用映射失败: {e}")

        # 3. 数据框转换
        print("\n3️⃣ 数据框转换:")
        try:
            if cf.vnames:
                sample_vars = list(cf.vnames)[:3]
                print(f"  📊 转换变量: {sample_vars}")
                df = cf.df(*sample_vars)
                print(f"  📋 数据框形状: {df.shape}")
                print(f"  📝 数据框预览:")
                print(df.head(3).to_string(index=False))
        except Exception as e:
            print(f"  ❌ 数据框转换失败: {e}")

        # 4. 统计信息
        print("\n4️⃣ 统计信息:")
        try:
            func_stats = cf.get_func_stats()
            var_stats = cf.get_var_stats()
            print(f"  ⚙️  函数统计: {func_stats}")
            print(f"  📦 变量统计: {var_stats}")
        except Exception as e:
            print(f"  ❌ 获取统计信息失败: {e}")

        self.phase_results[6] = {
            'func_tables': {},
            'refs_by_var': refs_by_var if 'refs_by_var' in locals() else {},
            'stats': {
                'func_stats': func_stats if 'func_stats' in locals() else {},
                'var_stats': var_stats if 'var_stats' in locals() else {}
            }
        }

        return cf


def main():
    """主函数：运行ComputationFrame深度理解演示"""
    print("🚀 ComputationFrame 深度理解用例")
    print("=" * 80)
    print("📖 本演示将展示ComputationFrame的完整生命周期和核心功能")
    print("🎯 目标：深入理解CF的创建、连接、拓扑排序、增删查改等操作")
    print("=" * 80)
    
    demo = CFDeepUnderstanding()
    
    try:
        # 阶段1: CF创建
        cf = demo.demonstrate_cf_creation()
        
        # 阶段2: CF扩展
        cf_expanded = demo.demonstrate_cf_expansion(cf)
        
        # 阶段3: 拓扑分析
        cf_analyzed = demo.demonstrate_topology_analysis(cf_expanded)

        # 阶段4: 节点操作
        cf_modified = demo.demonstrate_node_operations(cf_analyzed)

        # 阶段5: CF合并
        cf_merged = demo.demonstrate_cf_merging(cf_analyzed)

        # 阶段6: 数据提取
        demo.demonstrate_data_extraction(cf_merged)

        print("\n" + "=" * 80)
        print("🎉 ComputationFrame深度理解演示完成！")
        print("=" * 80)
        print("📊 演示总结:")
        for phase, results in demo.phase_results.items():
            print(f"  阶段{phase}: ✅ 完成")

        print("\n📈 完整生命周期覆盖:")
        print("  ✅ CF创建和初始化")
        print("  ✅ 计算图构建和连接")
        print("  ✅ 拓扑排序和图分析")
        print("  ✅ 节点增删查改操作")
        print("  ✅ 图的扩展和合并")
        print("  ✅ 数据提取和可视化")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
