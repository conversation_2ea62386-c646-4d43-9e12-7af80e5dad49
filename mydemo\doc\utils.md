# utils.py 文档

## 文件内容与作用总体说明

`utils.py` 文件是 mandala 框架的工具函数模块，提供了各种辅助功能，包括序列化/反序列化、哈希计算、数据结构操作、图算法、参数解析等。这些工具函数支撑着框架的核心功能，如数据存储、计算图操作、类型处理等。该文件还包含了用于处理 pandas DataFrame、numpy 数组等外部库对象的特殊逻辑。

## 文件内的所有变量的作用与说明

### 全局变量
- `Config`: 配置类，用于检查可选依赖的可用性
- `prettytable`: 可选导入的表格美化库

### 类型变量
- `TypeVar`: 用于泛型类型注解的类型变量

## 文件内的所有函数的作用与说明

### 数据展示函数

#### dataframe_to_prettytable(df: pd.DataFrame) -> str

**作用**: 将 pandas DataFrame 转换为美化的表格字符串

**输入参数**:
- `df`: pd.DataFrame，要转换的数据框

**内部调用的函数**：
- prettytable.PrettyTable: 创建美化表格对象
- table.add_row: 添加表格行
- df.to_string: DataFrame 的字符串表示（备用方案）

**输出参数**: str - 美化的表格字符串

**其他说明**：如果 prettytable 不可用，回退到 pandas 默认的字符串表示

### 序列化函数

#### serialize(obj: Any) -> bytes

**作用**: 将对象序列化为字节串

**输入参数**:
- `obj`: Any，要序列化的对象

**内部调用的函数**：
- io.BytesIO: 创建字节流缓冲区
- joblib.dump: 序列化对象

**输出参数**: bytes - 序列化后的字节串

**其他说明**：可能导致相等对象产生不同序列化结果（如集合顺序）

#### deserialize(value: bytes) -> Any

**作用**: 从字节串反序列化对象

**输入参数**:
- `value`: bytes，序列化的字节串

**内部调用的函数**：
- io.BytesIO: 创建字节流缓冲区
- joblib.load: 反序列化对象

**输出参数**: Any - 反序列化的对象

### 比较函数

#### _conservative_equality_check(safe_value: Any, unknown_value: Any) -> bool

**作用**: 保守的相等性检查，安全处理复杂对象的比较

**输入参数**:
- `safe_value`: Any，"安全"的简单类型值
- `unknown_value`: Any，未知类型的值

**内部调用的函数**：
- type: 获取对象类型
- np.array_equal: numpy 数组相等性检查
- pd.DataFrame.equals: pandas DataFrame 相等性检查

**输出参数**: bool - 是否相等

**其他说明**：特别处理 numpy 数组和 pandas DataFrame 的比较

### 哈希函数

#### get_content_hash(obj: Any) -> str

**作用**: 计算对象的内容哈希值，用于唯一标识

**输入参数**:
- `obj`: Any，要计算哈希的对象

**内部调用的函数**：
- obj.__get_mandala_dict__: 获取对象的 mandala 字典表示（如果存在）
- tensor_to_numpy: 转换 PyTorch 张量为 numpy 数组
- joblib.hash: 计算规范哈希

**输出参数**: str - 内容哈希字符串

**其他说明**：特别处理 PyTorch 张量和 pandas DataFrame

### 输出名称处理函数

#### dump_output_name(index: int, output_names: Optional[List[str]] = None) -> str

**作用**: 生成输出参数的名称

**输入参数**:
- `index`: int，输出索引
- `output_names`: Optional[List[str]]，自定义输出名称列表

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 输出名称

**其他说明**：如果提供了自定义名称则使用，否则生成默认名称

#### parse_output_name(name: str) -> int

**作用**: 从输出名称解析索引

**输入参数**:
- `name`: str，输出名称

**内部调用的函数**：
- str.split: 分割字符串
- int: 转换为整数

**输出参数**: int - 解析的索引

### 集合操作函数

#### get_setdict_union(a: Dict[str, Set[str]], b: Dict[str, Set[str]]) -> Dict[str, Set[str]]

**作用**: 计算两个集合字典的并集

**输入参数**:
- `a`: Dict[str, Set[str]]，第一个集合字典
- `b`: Dict[str, Set[str]]，第二个集合字典

**内部调用的函数**：
- set.union: 计算集合并集
- dict.get: 获取字典值

**输出参数**: Dict[str, Set[str]] - 并集结果

#### get_setdict_intersection(a: Dict[str, Set[str]], b: Dict[str, Set[str]]) -> Dict[str, Set[str]]

**作用**: 计算两个集合字典的交集

**输入参数**:
- `a`: Dict[str, Set[str]]，第一个集合字典
- `b`: Dict[str, Set[str]]，第二个集合字典

**内部调用的函数**：
- set.intersection: 计算集合交集

**输出参数**: Dict[str, Set[str]] - 交集结果

#### get_dict_union_over_keys(a: Dict[str, Any], b: Dict[str, Any]) -> Dict[str, Any]

**作用**: 在键的并集上合并两个字典

**输入参数**:
- `a`: Dict[str, Any]，第一个字典
- `b`: Dict[str, Any]，第二个字典

**内部调用的函数**：
- set.union: 计算键的并集

**输出参数**: Dict[str, Any] - 合并后的字典

#### get_dict_intersection_over_keys(a: Dict[str, Any], b: Dict[str, Any]) -> Dict[str, Any]

**作用**: 在键的交集上合并两个字典

**输入参数**:
- `a`: Dict[str, Any]，第一个字典
- `b`: Dict[str, Any]，第二个字典

**内部调用的函数**：
- set.intersection: 计算键的交集

**输出参数**: Dict[str, Any] - 合并后的字典

#### get_adjacency_union(a: Dict[str, Dict[str, Set[str]]], b: Dict[str, Dict[str, Set[str]]]) -> Dict[str, Dict[str, Set[str]]]

**作用**: 计算两个邻接表的并集

**输入参数**:
- `a`: Dict[str, Dict[str, Set[str]]]，第一个邻接表
- `b`: Dict[str, Dict[str, Set[str]]]，第二个邻接表

**内部调用的函数**：
- get_setdict_union: 计算集合字典并集

**输出参数**: Dict[str, Dict[str, Set[str]]] - 邻接表并集

#### get_adjacency_intersection(a: Dict[str, Dict[str, Set[str]]], b: Dict[str, Dict[str, Set[str]]]) -> Dict[str, Dict[str, Set[str]]]

**作用**: 计算两个邻接表的交集

**输入参数**:
- `a`: Dict[str, Dict[str, Set[str]]]，第一个邻接表
- `b`: Dict[str, Dict[str, Set[str]]]，第二个邻接表

**内部调用的函数**：
- get_setdict_intersection: 计算集合字典交集

**输出参数**: Dict[str, Dict[str, Set[str]]] - 邻接表交集

#### get_nullable_union(*sets: Set[str]) -> Set[str]

**作用**: 计算多个集合的并集，处理空集合情况

**输入参数**:
- `sets`: Set[str]，要合并的集合

**内部调用的函数**：
- set.union: 计算集合并集

**输出参数**: Set[str] - 并集结果

#### get_nullable_intersection(*sets: Set[str]) -> Set[str]

**作用**: 计算多个集合的交集，处理空集合情况

**输入参数**:
- `sets`: Set[str]，要求交集的集合

**内部调用的函数**：
- set.intersection: 计算集合交集

**输出参数**: Set[str] - 交集结果

### 参数处理函数

#### boundargs_to_args_kwargs(bound_args: inspect.BoundArguments) -> Tuple[Tuple[Any,...], Dict[str, Any]]

**作用**: 将 BoundArguments 对象转换为 args 和 kwargs

**输入参数**:
- `bound_args`: inspect.BoundArguments，绑定的参数对象

**内部调用的函数**：
- inspect.Parameter: 检查参数类型

**输出参数**: Tuple[Tuple[Any,...], Dict[str, Any]] - 位置参数和关键字参数

#### parse_returns(sig: inspect.Signature, returns: Any, nout: Union[Literal["auto", "var"], int], output_names: Optional[List[str]] = None) -> Tuple[Dict[str, Any], Dict[str, Any]]

**作用**: 解析函数返回值，生成输出字典和类型注解字典

**输入参数**:
- `sig`: inspect.Signature，函数签名
- `returns`: Any，返回值
- `nout`: Union[Literal["auto", "var"], int]，输出数量配置
- `output_names`: Optional[List[str]]，输出名称列表

**内部调用的函数**：
- dump_output_name: 生成输出名称
- inspect._empty: 检查空注解

**输出参数**: Tuple[Dict[str, Any], Dict[str, Any]] - 输出字典和类型注解字典

**其他说明**：处理单个返回值、元组返回值和可变数量返回值

### 图算法函数

#### almost_topological_sort(graph: Dict[str, Set[str]]) -> List[str]

**作用**: 对有向图进行近似拓扑排序，在强连通分量内部顺序任意

**输入参数**:
- `graph`: Dict[str, Set[str]]，图的邻接表表示

**内部调用的函数**：
- find_strongly_connected_components: 查找强连通分量
- create_super_graph: 创建超图
- topological_sort: 拓扑排序

**输出参数**: List[str] - 排序后的节点列表

**其他说明**：处理有环图的拓扑排序问题

#### get_adj_from_edges(edges: Set[Tuple[str, str, str]], node_support: Optional[Set[str]] = None) -> Tuple[Dict[str, Dict[str, Set[str]]], Dict[str, Dict[str, Set[str]]]]

**作用**: 从边集合构建邻接表

**输入参数**:
- `edges`: Set[Tuple[str, str, str]]，边的集合
- `node_support`: Optional[Set[str]]，节点支持集合

**内部调用的函数**：
- defaultdict: 创建默认字典

**输出参数**: Tuple[Dict[str, Dict[str, Set[str]]], Dict[str, Dict[str, Set[str]]]] - 输入和输出邻接表

#### unwrap_decorators(obj: Callable, strict: bool = True) -> Callable

**作用**: 解包装饰器，获取原始函数

**输入参数**:
- `obj`: Callable，被装饰的函数
- `strict`: bool，是否严格模式

**内部调用的函数**：
- hasattr: 检查属性
- getattr: 获取属性

**输出参数**: Callable - 原始函数

#### is_subdict(a: Dict, b: Dict) -> bool

**作用**: 检查字典 a 是否是字典 b 的子字典

**输入参数**:
- `a`: Dict，子字典候选
- `b`: Dict，父字典候选

**内部调用的函数**：
- all: 检查所有条件

**输出参数**: bool - 是否为子字典

#### invert_dict(d: Dict[_KT, _VT]) -> Dict[_VT, List[_KT]]

**作用**: 反转字典，将值作为键，原键作为值列表

**输入参数**:
- `d`: Dict[_KT, _VT]，要反转的字典

**内部调用的函数**：
- defaultdict: 创建默认字典

**输出参数**: Dict[_VT, List[_KT]] - 反转后的字典

#### find_strongly_connected_components(graph: Dict[str, Set[str]]) -> Tuple[Tuple[str,...],...]

**作用**: 使用 Tarjan 算法查找有向图的强连通分量

**输入参数**:
- `graph`: Dict[str, Set[str]]，图的邻接表表示

**内部调用的函数**：
- dfs: 深度优先搜索内部函数

**输出参数**: Tuple[Tuple[str,...],...] - 强连通分量的元组

**其他说明**：实现了 Tarjan 算法，用于检测图中的环

#### create_super_graph(graph: Dict[str, Set[str]], sccs: Tuple[Tuple[str,...],...]) -> Dict[str, Set[str]]

**作用**: 从强连通分量创建超图

**输入参数**:
- `graph`: Dict[str, Set[str]]，原图
- `sccs`: Tuple[Tuple[str,...],...] ，强连通分量

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Dict[str, Set[str]] - 超图的邻接表

#### topological_sort(graph: Dict[T, Set[T]]) -> List[T]

**作用**: 对有向无环图进行拓扑排序

**输入参数**:
- `graph`: Dict[T, Set[T]]，图的邻接表表示

**内部调用的函数**：
- dfs: 深度优先搜索内部函数

**输出参数**: List[T] - 拓扑排序后的节点列表

#### get_edges_in_paths(graph: Dict[str, Set[str]], start: str, end: str) -> Set[Tuple[str, str]]

**作用**: 获取从起始节点到结束节点的所有路径中的边

**输入参数**:
- `graph`: Dict[str, Set[str]]，图的邻接表
- `start`: str，起始节点
- `end`: str，结束节点

**内部调用的函数**：
- dfs: 深度优先搜索内部函数

**输出参数**: Set[Tuple[str, str]] - 路径中的边集合

#### ask_user(question: str, valid_options: List[str]) -> str

**作用**: 向用户询问问题并验证输入

**输入参数**:
- `question`: str，要询问的问题
- `valid_options`: List[str]，有效选项列表

**内部调用的函数**：
- input: 获取用户输入
- print: 输出信息

**输出参数**: str - 用户的有效选择

#### mock_input(prompts)

**作用**: 模拟用户输入，用于非交互式测试

**输入参数**:
- `prompts`: 预设的输入提示

**内部调用的函数**：
- iter: 创建迭代器
- next: 获取下一个值

**输出参数**: function - 模拟输入函数

**其他说明**：用于自动化测试，避免需要真实的用户交互
