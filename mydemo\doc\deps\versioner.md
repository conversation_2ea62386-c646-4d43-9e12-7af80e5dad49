# deps/versioner.py 文档

## 文件内容与作用总体说明

`deps/versioner.py` 文件是 mandala 框架依赖跟踪模块的版本管理核心，实现了代码状态跟踪、语义版本控制和依赖分析功能。该文件定义了 `CodeState` 和 `Versioner` 类，负责管理代码的不同版本、跟踪依赖关系变化、处理语义版本兼容性，以及提供版本历史查询和可视化功能。这是实现 mandala 记忆化功能中版本控制的关键组件。

## 文件内的所有变量的作用与说明

### 导入的模块和类型
- `typing`: 类型注解模块
- `OrderedDict`: 有序字典
- `textwrap`: 文本包装模块
- `is_subdict`: 字典子集检查函数
- `Config`: 配置类
- `DepKey`: 依赖键类型
- `hash_dict`: 字典哈希函数
- `Node`, `CallableNode`, `GlobalVarNode`, `TerminalNode`: 节点类型
- `DependencyGraph`: 依赖图类
- `crawl_static`: 静态爬取函数
- `TracerABC`: 跟踪器抽象基类
- `_get_colorized_diff`: 彩色差异显示函数
- `DAG`: 有向无环图类
- `Version`: 版本类

### CodeState 类的实例变量
- `nodes`: Dict[DepKey, Node]，依赖键到节点的映射，表示代码状态

### Versioner 类的实例变量
- `paths`: List[Path]，路径列表
- `TracerCls`: type，跟踪器类
- `strict`: bool，是否严格模式
- `skip_missing_deps`: bool，是否跳过缺失的依赖
- `skip_missing_silently`: bool，是否静默跳过缺失的依赖
- `track_globals`: bool，是否跟踪全局变量
- `skip_unhashable_globals`: bool，是否跳过不可哈希的全局变量
- `skip_globals_silently`: bool，是否静默跳过全局变量
- `allow_methods`: bool，是否允许方法
- `package_name`: Optional[str]，包名称
- `global_topology`: DependencyGraph，全局拓扑图
- `nodes`: Dict[DepKey, Node]，节点映射
- `component_dags`: Dict[DepKey, DAG]，组件有向无环图映射
- `versions`: Dict[DepKey, Dict[str, Version]]，版本映射
- `df`: pd.DataFrame，版本数据框

## 文件内的所有函数的作用与说明

### CodeState 类（代码状态）

#### __init__(self, nodes: Dict[DepKey, Node])

**作用**: 初始化代码状态，设置节点映射

**输入参数**:
- `nodes`: Dict[DepKey, Node]，依赖键到节点的映射

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### __repr__(self) -> str

**作用**: 返回代码状态的字符串表示

**输入参数**: 无

**内部调用的函数**：
- `node.content`: 获取节点内容

**输出参数**: str - 代码状态的字符串表示

#### get_content_version(self, support: Iterable[DepKey]) -> str

**作用**: 获取指定支持集合的内容版本哈希

**输入参数**:
- `support`: Iterable[DepKey]，支持的依赖键集合

**内部调用的函数**：
- `hash_dict`: 计算字典哈希

**输出参数**: str - 内容版本哈希

#### add_globals_from(self, graph: DependencyGraph)

**作用**: 从依赖图中添加全局变量节点

**输入参数**:
- `graph`: DependencyGraph，依赖图

**内部调用的函数**：
- `isinstance`: 检查对象类型

**输出参数**: 无返回值

**其他说明**：只添加不存在的全局变量节点

### Versioner 类（版本管理器）

#### __init__(self, paths: List[Path], TracerCls: type, strict: bool, track_globals: bool, skip_unhashable_globals: bool, skip_globals_silently: bool, skip_missing_deps: bool, skip_missing_silently: bool, track_methods: bool, package_name: Optional[str] = None)

**作用**: 初始化版本管理器，设置所有配置参数和数据结构

**输入参数**:
- `paths`: List[Path]，路径列表
- `TracerCls`: type，跟踪器类
- `strict`: bool，是否严格模式
- `track_globals`: bool，是否跟踪全局变量
- `skip_unhashable_globals`: bool，是否跳过不可哈希的全局变量
- `skip_globals_silently`: bool，是否静默跳过全局变量
- `skip_missing_deps`: bool，是否跳过缺失的依赖
- `skip_missing_silently`: bool，是否静默跳过缺失的依赖
- `track_methods`: bool，是否跟踪方法
- `package_name`: Optional[str]，包名称

**内部调用的函数**：
- `pd.DataFrame`: 创建数据框

**输出参数**: 无返回值

**其他说明**：初始化所有版本管理相关的数据结构

#### drop_semantic_version(self, semantic_version: str)

**作用**: 删除指定的语义版本

**输入参数**:
- `semantic_version`: str，要删除的语义版本

**内部调用的函数**：
- DataFrame 过滤操作

**输出参数**: 无返回值

**其他说明**：从数据框和版本字典中删除指定版本

#### get_version_ids(self, pre_call_uid: str, semantic_version: str, content_version: str) -> Tuple[str, str]

**作用**: 获取版本ID，包括语义版本和内容版本

**输入参数**:
- `pre_call_uid`: str，预调用唯一标识符
- `semantic_version`: str，语义版本
- `content_version`: str，内容版本

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Tuple[str, str] - 语义版本和内容版本的元组

#### update_global_topology(self, graph: DependencyGraph)

**作用**: 更新全局拓扑图，添加新的节点和边

**输入参数**:
- `graph`: DependencyGraph，依赖图

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `self.global_topology.add_node`: 添加节点
- `self.global_topology.add_edge`: 添加边

**输出参数**: 无返回值

#### make_tracer(self) -> TracerABC

**作用**: 创建跟踪器实例

**输入参数**: 无

**内部调用的函数**：
- `self.TracerCls`: 调用跟踪器构造函数

**输出参数**: TracerABC - 跟踪器实例

#### guess_code_state(self) -> CodeState

**作用**: 猜测当前的代码状态，通过静态分析和跟踪

**输入参数**: 无

**内部调用的函数**：
- `crawl_static`: 静态爬取
- `self.make_tracer`: 创建跟踪器
- `tracer.trace`: 执行跟踪
- `CodeState`: 创建代码状态

**输出参数**: CodeState - 猜测的代码状态

**其他说明**：结合静态分析和动态跟踪来确定代码状态

#### get_codestate_semantic_hashes(self, code_state: CodeState, support: Iterable[DepKey]) -> Dict[DepKey, str]

**作用**: 获取代码状态中指定支持集合的语义哈希

**输入参数**:
- `code_state`: CodeState，代码状态
- `support`: Iterable[DepKey]，支持的依赖键集合

**内部调用的函数**：
- `node.content_hash`: 获取节点内容哈希

**输出参数**: Dict[DepKey, str] - 依赖键到语义哈希的映射

#### apply_state_hypothesis(self, hypothesis: CodeState, trace_result: Dict[DepKey, Node])

**作用**: 应用状态假设，更新节点信息

**输入参数**:
- `hypothesis`: CodeState，状态假设
- `trace_result`: Dict[DepKey, Node]，跟踪结果

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `logger.warning`: 记录警告

**输出参数**: 无返回值

**其他说明**：处理节点不匹配的情况

#### get_semantic_version(self, semantic_hashes: Dict[DepKey, str], support: Iterable[DepKey]) -> str

**作用**: 根据语义哈希和支持集合计算语义版本

**输入参数**:
- `semantic_hashes`: Dict[DepKey, str]，语义哈希映射
- `support`: Iterable[DepKey]，支持集合

**内部调用的函数**：
- `hash_dict`: 计算字典哈希

**输出参数**: str - 语义版本哈希

#### init_component(self, component: DepKey, node: Node, initial_content: str)

**作用**: 初始化新组件，设置初始状态

**输入参数**:
- `component`: DepKey，组件依赖键
- `node`: Node，节点对象
- `initial_content`: str，初始内容

**内部调用的函数**：
- `DAG`: 创建有向无环图
- `Version`: 创建版本对象

**输出参数**: 无返回值

**其他说明**：为新组件创建DAG和初始版本

#### sync_codebase(self, code_state: CodeState)

**作用**: 同步代码库，更新所有已知组件的状态

**输入参数**:
- `code_state`: CodeState，代码状态

**内部调用的函数**：
- `self.sync_component`: 同步组件

**输出参数**: 无返回值

#### sync_component(self, component: DepKey, code_state: CodeState, require_exists: bool = False) -> str

**作用**: 同步单个组件的状态

**输入参数**:
- `component`: DepKey，组件依赖键
- `code_state`: CodeState，代码状态
- `require_exists`: bool，是否要求组件存在

**内部调用的函数**：
- `self.get_current_versions`: 获取当前版本
- `self.sync_version`: 同步版本

**输出参数**: str - 同步后的版本ID

#### get_current_versions(self, component: DepKey, code_state: CodeState) -> List[Version]

**作用**: 获取组件的当前版本列表

**输入参数**:
- `component`: DepKey，组件依赖键
- `code_state`: CodeState，代码状态

**内部调用的函数**：
- `node.content_hash`: 获取节点内容哈希

**输出参数**: List[Version] - 当前版本列表

#### get_semantically_compatible_versions(self, component: DepKey, code_state: CodeState) -> List[Version]

**作用**: 获取语义兼容的版本列表

**输入参数**:
- `component`: DepKey，组件依赖键
- `code_state`: CodeState，代码状态

**内部调用的函数**：
- `self.get_codestate_semantic_hashes`: 获取语义哈希
- `self.get_semantic_version`: 获取语义版本

**输出参数**: List[Version] - 语义兼容的版本列表

#### create_new_components_from_nodes(self, nodes: Dict[DepKey, Node])

**作用**: 从节点创建新组件

**输入参数**:
- `nodes`: Dict[DepKey, Node]，节点映射

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `self.init_component`: 初始化组件

**输出参数**: 无返回值

**其他说明**：只为可调用节点创建组件

#### sync_version(self, version: Version, require_exists: bool = False) -> Version

**作用**: 同步版本对象

**输入参数**:
- `version`: Version，版本对象
- `require_exists`: bool，是否要求存在

**内部调用的函数**：
- `version.sync`: 同步版本

**输出参数**: Version - 同步后的版本对象

#### lookup_call(self, component: DepKey, pre_call_uid: str, code_state: CodeState) -> Optional[Tuple[str, str]]

**作用**: 查找调用的版本信息

**输入参数**:
- `component`: DepKey，组件依赖键
- `pre_call_uid`: str，预调用唯一标识符
- `code_state`: CodeState，代码状态

**内部调用的函数**：
- `self.get_semantically_compatible_versions`: 获取兼容版本
- `is_subdict`: 检查字典子集

**输出参数**: Optional[Tuple[str, str]] - 语义版本和内容版本的元组，如果未找到则为 None

#### process_trace(self, graph: DependencyGraph, pre_call_uid: str, outputs: Dict[str, Any]) -> Tuple[str, str]

**作用**: 处理跟踪结果，更新版本信息

**输入参数**:
- `graph`: DependencyGraph，依赖图
- `pre_call_uid`: str，预调用唯一标识符
- `outputs`: Dict[str, Any]，输出字典

**内部调用的函数**：
- `self.update_global_topology`: 更新全局拓扑
- `self.create_new_components_from_nodes`: 创建新组件
- `self.guess_code_state`: 猜测代码状态

**输出参数**: Tuple[str, str] - 语义版本和内容版本

#### _check_semantic_distinguishability(self, component: DepKey, pre_call_uid: str, call_version: Version)

**作用**: 检查语义可区分性，确保版本的唯一性

**输入参数**:
- `component`: DepKey，组件依赖键
- `pre_call_uid`: str，预调用唯一标识符
- `call_version`: Version，调用版本

**内部调用的函数**：
- `logger.warning`: 记录警告

**输出参数**: 无返回值

**其他说明**：检测并警告语义版本冲突

#### get_flat_versions(self) -> Dict[str, Version]

**作用**: 获取扁平化的版本字典

**输入参数**: 无

**内部调用的函数**：
- 字典推导式

**输出参数**: Dict[str, Version] - 版本ID到版本对象的映射

#### get_dependent_versions(self, dep_key: DepKey, commit: str) -> List[Version]

**作用**: 获取依赖于指定提交的版本列表

**输入参数**:
- `dep_key`: DepKey，依赖键
- `commit`: str，提交哈希

**内部调用的函数**：
- `version.get_dependency_commit`: 获取依赖提交

**输出参数**: List[Version] - 依赖版本列表

#### present_dependencies(self, commits: Dict[DepKey, str], component: Optional[DepKey] = None, version_id: Optional[str] = None, show_source: bool = False, show_diffs: bool = False, show_all: bool = False) -> str

**作用**: 展示依赖关系的可读表示

**输入参数**:
- `commits`: Dict[DepKey, str]，提交映射
- `component`: Optional[DepKey]，组件依赖键
- `version_id`: Optional[str]，版本ID
- `show_source`: bool，是否显示源代码
- `show_diffs`: bool，是否显示差异
- `show_all`: bool，是否显示所有信息

**内部调用的函数**：
- `self.get_canonical_groups`: 获取规范分组
- `Panel`: 创建面板（如果有 rich）
- `Syntax`: 语法高亮（如果有 rich）

**输出参数**: str - 依赖关系的可读表示

#### show_versions(self, component: DepKey, version_id: Optional[str] = None, show_source: bool = False, show_diffs: bool = False, show_all: bool = False) -> str

**作用**: 显示组件的版本信息

**输入参数**:
- `component`: DepKey，组件依赖键
- `version_id`: Optional[str]，版本ID
- `show_source`: bool，是否显示源代码
- `show_diffs`: bool，是否显示差异
- `show_all`: bool，是否显示所有信息

**内部调用的函数**：
- `self.present_dependencies`: 展示依赖关系

**输出参数**: str - 版本信息的可读表示

#### get_canonical_groups(self, components: Iterable[DepKey]) -> typing.OrderedDict[str, List[DepKey]]

**作用**: 获取组件的规范分组

**输入参数**:
- `components`: Iterable[DepKey]，组件集合

**内部调用的函数**：
- `OrderedDict`: 创建有序字典

**输出参数**: typing.OrderedDict[str, List[DepKey]] - 规范分组映射

**其他说明**：按模块名对组件进行分组

### 设计模式和架构

#### 版本控制策略
- **语义版本控制**：基于代码语义变化的版本管理
- **内容版本控制**：基于实际内容哈希的版本管理
- **依赖跟踪**：跟踪组件间的依赖关系

#### 状态管理
- **代码状态快照**：捕获特定时刻的代码状态
- **增量更新**：只更新变化的组件
- **状态同步**：保持版本信息与实际代码的一致性

#### 兼容性检查
- **语义兼容性**：检查版本间的语义兼容性
- **依赖解析**：解析和验证依赖关系
- **冲突检测**：检测版本冲突和不一致性
