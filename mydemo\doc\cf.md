# cf.py 文档

## 文件内容与作用总体说明

`cf.py` 文件是 mandala 框架中的核心模块，实现了 `ComputationFrame` 类，这是一个用于表示和操作计算图的高级抽象。ComputationFrame 将计算历史组织为有向图结构，其中节点表示变量和函数，边表示数据流和依赖关系。该文件提供了丰富的图操作功能，包括图的构建、扩展、合并、查询、分析和可视化等。

## 文件内的所有变量的作用与说明

### 全局变量
- `Config`: 配置类，用于检查是否安装了 prettytable 等可选依赖
- `SOLARIZED_LIGHT`: 从 viz 模块导入的颜色配置，用于图形可视化

### ComputationFrame 类的实例变量
- `storage`: Storage 对象，用于存储和管理计算历史
- `inp`: Dict[str, Dict[str, Set[str]]] - 输入邻接表，节点名 -> 输入名 -> {节点名}
- `out`: Dict[str, Dict[str, Set[str]]] - 输出邻接表，节点名 -> 输出名 -> {节点名}
- `vs`: Dict[str, Set[str]] - 变量映射，变量名 -> {历史ID}，表示变量中的引用集合
- `fs`: Dict[str, Set[str]] - 函数映射，函数名 -> {历史ID}，表示函数中的调用集合
- `refinv`: Dict[str, Set[str]] - 引用反向映射，(引用)历史ID -> {变量名}，包含该引用的变量集合
- `callinv`: Dict[str, Set[str]] - 调用反向映射，(调用)历史ID -> {函数名}，包含该调用的函数集合
- `creator`: Dict[str, str] - 创建者映射，引用历史ID -> 创建该引用的调用历史ID
- `consumers`: Dict[str, Set[str]] - 消费者映射，引用历史ID -> 消费该引用的调用历史ID集合
- `refs`: Dict[str, Ref] - 引用对象映射，历史ID -> Ref对象
- `calls`: Dict[str, Call] - 调用对象映射，历史ID -> Call对象

## 文件内的所有函数的作用与说明

### ComputationFrame 类的方法

#### __init__(self, storage: "Storage", inp: Dict[str, Dict[str, Set[str]]] = None, out: Dict[str, Dict[str, Set[str]]] = None, vs: Dict[str, Set[str]] = None, fs: Dict[str, Set[str]] = None, refinv: Dict[str, Set[str]] = None, callinv: Dict[str, Set[str]] = None, creator: Dict[str, str] = None, consumers: Dict[str, Set[str]] = None, refs: Dict[str, Ref] = None, calls: Dict[str, Call] = None)

**作用**: 初始化 ComputationFrame 对象，设置所有内部数据结构

**输入参数**:
- `storage`: Storage 对象，用于数据存储和管理
- `inp`: 可选的输入邻接表
- `out`: 可选的输出邻接表
- `vs`: 可选的变量到引用的映射
- `fs`: 可选的函数到调用的映射
- `refinv`: 可选的引用反向映射
- `callinv`: 可选的调用反向映射
- `creator`: 可选的创建者映射
- `consumers`: 可选的消费者映射
- `refs`: 可选的引用对象映射
- `calls`: 可选的调用对象映射

**内部调用的函数**：
- 无直接函数调用，主要进行数据结构初始化

**输出参数**: 无返回值

**其他说明**：所有参数都有默认值，如果为 None 则初始化为空字典或空集合

#### nodes(self) -> Set[str]

**作用**: 获取计算图中所有节点的名称集合

**输入参数**: 无

**内部调用的函数**：
- set.union: 合并变量名和函数名集合

**输出参数**: Set[str] - 所有节点名称的集合

**其他说明**：节点包括变量节点和函数节点

#### vnames(self) -> Set[str]

**作用**: 获取所有变量节点的名称集合

**输入参数**: 无

**内部调用的函数**：
- set: 转换字典键为集合

**输出参数**: Set[str] - 所有变量名称的集合

#### fnames(self) -> Set[str]

**作用**: 获取所有函数节点的名称集合

**输入参数**: 无

**内部调用的函数**：
- set: 转换字典键为集合

**输出参数**: Set[str] - 所有函数名称的集合

#### edges(self) -> List[Tuple[str, str, str]]

**作用**: 获取计算图中所有边的列表，每条边表示为 (源节点, 目标节点, 标签) 的三元组

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用，通过遍历邻接表构建边列表

**输出参数**: List[Tuple[str, str, str]] - 边的列表

**其他说明**：边表示节点间的数据流关系

#### sources(self) -> Set[str]

**作用**: 获取图中所有源节点（没有输入边的节点）的集合

**输入参数**: 无

**内部调用的函数**：
- set.difference: 计算集合差集

**输出参数**: Set[str] - 源节点名称的集合

#### sinks(self) -> Set[str]

**作用**: 获取图中所有汇聚节点（没有输出边的节点）的集合

**输入参数**: 无

**内部调用的函数**：
- set.difference: 计算集合差集

**输出参数**: Set[str] - 汇聚节点名称的集合

#### in_neighbors(self, node: str) -> Set[str]

**作用**: 获取指定节点的所有输入邻居节点

**输入参数**:
- `node`: str，目标节点名称

**内部调用的函数**：
- set.union: 合并多个集合

**输出参数**: Set[str] - 输入邻居节点的集合

#### out_neighbors(self, node: str) -> Set[str]

**作用**: 获取指定节点的所有输出邻居节点

**输入参数**:
- `node`: str，目标节点名称

**内部调用的函数**：
- set.union: 合并多个集合

**输出参数**: Set[str] - 输出邻居节点的集合

#### in_edges(self, node: str) -> List[Tuple[str, str, str]]

**作用**: 获取指向指定节点的所有输入边

**输入参数**:
- `node`: str，目标节点名称

**内部调用的函数**：
- 无直接函数调用，通过遍历邻接表构建边列表

**输出参数**: List[Tuple[str, str, str]] - 输入边的列表

#### out_edges(self, node: str) -> List[Tuple[str, str, str]]

**作用**: 获取从指定节点出发的所有输出边

**输入参数**:
- `node`: str，源节点名称

**内部调用的函数**：
- 无直接函数调用，通过遍历邻接表构建边列表

**输出参数**: List[Tuple[str, str, str]] - 输出边的列表

#### get_reachable_nodes(self, start_nodes: Iterable[str], direction: Literal["forward", "backward", "both"] = "forward") -> Set[str]

**作用**: 从起始节点集合出发，获取在指定方向上可达的所有节点

**输入参数**:
- `start_nodes`: Iterable[str]，起始节点集合
- `direction`: Literal["forward", "backward", "both"]，搜索方向

**内部调用的函数**：
- collections.deque: 用于广度优先搜索
- self.out_neighbors: 获取输出邻居
- self.in_neighbors: 获取输入邻居

**输出参数**: Set[str] - 可达节点的集合

**其他说明**：使用广度优先搜索算法实现可达性分析

#### topsort_modulo_sccs(self) -> List[str]

**作用**: 对计算图进行拓扑排序，在强连通分量内部顺序任意

**输入参数**: 无

**内部调用的函数**：
- almost_topological_sort: 执行近似拓扑排序算法

**输出参数**: List[str] - 拓扑排序后的节点列表

**其他说明**：处理有环图的拓扑排序

#### subset(self, nodes: Iterable[str], *, ref_hids_subset: Optional[Set[str]] = None, call_hids_subset: Optional[Set[str]] = None) -> "ComputationFrame"

**作用**: 创建当前计算框架的子集，只包含指定的节点和相关元素

**输入参数**:
- `nodes`: Iterable[str]，要包含的节点集合
- `ref_hids_subset`: Optional[Set[str]]，要包含的引用历史ID子集
- `call_hids_subset`: Optional[Set[str]]，要包含的调用历史ID子集

**内部调用的函数**：
- ComputationFrame: 创建新的计算框架实例
- 多个集合操作函数用于过滤数据

**输出参数**: ComputationFrame - 新的子集计算框架

**其他说明**：保持原有图结构的完整性，只是限制包含的元素

#### intersection(self, other: "ComputationFrame") -> "ComputationFrame"

**作用**: 计算两个计算框架的交集

**输入参数**:
- `other`: ComputationFrame，另一个计算框架

**内部调用的函数**：
- get_adjacency_intersection: 计算邻接表交集
- get_setdict_intersection: 计算集合字典交集
- get_dict_intersection_over_keys: 计算字典交集
- ComputationFrame: 创建新的计算框架实例

**输出参数**: ComputationFrame - 交集计算框架

**其他说明**：交集操作保证结果是有效的计算框架

#### __and__(self, other: "ComputationFrame") -> "ComputationFrame"

**作用**: 实现计算框架的交集运算符 &

**输入参数**:
- `other`: ComputationFrame，另一个计算框架

**内部调用的函数**：
- self.intersection: 调用交集方法

**输出参数**: ComputationFrame - 交集计算框架

#### union(self, other: "ComputationFrame") -> "ComputationFrame"

**作用**: 计算两个计算框架的并集

**输入参数**:
- `other`: ComputationFrame，另一个计算框架

**内部调用的函数**：
- get_adjacency_union: 计算邻接表并集
- get_setdict_union: 计算集合字典并集
- get_dict_union_over_keys: 计算字典并集
- ComputationFrame: 创建新的计算框架实例

**输出参数**: ComputationFrame - 并集计算框架

#### __or__(self, other: "ComputationFrame") -> "ComputationFrame"

**作用**: 实现计算框架的并集运算符 |

**输入参数**:
- `other`: ComputationFrame，另一个计算框架

**内部调用的函数**：
- self.union: 调用并集方法

**输出参数**: ComputationFrame - 并集计算框架

#### __sub__(self, other: "ComputationFrame") -> "ComputationFrame"

**作用**: 实现计算框架的差集运算符 -

**输入参数**:
- `other`: ComputationFrame，另一个计算框架

**内部调用的函数**：
- 集合差集操作
- ComputationFrame: 创建新的计算框架实例

**输出参数**: ComputationFrame - 差集计算框架

#### expand_back(self, *, recursive: bool = False, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 向后扩展计算框架，添加创建当前引用的调用及其输入

**输入参数**:
- `recursive`: bool，是否递归扩展直到固定点
- `inplace`: bool，是否就地修改当前对象

**内部调用的函数**：
- self._expand_back_once: 执行一次向后扩展
- self.union: 合并扩展结果

**输出参数**: Optional[ComputationFrame] - 扩展后的计算框架（如果 inplace=True 则返回 None）

**其他说明**：向后扩展用于追溯数据来源

#### expand_forward(self, *, recursive: bool = False, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 向前扩展计算框架，添加消费当前引用的调用及其输出

**输入参数**:
- `recursive`: bool，是否递归扩展直到固定点
- `inplace`: bool，是否就地修改当前对象

**内部调用的函数**：
- self._expand_forward_once: 执行一次向前扩展
- self.union: 合并扩展结果

**输出参数**: Optional[ComputationFrame] - 扩展后的计算框架（如果 inplace=True 则返回 None）

**其他说明**：向前扩展用于追踪数据使用

#### expand_all(self, *, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 全方向扩展计算框架，结合向前和向后扩展

**输入参数**:
- `inplace`: bool，是否就地修改当前对象

**内部调用的函数**：
- self.expand_back: 向后递归扩展
- self.expand_forward: 向前递归扩展

**输出参数**: Optional[ComputationFrame] - 扩展后的计算框架（如果 inplace=True 则返回 None）

**其他说明**：获得完整的计算图视图

#### upstream(self, node: str, *, include_node: bool = True) -> "ComputationFrame"

**作用**: 获取指定节点的上游计算框架

**输入参数**:
- `node`: str，目标节点名称
- `include_node`: bool，是否包含节点本身

**内部调用的函数**：
- self.get_reachable_nodes: 获取可达节点
- self.subset: 创建子集

**输出参数**: ComputationFrame - 上游计算框架

#### downstream(self, node: str, *, include_node: bool = True) -> "ComputationFrame"

**作用**: 获取指定节点的下游计算框架

**输入参数**:
- `node`: str，目标节点名称
- `include_node`: bool，是否包含节点本身

**内部调用的函数**：
- self.get_reachable_nodes: 获取可达节点
- self.subset: 创建子集

**输出参数**: ComputationFrame - 下游计算框架

#### midstream(self, source_nodes: Iterable[str], sink_nodes: Iterable[str], *, include_sources: bool = True, include_sinks: bool = True) -> "ComputationFrame"

**作用**: 获取源节点到汇聚节点之间的中游计算框架

**输入参数**:
- `source_nodes`: Iterable[str]，源节点集合
- `sink_nodes`: Iterable[str]，汇聚节点集合
- `include_sources`: bool，是否包含源节点
- `include_sinks`: bool，是否包含汇聚节点

**内部调用的函数**：
- self.get_reachable_nodes: 获取可达节点
- self.subset: 创建子集

**输出参数**: ComputationFrame - 中游计算框架

#### eval(self, *nodes: str, values: Literal["refs", "objs"] = "objs", verbose: bool = True) -> pd.DataFrame

**作用**: 一站式工具，将计算框架中的值加载到 pandas 数据框中

**输入参数**:
- `nodes`: str，要评估的节点名称
- `values`: Literal["refs", "objs"]，返回引用还是对象
- `verbose`: bool，是否显示详细信息

**内部调用的函数**：
- self.df: 调用数据框生成方法

**输出参数**: pd.DataFrame - 包含节点值的数据框

#### df(self, *nodes: str, values: Literal["refs", "objs"] = "objs", lazy_vars: Optional[Iterable[str]] = None, verbose: bool = False, include_calls: bool = True, join_how: Literal["inner", "outer"] = "outer") -> pd.DataFrame

**作用**: 从计算框架中提取数据的通用方法

**输入参数**:
- `nodes`: str，要包含的节点名称
- `values`: Literal["refs", "objs"]，返回引用还是对象
- `lazy_vars`: Optional[Iterable[str]]，延迟加载的变量
- `verbose`: bool，是否显示详细信息
- `include_calls`: bool，是否包含调用信息
- `join_how`: Literal["inner", "outer"]，连接方式

**内部调用的函数**：
- self.get_history_df: 获取历史数据框
- self.eval_df: 评估数据框
- pd.merge: 合并数据框

**输出参数**: pd.DataFrame - 提取的数据框

**其他说明**：这是数据提取的核心方法，支持多种配置选项

#### eval_df(self, df: pd.DataFrame, skip_cols: Optional[Iterable[str]] = None, skip_calls: bool = False) -> pd.DataFrame

**作用**: 评估包含 Ref 和 Call 对象的数据框，通过应用 unwrap 到选定列

**输入参数**:
- `df`: pd.DataFrame，要评估的数据框
- `skip_cols`: Optional[Iterable[str]]，要跳过的列
- `skip_calls`: bool，是否跳过调用列

**内部调用的函数**：
- storage.unwrap: 解包引用对象
- classify_obj: 分类对象类型

**输出参数**: pd.DataFrame - 评估后的数据框

**其他说明**：主要工具用于评估包含引用和调用的数据框

#### draw(self, show_how: Optional[str] = "inline", path: Optional[str] = None, verbose: bool = False, print_dot: bool = False, orientation: Literal["LR", "TB"] = "TB")

**作用**: 使用 graphviz 绘制计算图，并添加注释信息

**输入参数**:
- `show_how`: Optional[str]，显示方式
- `path`: Optional[str]，输出路径
- `verbose`: bool，是否显示详细信息
- `print_dot`: bool，是否打印 DOT 代码
- `orientation`: Literal["LR", "TB"]，图的方向

**内部调用的函数**：
- self.get_sink_elts: 获取汇聚元素
- self.get_source_elts: 获取源元素
- to_dot_string: 生成 DOT 字符串
- write_output: 输出图形

**输出参数**: 无返回值

**其他说明**：提供图形可视化功能，支持多种输出格式

#### info(self, *nodes: str)

**作用**: 显示计算框架或特定节点的信息

**输入参数**:
- `nodes`: str，要显示信息的节点名称

**内部调用的函数**：
- print: 输出信息

**输出参数**: 无返回值

**其他说明**：如果没有指定节点，显示整个计算框架的概要信息

#### var_info(self, vname: str)

**作用**: 显示变量节点的详细信息

**输入参数**:
- `vname`: str，变量名称

**内部调用的函数**：
- self.get_var_values: 获取变量值
- print: 输出信息

**输出参数**: 无返回值

**其他说明**：显示变量的创建者和消费者操作信息

#### func_info(self, fname: str)

**作用**: 显示函数节点的详细信息

**输入参数**:
- `fname`: str，函数名称

**内部调用的函数**：
- 当前实现为空，待完善

**输出参数**: 无返回值

### 静态方法

#### from_op(storage: "Storage", f: Op) -> "ComputationFrame"

**作用**: 从操作对象创建计算框架

**输入参数**:
- `storage`: Storage，存储对象
- `f`: Op，操作对象

**内部调用的函数**：
- storage.call_storage.execute_df: 执行数据库查询
- storage.mget_call: 批量获取调用
- ComputationFrame: 创建计算框架实例

**输出参数**: ComputationFrame - 新创建的计算框架

**其他说明**：根据操作的所有调用历史构建计算框架

#### from_refs(storage: "Storage", refs: Iterable[Ref]) -> "ComputationFrame"

**作用**: 从引用集合创建计算框架

**输入参数**:
- `storage`: Storage，存储对象
- `refs`: Iterable[Ref]，引用集合

**内部调用的函数**：
- ComputationFrame: 创建计算框架实例
- res._add_var: 添加变量
- res.add_ref: 添加引用

**输出参数**: ComputationFrame - 新创建的计算框架

**其他说明**：将所有引用添加到单个变量节点中

#### from_vars(storage: "Storage", vars: Dict[str, Set[Ref]]) -> "ComputationFrame"

**作用**: 从变量字典创建计算框架

**输入参数**:
- `storage`: Storage，存储对象
- `vars`: Dict[str, Set[Ref]]，变量到引用集合的映射

**内部调用的函数**：
- ComputationFrame: 创建计算框架实例
- res._add_var: 添加变量
- res.add_ref: 添加引用

**输出参数**: ComputationFrame - 新创建的计算框架

**其他说明**：为每个变量创建对应的节点并添加引用

### 辅助函数

#### get_name_proj(op: Op) -> Callable[[str], str]

**作用**: 获取操作的名称投影函数

**输入参数**:
- `op`: Op，操作对象

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Callable[[str], str] - 名称投影函数

**其他说明**：用于生成节点标签，根据操作的输出名称配置决定如何处理参数名称

#### get_history_df(self, vname: str, include_calls: bool = True, verbose: bool = False) -> pd.DataFrame

**作用**: 返回一个数据框，其中行表示变量 vname 中所有引用的完整历史视图

**输入参数**:
- `vname`: str，变量名称
- `include_calls`: bool，是否包含调用信息
- `verbose`: bool，是否显示详细信息

**内部调用的函数**：
- self.get_total_history: 获取总历史
- pd.DataFrame: 创建数据框
- self._sort_df: 排序数据框

**输出参数**: pd.DataFrame - 历史数据框

**其他说明**：每行代表变量中一个引用的完整计算历史

#### get_joint_history_df(self, varnames: Iterable[str], how: Literal["inner", "outer"] = "outer", include_calls: bool = True, verbose: bool = False) -> pd.DataFrame

**作用**: 获取给定变量的联合计算历史，通过公共列连接各变量的历史表

**输入参数**:
- `varnames`: Iterable[str]，变量名称列表
- `how`: Literal["inner", "outer"]，连接方式
- `include_calls`: bool，是否包含调用信息
- `verbose`: bool，是否显示详细信息

**内部调用的函数**：
- self.get_history_df: 获取单个变量的历史
- pd.merge: 合并数据框

**输出参数**: pd.DataFrame - 联合历史数据框

#### get_func_table(self, fname: str) -> pd.DataFrame

**作用**: 获取函数的调用表，显示所有调用的输入输出

**输入参数**:
- `fname`: str，函数名称

**内部调用的函数**：
- pd.DataFrame: 创建数据框

**输出参数**: pd.DataFrame - 函数调用表

#### get_var_values(self, vname: str) -> Set[Ref]

**作用**: 获取变量中所有引用的集合

**输入参数**:
- `vname`: str，变量名称

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Set[Ref] - 引用集合

#### get_var_stats(self) -> pd.DataFrame

**作用**: 获取所有变量的统计信息

**输入参数**: 无

**内部调用的函数**：
- pd.DataFrame: 创建数据框

**输出参数**: pd.DataFrame - 变量统计表

#### get_func_stats(self) -> pd.DataFrame

**作用**: 获取所有函数的统计信息

**输入参数**: 无

**内部调用的函数**：
- pd.DataFrame: 创建数据框

**输出参数**: pd.DataFrame - 函数统计表

#### get_graph_desc(self) -> str

**作用**: 返回计算图的字符串表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 图的描述字符串

#### copy(self) -> "ComputationFrame"

**作用**: 创建计算框架的深拷贝

**输入参数**: 无

**内部调用的函数**：
- ComputationFrame: 创建新实例

**输出参数**: ComputationFrame - 拷贝的计算框架

#### cleanup(self, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 清理计算框架，移除空节点和无效边

**输入参数**:
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.copy: 创建拷贝（如果不是就地修改）

**输出参数**: Optional[ComputationFrame] - 清理后的计算框架

#### merge_vars(self, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 合并有交集的变量，将较小的变量合并到较大的变量中

**输入参数**:
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.copy: 创建拷贝（如果不是就地修改）

**输出参数**: Optional[ComputationFrame] - 合并后的计算框架

#### merge_into(self, node_to_merge: str, merge_into: str, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 将一个节点合并到另一个节点中

**输入参数**:
- `node_to_merge`: str，要合并的节点
- `merge_into`: str，合并目标节点
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.copy: 创建拷贝（如果不是就地修改）

**输出参数**: Optional[ComputationFrame] - 合并后的计算框架

#### get_reachable_nodes(self, nodes: Set[str], direction: Literal["back", "forward"]) -> Set[str]

**作用**: 获取从给定节点在指定方向上可达的所有节点

**输入参数**:
- `nodes`: Set[str]，起始节点集合
- `direction`: Literal["back", "forward"]，搜索方向

**内部调用的函数**：
- self.in_neighbors: 获取输入邻居
- self.out_neighbors: 获取输出邻居

**输出参数**: Set[str] - 可达节点集合

#### select_nodes(self, nodes: Iterable[str]) -> "ComputationFrame"

**作用**: 获取指定节点上的诱导计算框架

**输入参数**:
- `nodes`: Iterable[str]，要选择的节点

**内部调用的函数**：
- ComputationFrame: 创建新实例

**输出参数**: ComputationFrame - 诱导的计算框架

#### add_ref(self, vname: str, ref: Ref, allow_existing: bool = False)

**作用**: 向变量节点添加引用

**输入参数**:
- `vname`: str，变量名称
- `ref`: Ref，要添加的引用
- `allow_existing`: bool，是否允许已存在的引用

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### drop_ref(self, vname: str, hid: str)

**作用**: 从变量节点删除引用

**输入参数**:
- `vname`: str，变量名称
- `hid`: str，要删除的引用历史ID

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### add_call(self, fname: str, call: Call, with_refs: bool, allow_existing: bool = False)

**作用**: 向函数节点添加调用

**输入参数**:
- `fname`: str，函数名称
- `call`: Call，要添加的调用
- `with_refs`: bool，是否同时添加相关引用
- `allow_existing`: bool，是否允许已存在的调用

**内部调用的函数**：
- self.add_ref: 添加引用

**输出参数**: 无返回值

#### drop_call(self, fname: str, hid: str)

**作用**: 从函数节点删除调用

**输入参数**:
- `fname`: str，函数名称
- `hid`: str，要删除的调用历史ID

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### get_source_elts(self) -> Dict[str, Set[str]]

**作用**: 获取计算框架中不作为输出连接的元素视图

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Dict[str, Set[str]] - 源元素字典

#### get_sink_elts(self) -> Dict[str, Set[str]]

**作用**: 获取计算框架中不作为输入连接的元素视图

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Dict[str, Set[str]] - 汇聚元素字典

#### get_total_history(self, node: str, hids: Set[str], include_calls: bool) -> Dict[str, Set[str]]

**作用**: 获取指定节点和历史ID的完整计算历史

**输入参数**:
- `node`: str，节点名称
- `hids`: Set[str]，历史ID集合
- `include_calls`: bool，是否包含调用

**内部调用的函数**：
- self.get_direct_history: 获取直接历史
- self.get_total_history: 递归调用

**输出参数**: Dict[str, Set[str]] - 完整历史字典

#### get_direct_history(self, vname: str, hids: Set[str], include_calls: bool) -> Dict[str, Set[str]]

**作用**: 获取变量的直接计算历史

**输入参数**:
- `vname`: str，变量名称
- `hids`: Set[str]，历史ID集合
- `include_calls`: bool，是否包含调用

**内部调用的函数**：
- 无直接函数调用

**输出参数**: Dict[str, Set[str]] - 直接历史字典

#### get_reachable_elts(self, initial_state: Dict[str, Set[str]], direction: Literal["back", "forward"]) -> Dict[str, Set[str]]

**作用**: 从初始状态开始，获取在指定方向上可达的所有元素

**输入参数**:
- `initial_state`: Dict[str, Set[str]]，初始状态
- `direction`: Literal["back", "forward"]，搜索方向

**内部调用的函数**：
- self.get_adj_elts: 获取邻接元素

**输出参数**: Dict[str, Set[str]] - 可达元素字典

#### get_adj_elts(self, node: str, hids: Set[str], direction: Literal["back", "forward", "both"]) -> Dict[str, Set[str]]

**作用**: 获取指定节点和历史ID的邻接元素

**输入参数**:
- `node`: str，节点名称
- `hids`: Set[str]，历史ID集合
- `direction`: Literal["back", "forward", "both"]，方向

**内部调用的函数**：
- self.get_adj_elts_edge: 获取边的邻接元素

**输出参数**: Dict[str, Set[str]] - 邻接元素字典

#### sort_nodes(self, nodes: Iterable[str]) -> List[str]

**作用**: 返回给定节点的拓扑排序

**输入参数**:
- `nodes`: Iterable[str]，要排序的节点

**内部调用的函数**：
- self.topsort_modulo_sccs: 获取拓扑排序

**输出参数**: List[str] - 排序后的节点列表

#### _sort_df(self, df: pd.DataFrame) -> pd.DataFrame

**作用**: 给定列包含节点名称的数据框，返回按拓扑顺序排序的新数据框

**输入参数**:
- `df`: pd.DataFrame，要排序的数据框

**内部调用的函数**：
- self.sort_nodes: 排序节点

**输出参数**: pd.DataFrame - 排序后的数据框

#### get_new_vname(self, name_hint: str) -> str

**作用**: 生成新的变量名称，避免与现有变量冲突

**输入参数**:
- `name_hint`: str，名称提示

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 新的变量名称

#### get_new_fname(self, name_hint: str) -> str

**作用**: 生成新的函数名称，避免与现有函数冲突

**输入参数**:
- `name_hint`: str，名称提示

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - 新的函数名称

#### rename(self, vars: Optional[Dict[str, str]] = None, funcs: Optional[Dict[str, str]] = None, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 重命名变量和函数节点

**输入参数**:
- `vars`: Optional[Dict[str, str]]，变量重命名映射
- `funcs`: Optional[Dict[str, str]]，函数重命名映射
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.copy: 创建拷贝（如果不是就地修改）

**输出参数**: Optional[ComputationFrame] - 重命名后的计算框架

#### drop_var(self, vname: str, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 删除变量节点及其所有连接

**输入参数**:
- `vname`: str，要删除的变量名称
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.copy: 创建拷贝（如果不是就地修改）

**输出参数**: Optional[ComputationFrame] - 删除后的计算框架

#### drop_func(self, fname: str, inplace: bool = False) -> Optional["ComputationFrame"]

**作用**: 删除函数节点及其所有连接

**输入参数**:
- `fname`: str，要删除的函数名称
- `inplace`: bool，是否就地修改

**内部调用的函数**：
- self.copy: 创建拷贝（如果不是就地修改）

**输出参数**: Optional[ComputationFrame] - 删除后的计算框架

#### apply(self, f: Callable, to: Literal["refs", "vals"] = "vals") -> "ComputationFrame"

**作用**: 对计算框架中的引用或值应用函数

**输入参数**:
- `f`: Callable，要应用的函数
- `to`: Literal["refs", "vals"]，应用目标

**内部调用的函数**：
- self.storage.unwrap: 解包引用（如果应用到值）

**输出参数**: ComputationFrame - 应用函数后的计算框架

#### isin(self, values: Any, nodes: Optional[Iterable[str]] = None) -> "ComputationFrame"

**作用**: 检查计算框架中的值是否在给定值集合中

**输入参数**:
- `values`: Any，要检查的值集合
- `nodes`: Optional[Iterable[str]]，要检查的节点

**内部调用的函数**：
- self.storage.unwrap: 解包引用

**输出参数**: ComputationFrame - 过滤后的计算框架
