# tps.py 文档

## 文件内容与作用总体说明

`tps.py` 文件是 mandala 框架的类型系统模块，定义了框架专用的类型注解和类型处理机制。该文件提供了 mandala 特有的集合类型（如 `MList`、`MDict`、`MSet`、`MTuple`）以及相应的类型处理类（如 `Type`、`AtomType`、`ListType` 等）。这些类型用于支持框架的结构化数据处理、类型推断和序列化功能。

## 文件内的所有变量的作用与说明

### 类型变量
- `T`: TypeVar，泛型类型变量，用于泛型类型注解
- `_KT`: TypeVar，字典键类型变量
- `_VT`: TypeVar，字典值类型变量

### 集合类型类
- `MList`: mandala 专用的列表类型注解
- `MDict`: mandala 专用的字典类型注解
- `MSet`: mandala 专用的集合类型注解
- `MTuple`: mandala 专用的元组类型注解

### 类型处理类
- `Type`: 基础类型类，用于类型推断和处理
- `AtomType`: 原子类型类
- `ListType`: 列表类型类
- `DictType`: 字典类型类
- `SetType`: 集合类型类
- `TupleType`: 元组类型类

## 文件内的所有函数的作用与说明

### MList 类（mandala 列表类型）

#### identify(self)

**作用**: 返回类型的标识字符串

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - "Type annotation for `mandala` lists"

**其他说明**：用于类型识别和调试

### MDict 类（mandala 字典类型）

#### identify(self)

**作用**: 返回类型的标识字符串

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - "Type annotation for `mandala` dictionaries"

### MSet 类（mandala 集合类型）

#### identify(self)

**作用**: 返回类型的标识字符串

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - "Type annotation for `mandala` sets"

### MTuple 类（mandala 元组类型）

#### identify(self)

**作用**: 返回类型的标识字符串

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - "Type annotation for `mandala` tuples"

### Type 类（类型处理基类）

#### from_annotation(annotation: Any) -> "Type"

**作用**: 从类型注解创建相应的 Type 对象

**输入参数**:
- `annotation`: Any，类型注解

**内部调用的函数**：
- hasattr: 检查对象属性
- AtomType: 创建原子类型
- ListType: 创建列表类型
- DictType: 创建字典类型
- SetType: 创建集合类型
- TupleType: 创建元组类型
- Type.from_annotation: 递归调用处理嵌套类型

**输出参数**: Type - 相应的类型对象

**其他说明**：
- 处理 None 和 inspect._empty 注解为原子类型
- 处理 typing.Any 为原子类型
- 根据 __origin__ 属性识别泛型类型
- 递归处理嵌套的类型参数

#### __eq__(self, other: Any) -> bool

**作用**: 比较两个类型对象是否相等

**输入参数**:
- `other`: Any，要比较的对象

**内部调用的函数**：
- type: 获取对象类型
- isinstance: 检查对象类型

**输出参数**: bool - 是否相等

**其他说明**：
- 首先比较类型是否相同
- 对于原子类型，直接返回 True
- 其他类型需要子类实现具体比较逻辑

### AtomType 类（原子类型）

#### __eq__(self, other: Any) -> bool

**作用**: 比较两个原子类型是否相等

**输入参数**:
- `other`: Any，要比较的对象

**内部调用的函数**：
- isinstance: 检查对象类型

**输出参数**: bool - 是否相等

**其他说明**：所有原子类型都被认为是相等的

#### __repr__(self) -> str

**作用**: 返回原子类型的字符串表示

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: str - "AtomType()"

### ListType 类（列表类型）

#### __init__(self, elt: Type) -> None

**作用**: 初始化列表类型，指定元素类型

**输入参数**:
- `elt`: Type，列表元素的类型

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### __eq__(self, other: Any) -> bool

**作用**: 比较两个列表类型是否相等

**输入参数**:
- `other`: Any，要比较的对象

**内部调用的函数**：
- isinstance: 检查对象类型

**输出参数**: bool - 是否相等

**其他说明**：比较元素类型是否相等

#### __repr__(self) -> str

**作用**: 返回列表类型的字符串表示

**输入参数**: 无

**内部调用的函数**：
- repr: 获取元素类型的字符串表示

**输出参数**: str - "ListType(elt=...)"

### DictType 类（字典类型）

#### __init__(self, key: Type, val: Type) -> None

**作用**: 初始化字典类型，指定键和值的类型

**输入参数**:
- `key`: Type，字典键的类型
- `val`: Type，字典值的类型

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### __eq__(self, other: Any) -> bool

**作用**: 比较两个字典类型是否相等

**输入参数**:
- `other`: Any，要比较的对象

**内部调用的函数**：
- isinstance: 检查对象类型

**输出参数**: bool - 是否相等

**其他说明**：比较键类型和值类型是否都相等

#### __repr__(self) -> str

**作用**: 返回字典类型的字符串表示

**输入参数**: 无

**内部调用的函数**：
- repr: 获取键和值类型的字符串表示

**输出参数**: str - "DictType(key=..., val=...)"

### SetType 类（集合类型）

#### __init__(self, elt: Type) -> None

**作用**: 初始化集合类型，指定元素类型

**输入参数**:
- `elt`: Type，集合元素的类型

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

#### __eq__(self, other: Any) -> bool

**作用**: 比较两个集合类型是否相等

**输入参数**:
- `other`: Any，要比较的对象

**内部调用的函数**：
- isinstance: 检查对象类型

**输出参数**: bool - 是否相等

#### __repr__(self) -> str

**作用**: 返回集合类型的字符串表示

**输入参数**: 无

**内部调用的函数**：
- repr: 获取元素类型的字符串表示

**输出参数**: str - "SetType(elt=...)"

### TupleType 类（元组类型）

#### __init__(self, *elts: Type) -> None

**作用**: 初始化元组类型，指定各个位置的元素类型

**输入参数**:
- `elts`: Type，元组各位置元素的类型

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：支持固定长度元组和可变长度元组

#### __eq__(self, other: Any) -> bool

**作用**: 比较两个元组类型是否相等

**输入参数**:
- `other`: Any，要比较的对象

**内部调用的函数**：
- isinstance: 检查对象类型

**输出参数**: bool - 是否相等

**其他说明**：比较元组长度和各位置元素类型

#### __repr__(self) -> str

**作用**: 返回元组类型的字符串表示

**输入参数**: 无

**内部调用的函数**：
- repr: 获取各元素类型的字符串表示

**输出参数**: str - "TupleType(...)"

### 类型系统的设计理念

1. **类型安全**：通过专用的类型注解确保数据结构的正确性
2. **序列化支持**：类型信息用于指导对象的序列化和反序列化
3. **结构化处理**：支持复杂嵌套数据结构的类型推断
4. **扩展性**：易于添加新的数据类型支持

### 使用场景

- **函数签名注解**：在 @op 装饰的函数中使用 MList[int]、MDict[str, float] 等
- **类型推断**：从函数参数和返回值注解推断数据类型
- **序列化指导**：根据类型信息选择合适的序列化策略
- **结构验证**：确保数据结构符合预期的类型规范
