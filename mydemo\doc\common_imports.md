# common_imports.py 文档

## 文件内容与作用总体说明

`common_imports.py` 文件是 mandala 框架的公共导入模块，集中管理了框架中常用的标准库和第三方库的导入。该文件的主要作用是提供统一的导入接口，避免在各个模块中重复导入相同的库，同时配置了日志系统和调试工具。这种设计模式有助于保持代码的整洁性和一致性。

## 文件内的所有变量的作用与说明

### 标准库导入
- `time`: 时间相关功能模块
- `traceback`: 异常回溯信息处理模块
- `random`: 随机数生成模块
- `logging`: 日志记录模块
- `itertools`: 迭代器工具模块
- `copy`: 对象复制模块
- `hashlib`: 哈希算法模块
- `io`: 输入输出流处理模块
- `os`: 操作系统接口模块
- `shutil`: 高级文件操作模块
- `sys`: 系统特定参数和函数模块
- `joblib`: 并行计算和持久化模块
- `inspect`: 对象内省模块
- `binascii`: 二进制和ASCII转换模块
- `asyncio`: 异步编程模块
- `ast`: 抽象语法树模块
- `types`: 动态类型创建和内置类型名称模块
- `tempfile`: 临时文件和目录模块

### 集合类型导入
- `defaultdict`: 默认字典类型
- `OrderedDict`: 有序字典类型

### 类型注解导入
- `Any`: 任意类型
- `Dict`: 字典类型
- `List`: 列表类型
- `Callable`: 可调用类型
- `Tuple`: 元组类型
- `Iterable`: 可迭代类型
- `Optional`: 可选类型
- `Set`: 集合类型
- `Union`: 联合类型
- `TypeVar`: 类型变量
- `Literal`: 字面量类型

### 路径处理
- `Path`: 路径对象类型

### 数据处理库
- `pd`: pandas 库别名，用于数据分析
- `pa`: pyarrow 库别名，用于高性能数据处理
- `np`: numpy 库别名，用于数值计算

### 可选依赖
- `has_rich`: bool，标识是否安装了 rich 库
- `rich`: rich 库（如果可用），用于终端美化输出

### 日志配置
- `logger`: 配置好的日志记录器对象
- `logging_handler`: 日志处理器（RichHandler 或默认处理器）
- `FORMAT`: 日志格式字符串

### 调试工具
- `sess`: Session 对象实例，用于调试

## 文件内的所有函数的作用与说明

### Session 类（调试工具）

#### __init__(self)

**作用**: 初始化 Session 对象，用于调试时的变量管理

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：初始化空的项目列表和作用域

#### d(self)

**作用**: 捕获当前调用栈的局部变量作用域

**输入参数**: 无

**内部调用的函数**：
- inspect.currentframe: 获取当前栈帧
- f_back.f_locals: 获取调用者的局部变量

**输出参数**: 无返回值

**其他说明**：用于调试时保存变量状态

#### dump(self)

**作用**: 将保存的作用域变量转储到当前局部作用域

**输入参数**: 无

**内部调用的函数**：
- inspect.currentframe: 获取当前栈帧
- scope.update: 更新作用域变量
- print: 输出调试信息

**输出参数**: 无返回值

**其他说明**：用于调试时恢复变量状态

### 日志系统配置

#### Rich 日志配置（当 rich 可用时）

**组件**:
- `RichHandler`: rich 库提供的日志处理器
- `logger`: 使用 rich 美化的日志记录器
- `FORMAT`: 简化的日志格式 "%(message)s"

**特点**:
- 彩色输出
- 美化的日志格式
- 禁用链接路径显示

#### 标准日志配置（当 rich 不可用时）

**组件**:
- `logger`: 标准的日志记录器
- `FORMAT`: 详细的日志格式，包含文件名、行号、函数名

**格式**: "[%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s"

### 导入策略

#### 必需依赖
所有标准库和核心第三方库（pandas、numpy、pyarrow）都是必需的，直接导入。

#### 可选依赖
使用 try-except 块处理可选依赖：

```python
try:
    import rich
    has_rich = True
except ImportError:
    has_rich = False
```

这种模式确保：
1. 框架在没有可选依赖时仍能正常工作
2. 当可选依赖可用时提供增强功能
3. 避免导入错误导致的程序崩溃

### 模块设计优势

#### 集中管理
- 所有公共导入集中在一个文件中
- 便于管理依赖关系
- 减少重复导入

#### 一致性
- 确保所有模块使用相同的库别名
- 统一的导入风格
- 标准化的类型注解

#### 可维护性
- 依赖变更只需修改一个文件
- 易于添加新的公共依赖
- 便于版本管理

### 使用模式

其他模块通过以下方式使用：

```python
from .common_imports import *
```

这样可以直接使用所有导入的库和工具，无需重复导入。

### 调试支持

#### Session 对象用法

```python
# 在需要调试的地方
sess.d()  # 捕获当前变量

# 在其他地方
sess.dump()  # 恢复变量到当前作用域
```

这为调试复杂的计算流程提供了便利。

### 日志使用

配置好的 logger 可以直接使用：

```python
logger.info("信息消息")
logger.warning("警告消息")
logger.error("错误消息")
```

根据是否安装 rich，会自动选择合适的输出格式。
