# mandala1框架核心组件关系详解

## 概述

本文档详细说明mandala1框架中@op装饰器、Ref、ComputationFrame、Storage以及其他重点类之间的关系，并通过完整案例展示它们的协同工作机制。

## 核心组件关系图

```mermaid
graph TB
    subgraph "用户层"
        A[@op装饰器] --> B[普通函数]
    end
    
    subgraph "操作层"
        C[Op对象] --> D[函数包装]
        A --> C
    end
    
    subgraph "执行层"
        E[Storage上下文] --> F[函数调用]
        C --> F
        F --> G[Call记录]
        F --> H[Ref结果]
    end
    
    subgraph "分析层"
        I[ComputationFrame] --> J[计算图]
        H --> I
        G --> I
    end
    
    subgraph "持久化层"
        K[数据库] --> L[调用历史]
        M[文件系统] --> N[大对象存储]
        E --> K
        E --> M
    end
```

## 1. @op装饰器 (Decorator)

### 🎯 核心作用
@op装饰器是mandala1框架的入口点，它将普通Python函数转换为可记忆化的操作。

### 🔧 工作机制
```python
# 装饰器转换过程
@op(output_names=["result"], ignore_args=["debug"])
def my_function(data, multiplier, debug=False):
    return data * multiplier

# 等价于：
my_function = Op(
    name="my_function",
    f=my_function,
    output_names=["result"],
    ignore_args=["debug"],
    nout=1
)
```

### 📊 配置参数详解
- **output_names**: 指定输出变量的名称
- **nout**: 输出数量（默认为1）
- **ignore_args**: 不影响缓存的参数
- **__structural__**: 标记为结构化操作
- **__allow_side_effects__**: 允许副作用

### 🔗 与其他组件的关系
- **→ Op**: 装饰器创建Op对象
- **→ Storage**: Op调用时检查Storage上下文
- **→ Call**: 每次调用生成Call记录
- **→ Ref**: 调用结果包装为Ref

## 2. Ref引用系统

### 🎯 核心作用
Ref是mandala1中所有数据的统一包装器，提供内容寻址、历史追踪和内存管理功能。

### 🏗️ 类型层次结构
```python
Ref (抽象基类)
├── AtomRef (原子类型: int, str, float, bool)
├── ListRef (列表类型: [item1, item2, ...])
├── DictRef (字典类型: {key1: value1, ...})
├── SetRef (集合类型: {item1, item2, ...})
└── TupleRef (元组类型: (item1, item2, ...))
```

### 🔑 双重ID系统
```python
ref = wrap_atom(42)
print(f"内容ID: {ref.cid}")  # 基于内容的哈希
print(f"历史ID: {ref.hid}")  # 基于计算历史的哈希
```

**内容ID (cid)**:
- 基于对象内容计算
- 相同内容 → 相同cid
- 用于去重和内容比较

**历史ID (hid)**:
- 基于计算历史计算
- 记录数据来源和依赖
- 用于溯源和调试

### 🔄 状态管理
```python
# 内存状态转换
attached_ref = ref.attached(obj)    # 加载到内存
detached_ref = ref.detached()       # 移出内存
new_ref = ref.with_hid(new_hid)     # 创建新历史ID的副本
```

### 🔗 与其他组件的关系
- **← Op**: Op调用的结果包装为Ref
- **→ ComputationFrame**: CF从Ref构建计算图
- **↔ Storage**: Storage管理Ref的生命周期
- **→ Call**: Call记录包含输入输出Ref

## 3. Storage存储系统

### 🎯 核心作用
Storage是mandala1的核心引擎，负责记忆化、版本管理、数据持久化和上下文管理。

### 🏗️ 架构组成
```python
Storage
├── 数据库层 (SQLite)
│   ├── calls表 (调用记录)
│   ├── atoms表 (原子数据)
│   └── ops表 (操作定义)
├── 缓存层
│   ├── call_cache (调用缓存)
│   ├── atoms_cache (原子缓存)
│   └── ops_cache (操作缓存)
├── 文件系统层
│   ├── overflow_dir (大对象存储)
│   └── 临时文件管理
└── 版本管理层
    ├── 函数版本跟踪
    ├── 依赖变更检测
    └── 语义版本控制
```

### 🔄 记忆化机制
```python
# 记忆化工作流程
with storage:
    result = my_function(args)  # 1. 检查缓存
                               # 2. 命中 → 返回缓存
                               # 3. 未命中 → 执行函数
                               # 4. 保存结果到缓存
                               # 5. 返回Ref包装的结果
```

### 📊 上下文管理
```python
# Context生命周期
Context.current_context = None

with storage:  # __enter__
    Context.current_context = Context(storage)
    # 所有@op调用都会使用这个storage
    
# __exit__
Context.current_context = None
```

### 🔗 与其他组件的关系
- **← @op**: 装饰器检查Storage上下文
- **↔ Ref**: 管理Ref的创建和生命周期
- **→ Call**: 记录每次函数调用
- **→ ComputationFrame**: 提供CF构建的数据源

## 4. ComputationFrame计算图

### 🎯 核心作用
ComputationFrame将计算历史可视化为有向图，支持依赖分析、拓扑排序和图操作。

### 🏗️ 图结构
```python
ComputationFrame
├── 节点 (Nodes)
│   ├── 函数节点 (fnames)
│   └── 变量节点 (vnames)
├── 边 (Edges)
│   ├── 函数→变量 (数据生产)
│   └── 变量→函数 (数据消费)
└── 元数据
    ├── 源节点 (sources)
    ├── 汇节点 (sinks)
    └── 调用映射 (calls_by_func)
```

### 🔍 扩展策略
```python
# 三种扩展方向
cf_back = cf.expand_back(recursive=True)     # 向后：追溯依赖
cf_forward = cf.expand_forward(recursive=True) # 向前：追踪使用
cf_all = cf.expand_all()                     # 全向：完整上下文
```

### 📊 分析功能
```python
# 拓扑分析
topo_order = cf.topsort_modulo_sccs()  # 拓扑排序
upstream = cf.upstream(node)           # 上游依赖
downstream = cf.downstream(node)       # 下游使用
neighbors = cf.in_neighbors(node)      # 输入邻居
```

### 🔗 与其他组件的关系
- **← Storage**: 从Storage获取计算历史
- **← Ref**: 从Ref开始构建图
- **← Call**: 使用Call记录构建节点和边
- **→ 分析工具**: 提供图分析和可视化

## 5. Call调用记录

### 🎯 核心作用
Call记录每次函数调用的完整元数据，是记忆化和计算图构建的基础。

### 🏗️ 数据结构
```python
Call
├── 操作信息
│   ├── op (Op对象)
│   ├── semantic_version (语义版本)
│   └── content_version (内容版本)
├── 输入输出
│   ├── inputs (Dict[str, Ref])
│   └── outputs (Dict[str, Ref])
├── 标识符
│   ├── cid (内容ID)
│   └── hid (历史ID)
└── 元数据
    ├── 执行时间
    └── 环境信息
```

### 🔄 生命周期
```python
# Call的创建和使用
with storage:
    result = my_function(args)  # 1. 创建Call记录
                               # 2. 保存到数据库
                               # 3. 添加到缓存

call = storage.get_ref_creator(result)  # 4. 查询Call记录
detached_call = call.detached()         # 5. 创建detached副本
```

### 🔗 与其他组件的关系
- **← Op**: Op调用时创建Call
- **↔ Ref**: Call包含输入输出Ref
- **→ Storage**: Storage管理Call的持久化
- **→ ComputationFrame**: CF使用Call构建图

## 6. Op操作对象

### 🎯 核心作用
Op是函数的包装器，包含函数元数据和执行逻辑。

### 🏗️ 数据结构
```python
Op
├── 函数信息
│   ├── name (函数名)
│   ├── f (函数对象)
│   └── version (版本号)
├── 输出配置
│   ├── nout (输出数量)
│   └── output_names (输出名称)
├── 行为控制
│   ├── ignore_args (忽略参数)
│   ├── __structural__ (结构化标志)
│   └── __allow_side_effects__ (副作用标志)
└── 元数据
    ├── 创建时间
    └── 源码信息
```

### 🔄 调用流程
```python
# Op调用的完整流程
op_result = my_op(args)
# 1. 检查Context.current_context
# 2. 有上下文 → storage.call(op, args, kwargs)
# 3. 无上下文 → op.f(*args, **kwargs)
```

### 🔗 与其他组件的关系
- **← @op**: 装饰器创建Op对象
- **→ Call**: Op调用时创建Call记录
- **→ Storage**: Storage管理Op的版本和缓存
- **→ ComputationFrame**: CF中的函数节点对应Op

## 7. Context上下文管理

### 🎯 核心作用
Context管理Storage的全局状态，控制@op函数的行为。

### 🏗️ 实现机制
```python
class Context:
    current_context = None  # 全局状态
    
    def __init__(self, storage):
        self.storage = storage
    
    def __enter__(self):
        self.previous = Context.current_context
        Context.current_context = self
    
    def __exit__(self, *args):
        Context.current_context = self.previous
```

### 🔄 嵌套支持
```python
# 嵌套上下文的工作机制
with storage1:  # Context.current_context = Context(storage1)
    with storage2:  # Context.current_context = Context(storage2)
        pass  # 使用storage2
    # Context.current_context = Context(storage1)
# Context.current_context = None
```

### 🔗 与其他组件的关系
- **↔ Storage**: Context包装Storage
- **→ Op**: Op调用时检查当前Context
- **→ 全局状态**: 管理框架的全局行为

## 组件协同工作流程

### 🚀 完整执行流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Op as @op装饰器
    participant Storage as Storage
    participant Context as Context
    participant Call as Call记录
    participant Ref as Ref对象
    participant CF as ComputationFrame

    User->>Op: 调用@op函数
    Op->>Context: 检查current_context
    Context->>Storage: 获取Storage实例
    Storage->>Storage: 检查缓存
    alt 缓存命中
        Storage->>Ref: 返回缓存的Ref
    else 缓存未命中
        Storage->>Op: 执行原函数
        Op->>Storage: 返回结果
        Storage->>Call: 创建Call记录
        Storage->>Ref: 包装结果为Ref
        Storage->>Storage: 保存到缓存
    end
    Storage->>User: 返回Ref结果
    User->>CF: 创建ComputationFrame
    CF->>Storage: 查询计算历史
    Storage->>CF: 返回Call记录
    CF->>CF: 构建计算图
```

### 🔄 数据流转关系

```mermaid
graph LR
    A[用户函数] -->|@op装饰| B[Op对象]
    B -->|Storage上下文调用| C[Call记录]
    C -->|包装结果| D[Ref对象]
    D -->|构建图| E[ComputationFrame]
    C -->|持久化| F[数据库]
    D -->|缓存| G[内存/文件]
    E -->|分析| H[图结构信息]
```

## 关键设计原则

### 1. 分离关注点
- **@op**: 专注于函数装饰和元数据
- **Storage**: 专注于数据管理和记忆化
- **Ref**: 专注于数据包装和寻址
- **ComputationFrame**: 专注于图分析和可视化

### 2. 单一职责
每个组件都有明确的职责边界，避免功能重叠。

### 3. 松耦合设计
组件间通过明确的接口交互，便于测试和扩展。

### 4. 渐进式复杂度
从简单的@op装饰开始，逐步引入更复杂的功能。

## 总结

mandala1框架通过精心设计的组件关系，实现了：

- **🎯 简单易用**: @op装饰器提供简洁的API
- **🔄 自动记忆化**: Storage透明地处理缓存
- **📊 可视化分析**: ComputationFrame提供图分析
- **🔍 完整追踪**: Ref和Call提供完整的数据血缘
- **⚡ 高性能**: 多层缓存和智能存储策略

这种设计使得用户可以专注于业务逻辑，而框架自动处理性能优化、数据管理和分析可视化。
