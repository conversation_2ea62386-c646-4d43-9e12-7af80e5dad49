"""
ComputationFrame遍历器优化版本
充分利用mandala1框架的内置功能，避免重复实现
"""

import os
import sys
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref
except ImportError:
    # 如果mandala1不可用，添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
    from mandala1.model import Call, Ref


class CFTraverserOptimized:
    """优化的ComputationFrame遍历器，充分利用mandala1内置功能"""
    
    def __init__(self):
        pass
        
    def traverse_computation_graph(self, cf: ComputationFrame) -> List[Dict]:
        """
        遍历计算图，返回层级结构信息
        充分利用mandala1的内置方法
        
        参数:
            cf: ComputationFrame对象
            
        返回:
            包含层级信息的字典列表
        """
        if not cf.fnames:
            return []
            
        # 1. 使用mandala1内置的拓扑排序
        try:
            topo_order = cf.topsort_modulo_sccs()
        except Exception as e:
            print(f"拓扑排序失败: {e}")
            topo_order = list(cf.fnames)
        
        # 2. 使用mandala1的图结构信息计算深度
        node_depths = self._calculate_depths_using_cf_methods(cf, topo_order)
        
        # 3. 使用mandala1的查询方法提取信息
        result = []
        for fname in cf.fnames:
            node_info = self._extract_node_info_optimized(cf, fname, node_depths.get(fname, 0))
            result.append(node_info)
            
        # 4. 按深度排序
        result.sort(key=lambda x: (x['depth'], x['function_name']))
        return result
    
    def _calculate_depths_using_cf_methods(self, cf: ComputationFrame, topo_order: List[str]) -> Dict[str, int]:
        """
        使用ComputationFrame的内置方法计算深度
        
        参数:
            cf: ComputationFrame对象
            topo_order: 拓扑排序结果
            
        返回:
            节点名到深度的映射
        """
        depths = {}
        
        # 初始化所有函数节点深度为0
        for fname in cf.fnames:
            depths[fname] = 0
        
        # 基于拓扑顺序计算深度
        for node in topo_order:
            if node in cf.fnames:
                # 使用ComputationFrame的in_neighbors方法
                max_depth = 0
                try:
                    for neighbor in cf.in_neighbors(node):
                        if neighbor in depths:
                            max_depth = max(max_depth, depths[neighbor] + 1)
                    depths[node] = max_depth
                except Exception:
                    depths[node] = 0
                
        return depths
    
    def _extract_node_info_optimized(self, cf: ComputationFrame, fname: str, depth: int) -> Dict[str, Any]:
        """
        使用mandala1的内置方法提取节点信息
        
        参数:
            cf: ComputationFrame对象
            fname: 函数名
            depth: 节点深度
            
        返回:
            节点信息字典
        """
        info = {
            'function_name': fname,
            'depth': depth,
            'call_count': 0,
            'input_variables': [],
            'output_variables': [],
            'dependencies': [],
            'func_table_shape': None,
            'calls_info': []
        }
        
        try:
            # 1. 使用cf.fs直接获取调用次数
            if fname in cf.fs:
                info['call_count'] = len(cf.fs[fname])
            
            # 2. 使用get_func_table获取函数调用表
            try:
                func_table = cf.get_func_table(fname)
                if not func_table.empty:
                    info['func_table_shape'] = func_table.shape
                    # 获取输入输出列名
                    columns = list(func_table.columns)
                    info['input_variables'] = [col for col in columns if not col.startswith('output_')]
                    info['output_variables'] = [col for col in columns if col.startswith('output_')]
            except Exception as e:
                print(f"获取函数表失败 {fname}: {e}")
            
            # 3. 使用calls_by_func获取调用信息
            try:
                calls_dict = cf.calls_by_func()
                if fname in calls_dict:
                    calls = calls_dict[fname]
                    for call in list(calls)[:3]:  # 只取前3个调用作为示例
                        call_info = {
                            'call_id': call.hid[:8] if hasattr(call, 'hid') else 'unknown',
                            'inputs': list(call.inputs.keys()) if hasattr(call, 'inputs') else [],
                            'outputs': list(call.outputs.keys()) if hasattr(call, 'outputs') else []
                        }
                        info['calls_info'].append(call_info)
            except Exception as e:
                print(f"获取调用信息失败 {fname}: {e}")
            
            # 4. 使用in_neighbors和out_neighbors获取依赖关系
            try:
                # 获取函数依赖（输入邻居中的函数节点）
                for neighbor in cf.in_neighbors(fname):
                    if neighbor in cf.fnames:
                        info['dependencies'].append(neighbor)
                        
                # 如果没有从func_table获取到变量信息，使用边信息
                if not info['input_variables'] and not info['output_variables']:
                    for neighbor in cf.in_neighbors(fname):
                        if neighbor in cf.vnames:
                            info['input_variables'].append(neighbor)
                    for neighbor in cf.out_neighbors(fname):
                        if neighbor in cf.vnames:
                            info['output_variables'].append(neighbor)
            except Exception as e:
                print(f"获取邻居信息失败 {fname}: {e}")
                
        except Exception as e:
            print(f"提取节点 {fname} 信息时出错: {e}")
            
        return info
    
    def print_computation_graph(self, cf: ComputationFrame, show_details: bool = True, show_func_tables: bool = False):
        """
        打印计算图的层级结构
        
        参数:
            cf: ComputationFrame对象
            show_details: 是否显示详细信息
            show_func_tables: 是否显示函数调用表信息
        """
        print("=" * 60)
        print("计算图遍历结果（优化版本）")
        print("=" * 60)
        
        # 使用优化的遍历方法
        traversal_result = self.traverse_computation_graph(cf)
        
        if not traversal_result:
            print("计算图中没有函数节点")
            return
        
        # 显示ComputationFrame基本信息
        print(f"ComputationFrame概览:")
        print(f"  - 函数节点数: {len(cf.fnames)}")
        print(f"  - 变量节点数: {len(cf.vnames)}")
        print(f"  - 总节点数: {len(cf.nodes)}")
        
        # 按深度分组显示
        layers = defaultdict(list)
        for node_info in traversal_result:
            layers[node_info['depth']].append(node_info)
        
        for depth in sorted(layers.keys()):
            print(f"\n深度 {depth}:")
            nodes_at_depth = layers[depth]
            
            for i, node_info in enumerate(nodes_at_depth):
                is_last = (i == len(nodes_at_depth) - 1)
                prefix = "└─ " if is_last else "├─ "
                
                print(f"{prefix}函数: {node_info['function_name']}")
                
                if show_details:
                    detail_prefix = "   " if is_last else "│  "
                    print(f"{detail_prefix}├─ 调用次数: {node_info['call_count']}")
                    
                    if node_info['dependencies']:
                        deps_str = ", ".join(node_info['dependencies'])
                        print(f"{detail_prefix}├─ 依赖函数: {deps_str}")
                    
                    if node_info['input_variables']:
                        inputs_str = ", ".join(node_info['input_variables'][:3])
                        if len(node_info['input_variables']) > 3:
                            inputs_str += f" (共{len(node_info['input_variables'])}个)"
                        print(f"{detail_prefix}├─ 输入变量: {inputs_str}")
                    
                    if node_info['output_variables']:
                        outputs_str = ", ".join(node_info['output_variables'][:3])
                        if len(node_info['output_variables']) > 3:
                            outputs_str += f" (共{len(node_info['output_variables'])}个)"
                        print(f"{detail_prefix}├─ 输出变量: {outputs_str}")
                    
                    if show_func_tables and node_info['func_table_shape']:
                        print(f"{detail_prefix}├─ 函数表形状: {node_info['func_table_shape']}")
                    
                    if node_info['calls_info']:
                        print(f"{detail_prefix}└─ 调用示例:")
                        for j, call_info in enumerate(node_info['calls_info']):
                            call_prefix = f"{detail_prefix}   "
                            is_last_call = (j == len(node_info['calls_info']) - 1)
                            call_symbol = "└─" if is_last_call else "├─"
                            print(f"{call_prefix}{call_symbol} {call_info['call_id']}: {call_info['inputs']} -> {call_info['outputs']}")
                    else:
                        print(f"{detail_prefix}└─ 调用示例: 无")
        
        print(f"\n总计: {len(traversal_result)} 个函数节点，{len(layers)} 个深度层级")
        print("=" * 60)
    
    def get_computation_summary(self, cf: ComputationFrame) -> Dict[str, Any]:
        """
        使用mandala1内置方法获取计算图摘要
        
        参数:
            cf: ComputationFrame对象
            
        返回:
            计算图摘要信息
        """
        summary = {
            'total_functions': len(cf.fnames),
            'total_variables': len(cf.vnames),
            'total_nodes': len(cf.nodes),
            'source_nodes': list(cf.sources),
            'sink_nodes': list(cf.sinks),
            'function_call_counts': {},
            'graph_description': None
        }
        
        try:
            # 使用get_graph_desc获取图描述
            summary['graph_description'] = cf.get_graph_desc()
        except Exception as e:
            print(f"获取图描述失败: {e}")
        
        # 使用calls_by_func获取调用统计
        try:
            calls_dict = cf.calls_by_func()
            for fname, calls in calls_dict.items():
                summary['function_call_counts'][fname] = len(calls)
        except Exception as e:
            print(f"获取调用统计失败: {e}")
        
        return summary


def demo_optimized_cf_traverser():
    """演示优化的ComputationFrame遍历功能"""
    print("优化的ComputationFrame遍历功能演示")
    print("=" * 50)
    
    # 创建一些示例函数
    @op
    def add(x, y):
        """加法函数"""
        return x + y
    
    @op
    def multiply(x, y):
        """乘法函数"""
        return x * y
    
    @op
    def power(x, n):
        """幂函数"""
        return x ** n
    
    # 创建存储并执行计算
    storage = Storage()
    
    with storage:
        # 执行一些计算
        a = add(1, 2)
        b = add(3, 4)
        c = multiply(a, b)
        d = power(c, 2)
    
    # 在Storage上下文外创建计算框架
    cf = storage.cf(d).expand_back(recursive=True)
    
    # 创建优化的遍历器
    traverser = CFTraverserOptimized()
    
    # 打印详细结果
    traverser.print_computation_graph(cf, show_details=True, show_func_tables=True)
    
    # 打印摘要信息
    print("\n计算图摘要:")
    summary = traverser.get_computation_summary(cf)
    for key, value in summary.items():
        if key != 'graph_description':
            print(f"  {key}: {value}")


if __name__ == "__main__":
    demo_optimized_cf_traverser()
