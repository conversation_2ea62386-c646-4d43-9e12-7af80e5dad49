# .coveragerc to control coverage.py
[run]
branch = True
omit = 
    mandala_lite/tests/*.py
    mandala_lite/demos/*

[report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    # Don't complain if you don't run into internal errors
    raise InternalError

    # Don't complain about abstract methods, they aren't run:
    @(abc\.)?abstractmethod
