# 栈重放系统详细需求文档

## 1. 项目概述

### 1.1 系统目标
栈重放系统是一个面向Python开发人员的高级可视化调试工具。它旨在通过捕获和记录程序运行时的完整函数调用栈，提供一个交互式界面，允许用户对执行过程进行回溯、分析、参数修改重放、甚至动态修改调用图，从而极大地提升复杂应用和算法的调试效率、代码理解深度和性能分析能力。

### 1.2 核心价值
- **可视化调试**：将抽象的函数调用关系具象化为可交互的树状图。
- **无侵入捕获**：通过猴子补丁或装饰器方式，无需修改业务代码即可捕获运行时信息。
- **可复现分析**：保存完整的执行上下文，使得任何一次函数调用都可以在隔离环境中精确重放。
- **动态场景测试**：支持修改参数重放，方便开发者快速测试边界条件和异常路径。

### 1.3 目标用户
- 从事复杂业务系统开发的工程师。
- 算法和数据结构研究人员。
- 需要深入理解第三方库内部逻辑的开发者。

---

## 2. 核心用例与界面流程

本章节结合《栈重放示意图.html》的用户界面设计，详细描述核心用户操作流程。

### 2.1 用例1：程序执行与调用栈捕获
1.  **用户操作**：点击主界面的"执行程序"按钮。
2.  **系统响应**：
    -   系统启动目标Python程序，并通过预设的捕获机制（如`PySnooper`的猴子补丁）挂载钩子。
    -   实时捕获程序运行过程中的每一次函数调用事件（进入、退出、异常）。
    -   为每一次函数调用创建一个`ComputationFrame`（计算帧）对象，记录其详细信息（函数名、参数、返回值、上下文等）。
    -   根据调用关系，在内存中构建一个有向无环图（`CallGraph`）。
    -   将`CallGraph`实时渲染到"函数调用关系树"视图中。
    -   在"运行信息"面板中同步输出捕获日志。

### 2.2 用例2：调用图交互与导航
1.  **用户操作**：
    -   点击节点旁的展开/折叠图标（📁/📂）。
    -   点击任一节点（如 `B1`）的标题区域。
    -   点击顶部控制栏的"折叠所有"或"展开所有"按钮。
2.  **系统响应**：
    -   对应节点的子节点树被显示或隐藏。
    -   被点击的节点高亮显示，成为当前"选中节点"。
    -   整个调用树的所有节点全部折叠或展开。

### 2.3 用例3：节点与子树的回放
1.  **用户操作**：
    -   右键点击目标节点（如`c1`），选择"重放节点"。
    -   右键点击目标节点（如`B1`），选择"重放子树"。
2.  **系统响应**：
    -   **节点重放**：系统取出`c1`节点的原始`ComputationFrame`，恢复其执行前的上下文，使用原始参数重放该函数。执行结果（返回值或异常）将更新到`c1`节点上，并显示在日志区。
    -   **子树重放**：系统按深度优先的顺序，依次重放`B1`节点及其所有子孙节点（`c1`, `c2`, `c3`）。每次重放都恢复对应节点的原始上下文。

### 2.4 用例4：修改参数并重放
1.  **用户操作**：右键点击目标节点（如`c2`），选择"修改参数后重放"。
2.  **系统响应**：
    -   弹出一个"参数编辑器"对话框，其中预填了`c2`节点的原始参数。
    -   用户修改参数后点击"确定"。
    -   系统使用修改后的参数重放`c2`函数。
    -   **关键行为**：仅`c2`节点的结果被更新。本次重放不影响调用图中其他节点的原始状态或上下文，确保了操作的原子性和可预测性。

### 2.5 用例5：动态添加与删除节点
1.  **用户操作（添加）**：右键父节点`A`，选择"添加节点"，在对话框中指定新函数`B_new`及其参数。
2.  **系统响应（添加）**：
    -   系统在`A`和其子节点之间插入新节点`B_new`。
    -   系统使用`A`节点的上下文执行`B_new`。
    -   后续节点（原`A`的子节点）将使用`B_new`执行后的上下文作为其新的输入上下文，并被自动重放。
    -   调用图结构和界面同步更新。
3.  **用户操作（删除）**：右键节点`B1`，选择"删除节点"。
4.  **系统响应（删除）**：
    -   系统将`B1`的父节点`A`直接连接到`B1`的子节点（`c1`, `c2`, `c3`）。
    -   `c1`, `c2`, `c3`将使用`A`的上下文被重新执行。
    -   调用图结构和界面同步更新。

---

## 3. 功能需求分解 (遵从整洁架构)

### 3.1 领域层 (Domain)
-   **`ComputationFrame` (实体/Entity)**：领域核心对象，代表一次独立的函数调用。
    -   **属性**：`id`, `function_name`, `module`, `args`, `kwargs`, `return_value`, `exception`, `start_time`, `duration`, `context_snapshot`。
-   **`CallGraph` (聚合/Aggregate)**：由`ComputationFrame`组成的调用图。
    -   **行为**：提供图操作的基础方法（添加/删除节点、查询父子关系），确保图的结构一致性。封装`networkx.DiGraph`实现。
-   **`ReplayService` (领域服务/Service)**：
    -   **职责**：定义纯粹的函数重放逻辑。接收一个`ComputationFrame`和（可选的）新参数，负责在隔离环境中执行函数并返回结果，不关心UI或外部状态。

### 3.2 应用层 (Application)
-   **用例 (Use Cases)**：
    -   `CaptureExecutionUseCase`: 协调捕获过程。
    -   `ReplayNodeUseCase`: 处理节点重放的用户请求，调用`ReplayService`。
    -   `ManipulateGraphUseCase`: 处理添加/删除节点的请求，并管理级联重放。
-   **接口/端口 (Ports)**：
    -   `CapturePort`: 定义捕获能力的接口（如`start_capture`, `stop_capture`）。
    -   `GraphStoragePort`: 定义图数据持久化的接口（`save`, `load`）。
    -   `VisualizationPort`: 定义与前端UI解耦的接口（`render_graph`, `update_node`, `show_error`）。

### 3.3 适配器层 (Interface Adapters)
-   **输入适配器**：
    -   `GuiEventHandler`: 接收来自`PyQtGraph`的界面事件（按钮点击、右键菜单），调用对应的应用层`UseCase`。
-   **输出适配器 (Presenters)**：
    -   `GraphPresenter`: 将应用层返回的`CallGraph`模型转换为UI视图模型。
    -   `LogPresenter`: 格式化日志信息，供UI显示。

### 3.4 基础设施层 (Infrastructure)
-   **`PySnooperCaptureAdapter` (实现`CapturePort`)**: 使用`pysnooper`或类似的猴子补丁技术实现函数调用捕获。
-   **`JsonGraphStorageAdapter` (实现`GraphStoragePort`)**: 将`CallGraph`对象序列化为JSON文件进行存取。
-   **`PyQtGraphVisualizer` (实现`VisualizationPort`)**: 使用`PyQtGraph`库渲染和操作可视化界面。
-   **`ContextManager`**: 基于`contextvars`实现，负责捕获和恢复函数执行的上下文快照。

---

## 4. 非功能性需求

-   **性能**：系统应能流畅处理包含至少5000个节点的调用图，任何UI交互的响应时间应低于500毫秒。
-   **可靠性**：重放具有副作用（如文件IO）的函数时，应向用户提供明确警告。任何重放失败都不能导致主程序崩溃，并应有清晰的错误报告。
-   **易用性**：UI设计需直观，交互逻辑需符合开发者心智模型。
-   **扩展性**：架构应支持未来轻松替换或增加新的捕获工具、存储方案或UI框架。

---

## 5. 风险与待定项

-   **副作用管理**：重放具有强副作用的函数（如数据库写入）是主要风险。初步方案是在UI上明确标记此类函数，并提供"模拟重放"模式。
-   **上下文捕获的深度与广度**：捕获完整的程序状态（包括所有全局变量、闭包变量）可能导致巨大的性能开销和内存占用。需要制定精细化的上下文捕获策略。
-   **并发/异步支持**：V1版本将专注于单线程同步代码。对`asyncio`或多线程代码的捕获与可视化将作为后续版本的重要目标。
-   **技术选型确认**：`PyQtGraph`在处理大规模可拖拽图方面的性能有待验证。备选方案包括 `vis.js` 的Python封装或自定义OpenGL渲染。