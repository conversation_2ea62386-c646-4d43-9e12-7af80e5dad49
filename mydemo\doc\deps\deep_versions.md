# deps/deep_versions.py 文档

## 文件内容与作用总体说明

`deps/deep_versions.py` 文件是 mandala 框架依赖跟踪模块的深层版本管理组件，实现了包含依赖关系的组件版本跟踪功能。该文件定义了 `Version` 类，用于管理组件的"深层"版本，不仅包含组件自身的内容变化，还包含其所有依赖关系的版本信息。深层版本支持传递依赖展开、语义版本计算、版本同步等高级功能，是实现 mandala 记忆化缓存失效的核心机制。

## 文件内的所有变量的作用与说明

### 导入的模块和类型
- `DepKey`: 依赖键类型
- `hash_dict`: 字典哈希函数
- `Node`, `CallableNode`, `GlobalVarNode`, `TerminalNode`: 节点类型
- `DAG`: 有向无环图类

### Version 类的实例变量

#### 原始跟踪数据
- `component`: DepKey，被跟踪依赖关系的组件
- `direct_deps_commits`: Dict[DepKey, str]，直接依赖的内容哈希
- `memoized_deps_content_versions`: Dict[DepKey, Set[str]]，记忆化调用的内容版本指针

#### 缓存数据（针对依赖状态设置）
- `_is_synced`: bool，是否已同步
- `_content_expansion`: Dict[DepKey, Set[str]]，扩展的依赖集合（包含所有传递依赖）
- `_content_version`: str，唯一标识此版本依赖内容的哈希
- `_semantic_expansion`: Dict[DepKey, str]，所有依赖的语义哈希
- `_semantic_version`: str，此版本的整体语义哈希

## 文件内的所有函数的作用与说明

### Version 类（深层版本）

#### __init__(self, component: DepKey, dynamic_deps_commits: Dict[DepKey, str], memoized_deps_content_versions: Dict[DepKey, Set[str]])

**作用**: 初始化深层版本对象，包含组件及其依赖关系的版本信息

**输入参数**:
- `component`: DepKey，组件的依赖键
- `dynamic_deps_commits`: Dict[DepKey, str]，动态依赖的提交哈希
- `memoized_deps_content_versions`: Dict[DepKey, Set[str]]，记忆化依赖的内容版本

**内部调用的函数**：
- 无直接函数调用

**输出参数**: 无返回值

**其他说明**：初始化时缓存数据为空，需要通过 sync 方法同步

#### presentation(self) -> str

**作用**: 返回版本的可读表示

**输入参数**: 无

**内部调用的函数**：
- `self.content_version`: 获取内容版本
- `self.semantic_version`: 获取语义版本

**输出参数**: str - 版本的可读表示

#### from_trace(component: DepKey, nodes: Dict[DepKey, Node], strict: bool = True) -> "Version"

**作用**: 从跟踪结果创建版本对象

**输入参数**:
- `component`: DepKey，组件依赖键
- `nodes`: Dict[DepKey, Node]，节点映射
- `strict`: bool，是否严格模式

**内部调用的函数**：
- `isinstance`: 检查对象类型
- `logger.warning`: 记录警告（严格模式下）

**输出参数**: Version - 新创建的版本对象

**其他说明**：
- 静态方法，从跟踪图的状态创建版本
- 区分可调用节点、全局变量节点和终端节点
- 严格模式下对未知节点类型发出警告

#### _set_content_expansion(self, all_versions: Dict[DepKey, Dict[str, "Version"]])

**作用**: 设置内容扩展，计算所有传递依赖的内容版本

**输入参数**:
- `all_versions`: Dict[DepKey, Dict[str, "Version"]]，所有版本的映射

**内部调用的函数**：
- `defaultdict`: 创建默认字典
- `version.content_expansion`: 递归获取依赖的内容扩展

**输出参数**: 无返回值

**其他说明**：递归展开所有传递依赖的内容版本

#### _set_content_version(self)

**作用**: 设置内容版本哈希，基于内容扩展计算

**输入参数**: 无

**内部调用的函数**：
- `hash_dict`: 计算字典哈希

**输出参数**: 无返回值

**其他说明**：内容版本唯一标识此版本的依赖内容

#### _set_semantic_expansion(self, component_dags: Dict[DepKey, DAG], all_versions: Dict[DepKey, Dict[str, "Version"]])

**作用**: 设置语义扩展，计算所有依赖的语义哈希

**输入参数**:
- `component_dags`: Dict[DepKey, DAG]，组件DAG映射
- `all_versions`: Dict[DepKey, Dict[str, "Version"]]，所有版本映射

**内部调用的函数**：
- `dag.commits`: 访问DAG的提交
- `version.semantic_expansion`: 递归获取语义扩展

**输出参数**: 无返回值

**其他说明**：
- 系统强制要求同一组件的所有提交具有相同的语义哈希
- 递归处理记忆化依赖的语义版本

#### sync(self, component_dags: Dict[DepKey, DAG], all_versions: Dict[DepKey, Dict[str, "Version"]])

**作用**: 同步版本对象，计算所有缓存的版本信息

**输入参数**:
- `component_dags`: Dict[DepKey, DAG]，组件DAG映射
- `all_versions`: Dict[DepKey, Dict[str, "Version"]]，所有版本映射

**内部调用的函数**：
- `self._set_content_expansion`: 设置内容扩展
- `self._set_content_version`: 设置内容版本
- `self._set_semantic_expansion`: 设置语义扩展
- `hash_dict`: 计算语义版本哈希

**输出参数**: 无返回值

**其他说明**：必须在访问版本属性之前调用此方法

#### content_version(self) -> str

**作用**: 获取内容版本哈希

**输入参数**: 无

**内部调用的函数**：
- 断言检查同步状态

**输出参数**: str - 内容版本哈希

#### semantic_version(self) -> str

**作用**: 获取语义版本哈希

**输入参数**: 无

**内部调用的函数**：
- 断言检查同步状态

**输出参数**: str - 语义版本哈希

#### semantic_expansion(self) -> Dict[DepKey, str]

**作用**: 获取语义扩展映射

**输入参数**: 无

**内部调用的函数**：
- 断言检查同步状态

**输出参数**: Dict[DepKey, str] - 依赖键到语义哈希的映射

#### content_expansion(self) -> Dict[DepKey, Set[str]]

**作用**: 获取内容扩展映射

**输入参数**: 无

**内部调用的函数**：
- 断言检查同步状态

**输出参数**: Dict[DepKey, Set[str]] - 依赖键到内容版本集合的映射

#### support(self) -> Iterable[DepKey]

**作用**: 获取版本支持的依赖键集合

**输入参数**: 无

**内部调用的函数**：
- `self.content_expansion.keys`: 获取内容扩展的键

**输出参数**: Iterable[DepKey] - 支持的依赖键

#### is_synced(self) -> bool

**作用**: 检查版本是否已同步

**输入参数**: 无

**内部调用的函数**：
- 无直接函数调用

**输出参数**: bool - 是否已同步

#### set_synced(self)

**作用**: 设置版本为已同步状态

**输入参数**: 无

**内部调用的函数**：
- `logger.warning`: 记录警告（如果已经同步）

**输出参数**: 无返回值

**其他说明**：只能从未同步状态转换为已同步状态

#### __repr__(self) -> str

**作用**: 返回版本对象的详细字符串表示

**输入参数**: 无

**内部调用的函数**：
- 各种属性访问

**输出参数**: str - 详细的字符串表示

**其他说明**：包含组件、直接依赖、记忆化依赖等信息

### 设计模式和架构

#### 延迟计算模式
- **按需同步**：版本信息在需要时才计算
- **缓存机制**：计算结果被缓存以提高性能
- **状态检查**：通过断言确保数据一致性

#### 依赖展开策略
- **传递闭包**：递归计算所有传递依赖
- **版本集合**：支持一个依赖的多个版本
- **语义一致性**：确保语义版本的一致性

#### 版本标识
- **内容版本**：基于实际内容的精确版本标识
- **语义版本**：基于语义等价性的版本标识
- **双重哈希**：提供不同粒度的版本比较

### 使用场景

1. **缓存失效**：确定何时需要重新计算记忆化结果
2. **依赖分析**：分析组件间的复杂依赖关系
3. **版本兼容性**：检查版本间的兼容性
4. **增量计算**：支持基于依赖变化的增量计算

### 与其他模块的关系

- **shallow_versions.py**：使用浅层版本管理单个组件
- **model.py**：使用节点类型表示依赖关系
- **versioner.py**：被版本管理器使用进行整体版本控制
- **utils.py**：使用工具函数进行哈希计算
