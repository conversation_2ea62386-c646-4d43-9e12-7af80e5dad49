# Mandala1 框架文档

## 概述

本目录包含了 mandala1 框架所有核心模块的详细文档。mandala1 是一个用于计算记忆化、依赖跟踪和计算图分析的 Python 框架。

## 文档结构

### 核心模块文档

#### [cf.md](./cf.md) - 计算框架模块
- **重点模块** - ComputationFrame 类的完整实现
- 计算图的构建、操作、分析和可视化
- 图的扩展、合并、查询等高级操作
- 数据提取和评估功能

#### [storage.md](./storage.md) - 存储管理模块  
- **重点模块** - Storage 类的核心实现
- 数据持久化和记忆化功能
- 调用和引用的存储管理
- 版本控制和依赖跟踪

#### [model.md](./model.md) - 数据模型模块
- 核心数据结构：Ref（引用）、Call（调用）、Op（操作）
- @op 装饰器的实现
- 各种数据类型的引用类（ListRef、DictRef等）
- 对象包装和类型处理

#### [utils.md](./utils.md) - 工具函数模块
- 序列化/反序列化功能
- 哈希计算和数据比较
- 集合操作和图算法
- 参数解析和类型处理

### 配置和类型模块

#### [config.md](./config.md) - 配置模块
- 全局配置管理
- 可选依赖检测
- 环境适配功能

#### [tps.md](./tps.md) - 类型系统模块
- mandala 专用类型注解（MList、MDict等）
- 类型推断和处理机制
- 结构化数据类型支持

### 可视化和接口模块

#### [viz.md](./viz.md) - 可视化模块
- 计算图的图形化展示
- Graphviz DOT 格式生成
- 多种输出格式支持

#### [imports.md](./imports.md) - 主导入接口
- 用户友好的导入接口
- 核心功能的统一入口
- 简化的使用体验

#### [common_imports.md](./common_imports.md) - 公共导入模块
- 标准库和第三方库的集中管理
- 日志系统配置
- 调试工具支持

#### [__init__.md](./__init__.md) - 包初始化文档
- 包结构和命名空间管理
- 公共接口定义
- 包级别的配置和元数据

## 重点关注模块

根据文档生成要求，以下两个模块需要特别关注：

### 1. cf.py（计算框架）
- **核心功能**：ComputationFrame 类实现了计算图的高级抽象
- **主要特性**：
  - 图的构建和操作（并集、交集、差集）
  - 图的扩展（向前、向后、全方向）
  - 数据查询和分析
  - 可视化支持
- **关键方法**：expand_back、expand_forward、eval、df、draw 等

### 2. storage.py（存储管理）
- **核心功能**：Storage 类是整个框架的中央协调器
- **主要特性**：
  - 数据持久化和记忆化
  - 调用和引用管理
  - 版本控制和依赖跟踪
  - 与 @op 装饰器的集成
- **关键方法**：call、save_call、cf、unwrap、attach 等

## 使用指南

### 基本使用流程

1. **创建存储**：
   ```python
   from mandala.imports import Storage
   storage = Storage(db_path="my_storage.db")
   ```

2. **定义操作**：
   ```python
   from mandala.imports import op
   
   @op
   def my_function(x: int) -> int:
       return x * 2
   ```

3. **执行计算**：
   ```python
   with storage:
       result = my_function(5)
   ```

4. **分析计算图**：
   ```python
   cf = storage.cf(result)
   cf.expand_back(recursive=True)
   cf.draw()
   ```

### 高级功能

- **类型注解**：使用 MList、MDict 等进行类型标注
- **计算图操作**：使用 ComputationFrame 进行复杂的图分析
- **数据提取**：使用 eval 和 df 方法提取计算结果
- **可视化**：使用 draw 方法生成计算图的可视化

## 架构设计

### 分层架构
1. **用户接口层**：imports.py 提供简化的用户接口
2. **核心功能层**：storage.py 和 cf.py 提供核心功能
3. **数据模型层**：model.py 定义基础数据结构
4. **工具支持层**：utils.py、viz.py 等提供辅助功能

### 设计原则
- **模块化**：清晰的模块分工和接口定义
- **可扩展性**：支持新功能的添加和定制
- **用户友好**：简化的接口和丰富的文档
- **性能优化**：高效的存储和计算机制

## 开发和扩展

### 添加新功能
1. 在相应模块中实现功能
2. 更新相关的文档
3. 在 imports.py 中暴露新接口（如需要）
4. 添加测试用例

### 文档维护
- 每个模块的文档应与代码保持同步
- 重要的 API 变更需要更新文档
- 提供充分的使用示例

## 相关资源

- **源代码**：`mandala1/` 目录
- **示例代码**：`mydemo/案例/` 目录
- **测试用例**：`mandala1/tests/` 目录
- **配置文件**：`.cursor/生成文档的要求.md`

## 贡献指南

1. 阅读相关模块的文档
2. 理解代码结构和设计原则
3. 遵循现有的代码风格和文档格式
4. 提交前确保文档的完整性和准确性
