{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Gotchas\n", "<a href=\"https://colab.research.google.com/github/amakelov/mandala/blob/master/docs_notebooks/tutorials/gotchas.ipynb\"> \n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/> </a>\n", "\n", "This notebook lists some things that can go wrong when using `mandala`, and\n", "how to avoid and/or fix them."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:02.138253Z", "iopub.status.busy": "2024-08-20T14:33:02.138000Z", "iopub.status.idle": "2024-08-20T14:33:02.154829Z", "shell.execute_reply": "2024-08-20T14:33:02.154150Z"}}, "outputs": [], "source": ["# for Google Colab\n", "try:\n", "    import google.colab\n", "    !pip install git+https://github.com/amakelov/mandala\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Versioning tips"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Avoiding overhead when versioning large global variables\n", "If you use versioning (by passing the `deps_path=...` argument to a `Storage`),\n", "`mandala` will automatically track the content hashes of any global variables\n", "accessed by your `@op`s. \n", "\n", "This is a useful way to avoid a class of bugs, but can also lead to significant\n", "overhead if you have large global variables, because each time a `with storage:`\n", "context is entered, `mandala` will compute the hashes of all known global\n", "variables to check for changes, which can be slow.\n", "\n", "To overcome this, if you have a large global variable that you know will not\n", "change often, you can effectively manually pre-compute its hash so that\n", "`mandala` does not need to recompute it each time. This can be done by simply\n", "wrapping the global in a `Ref` object, and then using `ref.obj` when you want\n", "to access the underlying object in a function."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:02.157800Z", "iopub.status.busy": "2024-08-20T14:33:02.157586Z", "iopub.status.idle": "2024-08-20T14:33:04.377110Z", "shell.execute_reply": "2024-08-20T14:33:04.376401Z"}}, "outputs": [], "source": ["import numpy as np\n", "from mandala.imports import wrap_atom, op, Storage, track\n", "\n", "LARGE_GLOBAL = wrap_atom(np.ones((10_000, 5000)))\n", "\n", "@op\n", "def test_op(x):\n", "    return x + LARGE_GLOBAL.obj\n", "\n", "storage = Storage(deps_path='__main__', strict_tracing=False)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:04.381342Z", "iopub.status.busy": "2024-08-20T14:33:04.380448Z", "iopub.status.idle": "2024-08-20T14:33:07.359156Z", "shell.execute_reply": "2024-08-20T14:33:07.355163Z"}}, "outputs": [], "source": ["with storage:\n", "    y = test_op(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can check that now (unlike the case when you don't wrap the global),\n", "retracing the memoized code takes very little time because the hash of the large\n", "global variable is not recomputed:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:07.363514Z", "iopub.status.busy": "2024-08-20T14:33:07.363274Z", "iopub.status.idle": "2024-08-20T14:33:07.596611Z", "shell.execute_reply": "2024-08-20T14:33:07.596020Z"}}, "outputs": [], "source": ["with storage:\n", "    y = test_op(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also see the object reflected in the version of `test_op`:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:07.599046Z", "iopub.status.busy": "2024-08-20T14:33:07.598818Z", "iopub.status.idle": "2024-08-20T14:33:07.661554Z", "shell.execute_reply": "2024-08-20T14:33:07.661018Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### Dependencies for version of function test_op from module __main__</span><span style=\"background-color: #fdf6e3\">                                          </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### content_version_id=0b20075a89aec9dc391db79ff1d0aef6</span><span style=\"background-color: #fdf6e3\">                                                        </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### semantic_version_id=4360b08a7c57f017bbebbdec2fbd92b3</span><span style=\"background-color: #fdf6e3\">                                                       </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">### IN MODULE \"__main__\"</span><span style=\"background-color: #fdf6e3\">                                                                                       </span> │\n", "│ <span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3; font-style: italic\">################################################################################</span><span style=\"background-color: #fdf6e3\">                               </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">LARGE_GLOBAL </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">=</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> AtomRef(array([[</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">...</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">], [</span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">...</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, </span><span style=\"color: #2aa198; text-decoration-color: #2aa198; background-color: #fdf6e3\">1.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">, [</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">...</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">]</span><span style=\"background-color: #fdf6e3\">                   </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "│ <span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">@op</span><span style=\"background-color: #fdf6e3\">                                                                                                            </span> │\n", "│ <span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">def</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> </span><span style=\"color: #268bd2; text-decoration-color: #268bd2; background-color: #fdf6e3\">test_op</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">(x):</span><span style=\"background-color: #fdf6e3\">                                                                                                </span> │\n", "│ <span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">    </span><span style=\"color: #859900; text-decoration-color: #859900; background-color: #fdf6e3\">return</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> x </span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">+</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\"> LARGE_GLOBAL</span><span style=\"color: #93a1a1; text-decoration-color: #93a1a1; background-color: #fdf6e3\">.</span><span style=\"color: #657b83; text-decoration-color: #657b83; background-color: #fdf6e3\">obj</span><span style=\"background-color: #fdf6e3\">                                                                                </span> │\n", "│ <span style=\"background-color: #fdf6e3\">                                                                                                               </span> │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### Dependencies for version of function test_op from module __main__\u001b[0m\u001b[48;2;253;246;227m                                          \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### content_version_id=0b20075a89aec9dc391db79ff1d0aef6\u001b[0m\u001b[48;2;253;246;227m                                                        \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### semantic_version_id=4360b08a7c57f017bbebbdec2fbd92b3\u001b[0m\u001b[48;2;253;246;227m                                                       \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m### IN MODULE \"__main__\"\u001b[0m\u001b[48;2;253;246;227m                                                                                       \u001b[0m │\n", "│ \u001b[3;38;2;147;161;161;48;2;253;246;227m################################################################################\u001b[0m\u001b[48;2;253;246;227m                               \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227mLARGE_GLOBAL\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m=\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mAtomRef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227marray\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m[\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m[\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m]\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m[\u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;42;161;152;48;2;253;246;227m1.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m,\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m[\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m]\u001b[0m\u001b[48;2;253;246;227m                   \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "│ \u001b[38;2;38;139;210;48;2;253;246;227m@op\u001b[0m\u001b[48;2;253;246;227m                                                                                                            \u001b[0m │\n", "│ \u001b[38;2;133;153;0;48;2;253;246;227mdef\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;38;139;210;48;2;253;246;227mtest_op\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m(\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mx\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m)\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m:\u001b[0m\u001b[48;2;253;246;227m                                                                                                \u001b[0m │\n", "│ \u001b[38;2;101;123;131;48;2;253;246;227m    \u001b[0m\u001b[38;2;133;153;0;48;2;253;246;227m<PERSON><PERSON>\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mx\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m+\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227m \u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mLARGE_GLOBAL\u001b[0m\u001b[38;2;147;161;161;48;2;253;246;227m.\u001b[0m\u001b[38;2;101;123;131;48;2;253;246;227mobj\u001b[0m\u001b[48;2;253;246;227m                                                                                \u001b[0m │\n", "│ \u001b[48;2;253;246;227m                                                                                                               \u001b[0m │\n", "╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["storage.versions(test_op)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Caveats of hashing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### If `x == y`, this doesn't guarantee that `x` and `y` will have the same content hash"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:07.695351Z", "iopub.status.busy": "2024-08-20T14:33:07.695152Z", "iopub.status.idle": "2024-08-20T14:33:07.715959Z", "shell.execute_reply": "2024-08-20T14:33:07.715102Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Is 1 == True? True\n", "Is the hash of 1 == the hash of True? False\n", "Is 23 == 23.0? True\n", "Is the hash of 23 == the hash of 23.0? False\n"]}], "source": ["from mandala.utils import get_content_hash\n", "\n", "print(f'Is 1 == True? {1 == True}')\n", "print(f'Is the hash of 1 == the hash of True? {get_content_hash(1) == get_content_hash(True)}')\n", "print(f'Is 23 == 23.0? {23 == 23.0}')\n", "print(f'Is the hash of 23 == the hash of 23.0? {get_content_hash(23) == get_content_hash(23.0)}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hashing numerical values is sensitive to precision and type\n", "All three of the values `42, 42.0, 42.000000001` have different content hashes:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:07.718247Z", "iopub.status.busy": "2024-08-20T14:33:07.718071Z", "iopub.status.idle": "2024-08-20T14:33:07.736923Z", "shell.execute_reply": "2024-08-20T14:33:07.736147Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d922f805b5eead8c40ee21f14329d6c7\n", "ca276c58eef17e13c4f274c9280abc1e\n", "b61bb24b62bf6b1ab95506a62843be08\n"]}], "source": ["from mandala.utils import get_content_hash\n", "\n", "print(get_content_hash(42))\n", "print(get_content_hash(42.0))\n", "print(get_content_hash(42.00000000001))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It's possible to define custom types that will be insensitive to types and\n", "rounding errors when hashed, but this is currently not implemented."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Non-deterministic hashes for complex objects\n", "Below we illustrate several potentially confusing behaviors that are hard to\n", "eradicate in general:\n", "- even if we set all random seeds properly, certain computations (e.g., training\n", "a `scikit-learn` model) result in objects with non-deterministic content IDs\n", "- certain objects can change their content ID after making a roundtrip through\n", "the serialization-deserialization pipeline"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2024-08-20T14:33:07.740173Z", "iopub.status.busy": "2024-08-20T14:33:07.739827Z", "iopub.status.idle": "2024-08-20T14:33:09.126549Z", "shell.execute_reply": "2024-08-20T14:33:09.125905Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Content IDs of the two models: c8d1485ebe003581fb2019b73a2de97a and c8d1485ebe003581fb2019b73a2de97a\n", "Content IDs of the original and restored model: c8d1485ebe003581fb2019b73a2de97a and c8d1485ebe003581fb2019b73a2de97a\n"]}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.datasets import load_digits\n", "import random\n", "import numpy as np\n", "\n", "from mandala.utils import get_content_hash, serialize, deserialize\n", "\n", "X, y = load_digits(n_class=10, return_X_y=True)\n", "\n", "def train_model():\n", "    ### set both the numpy and python random seed\n", "    np.random.seed(42)\n", "    random.seed(42)\n", "    ### train a model, passing the random_state explicitly\n", "    model = RandomForestClassifier(max_depth=2, \n", "                                n_estimators=100, random_state=42).fit(X, y)\n", "    return model\n", "\n", "### training in the exact same way will produce different content hashes\n", "model_1 = train_model()\n", "model_2 = train_model()\n", "print(f'Content IDs of the two models: {get_content_hash(model_1)} and {get_content_hash(model_2)}')\n", "\n", "### a roundtrip serialization will produce a different content hash\n", "roundtrip_model_1 = deserialize(serialize(model_1))\n", "print(f'Content IDs of the original and restored model: {get_content_hash(model_1)} and {get_content_hash(roundtrip_model_1)}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Why is this hard to get rid of in general?** One pervasive issue is that some\n", "custom Python objects, e.g. many kinds of ML models and even `pytorch` tensors,\n", "create internal state related to system resources, such as memory layout. These \n", "can be different between objects that otherwise have semantically equivalent\n", "state, leading to different content hashes. It is impossible to write down a\n", "hash function that always ignores these aspects for arbitrary classes, because \n", "we don't know how to interpret which attributes of the object are semantically\n", "meaningful and which are contingent.\n", "\n", "**What should you do about it?** This issue does come up that often in practice.\n", "Note that this is not an issue for many kinds of objects, such as primitive\n", "Python types and nested python collections thereof, as well as some other types\n", "like numpy arrays. If you always pass as inputs to `@op`s objects like this, or\n", "`Ref`s obtained from other `@op`s, this issue will not come up. Indeed, if\n", "\"unwieldy\" objects are always results of `@op`s, a single copy of each such\n", "object will be saved and deserialized every time.\n", "\n", "This problem does, however, make it very difficult to detect when your `@op`s\n", "have side effects."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}