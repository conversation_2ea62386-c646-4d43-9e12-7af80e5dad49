"""
ComputationFrame 和 Storage 综合演示程序
展示两个核心组件如何协同工作，提供完整的程序运行周期观察

功能特点：
1. 完整的机器学习流水线演示
2. Storage和ComputationFrame的深度集成
3. 丰富的高可读性打印信息
4. 覆盖整个程序运行周期
5. 展示版本管理和计算图分析

基于mandala1框架，不创建新类，充分利用现有功能
"""

import os
import sys
import time
import tempfile
from typing import Dict, List, Any, Optional
import random

# 添加mandala1路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from mandala1.imports import Storage, op
    from mandala1.cf import ComputationFrame


class ComprehensiveDemo:
    """ComputationFrame和Storage综合演示类"""
    
    def __init__(self):
        self.storage = None
        self.temp_dir = None
        self.demo_results = {}
        
    def print_section_header(self, title: str, description: str, emoji: str = "🔍"):
        """打印章节标题"""
        print("\n" + "=" * 100)
        print(f"{emoji} {title}")
        print("=" * 100)
        print(f"📝 {description}")
        print("-" * 100)
    
    def print_subsection(self, title: str, emoji: str = "🔧"):
        """打印子章节标题"""
        print(f"\n{emoji} {title}")
        print("-" * 60)
    
    def print_storage_cf_status(self, storage: Storage, cf: ComputationFrame = None, 
                               title: str = "系统状态"):
        """打印Storage和ComputationFrame的状态"""
        print(f"\n📊 {title}:")
        
        # Storage状态
        print("  💾 Storage状态:")
        try:
            call_cache_size = len(storage.call_cache.cache) if hasattr(storage.call_cache, 'cache') else 0
            atoms_cache_size = len(storage.atoms.cache) if hasattr(storage.atoms, 'cache') else 0
            print(f"    🔄 调用缓存: {call_cache_size} 个条目")
            print(f"    ⚛️  原子缓存: {atoms_cache_size} 个条目")
            print(f"    🗄️  数据库: {storage.db.db_path}")
            print(f"    🔄 版本控制: {'启用' if storage.versioned else '禁用'}")
        except Exception as e:
            print(f"    ❌ Storage状态获取失败: {e}")
        
        # ComputationFrame状态
        if cf:
            print("  🔍 ComputationFrame状态:")
            print(f"    🔢 总节点数: {len(cf.nodes)}")
            print(f"    📦 变量节点: {len(cf.vnames)}")
            print(f"    ⚙️  函数节点: {len(cf.fnames)}")
            print(f"    🔗 边数量: {len(list(cf.edges()))}")
            print(f"    🌱 源节点: {len(cf.sources)}")
            print(f"    🎯 汇节点: {len(cf.sinks)}")
    
    def setup_ml_pipeline(self):
        """设置机器学习流水线"""
        self.print_section_header("机器学习流水线设置", 
                                 "定义完整的ML流水线函数，展示Storage和CF的协同工作", "🚀")
        
        # 数据生成和预处理
        @op
        def generate_dataset(n_samples: int = 1000, n_features: int = 10, noise: float = 0.1):
            """生成模拟数据集"""
            print(f"    📊 生成数据集: {n_samples} 样本, {n_features} 特征, 噪声={noise}")
            time.sleep(0.1)
            
            # 生成模拟数据
            random.seed(42)
            X = [[random.gauss(0, 1) for _ in range(n_features)] for _ in range(n_samples)]
            y = [sum(row[:3]) + random.gauss(0, noise) for row in X]  # 简单的线性关系
            
            return {'X': X, 'y': y, 'n_samples': n_samples, 'n_features': n_features}
        
        @op
        def split_dataset(dataset: dict, test_ratio: float = 0.2):
            """分割数据集"""
            print(f"    ✂️  分割数据集: 测试比例={test_ratio}")
            time.sleep(0.05)
            
            X, y = dataset['X'], dataset['y']
            n_test = int(len(X) * test_ratio)
            
            return {
                'X_train': X[:-n_test],
                'X_test': X[-n_test:],
                'y_train': y[:-n_test],
                'y_test': y[-n_test:],
                'train_size': len(X) - n_test,
                'test_size': n_test
            }
        
        @op
        def normalize_features(split_data: dict):
            """特征标准化"""
            print(f"    📏 特征标准化")
            time.sleep(0.05)
            
            X_train, X_test = split_data['X_train'], split_data['X_test']
            
            # 计算训练集的均值和标准差
            n_features = len(X_train[0])
            means = [sum(row[i] for row in X_train) / len(X_train) for i in range(n_features)]
            stds = [
                (sum((row[i] - means[i])**2 for row in X_train) / len(X_train))**0.5 
                for i in range(n_features)
            ]
            
            # 标准化
            X_train_norm = [
                [(row[i] - means[i]) / (stds[i] + 1e-8) for i in range(n_features)]
                for row in X_train
            ]
            X_test_norm = [
                [(row[i] - means[i]) / (stds[i] + 1e-8) for i in range(n_features)]
                for row in X_test
            ]
            
            result = split_data.copy()
            result.update({
                'X_train_norm': X_train_norm,
                'X_test_norm': X_test_norm,
                'feature_means': means,
                'feature_stds': stds
            })
            return result
        
        # 模型训练和评估
        @op
        def train_linear_model(data: dict, learning_rate: float = 0.01, epochs: int = 100):
            """训练线性模型"""
            print(f"    🎯 训练线性模型: lr={learning_rate}, epochs={epochs}")
            time.sleep(0.2)
            
            X_train, y_train = data['X_train_norm'], data['y_train']
            n_features = len(X_train[0])
            
            # 初始化权重
            weights = [0.0] * n_features
            bias = 0.0
            
            # 简单的梯度下降
            for epoch in range(epochs):
                total_loss = 0
                for i, (x, y_true) in enumerate(zip(X_train, y_train)):
                    # 前向传播
                    y_pred = sum(w * xi for w, xi in zip(weights, x)) + bias
                    loss = (y_pred - y_true) ** 2
                    total_loss += loss
                    
                    # 反向传播
                    error = y_pred - y_true
                    for j in range(n_features):
                        weights[j] -= learning_rate * error * x[j]
                    bias -= learning_rate * error
            
            return {
                'weights': weights,
                'bias': bias,
                'final_loss': total_loss / len(X_train),
                'learning_rate': learning_rate,
                'epochs': epochs
            }
        
        @op
        def evaluate_model(model: dict, data: dict):
            """评估模型"""
            print(f"    📈 评估模型性能")
            time.sleep(0.1)
            
            X_test, y_test = data['X_test_norm'], data['y_test']
            weights, bias = model['weights'], model['bias']
            
            # 预测
            predictions = [
                sum(w * xi for w, xi in zip(weights, x)) + bias
                for x in X_test
            ]
            
            # 计算指标
            mse = sum((pred - true)**2 for pred, true in zip(predictions, y_test)) / len(y_test)
            mae = sum(abs(pred - true) for pred, true in zip(predictions, y_test)) / len(y_test)
            
            # 计算R²
            y_mean = sum(y_test) / len(y_test)
            ss_tot = sum((y - y_mean)**2 for y in y_test)
            ss_res = sum((pred - true)**2 for pred, true in zip(predictions, y_test))
            r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            return {
                'mse': mse,
                'mae': mae,
                'r2': r2,
                'predictions': predictions[:10],  # 只保存前10个预测
                'actuals': y_test[:10]
            }
        
        print("✅ 机器学习流水线函数定义完成")
        print("  📊 数据生成: generate_dataset")
        print("  ✂️  数据分割: split_dataset")
        print("  📏 特征标准化: normalize_features")
        print("  🎯 模型训练: train_linear_model")
        print("  📈 模型评估: evaluate_model")
        
        return {
            'generate_dataset': generate_dataset,
            'split_dataset': split_dataset,
            'normalize_features': normalize_features,
            'train_linear_model': train_linear_model,
            'evaluate_model': evaluate_model
        }
    
    def run_initial_experiment(self, pipeline_funcs: dict):
        """运行初始实验"""
        self.print_section_header("初始实验执行", 
                                 "执行完整的ML流水线，观察Storage的记忆化和CF的构建", "🧪")
        
        # 创建Storage
        self.temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(self.temp_dir, "ml_demo.db")
        
        self.storage = Storage(
            db_path=db_path,
            deps_path='__main__',
            overflow_dir=os.path.join(self.temp_dir, "overflow"),
            track_globals=True
        )
        
        print(f"💾 创建Storage: {db_path}")
        
        # 执行流水线
        self.print_subsection("执行ML流水线")
        
        with self.storage:
            start_time = time.time()
            
            # 数据准备
            dataset = pipeline_funcs['generate_dataset'](n_samples=500, n_features=5)
            split_data = pipeline_funcs['split_dataset'](self.storage.unwrap(dataset))
            normalized_data = pipeline_funcs['normalize_features'](self.storage.unwrap(split_data))
            
            # 模型训练
            model = pipeline_funcs['train_linear_model'](
                self.storage.unwrap(normalized_data), 
                learning_rate=0.01, 
                epochs=50
            )
            
            # 模型评估
            evaluation = pipeline_funcs['evaluate_model'](
                self.storage.unwrap(model), 
                self.storage.unwrap(normalized_data)
            )
            
            execution_time = time.time() - start_time
        
        # 显示结果
        print("📊 实验结果:")
        eval_result = self.storage.unwrap(evaluation)
        print(f"  📈 MSE: {eval_result['mse']:.4f}")
        print(f"  📈 MAE: {eval_result['mae']:.4f}")
        print(f"  📈 R²: {eval_result['r2']:.4f}")
        print(f"  ⏱️  执行时间: {execution_time:.2f}秒")
        
        # 创建ComputationFrame
        self.print_subsection("创建ComputationFrame")
        cf = self.storage.cf(evaluation).expand_back(recursive=True)
        
        self.print_storage_cf_status(self.storage, cf, "初始实验后的状态")
        
        self.demo_results['initial'] = {
            'dataset': dataset,
            'model': model,
            'evaluation': evaluation,
            'cf': cf,
            'execution_time': execution_time
        }
        
        return cf

    def demonstrate_caching_benefits(self, pipeline_funcs: dict):
        """演示缓存带来的性能提升"""
        self.print_section_header("缓存性能演示",
                                 "重复执行相同计算，展示Storage记忆化的性能优势", "⚡")

        self.print_subsection("重复执行测试")

        # 重复执行相同的计算
        with self.storage:
            start_time = time.time()

            # 这些调用应该从缓存中加载
            dataset = pipeline_funcs['generate_dataset'](n_samples=500, n_features=5)
            split_data = pipeline_funcs['split_dataset'](self.storage.unwrap(dataset))
            normalized_data = pipeline_funcs['normalize_features'](self.storage.unwrap(split_data))
            model = pipeline_funcs['train_linear_model'](
                self.storage.unwrap(normalized_data),
                learning_rate=0.01,
                epochs=50
            )
            evaluation = pipeline_funcs['evaluate_model'](
                self.storage.unwrap(model),
                self.storage.unwrap(normalized_data)
            )

            cached_time = time.time() - start_time

        # 性能对比
        original_time = self.demo_results['initial']['execution_time']
        speedup = original_time / cached_time if cached_time > 0 else float('inf')

        print("📊 性能对比:")
        print(f"  🕐 首次执行时间: {original_time:.3f}秒")
        print(f"  ⚡ 缓存执行时间: {cached_time:.3f}秒")
        print(f"  🚀 性能提升: {speedup:.1f}x")

        # 验证结果一致性
        original_eval = self.storage.unwrap(self.demo_results['initial']['evaluation'])
        current_eval = self.storage.unwrap(evaluation)

        print("🔍 结果一致性验证:")
        print(f"  📈 MSE一致: {abs(original_eval['mse'] - current_eval['mse']) < 1e-10}")
        print(f"  📈 R²一致: {abs(original_eval['r2'] - current_eval['r2']) < 1e-10}")

        return speedup

    def demonstrate_cf_analysis(self):
        """演示ComputationFrame的深度分析"""
        self.print_section_header("ComputationFrame深度分析",
                                 "分析计算图结构，展示函数依赖关系和执行流程", "🔍")

        cf = self.demo_results['initial']['cf']

        # 1. 拓扑分析
        self.print_subsection("拓扑结构分析")
        try:
            topo_order = cf.topsort_modulo_sccs()
            print(f"📋 拓扑排序节点数: {len(topo_order)}")
            print(f"📋 函数节点: {list(cf.fnames)}")
            print(f"📋 变量节点数: {len(cf.vnames)}")
        except Exception as e:
            print(f"❌ 拓扑分析失败: {e}")

        # 2. 函数调用分析
        self.print_subsection("函数调用分析")
        try:
            calls_by_func = cf.calls_by_func()
            print("⚙️  函数调用统计:")
            for fname, calls in calls_by_func.items():
                print(f"  {fname}: {len(calls)} 次调用")

                # 获取函数调用表
                try:
                    func_table = cf.get_func_table(fname)
                    if not func_table.empty:
                        print(f"    📊 调用表形状: {func_table.shape}")
                        print(f"    📋 参数列: {[col for col in func_table.columns if not col.startswith('output_')]}")
                        print(f"    📋 输出列: {[col for col in func_table.columns if col.startswith('output_')]}")
                except Exception as e:
                    print(f"    ❌ 函数表获取失败: {e}")
        except Exception as e:
            print(f"❌ 函数调用分析失败: {e}")

        # 3. 依赖关系分析
        self.print_subsection("依赖关系分析")
        for fname in cf.fnames:
            try:
                print(f"🎯 分析函数: {fname}")

                # 上游分析
                upstream_cf = cf.upstream(fname)
                print(f"  ⬆️  上游函数: {list(upstream_cf.fnames)}")

                # 下游分析
                downstream_cf = cf.downstream(fname)
                print(f"  ⬇️  下游函数: {list(downstream_cf.fnames)}")

                # 邻居分析
                in_neighbors = cf.in_neighbors(fname)
                out_neighbors = cf.out_neighbors(fname)
                print(f"  ⬅️  输入邻居: {len(in_neighbors)} 个")
                print(f"  ➡️  输出邻居: {len(out_neighbors)} 个")

            except Exception as e:
                print(f"  ❌ {fname} 依赖分析失败: {e}")

        # 4. 数据流分析
        self.print_subsection("数据流分析")
        try:
            # 获取变量引用映射
            refs_by_var = cf.refs_by_var()
            print("📦 数据流统计:")

            # 统计不同类型的变量
            input_vars = []
            intermediate_vars = []
            output_vars = []

            for vname in cf.vnames:
                in_edges = list(cf.in_edges(vname))
                out_edges = list(cf.out_edges(vname))

                if not in_edges and out_edges:
                    input_vars.append(vname)
                elif in_edges and not out_edges:
                    output_vars.append(vname)
                elif in_edges and out_edges:
                    intermediate_vars.append(vname)

            print(f"  📥 输入变量: {len(input_vars)} 个")
            print(f"  🔄 中间变量: {len(intermediate_vars)} 个")
            print(f"  📤 输出变量: {len(output_vars)} 个")

        except Exception as e:
            print(f"❌ 数据流分析失败: {e}")

    def demonstrate_version_evolution(self, pipeline_funcs: dict):
        """演示版本演化和变更检测"""
        self.print_section_header("版本演化演示",
                                 "修改函数实现，展示Storage的版本管理能力", "🔄")

        # 1. 检查当前版本
        self.print_subsection("当前版本状态")
        if self.storage.versioned:
            for fname in ['train_linear_model', 'evaluate_model']:
                try:
                    func = pipeline_funcs[fname]
                    versions = self.storage.versions(func)
                    print(f"⚙️  {fname}: {len(versions)} 个版本")
                except Exception as e:
                    print(f"❌ {fname} 版本查询失败: {e}")

        # 2. 修改训练函数（增加正则化）
        self.print_subsection("修改训练函数（增加正则化）")

        @op
        def train_linear_model(data: dict, learning_rate: float = 0.01, epochs: int = 100,
                             regularization: float = 0.01):
            """训练线性模型（带正则化）"""
            print(f"    🎯 训练线性模型（正则化版本）: lr={learning_rate}, epochs={epochs}, reg={regularization}")
            time.sleep(0.2)

            X_train, y_train = data['X_train_norm'], data['y_train']
            n_features = len(X_train[0])

            # 初始化权重
            weights = [0.0] * n_features
            bias = 0.0

            # 带正则化的梯度下降
            for epoch in range(epochs):
                total_loss = 0
                for i, (x, y_true) in enumerate(zip(X_train, y_train)):
                    # 前向传播
                    y_pred = sum(w * xi for w, xi in zip(weights, x)) + bias
                    loss = (y_pred - y_true) ** 2
                    total_loss += loss

                    # 反向传播（带L2正则化）
                    error = y_pred - y_true
                    for j in range(n_features):
                        weights[j] -= learning_rate * (error * x[j] + regularization * weights[j])
                    bias -= learning_rate * error

            return {
                'weights': weights,
                'bias': bias,
                'final_loss': total_loss / len(X_train),
                'learning_rate': learning_rate,
                'epochs': epochs,
                'regularization': regularization  # 新增字段
            }

        # 3. 使用新版本函数
        with self.storage:
            # 获取之前的数据
            normalized_data = self.demo_results['initial']['dataset']
            split_data = pipeline_funcs['split_dataset'](self.storage.unwrap(normalized_data))
            normalized_data = pipeline_funcs['normalize_features'](self.storage.unwrap(split_data))

            # 使用新版本训练
            new_model = train_linear_model(
                self.storage.unwrap(normalized_data),
                learning_rate=0.01,
                epochs=50,
                regularization=0.01
            )

            # 评估新模型
            new_evaluation = pipeline_funcs['evaluate_model'](
                self.storage.unwrap(new_model),
                self.storage.unwrap(normalized_data)
            )

        # 4. 对比结果
        self.print_subsection("版本对比结果")
        original_eval = self.storage.unwrap(self.demo_results['initial']['evaluation'])
        new_eval = self.storage.unwrap(new_evaluation)

        print("📊 模型性能对比:")
        print(f"  原始模型 - MSE: {original_eval['mse']:.4f}, R²: {original_eval['r2']:.4f}")
        print(f"  正则化模型 - MSE: {new_eval['mse']:.4f}, R²: {new_eval['r2']:.4f}")
        print(f"  性能变化 - MSE: {new_eval['mse'] - original_eval['mse']:.4f}, R²: {new_eval['r2'] - original_eval['r2']:.4f}")

        # 5. 创建新的ComputationFrame
        new_cf = self.storage.cf(new_evaluation).expand_back(recursive=True)

        # 6. 合并ComputationFrame
        self.print_subsection("ComputationFrame合并")
        original_cf = self.demo_results['initial']['cf']

        # 并集操作
        combined_cf = original_cf | new_cf
        print(f"🔗 合并后的CF:")
        print(f"  原始CF节点数: {len(original_cf.nodes)}")
        print(f"  新CF节点数: {len(new_cf.nodes)}")
        print(f"  合并CF节点数: {len(combined_cf.nodes)}")

        self.demo_results['version_evolution'] = {
            'new_model': new_model,
            'new_evaluation': new_evaluation,
            'new_cf': new_cf,
            'combined_cf': combined_cf
        }

        return combined_cf


def main():
    """主函数：运行综合演示"""
    print("🚀 ComputationFrame 和 Storage 综合演示")
    print("=" * 100)
    print("📖 本演示展示Storage和ComputationFrame如何协同工作")
    print("🎯 通过完整的机器学习流水线演示两个组件的深度集成")
    print("=" * 100)
    
    demo = ComprehensiveDemo()
    
    try:
        # 设置ML流水线
        pipeline_funcs = demo.setup_ml_pipeline()
        
        # 运行初始实验
        cf = demo.run_initial_experiment(pipeline_funcs)

        # 演示缓存性能
        speedup = demo.demonstrate_caching_benefits(pipeline_funcs)

        # 演示CF分析
        demo.demonstrate_cf_analysis()

        # 演示版本演化
        combined_cf = demo.demonstrate_version_evolution(pipeline_funcs)

        print("\n" + "=" * 100)
        print("🎉 综合演示完成！")
        print("=" * 100)
        print("📊 演示总结:")
        print("  ✅ ML流水线设置完成")
        print("  ✅ 初始实验执行完成")
        print("  ✅ Storage记忆化验证")
        print("  ✅ ComputationFrame构建验证")
        print(f"  ✅ 缓存性能提升: {speedup:.1f}x")
        print("  ✅ CF深度分析完成")
        print("  ✅ 版本演化演示完成")

        print("\n🎯 核心功能展示:")
        print("  💾 Storage: 记忆化、版本管理、数据持久化")
        print("  🔍 ComputationFrame: 计算图构建、依赖分析、图操作")
        print("  🤝 协同工作: 完整ML流水线、性能优化、版本控制")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if demo.temp_dir and os.path.exists(demo.temp_dir):
            import shutil
            try:
                shutil.rmtree(demo.temp_dir)
                print(f"\n🧹 清理临时目录: {demo.temp_dir}")
            except Exception as e:
                print(f"\n⚠️  清理临时目录失败: {e}")


if __name__ == "__main__":
    main()
