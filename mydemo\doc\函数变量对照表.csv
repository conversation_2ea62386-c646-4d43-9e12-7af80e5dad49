文件名,类型,名称,是否已生成文档,文档位置
cf.py,类,ComputationFrame,是,cf.md
cf.py,函数,get_name_proj,是,cf.md
cf.py,函数,get_reverse_proj,是,cf.md
cf.py,方法,__init__,是,cf.md
cf.py,方法,_check,是,cf.md
cf.py,方法,vnames,是,cf.md
cf.py,方法,fnames,是,cf.md
cf.py,方法,ops,是,cf.md
cf.py,方法,nodes,是,cf.md
cf.py,方法,refs_by_var,是,cf.md
cf.py,方法,calls_by_func,是,cf.md
cf.py,方法,drop,是,cf.md
cf.py,方法,drop_node,是,cf.md
cf.py,方法,rename,是,cf.md
cf.py,方法,_add_var,是,cf.md
cf.py,方法,drop_var,是,cf.md
cf.py,方法,rename_var,是,cf.md
cf.py,方法,_add_func,是,cf.md
cf.py,方法,drop_func,是,cf.md
cf.py,方法,_add_edge,是,cf.md
cf.py,方法,_drop_edge,是,cf.md
cf.py,方法,edges,是,cf.md
cf.py,方法,add_ref,是,cf.md
cf.py,方法,drop_ref,是,cf.md
cf.py,方法,add_call,是,cf.md
cf.py,方法,drop_call,是,cf.md
cf.py,方法,in_neighbors,是,cf.md
cf.py,方法,out_neighbors,是,cf.md
cf.py,方法,in_neighbor_elts,是,cf.md
cf.py,方法,in_edges,是,cf.md
cf.py,方法,out_edges,是,cf.md
cf.py,方法,sources,是,cf.md
cf.py,方法,get_source_elts,是,cf.md
cf.py,方法,sinks,是,cf.md
cf.py,方法,get_sink_elts,是,cf.md
cf.py,方法,get_names_projecting_to,是,cf.md
cf.py,方法,get_io_proj,是,cf.md
cf.py,方法,get_adj_elts_edge,是,cf.md
cf.py,方法,get_adj_elts,是,cf.md
cf.py,方法,select_nodes,是,cf.md
cf.py,方法,select_subsets,是,cf.md
cf.py,方法,get_reachable_nodes,是,cf.md
cf.py,方法,downstream,是,cf.md
cf.py,方法,upstream,是,cf.md
cf.py,方法,midstream,是,cf.md
cf.py,方法,_binary_union,是,cf.md
cf.py,方法,_binary_intersection,是,cf.md
cf.py,方法,_binary_setwise_difference,是,cf.md
cf.py,方法,_binary_difference,是,cf.md
cf.py,方法,union,是,cf.md
cf.py,方法,intersection,是,cf.md
cf.py,方法,__or__,是,cf.md
cf.py,方法,__and__,是,cf.md
cf.py,方法,__sub__,是,cf.md
cf.py,方法,_group_calls,是,cf.md
cf.py,方法,_expand_from_call_groups,是,cf.md
cf.py,方法,get_creators,是,cf.md
cf.py,方法,get_consumers,是,cf.md
cf.py,方法,_expand_unidirectional,是,cf.md
cf.py,方法,expand_back,是,cf.md
cf.py,方法,expand_forward,是,cf.md
cf.py,方法,expand_all,是,cf.md
cf.py,方法,complete_func,是,cf.md
cf.py,方法,topsort_modulo_sccs,是,cf.md
cf.py,方法,sort_nodes,是,cf.md
cf.py,方法,_sort_df,是,cf.md
cf.py,方法,get_all_edges_on_paths_between,是,cf.md
cf.py,方法,eval,是,cf.md
cf.py,方法,df,是,cf.md
cf.py,方法,attach,是,cf.md
cf.py,方法,eval_df,是,cf.md
cf.py,方法,get,是,cf.md
cf.py,方法,get_var_values,是,cf.md
cf.py,方法,get_func_table,是,cf.md
cf.py,方法,_unify_subobjects,是,cf.md
cf.py,方法,_is_subobject,是,cf.md
cf.py,方法,get_direct_history,是,cf.md
cf.py,方法,get_total_history,是,cf.md
cf.py,方法,get_history_df,是,cf.md
cf.py,方法,get_joint_history_df,是,cf.md
cf.py,方法,sets,是,cf.md
cf.py,方法,values,是,cf.md
cf.py,方法,apply,是,cf.md
cf.py,方法,copy,是,cf.md
cf.py,方法,__getitem__,是,cf.md
cf.py,方法,get_reachable_elts,是,cf.md
cf.py,方法,get_reachable_elts_acyclic,是,cf.md
cf.py,方法,_add_adj_calls,是,cf.md
cf.py,方法,__lt__,是,cf.md
cf.py,方法,isin,是,cf.md
cf.py,方法,move_ref,是,cf.md
cf.py,方法,merge_into,是,cf.md
cf.py,方法,merge_vars,是,cf.md
cf.py,方法,merge,是,cf.md
cf.py,方法,split,是,cf.md
cf.py,方法,drop_unreachable,是,cf.md
cf.py,方法,simplify,是,cf.md
cf.py,方法,cleanup,是,cf.md
cf.py,静态方法,from_op,是,cf.md
cf.py,静态方法,from_refs,是,cf.md
cf.py,静态方法,from_vars,是,cf.md
cf.py,方法,delete_calls,是,cf.md
cf.py,方法,delete_calls_from_df,是,cf.md
cf.py,方法,get_new_vname,是,cf.md
cf.py,方法,get_new_fname,是,cf.md
cf.py,方法,print_graph,是,cf.md
cf.py,方法,get_graph_desc,是,cf.md
cf.py,方法,draw,是,cf.md
cf.py,方法,__repr__,是,cf.md
cf.py,方法,get_var_stats,是,cf.md
cf.py,方法,get_func_stats,是,cf.md
cf.py,方法,_get_prettytable_str,是,cf.md
cf.py,方法,var_info,是,cf.md
cf.py,方法,func_info,是,cf.md
cf.py,方法,info,是,cf.md
cf.py,方法,_ipython_key_completions_,是,cf.md
storage.py,类,Storage,是,storage.md
storage.py,方法,__init__,是,storage.md
storage.py,方法,dump_config,是,storage.md
storage.py,方法,conn,是,storage.md
storage.py,方法,vacuum,是,storage.md
storage.py,方法,mode,是,storage.md
storage.py,方法,allow_new_calls,是,storage.md
storage.py,方法,clear_cache,是,storage.md
storage.py,方法,cache_info,是,storage.md
storage.py,方法,preload_calls,是,storage.md
storage.py,方法,preload_shapes,是,storage.md
storage.py,方法,preload_ops,是,storage.md
storage.py,方法,preload_atoms,是,storage.md
storage.py,方法,preload,是,storage.md
storage.py,方法,commit,是,storage.md
storage.py,方法,__repr__,是,storage.md
storage.py,方法,in_context,是,storage.md
storage.py,方法,_tables,是,storage.md
storage.py,方法,save_ref,是,storage.md
storage.py,方法,load_ref,是,storage.md
storage.py,方法,_drop_ref_hid,是,storage.md
storage.py,方法,_drop_ref,是,storage.md
storage.py,方法,cleanup_refs,是,storage.md
storage.py,方法,exists_call,是,storage.md
storage.py,方法,save_call,是,storage.md
storage.py,方法,mget_call,是,storage.md
storage.py,方法,_get_call_from_data,是,storage.md
storage.py,方法,get_call,是,storage.md
storage.py,方法,drop_calls,是,storage.md
storage.py,方法,get_ref_creator,是,storage.md
storage.py,方法,get_creators,是,storage.md
storage.py,方法,get_consumers,是,storage.md
storage.py,方法,get_orphans,是,storage.md
storage.py,方法,get_unreferenced_cids,是,storage.md
storage.py,方法,_unwrap_atom,是,storage.md
storage.py,方法,_attach_atom,是,storage.md
storage.py,方法,unwrap,是,storage.md
storage.py,方法,attach,是,storage.md
storage.py,方法,get_struct_builder,是,storage.md
storage.py,方法,get_struct_inputs,是,storage.md
storage.py,方法,get_struct_tps,是,storage.md
storage.py,方法,construct,是,storage.md
storage.py,方法,destruct,是,storage.md
storage.py,方法,lookup_call,是,storage.md
storage.py,方法,get_defaults,是,storage.md
storage.py,方法,parse_args,是,storage.md
storage.py,方法,call_internal,是,storage.md
storage.py,方法,get_versioner,是,storage.md
storage.py,方法,save_versioner,是,storage.md
storage.py,方法,versioned,是,storage.md
storage.py,方法,guess_code_state,是,storage.md
storage.py,方法,sync_code,是,storage.md
storage.py,方法,sync_component,是,storage.md
storage.py,方法,_show_version_data,是,storage.md
storage.py,方法,versions,是,storage.md
storage.py,方法,source_history,是,storage.md
storage.py,方法,code,是,storage.md
storage.py,方法,get_code,是,storage.md
storage.py,方法,diff,是,storage.md
storage.py,方法,drop_version,是,storage.md
storage.py,方法,cf,是,storage.md
storage.py,方法,call,是,storage.md
storage.py,方法,__call__,是,storage.md
storage.py,方法,__enter__,是,storage.md
storage.py,方法,__exit__,是,storage.md
storage.py,函数,noop,是,storage.md
model.py,类,Ref,是,model.md
model.py,类,AtomRef,是,model.md
model.py,类,_Ignore,是,model.md
model.py,类,_NewArgDefault,是,model.md
model.py,类,ValuePointer,是,model.md
model.py,类,Op,是,model.md
model.py,类,Call,是,model.md
model.py,类,ListRef,是,model.md
model.py,类,DictRef,是,model.md
model.py,类,SetRef,是,model.md
model.py,类,Context,是,model.md
model.py,类,RefCollection,是,model.md
model.py,类,ValueCollection,是,model.md
model.py,类,CallCollection,是,model.md
model.py,函数,Ignore,是,model.md
model.py,函数,NewArgDefault,是,model.md
model.py,函数,unwrap_special_value,是,model.md
model.py,函数,wrap_atom,是,model.md
model.py,函数,recurse_on_ref_collections,是,model.md
model.py,函数,__make_list__,是,model.md
model.py,函数,__make_dict__,是,model.md
model.py,函数,__make_set__,是,model.md
model.py,函数,__make_tuple__,是,model.md
model.py,函数,__list_getitem__,是,model.md
model.py,函数,__dict_getitem__,是,model.md
model.py,函数,make_ref_set,是,model.md
model.py,函数,op,是,model.md
utils.py,函数,dataframe_to_prettytable,是,utils.md
utils.py,函数,serialize,是,utils.md
utils.py,函数,deserialize,是,utils.md
utils.py,函数,_conservative_equality_check,是,utils.md
utils.py,函数,get_content_hash,是,utils.md
utils.py,函数,dump_output_name,是,utils.md
utils.py,函数,parse_output_name,是,utils.md
utils.py,函数,get_setdict_union,是,utils.md
utils.py,函数,get_setdict_intersection,是,utils.md
utils.py,函数,get_dict_union_over_keys,是,utils.md
utils.py,函数,get_dict_intersection_over_keys,是,utils.md
utils.py,函数,get_adjacency_union,是,utils.md
utils.py,函数,get_adjacency_intersection,是,utils.md
utils.py,函数,get_nullable_union,是,utils.md
utils.py,函数,get_nullable_intersection,是,utils.md
utils.py,函数,get_adj_from_edges,是,utils.md
utils.py,函数,boundargs_to_args_kwargs,是,utils.md
utils.py,函数,parse_returns,是,utils.md
utils.py,函数,unwrap_decorators,是,utils.md
utils.py,函数,is_subdict,是,utils.md
utils.py,函数,invert_dict,是,utils.md
utils.py,函数,find_strongly_connected_components,是,utils.md
utils.py,函数,create_super_graph,是,utils.md
utils.py,函数,topological_sort,是,utils.md
utils.py,函数,almost_topological_sort,是,utils.md
utils.py,函数,get_edges_in_paths,是,utils.md
utils.py,函数,ask_user,是,utils.md
utils.py,函数,mock_input,是,utils.md
storage_utils.py,类,DBAdapter,是,storage_utils.md
storage_utils.py,类,DictStorage,是,storage_utils.md
storage_utils.py,类,JoblibDictStorage,是,storage_utils.md
storage_utils.py,类,SQLiteDictStorage,是,storage_utils.md
storage_utils.py,类,CachedDictStorage,是,storage_utils.md
storage_utils.py,类,InMemCallStorage,是,storage_utils.md
storage_utils.py,类,SQLiteCallStorage,是,storage_utils.md
storage_utils.py,类,CachedCallStorage,是,storage_utils.md
storage_utils.py,函数,is_in_memory_db,是,storage_utils.md
storage_utils.py,函数,transaction,是,storage_utils.md
config.py,类,Config,是,config.md
config.py,函数,get_mandala_path,是,config.md
config.py,函数,tensor_to_numpy,是,config.md
tps.py,类,MList,是,tps.md
tps.py,类,MDict,是,tps.md
tps.py,类,MSet,是,tps.md
tps.py,类,MTuple,是,tps.md
tps.py,类,Type,是,tps.md
tps.py,类,AtomType,是,tps.md
tps.py,类,ListType,是,tps.md
tps.py,类,DictType,是,tps.md
tps.py,类,SetType,是,tps.md
tps.py,类,TupleType,是,tps.md
viz.py,类,Node,是,viz.md
viz.py,类,Edge,是,viz.md
viz.py,类,Group,是,viz.md
viz.py,函数,dict_to_dot_string,是,viz.md
viz.py,函数,get_group_string,是,viz.md
viz.py,函数,to_dot_string,是,viz.md
viz.py,函数,write_output,是,viz.md
viz.py,函数,_get_colorized_diff,是,viz.md
common_imports.py,类,Session,是,common_imports.md
imports.py,函数,pprint_dict,是,imports.md
__init__.py,包初始化,包级别导入,是,__init__.md
deps/crawler.py,函数,crawl_obj,是,deps/crawler.md
deps/crawler.py,函数,crawl_static,是,deps/crawler.md
deps/model.py,类,Node,是,deps/model.md
deps/model.py,类,CallableNode,是,deps/model.md
deps/model.py,类,GlobalVarNode,是,deps/model.md
deps/model.py,类,TerminalData,是,deps/model.md
deps/model.py,类,TerminalNode,是,deps/model.md
deps/model.py,类,DependencyGraph,是,deps/model.md
deps/model.py,方法,__init__,是,deps/model.md
deps/model.py,方法,key,是,deps/model.md
deps/model.py,方法,present_key,是,deps/model.md
deps/model.py,方法,represent,是,deps/model.md
deps/model.py,方法,content,是,deps/model.md
deps/model.py,方法,readable_content,是,deps/model.md
deps/model.py,方法,content_hash,是,deps/model.md
deps/model.py,方法,load_obj,是,deps/model.md
deps/model.py,静态方法,from_obj,是,deps/model.md
deps/model.py,静态方法,from_runtime,是,deps/model.md
deps/model.py,方法,_set_representation,是,deps/model.md
deps/model.py,方法,is_method,是,deps/model.md
deps/model.py,方法,class_name,是,deps/model.md
deps/model.py,方法,get_trace_state,是,deps/model.md
deps/model.py,方法,show,是,deps/model.md
deps/model.py,方法,__repr__,是,deps/model.md
deps/model.py,方法,add_node,是,deps/model.md
deps/model.py,方法,add_edge,是,deps/model.md
deps/utils.py,类,GlobalClassifier,是,deps/utils.md
deps/utils.py,静态方法,is_excluded,是,deps/utils.md
deps/utils.py,静态方法,is_scalar,是,deps/utils.md
deps/utils.py,静态方法,is_data,是,deps/utils.md
deps/utils.py,函数,is_global_val,是,deps/utils.md
deps/utils.py,函数,is_callable_obj,是,deps/utils.md
deps/utils.py,函数,extract_func_obj,是,deps/utils.md
deps/utils.py,函数,extract_code,是,deps/utils.md
deps/utils.py,函数,get_runtime_description,是,deps/utils.md
deps/utils.py,函数,get_global_names_candidates,是,deps/utils.md
deps/utils.py,函数,get_sanitized_bytecode_representation,是,deps/utils.md
deps/utils.py,函数,unknown_function,是,deps/utils.md
deps/utils.py,函数,get_bytecode,是,deps/utils.md
deps/utils.py,函数,hash_dict,是,deps/utils.md
deps/utils.py,函数,load_obj,是,deps/utils.md
deps/utils.py,函数,get_dep_key_from_func,是,deps/utils.md
deps/utils.py,函数,get_func_qualname,是,deps/utils.md
deps/utils.py,函数,get_func_qualname_fallback,是,deps/utils.md
deps/versioner.py,类,CodeState,是,deps/versioner.md
deps/versioner.py,类,Versioner,是,deps/versioner.md
deps/versioner.py,方法,__init__,是,deps/versioner.md
deps/versioner.py,方法,__repr__,是,deps/versioner.md
deps/versioner.py,方法,get_content_version,是,deps/versioner.md
deps/versioner.py,方法,add_globals_from,是,deps/versioner.md
deps/versioner.py,方法,drop_semantic_version,是,deps/versioner.md
deps/versioner.py,方法,get_version_ids,是,deps/versioner.md
deps/versioner.py,方法,update_global_topology,是,deps/versioner.md
deps/versioner.py,方法,make_tracer,是,deps/versioner.md
deps/versioner.py,方法,guess_code_state,是,deps/versioner.md
deps/versioner.py,方法,get_codestate_semantic_hashes,是,deps/versioner.md
deps/versioner.py,方法,apply_state_hypothesis,是,deps/versioner.md
deps/versioner.py,方法,get_semantic_version,是,deps/versioner.md
deps/versioner.py,方法,init_component,是,deps/versioner.md
deps/versioner.py,方法,sync_codebase,是,deps/versioner.md
deps/versioner.py,方法,sync_component,是,deps/versioner.md
deps/versioner.py,方法,get_current_versions,是,deps/versioner.md
deps/versioner.py,方法,get_semantically_compatible_versions,是,deps/versioner.md
deps/versioner.py,方法,create_new_components_from_nodes,是,deps/versioner.md
deps/versioner.py,方法,sync_version,是,deps/versioner.md
deps/versioner.py,方法,lookup_call,是,deps/versioner.md
deps/versioner.py,方法,process_trace,是,deps/versioner.md
deps/versioner.py,方法,_check_semantic_distinguishability,是,deps/versioner.md
deps/versioner.py,方法,get_flat_versions,是,deps/versioner.md
deps/versioner.py,方法,get_dependent_versions,是,deps/versioner.md
deps/versioner.py,方法,present_dependencies,是,deps/versioner.md
deps/versioner.py,方法,show_versions,是,deps/versioner.md
deps/versioner.py,方法,get_canonical_groups,是,deps/versioner.md
deps/shallow_versions.py,函数,get_diff,是,deps/shallow_versions.md
deps/shallow_versions.py,函数,apply_diff,是,deps/shallow_versions.md
deps/shallow_versions.py,类,Commit,是,deps/shallow_versions.md
deps/shallow_versions.py,类,DAG,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,__init__,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,__repr__,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,get_content,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,diff_representation,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,add_commit,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,get_presentable_content,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,show_history,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,get_history,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,load_all,是,deps/shallow_versions.md
deps/shallow_versions.py,方法,merge_commits,是,deps/shallow_versions.md
deps/deep_versions.py,类,Version,是,deps/deep_versions.md
deps/deep_versions.py,方法,__init__,是,deps/deep_versions.md
deps/deep_versions.py,方法,presentation,是,deps/deep_versions.md
deps/deep_versions.py,静态方法,from_trace,是,deps/deep_versions.md
deps/deep_versions.py,方法,_set_content_expansion,是,deps/deep_versions.md
deps/deep_versions.py,方法,_set_content_version,是,deps/deep_versions.md
deps/deep_versions.py,方法,_set_semantic_expansion,是,deps/deep_versions.md
deps/deep_versions.py,方法,sync,是,deps/deep_versions.md
deps/deep_versions.py,方法,content_version,是,deps/deep_versions.md
deps/deep_versions.py,方法,semantic_version,是,deps/deep_versions.md
deps/deep_versions.py,方法,semantic_expansion,是,deps/deep_versions.md
deps/deep_versions.py,方法,content_expansion,是,deps/deep_versions.md
deps/deep_versions.py,方法,support,是,deps/deep_versions.md
deps/deep_versions.py,方法,is_synced,是,deps/deep_versions.md
deps/deep_versions.py,方法,set_synced,是,deps/deep_versions.md
deps/deep_versions.py,方法,__repr__,是,deps/deep_versions.md
deps/viz.py,函数,to_string,是,deps/viz.md
deps/viz.py,函数,to_dot,是,deps/viz.md
deps/tracers/__init__.py,包初始化,TracerABC导出,是,deps/tracers/__init__.md
deps/tracers/__init__.py,包初始化,DecTracer导出,是,deps/tracers/__init__.md
deps/tracers/__init__.py,包初始化,SysTracer导出,是,deps/tracers/__init__.md
deps/tracers/tracer_base.py,类,TracerABC,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,方法,__init__,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,方法,__enter__,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,方法,__exit__,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,静态方法,get_active_trace_obj,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,静态方法,set_active_trace_obj,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,静态方法,register_leaf_event,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,函数,get_closure_names,是,deps/tracers/tracer_base.md
deps/tracers/tracer_base.py,函数,get_module_flow,是,deps/tracers/tracer_base.md
